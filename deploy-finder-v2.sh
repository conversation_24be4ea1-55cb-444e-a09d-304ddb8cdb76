#!/bin/bash

# Finder-v2 Vue.js Deployment Script
# Supports both development and production builds
# Usage: 
#   ./deploy-finder-v2.sh          # Production build (default)
#   ./deploy-finder-v2.sh dev      # Development build (keeps console.logs)
#   ./deploy-finder-v2.sh prod     # Production build (removes console.logs)
# Run from wheel-size-services project root directory

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="$(pwd)"
WIDGET_APP_PATH="src/apps/widgets/finder_v2/app"
STATIC_PATH="src/apps/widgets/finder_v2/static/finder_v2"
DIST_PATH="${WIDGET_APP_PATH}/dist"

# Parse build mode argument
BUILD_MODE="${1:-prod}"  # Default to production if not specified

# Validate build mode
if [[ "$BUILD_MODE" != "dev" && "$BUILD_MODE" != "prod" && "$BUILD_MODE" != "development" && "$BUILD_MODE" != "production" ]]; then
    echo -e "${RED}❌ Invalid build mode: ${BUILD_MODE}${NC}"
    echo -e "${YELLOW}Usage:${NC}"
    echo -e "  ${CYAN}./deploy-finder-v2.sh${NC}          # Production build (default)"
    echo -e "  ${CYAN}./deploy-finder-v2.sh dev${NC}      # Development build (keeps console.logs)"
    echo -e "  ${CYAN}./deploy-finder-v2.sh prod${NC}     # Production build (removes console.logs)"
    exit 1
fi

# Normalize build mode
if [[ "$BUILD_MODE" == "development" ]]; then
    BUILD_MODE="dev"
elif [[ "$BUILD_MODE" == "production" ]]; then
    BUILD_MODE="prod"
fi

# Set build command based on mode
if [[ "$BUILD_MODE" == "dev" ]]; then
    BUILD_COMMAND="npm run build -- --mode development"
    BUILD_MODE_DISPLAY="DEVELOPMENT (console.logs preserved)"
    BUILD_COLOR=$CYAN
else
    BUILD_COMMAND="npm run build"
    BUILD_MODE_DISPLAY="PRODUCTION (console.logs removed)"
    BUILD_COLOR=$MAGENTA
fi

echo -e "${BLUE}🚀 Starting Finder-v2 Vue.js Deployment Workflow${NC}"
echo -e "${BLUE}========================================================${NC}"
echo -e "${BUILD_COLOR}📦 Build Mode: ${BUILD_MODE_DISPLAY}${NC}"
echo -e "${BLUE}========================================================${NC}"

# Step 1: Verify we're in the correct directory
echo -e "\n${YELLOW}📍 Step 1: Verifying project directory...${NC}"
if [[ ! -f "docker-compose.yml" ]] || [[ ! -d "src/apps/widgets/finder_v2" ]]; then
    echo -e "${RED}❌ Error: Must run from wheel-size-services project root directory${NC}"
    echo -e "${RED}   Current directory: ${PROJECT_ROOT}${NC}"
    echo -e "${RED}   Expected files: docker-compose.yml, src/apps/widgets/finder_v2/${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Project directory verified: ${PROJECT_ROOT}${NC}"

# Step 2: Check if Docker containers are running
echo -e "\n${YELLOW}📍 Step 2: Checking Docker containers...${NC}"
if ! docker compose ps | grep -q "ws_services.*Up"; then
    echo -e "${RED}❌ Error: Docker containers are not running${NC}"
    echo -e "${YELLOW}   Starting containers...${NC}"
    docker compose up -d
    echo -e "${GREEN}✅ Docker containers started${NC}"
else
    echo -e "${GREEN}✅ Docker containers are running${NC}"
fi

# Step 3: Build Vue.js application
echo -e "\n${YELLOW}📍 Step 3: Building Vue.js application...${NC}"
echo -e "${BUILD_COLOR}   Mode: ${BUILD_MODE_DISPLAY}${NC}"
echo -e "${BLUE}   Running: ${BUILD_COMMAND} in Docker container${NC}"

# Show build configuration details
if [[ "$BUILD_MODE" == "dev" ]]; then
    echo -e "${CYAN}   ℹ️  Development build features:${NC}"
    echo -e "${CYAN}      • Console.log statements preserved${NC}"
    echo -e "${CYAN}      • Source maps included${NC}"
    echo -e "${CYAN}      • Debugging information available${NC}"
else
    echo -e "${MAGENTA}   ℹ️  Production build features:${NC}"
    echo -e "${MAGENTA}      • Console.log statements removed${NC}"
    echo -e "${MAGENTA}      • Code minified and optimized${NC}"
    echo -e "${MAGENTA}      • Smaller bundle size${NC}"
fi

if docker compose exec web bash -c "cd ${WIDGET_APP_PATH} && ${BUILD_COMMAND}"; then
    echo -e "${GREEN}✅ Vue.js build completed successfully${NC}"
else
    echo -e "${RED}❌ Error: Vue.js build failed${NC}"
    exit 1
fi

# Step 4: Verify dist directory exists
echo -e "\n${YELLOW}📍 Step 4: Verifying build output...${NC}"
if [[ ! -d "${DIST_PATH}" ]]; then
    echo -e "${RED}❌ Error: Build output directory not found: ${DIST_PATH}${NC}"
    exit 1
fi

# List built files with size information
echo -e "${BLUE}   Built files:${NC}"
total_size=0
while IFS= read -r file; do
    if [[ -f "$file" ]]; then
        size=$(du -h "$file" | cut -f1)
        filename=$(basename "$file")
        echo -e "${BLUE}   📄 ${filename} (${size})${NC}"
        
        # Calculate total size for JS files
        if [[ "$filename" == *.js ]]; then
            size_bytes=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
            total_size=$((total_size + size_bytes))
        fi
    fi
done < <(find "${DIST_PATH}" -name "*.js" -o -name "*.css")

# Show total bundle size
if [[ $total_size -gt 0 ]]; then
    total_size_kb=$((total_size / 1024))
    echo -e "${BUILD_COLOR}   📊 Total JS bundle size: ${total_size_kb} KB${NC}"
fi

# Step 5: Copy static files to Django directory
echo -e "\n${YELLOW}📍 Step 5: Copying static files to Django directory...${NC}"
echo -e "${BLUE}   Source: ${DIST_PATH}/*${NC}"
echo -e "${BLUE}   Target: ${STATIC_PATH}/${NC}"

# Create static directory if it doesn't exist
mkdir -p "${STATIC_PATH}"

# Copy files
if cp -R "${DIST_PATH}"/* "${STATIC_PATH}/"; then
    echo -e "${GREEN}✅ Static files copied successfully${NC}"
    
    # Show copied files with timestamps
    echo -e "${BLUE}   Deployed files:${NC}"
    find "${STATIC_PATH}" -name "*.js" -o -name "*.css" | head -10 | while read -r file; do
        timestamp=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$file" 2>/dev/null || stat -c "%y" "$file" 2>/dev/null | cut -d'.' -f1)
        echo -e "${BLUE}   📄 $(basename "$file") - ${timestamp}${NC}"
    done
else
    echo -e "${RED}❌ Error: Failed to copy static files${NC}"
    exit 1
fi

# Step 6: Restart Django development server
echo -e "\n${YELLOW}📍 Step 6: Restarting Django development server...${NC}"
if docker compose restart web; then
    echo -e "${GREEN}✅ Django server restarted successfully${NC}"
    
    # Wait for server to be ready
    echo -e "${BLUE}   Waiting for server to be ready...${NC}"
    sleep 3
    
    # Check if server is responding
    if curl -s -o /dev/null -w "%{http_code}" "http://development.local:8000/widget/finder-v2/?config" | grep -q "200"; then
        echo -e "${GREEN}✅ Server is responding correctly${NC}"
    else
        echo -e "${YELLOW}⚠️  Server may still be starting up${NC}"
    fi
else
    echo -e "${RED}❌ Error: Failed to restart Django server${NC}"
    exit 1
fi

# Step 7: Success summary
echo -e "\n${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}✅ Vue.js application built in ${BUILD_COLOR}${BUILD_MODE_DISPLAY}${GREEN} mode${NC}"
echo -e "${GREEN}✅ Static files deployed${NC}"
echo -e "${GREEN}✅ Django server restarted${NC}"

# Show build mode specific information
if [[ "$BUILD_MODE" == "dev" ]]; then
    echo -e "\n${CYAN}🔧 Development Build Info:${NC}"
    echo -e "${CYAN}   • Console.log statements are ACTIVE${NC}"
    echo -e "${CYAN}   • Use browser DevTools to see debug output${NC}"
    echo -e "${CYAN}   • Larger bundle size due to debug information${NC}"
else
    echo -e "\n${MAGENTA}⚡ Production Build Info:${NC}"
    echo -e "${MAGENTA}   • Console.log statements are REMOVED${NC}"
    echo -e "${MAGENTA}   • Optimized for performance${NC}"
    echo -e "${MAGENTA}   • Smaller bundle size${NC}"
fi

echo -e "\n${BLUE}🌐 Widget URL: http://development.local:8000/widget/finder-v2/?config${NC}"
echo -e "${BLUE}💡 You can now test your Vue.js changes in the browser${NC}"

# Show usage reminder
echo -e "\n${YELLOW}📝 Usage reminder:${NC}"
echo -e "   ${CYAN}./deploy-finder-v2.sh dev${NC}  - Development build (with console.logs)"
echo -e "   ${CYAN}./deploy-finder-v2.sh prod${NC} - Production build (without console.logs)"
echo -e "   ${CYAN}./deploy-finder-v2.sh${NC}      - Production build (default)"