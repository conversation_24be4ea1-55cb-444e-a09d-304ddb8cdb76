# Production Deployment Checklist (Wheel-Size Services)

Use this as a concise runbook for production releases. For background, see `docs/upgrade/production-deployment-guide.md` (may be outdated).

## 1) Pre-deployment
- Backup DB (mandatory):
```bash
pg_dump -h <db_host> -U <db_user> -d <db_name> > backup_$(date +%Y%m%d_%H%M%S).sql
```
- Enable maintenance mode:
```bash
python manage.py maintenance_mode on
```
- Pull latest code (production branch):
```bash
git fetch origin && git checkout <production-branch> && git pull --ff-only
```

## 2) Dependencies (Poetry)
- Ensure private PyPI configured (once):
```bash
poetry config repositories.ws https://pypi.wheel-size.com/
# If needed: poetry config http-basic.ws <user> <token>
```
- Install prod deps:
```bash
poetry install --only=main --no-dev
```
- Optional sanity check:
```bash
python - <<'PY'
import ws_fields, ws_django_helpers, ws_live_settings, ws_calc
print('✅ WS packages import OK')
PY
```

## 3) Finder‑v2 frontend build
- Build Vue app and copy static:
```bash
# Adjust container name if different
docker exec ws_services bash -c "cd src/apps/widgets/finder_v2/app && npm ci && npm run build"
cp -R src/apps/widgets/finder_v2/app/dist/* src/apps/widgets/finder_v2/static/finder_v2/
```

## 4) Django migrations
```bash
python manage.py check --deploy
python manage.py showmigrations --plan
python manage.py migrate --verbosity=2
python manage.py showmigrations
```

## 5) Static files
```bash
python manage.py collectstatic --noinput
```

## 6) Production settings sanity (critical)
- Verify CSRF:
```python
# src/settings/aws_prod.py
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': False,
    'trusted_hostnames': ['services.wheel-size.com'],
    'debug_csrf_validation': False,
}
```
- Widget JS fallback host should be `services.wheel-size.com`.
- REST proxy host/headers must target production API.

## 7) Finder‑v2 default configuration
- Defaults are auto-created on first migrate (post_migrate). Verify empty Domain Names:
```bash
python manage.py shell -c "from src.apps.widgets.common.models import WidgetConfig; c=WidgetConfig.get_default('finder-v2'); print('Domains:', c.params['permissions']['domains'])"
# Expected: Domains: []
```
- If an old default persists, recreate once (if helper is enabled):
- `https://services.wheel-size.com/recreate-finder-v2-default/`

## 8) Services
```bash
# Restart app services
sudo systemctl restart ws-services || {
  sudo systemctl restart gunicorn
  # sudo systemctl restart celery  # if used
}
```

## 9) Go‑live checks
```bash
# Disable maintenance
python manage.py maintenance_mode off

# Admin health
curl -I https://services.wheel-size.com/admin/

# Widget JS
curl -I https://services.wheel-size.com/code/ws-widget.js

# Finder‑v2 iframe (public try/demo if available)
curl -I https://services.wheel-size.com/widget/finder-v2/try/

# CSRF spot-check (should be blocked with bad Referer)
curl -I -H "Referer: https://evil.com/" \
  https://services.wheel-size.com/widget/finder-v2/api/yr
```

## 10) Monitoring & rollback
- Monitor logs ≥ 30 min:
```bash
tail -f /var/log/gunicorn/error.log
tail -f /var/log/nginx/error.log
```
- Emergency rollback: revert code, reinstall Poetry deps, restore DB if needed, restart services, re-enable maintenance off when stable.

Notes
- New Finder‑v2 widgets start with an empty Domain Names list; cross‑origin embeds are blocked by default. Same‑origin portal previews are always allowed.


