# Docker Project Configuration Summary

Last Modified: 2025-01-21 19:30 UTC+6

## Project Context

### Application Overview
- **Framework**: Django 4.2 LTS application with Vue.js widgets
- **Primary Widget**: finder-v2 (Vue.js-based wheel/tire finder)
- **Package Management**: Poetry for Python dependencies
- **Private Repository**: WS packages hosted on private PyPI (https://pypi.wheel-size.com/)
- **CAPTCHA Solution**: Cloudflare Turnstile (replaced django-recaptcha)
- **Development Host**: development.local (instead of localhost)

### Architecture Components
- **Backend**: Django 4.2 REST API and admin interface
- **Frontend**: Vue.js widgets with TailwindCSS styling
- **Database**: PostgreSQL 15 (external container)
- **Cache**: Redis (for sessions and caching)
- **Reverse Proxy**: Nginx (for static files and routing)

## Docker Configuration Details

### Base Infrastructure
```dockerfile
# Base Image
FROM public.ecr.aws/amazonlinux/amazonlinux:latest

# Operating System: Amazon Linux (latest)
# Rationale: AWS-optimized, security-focused, production-ready
```

### Python Environment
```dockerfile
# Python Version: 3.12.0 (via pyenv)
# Installation: /root/.pyenv/versions/3.12.0/
# Global Setting: pyenv global 3.12.0
# Package Manager: Poetry (latest)
```

### Node.js Environment
```dockerfile
# Node.js: LTS version (via NodeSource repository)
# Package Manager: npm (for Vue.js widget builds)
# Global Packages: less (for legacy LESS compilation)
```

### Poetry Configuration
```dockerfile
# Virtual Environment: Disabled (packages installed globally)
# Configuration:
poetry config virtualenvs.create false
poetry config virtualenvs.in-project false
poetry config repositories.ws https://pypi.wheel-size.com/
poetry config http-basic.ws repo-user Ojc3ZSPwIBEEisX
```

### Private PyPI Authentication
```bash
# Repository URL: https://pypi.wheel-size.com/
# Authentication: HTTP Basic Auth
# Username: repo-user
# Password: Ojc3ZSPwIBEEisX (embedded in Dockerfile)
# Security Note: Consider using build secrets for production
```

## Dependencies and Packages

### Core Python Dependencies (pyproject.toml)
```toml
# Framework
django = "^4.2.21"
gunicorn = "^21.2.0"

# Database & Storage
psycopg2-binary = "^2.9.7"
boto3 = "^1.28.0"
django-storages = "^1.9.1"

# Authentication & Security
django-turnstile = "^0.1.2"  # Cloudflare Turnstile CAPTCHA
sentry-sdk = "^1.32.0"

# UI & Forms
django-admin-interface = "^0.30.0"
django-registration-redux = "^2.13"
whitenoise = "^6.5.0"
```

### WS Packages (Private PyPI)
```toml
# All upgraded to Django 4.2 compatibility (v2.0.0+)
ws-django-helpers = "^2.0.0"
ws-django-fields = "^2.0.0" 
ws-django-live-settings = "^2.0.0"
ws-django-rest-framework-proxy = "^2.0.1"  # Includes HEADERS bug fix
ws-django-tire-calc = "2.0.0"  # Specific version with JavaScript fixes
ws-django-betterforms = "^2.0.0"  # Django 4.2 compatible fork
```

### Development Dependencies
```toml
# Testing
pytest = "^7.4.0"
pytest-django = "^4.5.2"
selenium = "^4.15.0"

# Code Quality
black = "^23.7.0"
flake8 = "^6.0.0"

# Development Tools
django-extensions = "^3.2.0"
nodeenv = "^1.8.0"  # For TailwindCSS builds
```

### Node.js Dependencies (finder-v2 widget)
```json
# Vue.js Framework
"vue": "^3.x"
"@vitejs/plugin-vue": "^4.x"

# Build Tools
"vite": "^4.x"
"tailwindcss": "^3.x"

# Development
"@vue/devtools": "^6.x"
```

### Removed Dependencies (and Reasons)
```toml
# django-recaptcha = "^4.1.0"
# Reason: Replaced by django-turnstile (Cloudflare Turnstile)
# Impact: Eliminates Google reCAPTCHA dependency, improves privacy

# jupyter = "^1.0.0" 
# Reason: Not used in project, adds 50+ unnecessary dependencies
# Impact: Reduces build time by ~50%, smaller image size
```

## Build Process

### Multi-Stage Build Overview
```dockerfile
# Stage 1: System Dependencies (30s)
- Install system packages (gcc, postgresql-devel, git)
- Install Node.js LTS from NodeSource
- Install pyenv for Python version management

# Stage 2: Python Environment (15s)
- Install Python 3.12.0 via pyenv
- Set as global Python version
- Upgrade pip to latest version
- Install Poetry package manager

# Stage 3: Python Dependencies (97s - optimized from 194s)
- Configure Poetry for system-wide installation
- Configure private PyPI repository access
- Install all Python packages via Poetry
- Verify critical imports (e.g., Turnstile)

# Stage 4: Node.js Dependencies (57s)
- Copy package.json for finder-v2 widget
- Install Node.js packages with npm install --omit=dev
- Build Vue.js components with Vite

# Stage 5: Application Setup (5s)
- Copy application code
- Set working directory and permissions
- Configure entrypoint and startup commands
```

### Build Performance Optimizations
```dockerfile
# Layer Caching Strategy
- System dependencies cached (rarely change)
- Python environment cached (stable)
- Dependencies cached until pyproject.toml changes
- Application code copied last (changes frequently)

# Dependency Optimization Results
- Poetry install: 194s → 97s (50% improvement)
- Total build time: ~4.5 minutes
- Image size reduction: ~200MB (removed Jupyter ecosystem)
```

### Static File Handling
```dockerfile
# Vue.js Widget Build Process
WORKDIR /code/src/apps/widgets/finder_v2/app
RUN npm install --omit=dev
RUN npm run build

# Django Static Files
RUN python manage.py collectstatic --noinput
```

## Common Issues and Solutions

### 502 Bad Gateway Errors
**Root Cause**: Missing Python packages after dependency changes
```bash
# Diagnostic Steps
docker logs ws_services --tail 20
docker exec ws_services python -c "import turnstile"

# Solution: Rebuild container after pyproject.toml changes
docker-compose build web
docker-compose up -d
```

### Dependency Conflicts
**Root Cause**: Virtual environment vs system installation mismatch
```bash
# Prevention: Ensure Poetry uses system Python
poetry config virtualenvs.create false
poetry config virtualenvs.in-project false

# Recovery: Remove virtual environment and reinstall
rm -rf .venv
poetry install --no-root
```

### Build Performance Issues
**Root Cause**: Unnecessary dependencies or poor layer caching
```bash
# Solution: Regular dependency cleanup
poetry show --outdated
poetry remove unused-package

# Optimization: Use .dockerignore
echo "*.pyc\n__pycache__\n.git" > .dockerignore
```

### Container Restart Requirements
**Critical**: Always rebuild after dependency changes
```bash
# Required after changes to:
- pyproject.toml (Python dependencies)
- package.json (Node.js dependencies) 
- Dockerfile (build configuration)

# Command sequence:
docker-compose build web
docker-compose up -d
```

## Development Workflow

### Docker Compose Services
```yaml
# Primary Services
web:          # Django application (ws_services)
db:           # PostgreSQL 15 database
redis:        # Redis cache and sessions
nginx:        # Reverse proxy and static files

# Development Services  
poetry:       # Poetry package management (docker-compose.poetry.yml)
```

### Volume Mounts (Development)
```yaml
volumes:
  - .:/code                    # Application code (live reload)
  - /code/src/static/          # Static files (generated)
  - postgres_data:/var/lib/postgresql/data  # Database persistence
```

### Environment Variables
```bash
# Required Variables (.env.local)
DJANGO_SETTINGS_MODULE=src.settings.dev_docker
DATABASE_URL=******************************/dbname
REDIS_URL=redis://redis:6379/0
DEBUG=True

# Optional Variables
SENTRY_DSN=https://...
AWS_ACCESS_KEY_ID=...
TURNSTILE_SITE_KEY=...
```

### Helper Scripts
```bash
# docker-poetry.sh - Poetry operations in container
./scripts/docker-poetry.sh "install package-name"
./scripts/docker-poetry.sh "show --outdated"
./scripts/docker-poetry.sh "lock"

# docker-compose.poetry.yml - Dedicated Poetry service
docker-compose -f docker-compose.yml -f docker-compose.poetry.yml run poetry install
```

### Development Commands
```bash
# Start development environment
docker-compose up -d

# View logs
docker-compose logs -f web

# Execute Django commands (recommended: absolute python path + explicit workdir)
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python /code/manage.py migrate
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python /code/manage.py createsuperuser

# Alternative: load pyenv shims via login shell and set workdir
docker exec ws_services bash -lc 'cd /code && python manage.py migrate'
docker exec ws_services bash -lc 'cd /code && python manage.py createsuperuser'

# Example: run the user cleanup command (dry-run first)
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python /code/manage.py cleanup_inactive_users --dry-run
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python /code/manage.py cleanup_inactive_users

# Access container shell
docker exec -it ws_services bash

# Rebuild after dependency changes
docker-compose build web && docker-compose up -d
```

## Production Deployment Considerations

### Container Strategy
- **Base Image**: Same Amazon Linux for consistency
- **Python Version**: Identical 3.12.0 for compatibility
- **Dependencies**: Exact versions via poetry.lock
- **Environment**: Production-specific settings module

### Security Enhancements
```dockerfile
# Production Dockerfile additions:
- Use multi-stage builds to reduce final image size
- Use build secrets for private PyPI credentials
- Run as non-root user
- Enable security scanning
```

### Performance Optimizations
```dockerfile
# Production optimizations:
- Use gunicorn with multiple workers
- Enable static file compression
- Configure proper logging levels
- Use external Redis/PostgreSQL services
```

### Monitoring and Health Checks
```yaml
# docker-compose.yml additions:
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
  interval: 30s
  timeout: 10s
  retries: 3
```

## Maintenance and Updates

### Regular Maintenance Tasks
```bash
# Weekly: Clean up Docker resources
docker system prune -f

# Monthly: Update base images
docker-compose pull
docker-compose build --pull

# Quarterly: Update Python/Node.js versions
# Update Dockerfile with new versions
# Test thoroughly before deployment
```

### Dependency Updates
```bash
# Check for updates
poetry show --outdated

# Update specific package
poetry update package-name

# Update all packages (test thoroughly)
poetry update

# Always rebuild after updates
docker-compose build web
```

### Security Updates
```bash
# Update base image
docker pull public.ecr.aws/amazonlinux/amazonlinux:latest

# Scan for vulnerabilities
docker scan ws_services

# Update system packages in Dockerfile
RUN yum update -y
```

## Related Documentation

- [Docker Container Troubleshooting](../update/docker-container-troubleshooting.md) - Detailed troubleshooting procedures
- [Cloudflare Turnstile Docker 502 Fix](../update/cloudflare-turnstile-docker-502-fix.md) - CAPTCHA migration issues and solutions

## Quick Reference

### Essential Commands
```bash
# Container status
docker ps -a

# Service logs  
docker logs ws_services --tail 50 -f

# Interactive shell
docker exec -it ws_services bash

# Restart service
docker-compose restart web

# Full rebuild
docker-compose build --no-cache web
docker-compose up -d
```

### File Locations
```bash
# Application code: /code/src/
# Python packages: /root/.pyenv/versions/3.12.0/lib/python3.12/site-packages/
# Environment variables: /code/.env.local
# Static files: /code/src/static/
# Vue.js widgets: /code/src/apps/widgets/finder_v2/app/
```

### Performance Benchmarks
```bash
# Build Times (after optimization):
- Poetry install: 97 seconds
- npm install: 57 seconds  
- Total build: ~4.5 minutes
- Container startup: ~10 seconds
```
