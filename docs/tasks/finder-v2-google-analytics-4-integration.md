# Finder-v2 Google Analytics 4 Integration

Last Modified: 2025-01-21 17:45 UTC+6

## Task Overview

This document outlines the complete implementation plan for migrating from the current finder-v1 Google Analytics Universal (UA) implementation to Google Analytics 4 (GA4) for the finder-v2 Vue.js widget. The goal is to maintain the same level of analytics coverage while leveraging GA4's enhanced event-driven architecture and improved cross-domain tracking capabilities.

### Current State Analysis

**Finder-v1 Implementation:**
- Uses Google Analytics Universal (UA-35890489-11)
- Tracks 9 custom dimensions (widget UUID, user UUID, client hostname, etc.)
- Event tracking via `WheelSizeMessages` system with `postMessage` communication
- Basic pageview and event tracking for tab activations and form interactions

**Target State:**
- Migrate to Google Analytics 4 with enhanced event tracking
- Implement Vue.js composable for analytics integration
- Track comprehensive widget interactions and user behavior
- Maintain cross-domain iframe tracking capabilities
- Add performance monitoring and error tracking

## Technical Requirements

### GA4 Property Setup
- **Property Type:** GA4 (Google Analytics 4)
- **Data Stream:** Web stream for widget tracking
- **Measurement ID:** `G-XXXXXXXXXX` (to be configured)
- **Enhanced Measurement:** Enabled for automatic event tracking
- **Cross-domain Tracking:** Configured for iframe embedding

### Vue.js Integration Requirements
- **Framework:** Vue 3 with Composition API
- **State Management:** Pinia store integration
- **Bundle Impact:** < 5KB additional bundle size
- **Performance:** Non-blocking analytics loading
- **Error Handling:** Graceful degradation when GA4 unavailable

### Browser Compatibility
- **Modern Browsers:** Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Legacy Support:** Graceful degradation for older browsers
- **Privacy:** Respect Do Not Track and cookie preferences

## Implementation Plan

### Phase 1: GA4 Property Setup and Configuration

#### 1.1 Google Analytics 4 Property Creation
- [ ] Create new GA4 property in Google Analytics
- [ ] Configure data stream for widget tracking
- [ ] Set up custom dimensions for widget-specific data
- [ ] Configure enhanced measurement settings
- [ ] Set up cross-domain tracking for iframe embedding

#### 1.2 Custom Dimensions Configuration
Map existing finder-v1 dimensions to GA4 custom dimensions:

| Finder-v1 Dimension | GA4 Custom Dimension | Description |
|---------------------|---------------------|-------------|
| dimension1 | widget_uuid | Widget instance identifier |
| dimension2 | user_profile_uuid | User profile identifier |
| dimension3 | client_hostname | Client website hostname |
| dimension4 | client_port | Client website port |
| dimension5 | client_page_url | Full client page URL |
| dimension6 | widget_width | Widget width configuration |
| dimension7 | widget_height | Widget height configuration |
| dimension8 | widget_type | Widget type (finder-v2) |
| dimension9 | subscription_paid | User subscription status |

#### 1.3 Enhanced Dimensions for Finder-v2
Additional custom dimensions for improved tracking:

| GA4 Custom Dimension | Description | Scope |
|---------------------|-------------|-------|
| search_flow_type | Primary/secondary search flow | Event |
| api_version | API version (v2) | Event |
| theme_name | Widget theme identifier | Event |
| search_completion_time | Time to complete search | Event |
| error_type | Error category for debugging | Event |

### Phase 2: Vue.js Analytics Composable Development

#### 2.1 Create Analytics Composable
- [x] Create `src/apps/widgets/finder_v2/app/src/composables/useAnalytics.js`
- [x] Implement GA4 gtag integration
- [x] Add event tracking methods
- [x] Implement error handling and fallbacks
- [x] Add performance monitoring capabilities

#### 2.2 Analytics Composable Structure
```javascript
// src/apps/widgets/finder_v2/app/src/composables/useAnalytics.js
import { ref, computed, onMounted } from 'vue'

export function useAnalytics(widgetConfig) {
  const isEnabled = ref(true)
  const measurementId = ref('G-XXXXXXXXXX') // To be configured
  const isInitialized = ref(false)

  // Initialize GA4 gtag
  const initialize = async () => {
    if (isInitialized.value || !isEnabled.value) return

    try {
      // Load gtag script
      const script = document.createElement('script')
      script.async = true
      script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId.value}`
      document.head.appendChild(script)

      // Initialize gtag
      window.dataLayer = window.dataLayer || []
      window.gtag = function() { dataLayer.push(arguments) }

      gtag('js', new Date())
      gtag('config', measurementId.value, {
        // Cross-domain tracking for iframe
        linker: {
          domains: ['wheel-size.com', 'development.local']
        },
        // Custom dimensions
        custom_map: {
          'custom_parameter_1': 'widget_uuid',
          'custom_parameter_2': 'user_profile_uuid',
          'custom_parameter_3': 'client_hostname'
        }
      })

      isInitialized.value = true
      console.log('GA4 Analytics initialized for finder-v2 widget')
    } catch (error) {
      console.warn('Failed to initialize GA4 analytics:', error)
      isEnabled.value = false
    }
  }

  // Track custom events
  const trackEvent = (eventName, parameters = {}) => {
    if (!isInitialized.value || !isEnabled.value) return

    try {
      // Add widget context to all events
      const eventData = {
        ...parameters,
        widget_uuid: widgetConfig.uuid,
        widget_type: 'finder-v2',
        client_hostname: getClientHostname(),
        timestamp: Date.now()
      }

      gtag('event', eventName, eventData)
      console.log('GA4 Event tracked:', eventName, eventData)
    } catch (error) {
      console.warn('Failed to track GA4 event:', error)
    }
  }

  // Track widget interactions
  const trackWidgetInteraction = (interactionType, data = {}) => {
    trackEvent('widget_interaction', {
      interaction_type: interactionType,
      ...data
    })
  }

  // Track search events
  const trackSearch = (searchType, searchData = {}) => {
    trackEvent('widget_search', {
      search_type: searchType,
      search_flow_type: widgetConfig.flowType || 'primary',
      api_version: widgetConfig.apiVersion || 'v2',
      ...searchData
    })
  }

  // Track errors
  const trackError = (errorType, errorData = {}) => {
    trackEvent('widget_error', {
      error_type: errorType,
      widget_state: errorData.widgetState || 'unknown',
      ...errorData
    })
  }

  // Get client hostname from parent window
  const getClientHostname = () => {
    try {
      if (window.parent && window.parent !== window) {
        return window.parent.location.hostname
      }
      return window.location.hostname
    } catch (error) {
      // Cross-origin restriction, use referrer
      try {
        const referrer = document.referrer
        if (referrer) {
          const url = new URL(referrer)
          return url.hostname
        }
      } catch (e) {
        console.warn('Unable to determine client hostname')
      }
      return 'unknown'
    }
  }

  // Initialize on mount
  onMounted(() => {
    initialize()
  })

  return {
    isEnabled,
    isInitialized,
    initialize,
    trackEvent,
    trackWidgetInteraction,
    trackSearch,
    trackError
  }
}
```

### Phase 3: Event Tracking Implementation

#### 3.1 Widget Lifecycle Events
- [x] **widget_load** - Widget initialization and configuration
- [x] **widget_ready** - Widget fully loaded and interactive
- [x] **widget_error** - Widget loading or runtime errors
- [x] **widget_unload** - Widget cleanup and session end

#### 3.2 User Interaction Events
- [x] **dropdown_open** - Dropdown selector opened
- [x] **dropdown_close** - Dropdown selector closed
- [x] **option_select** - Option selected in dropdown (year, make, model, modification, generation)
- [x] **search_initiate** - Search form submitted
- [x] **search_complete** - Search results received
- [x] **search_error** - Search API error occurred
- [x] **result_view** - Search results displayed
- [x] **result_click** - Result item clicked

#### 3.3 Search Flow Events
- [x] **flow_step** - User completes search step (year, make, model, etc.)
- [x] **search_complete** - User completes search with results
- [x] **data_load_complete** - API data loading completed
- [x] **data_load_failed** - API data loading failed

### Phase 4: Cross-Domain Iframe Tracking Setup

#### 4.1 PostMessage Communication
- [x] Implement analytics message passing between iframe and parent
- [x] Add client hostname detection from parent window
- [x] Configure cross-domain tracking parameters
- [ ] Test tracking across different client domains

```javascript
// src/apps/widgets/finder_v2/app/src/composables/useCrossDomainTracking.js
import { ref, onMounted } from 'vue'

export function useCrossDomainTracking() {
  const parentHostname = ref('unknown')
  const parentUrl = ref('')

  // Get parent window information
  const getParentInfo = () => {
    try {
      if (window.parent && window.parent !== window) {
        // Try to access parent location (same-origin)
        parentHostname.value = window.parent.location.hostname
        parentUrl.value = window.parent.location.href
      }
    } catch (error) {
      // Cross-origin restriction, use postMessage
      requestParentInfo()
    }
  }

  // Request parent info via postMessage
  const requestParentInfo = () => {
    if (window.parent && window.parent !== window) {
      window.parent.postMessage({
        type: 'widget_request_parent_info',
        widgetId: window.FinderV2Config?.widgetUuid
      }, '*')
    }
  }

  // Listen for parent info response
  const handleParentMessage = (event) => {
    if (event.data.type === 'widget_parent_info') {
      parentHostname.value = event.data.hostname || 'unknown'
      parentUrl.value = event.data.url || ''
    }
  }

  onMounted(() => {
    getParentInfo()
    window.addEventListener('message', handleParentMessage)
  })

  return {
    parentHostname,
    parentUrl
  }
}
```

#### 4.2 Privacy and Consent Management
- [x] Implement consent checking before tracking
- [x] Add opt-out mechanisms for privacy compliance
- [x] Configure data retention settings
- [x] Add GDPR/CCPA compliance features

```javascript
// Privacy-compliant analytics initialization
const initializeWithConsent = () => {
  // Check for Do Not Track
  if (navigator.doNotTrack === '1') {
    isEnabled.value = false
    return
  }

  // Check for widget-specific opt-out
  const optOut = localStorage.getItem('ws_widget_analytics_opt_out')
  if (optOut === 'true') {
    isEnabled.value = false
    return
  }

  // Initialize with privacy settings
  gtag('config', measurementId.value, {
    anonymize_ip: true,
    allow_google_signals: false,
    allow_ad_personalization_signals: false
  })
}
```

## Event Tracking Specification

### Core Widget Events

#### widget_load
**Trigger:** Widget initialization
**Parameters:**
```javascript
{
  widget_uuid: 'abc123...',
  widget_type: 'finder-v2',
  client_hostname: 'example.com',
  client_page_url: 'https://example.com/page',
  widget_width: '800',
  widget_height: 'auto',
  theme_name: 'default',
  api_version: 'v2',
  subscription_paid: true,
  user_profile_uuid: 'user123...'
}
```

#### search_complete
**Trigger:** Successful search results received
**Parameters:**
```javascript
{
  search_flow_type: 'primary',
  search_type: 'by_vehicle',
  selected_year: '2020',
  selected_make: 'BMW',
  selected_model: 'X5',
  selected_modification: 'xDrive40i',
  results_count: 15,
  search_completion_time: 1250, // milliseconds
  api_response_time: 800
}
```

#### user_interaction
**Trigger:** User interacts with widget elements
**Parameters:**
```javascript
{
  interaction_type: 'dropdown_open',
  element_type: 'year_selector',
  element_value: '2020',
  flow_step: 1,
  session_duration: 30000 // milliseconds since widget load
}
```

### Error Tracking Events

#### widget_error
**Trigger:** Widget errors occur
**Parameters:**
```javascript
{
  error_type: 'api_error',
  error_message: 'Failed to load years',
  error_code: 'NETWORK_ERROR',
  api_endpoint: '/api/v2/years',
  user_agent: navigator.userAgent,
  widget_state: 'loading_years'
}
```

## Testing Strategy

### 4.1 Development Testing
- [ ] Set up GA4 debug mode for development
- [ ] Create test widget instances with different configurations
- [ ] Verify event tracking in GA4 DebugView
- [ ] Test cross-domain tracking with local development setup

```javascript
// Development testing setup
const setupDebugMode = () => {
  if (window.location.hostname === 'development.local') {
    gtag('config', measurementId.value, {
      debug_mode: true,
      send_page_view: false // Prevent duplicate pageviews in testing
    })

    // Enable console logging for all events
    window.gtag_debug = true
  }
}
```

### 4.2 Staging Environment Testing
- [ ] Deploy to staging environment with test GA4 property
- [ ] Test widget embedding on multiple test domains
- [ ] Verify custom dimensions are populated correctly
- [ ] Test error scenarios and fallback behavior

**Test Scenarios:**
```javascript
// Test different widget configurations
const testConfigurations = [
  { theme: 'default', flowType: 'primary', apiVersion: 'v2' },
  { theme: 'dark', flowType: 'secondary', apiVersion: 'v2' },
  { theme: 'custom', flowType: 'primary', apiVersion: 'v1' }
]

// Test error scenarios
const simulateErrors = () => {
  trackError('api_timeout', { endpoint: '/api/v2/years', timeout: 5000 })
  trackError('network_error', { message: 'Failed to fetch' })
  trackError('validation_error', { field: 'year', value: 'invalid' })
}
```

### 4.3 Production Validation
- [ ] Gradual rollout to subset of widgets
- [ ] Monitor GA4 real-time reports for data accuracy
- [ ] Compare data with existing finder-v1 analytics
- [ ] Validate cross-domain tracking across client websites

**Validation Checklist:**
```javascript
// Data validation queries for GA4
const validationQueries = {
  totalEvents: "SELECT COUNT(*) FROM events WHERE event_name LIKE 'widget_%'",
  uniqueWidgets: "SELECT COUNT(DISTINCT widget_uuid) FROM events",
  errorRate: "SELECT COUNT(*) FROM events WHERE event_name = 'widget_error'",
  searchCompletions: "SELECT COUNT(*) FROM events WHERE event_name = 'search_complete'"
}
```

### 4.4 Performance Testing
- [ ] Measure bundle size impact (target: < 5KB increase)
- [ ] Test widget load time with analytics enabled
- [ ] Verify non-blocking analytics loading
- [ ] Test graceful degradation when GA4 unavailable

**Performance Monitoring:**
```javascript
// Performance measurement
const measureAnalyticsImpact = () => {
  const startTime = performance.now()

  initialize().then(() => {
    const loadTime = performance.now() - startTime
    console.log(`GA4 initialization time: ${loadTime}ms`)

    // Track performance metric
    trackEvent('analytics_performance', {
      initialization_time: Math.round(loadTime),
      bundle_size_impact: getBundleSizeImpact()
    })
  })
}
```

## Documentation and Resources

### GA4 Documentation Links
- [GA4 Setup Guide](https://support.google.com/analytics/answer/9304153)
- [GA4 Custom Dimensions](https://support.google.com/analytics/answer/10075209)
- [GA4 Cross-Domain Tracking](https://support.google.com/analytics/answer/10071811)
- [GA4 Enhanced Measurement](https://support.google.com/analytics/answer/9216061)
- [GA4 DebugView](https://support.google.com/analytics/answer/7201382)

### Implementation References
- [Vue.js GA4 Integration](https://developers.google.com/analytics/devguides/collection/ga4/vue)
- [gtag.js Reference](https://developers.google.com/analytics/devguides/collection/ga4/reference)
- [GA4 Event Parameters](https://developers.google.com/analytics/devguides/collection/ga4/reference/events)

## Success Criteria

### Functional Requirements
- [ ] All finder-v1 analytics coverage maintained in GA4
- [ ] Enhanced event tracking for user interactions implemented
- [ ] Cross-domain iframe tracking working correctly
- [ ] Error tracking and performance monitoring active
- [ ] Privacy compliance features implemented

### Performance Requirements
- [ ] Bundle size increase < 5KB
- [ ] Widget load time impact < 100ms
- [ ] Analytics loading does not block widget functionality
- [ ] Graceful degradation when analytics unavailable

### Data Quality Requirements
- [ ] 95%+ event tracking accuracy compared to finder-v1
- [ ] Custom dimensions populated correctly
- [ ] Real-time data available in GA4 reports
- [ ] Historical data comparison validates migration success

## Integration with Existing Finder-v2 Components

### Finder Store Integration
```javascript
// src/apps/widgets/finder_v2/app/src/stores/finder.js
import { useAnalytics } from '../composables/useAnalytics'

export const useFinderStore = defineStore('finder', () => {
  // ... existing store code ...

  // Initialize analytics
  const analytics = useAnalytics(config.value)

  // Track search completion
  async function searchByVehicle() {
    const startTime = performance.now()

    try {
      // ... existing search logic ...

      const searchTime = performance.now() - startTime

      // Track successful search
      analytics.trackSearch('by_vehicle', {
        selected_year: selectedYear.value,
        selected_make: selectedMake.value,
        selected_model: selectedModel.value,
        results_count: results.value.length,
        search_completion_time: Math.round(searchTime)
      })

    } catch (error) {
      // Track search error
      analytics.trackError('search_failed', {
        error_message: error.message,
        search_type: 'by_vehicle',
        widget_state: 'searching'
      })
      throw error
    }
  }

  return {
    // ... existing returns ...
    analytics
  }
})
```

### Component Integration Example
```javascript
// src/apps/widgets/finder_v2/app/src/components/VehicleSearch.vue
<script>
import { useFinderStore } from '../stores/finder'

export default {
  setup() {
    const finderStore = useFinderStore()
    const { analytics } = finderStore

    const onYearChange = (year) => {
      // ... existing logic ...

      // Track user interaction
      analytics.trackWidgetInteraction('year_selected', {
        selected_year: year,
        flow_step: 1,
        flow_type: 'primary'
      })
    }

    const onMakeChange = (make) => {
      // ... existing logic ...

      analytics.trackWidgetInteraction('make_selected', {
        selected_make: make,
        flow_step: 2,
        flow_type: 'primary'
      })
    }

    return {
      onYearChange,
      onMakeChange
      // ... other returns ...
    }
  }
}
</script>
```

## Timeline and Milestones

### Week 1-2: Setup and Planning
- [ ] GA4 property creation and configuration
- [ ] Custom dimensions setup
- [ ] Development environment preparation
- [ ] Analytics composable architecture design

### Week 3-4: Core Implementation
- [ ] Analytics composable development (`useAnalytics.js`)
- [ ] Basic event tracking implementation
- [ ] Cross-domain tracking setup (`useCrossDomainTracking.js`)
- [ ] Integration with Finder store

### Week 5-6: Enhanced Features
- [ ] Advanced event tracking for all user interactions
- [ ] Error monitoring and performance tracking
- [ ] Privacy compliance features
- [ ] Bundle size optimization

### Week 7-8: Testing and Deployment
- [ ] Comprehensive testing across environments
- [ ] Production deployment with gradual rollout
- [ ] Data validation and comparison with finder-v1
- [ ] Documentation and knowledge transfer

## Risk Mitigation

### Technical Risks
- **GA4 API Changes:** Monitor GA4 updates and maintain compatibility
- **Cross-Domain Issues:** Implement robust fallback mechanisms
- **Performance Impact:** Continuous monitoring and optimization

### Data Risks
- **Data Loss:** Parallel tracking during migration period
- **Privacy Compliance:** Regular compliance audits and updates
- **Tracking Accuracy:** Continuous validation against baseline metrics

## Maintenance and Monitoring

### Ongoing Tasks
- [ ] Monthly GA4 data quality reviews
- [ ] Quarterly performance impact assessments
- [ ] Annual privacy compliance audits
- [ ] Regular GA4 feature updates and optimizations

### Monitoring Dashboards
- [ ] Widget usage and performance metrics
- [ ] Error rates and debugging information
- [ ] Cross-domain tracking effectiveness
- [ ] Privacy compliance status

---

**Next Steps:** Begin with Phase 1 GA4 property setup and proceed through implementation phases systematically, ensuring thorough testing at each stage.
