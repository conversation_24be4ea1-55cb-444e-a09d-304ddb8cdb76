// типографика

Design clear typography hierarchy using modern sans-serif font. 
Large heading, medium subheading, readable body text. 
Ensure good line spacing and visual rhythm throughout the page.

// UI-компоненты

Create card components with contemporary styling - subtle shadows, 
rounded corners, clean white background. Add hover effects 
and make them feel interactive and polished.

// цвета

Use professional color palette - primary brand color, neutral grays, 
success/error states. Ensure good contrast for accessibility 
and maintain consistent color usage across all components.

https://tailwind-generator.com/color-palette-generator/generator?color1=orange-500&color2=amber-900&color3=amber-950&color4=gray-500&color5=gray-100


2. Создай свою цветовую схему через CSS-переменные:
- UI Colors - генерация полных палитр 50-950 из одного цвета
- TweakCN - визуальный редактор ShadCN тем, экспорт CSS-переменных
- Tailwind Color Generator - HSL-совместимые палитры

3. Типографика:
- Fontjoy https://fontjoy.com/ - автоматический подбор font pairings
- Font Combinations https://www.fontpair.co/ - проверенные комбинации шрифтов


А вот пример того, что можно добавить в проектные правила (cursor rules, claude md)
## Design Principles
- Generous spacing: Use plenty of whitespace, avoid cramped layouts
- Cards: Subtle elevation, consistent padding, avoid heavy borders
- Modern aesthetics: Subtle shadows, rounded corners, clean typography
- Interactive states: Smooth hover effects, button feedback, loading states
- Visual hierarchy: Clear information structure with proper heading levels
- Accessibility: Good color contrast, readable fonts, proper focus states
- Consistent system: Reusable components, unified spacing scale
- Use consistent spacing units(8px, 16px, 24px, 32px) throughout your design system
- Test colors in both light and dark modes
- Implement consistent iconography from a single icon family