# 🏗️ Store Refactoring Plan for Finder-v2 Widget

## Executive Summary

The finder.js store file currently contains 818 lines of code, handling multiple responsibilities including state management, API calls, search history, analytics, and business logic. This plan outlines a systematic approach to refactor this monolithic store into smaller, focused modules following the Single Responsibility Principle.

## Current State Analysis

### File Statistics
- **Total Lines**: 818
- **Functions**: 20+ major functions
- **State Variables**: 25+ reactive refs
- **Responsibilities**: 8+ distinct domains

### Identified Responsibilities
1. **State Management**: Core vehicle selection state
2. **API Communication**: HTTP requests and response handling
3. **Request Deduplication**: Preventing duplicate API calls
4. **Search History**: Managing and restoring previous searches
5. **Analytics Integration**: Tracking user interactions
6. **Cross-Domain Tracking**: Managing tracking across domains
7. **Data Transformation**: Converting API responses to UI format
8. **Business Logic**: Search validation, filtering, flow control

### Code Smells
- 🔴 **God Object**: Single store managing everything
- 🔴 **Long File**: 818 lines violates readability standards
- 🟡 **Mixed Concerns**: UI logic mixed with business logic
- 🟡 **Duplicate Logic**: Similar patterns in load functions
- 🟢 **Good Composables**: Already using some external composables

## Refactoring Strategy

### 🎯 Goals
1. **Improve Maintainability**: Easier to understand and modify
2. **Enable Testing**: Isolated units for better testing
3. **Reduce Complexity**: Smaller, focused modules
4. **Improve Performance**: Lazy loading of features
5. **Better Type Safety**: Prepare for TypeScript migration

### Design Principles
- **Single Responsibility**: Each module has one clear purpose
- **Dependency Injection**: Loose coupling between modules
- **Composition over Inheritance**: Use composables
- **Interface Segregation**: Small, focused APIs
- **Open/Closed**: Extensible without modification

## Proposed Architecture

```
src/stores/
├── finder.js                 # Main store (orchestrator) ~150 lines
├── modules/
│   ├── state.js             # Core state management ~100 lines
│   ├── api.js               # API client and deduplication ~150 lines
│   ├── vehicle-loader.js    # Vehicle data loading logic ~200 lines
│   ├── search-executor.js   # Search execution logic ~100 lines
│   ├── data-transformer.js  # Response transformation ~80 lines
│   └── filter-builder.js    # Filter parameter building ~50 lines
└── composables/
    ├── useSearchHistory.js   # Already exists
    ├── useAnalytics.js       # Already exists
    ├── useWidgetEvents.js    # Already exists
    └── useApiClient.js       # New: Extracted API logic
```

## Detailed Refactoring Plan

### Phase 1: Extract API Layer

#### 1.1 Create API Client Module
```javascript
// src/stores/modules/api.js
import axios from 'axios'

export class ApiClient {
  constructor(config) {
    this.baseURL = config.apiUrl
    this.csrfToken = config.csrfToken
    this.ongoingRequests = new Map()
    this.setupInterceptors()
  }

  setupInterceptors() {
    // Request interceptor
    axios.interceptors.request.use((config) => {
      if (this.csrfToken) {
        config.headers['X-CSRF-TOKEN'] = this.csrfToken
      }
      return config
    })

    // Response interceptor
    axios.interceptors.response.use(
      response => response,
      error => this.handleError(error)
    )
  }

  async get(endpoint, params = {}) {
    const key = this.generateRequestKey(endpoint, params)
    
    // Check for ongoing request
    if (this.ongoingRequests.has(key)) {
      return this.ongoingRequests.get(key)
    }

    // Create new request
    const request = this.executeRequest(endpoint, params)
    this.ongoingRequests.set(key, request)

    try {
      const result = await request
      return result
    } finally {
      this.ongoingRequests.delete(key)
    }
  }

  async executeRequest(endpoint, params) {
    const url = `${this.baseURL}/${endpoint}/`
    const response = await axios.get(url, { params })
    return response.data
  }

  generateRequestKey(endpoint, params) {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, key) => ({ ...acc, [key]: params[key] }), {})
    return `${endpoint}:${JSON.stringify(sortedParams)}`
  }

  handleError(error) {
    // Centralized error handling
    if (error.response?.status === 403) {
      // Handle CSRF challenges
    }
    return Promise.reject(error)
  }
}
```

#### 1.2 Create API Composable
```javascript
// src/composables/useApiClient.js
import { ApiClient } from '@/stores/modules/api'

let apiClient = null

export function useApiClient(config) {
  if (!apiClient) {
    apiClient = new ApiClient(config)
  }
  return apiClient
}
```

### Phase 2: Extract State Management

#### 2.1 Create State Module
```javascript
// src/stores/modules/state.js
import { ref, computed } from 'vue'

export function createVehicleState() {
  // Core selection state
  const selectedYear = ref('')
  const selectedMake = ref('')
  const selectedModel = ref('')
  const selectedModification = ref('')
  const selectedGeneration = ref('')

  // Available options
  const years = ref([])
  const makes = ref([])
  const models = ref([])
  const modifications = ref([])
  const generations = ref([])

  // Loading states
  const loadingYears = ref(false)
  const loadingMakes = ref(false)
  const loadingModels = ref(false)
  const loadingGenerations = ref(false)
  const loadingModifications = ref(false)

  // State loaded flags
  const stateLoadedYears = ref(false)
  const stateLoadedMakes = ref(false)
  const stateLoadedModels = ref(false)
  const stateLoadedGenerations = ref(false)
  const stateLoadedModifications = ref(false)

  // Computed properties
  const hasYearSelected = computed(() => !!selectedYear.value)
  const hasMakeSelected = computed(() => !!selectedMake.value)
  const hasModelSelected = computed(() => !!selectedModel.value)
  const hasGenerationSelected = computed(() => !!selectedGeneration.value)
  const hasModificationSelected = computed(() => !!selectedModification.value)

  // Reset functions
  function resetSelection() {
    selectedYear.value = ''
    selectedMake.value = ''
    selectedModel.value = ''
    selectedModification.value = ''
    selectedGeneration.value = ''
  }

  function resetOptions() {
    years.value = []
    makes.value = []
    models.value = []
    modifications.value = []
    generations.value = []
  }

  return {
    // State
    selectedYear,
    selectedMake,
    selectedModel,
    selectedModification,
    selectedGeneration,
    years,
    makes,
    models,
    modifications,
    generations,
    
    // Loading states
    loadingYears,
    loadingMakes,
    loadingModels,
    loadingGenerations,
    loadingModifications,
    
    // State loaded flags
    stateLoadedYears,
    stateLoadedMakes,
    stateLoadedModels,
    stateLoadedGenerations,
    stateLoadedModifications,
    
    // Computed
    hasYearSelected,
    hasMakeSelected,
    hasModelSelected,
    hasGenerationSelected,
    hasModificationSelected,
    
    // Methods
    resetSelection,
    resetOptions
  }
}
```

### Phase 3: Extract Vehicle Loading Logic

#### 3.1 Create Vehicle Loader Module
```javascript
// src/stores/modules/vehicle-loader.js
export class VehicleLoader {
  constructor(apiClient, state, config) {
    this.api = apiClient
    this.state = state
    this.config = config
  }

  async loadYears(make = null, model = null) {
    if (this.state.loadingYears.value) return

    this.state.loadingYears.value = true
    try {
      const params = this.buildParams({ make, model })
      const data = await this.api.get('year', params)
      
      this.state.years.value = this.transformYearData(data)
      this.state.stateLoadedYears.value = true
      
      return this.state.years.value
    } finally {
      this.state.loadingYears.value = false
    }
  }

  async loadMakes(year = null) {
    if (this.state.loadingMakes.value) return

    this.state.loadingMakes.value = true
    try {
      const params = this.buildParams({ year })
      const data = await this.api.get('make', params)
      
      this.state.makes.value = this.transformMakeData(data)
      this.state.stateLoadedMakes.value = true
      
      return this.state.makes.value
    } finally {
      this.state.loadingMakes.value = false
    }
  }

  async loadModels(make, year = null) {
    if (!make || this.state.loadingModels.value) return

    this.state.loadingModels.value = true
    try {
      const params = this.buildParams({ make, year })
      const data = await this.api.get('model', params)
      
      this.state.models.value = this.transformModelData(data)
      this.state.stateLoadedModels.value = true
      
      return this.state.models.value
    } finally {
      this.state.loadingModels.value = false
    }
  }

  buildParams(baseParams) {
    const params = { ...baseParams }
    
    // Add region filtering
    if (this.config.region?.selected?.length) {
      params.region = this.config.region.selected
    }
    
    // Add brand filtering
    if (this.config.brandFilter?.mode) {
      params[`brand_${this.config.brandFilter.mode}`] = 
        this.config.brandFilter.brands
    }
    
    return params
  }

  transformYearData(data) {
    return data.data?.map(item => ({
      value: item.year,
      label: item.year.toString()
    })) || []
  }

  transformMakeData(data) {
    return data.data?.map(item => ({
      value: item.slug || item.make,
      label: item.make
    })) || []
  }

  transformModelData(data) {
    return data.data?.map(item => ({
      value: item.slug || item.model,
      label: item.model
    })) || []
  }
}
```

### Phase 4: Extract Search Logic

#### 4.1 Create Search Executor Module
```javascript
// src/stores/modules/search-executor.js
export class SearchExecutor {
  constructor(apiClient, state, config, events) {
    this.api = apiClient
    this.state = state
    this.config = config
    this.events = events
    this.activeSearchSignature = ''
  }

  async searchByVehicle() {
    // Generate search signature to prevent duplicates
    const signature = this.generateSearchSignature()
    
    if (this.activeSearchSignature === signature) {
      console.log('Duplicate search prevented')
      return
    }

    this.activeSearchSignature = signature
    
    try {
      // Validate search
      if (!this.validateSearch()) {
        throw new Error('Invalid search parameters')
      }

      // Track search start
      this.events.emit('search:start', {
        year: this.state.selectedYear.value,
        make: this.state.selectedMake.value,
        model: this.state.selectedModel.value
      })

      // Build search parameters
      const params = this.buildSearchParams()

      // Execute search
      const results = await this.api.get('search_by_model', params)

      // Process results
      const processedResults = this.processResults(results)

      // Track search complete
      this.events.emit('search:complete', {
        resultCount: processedResults.length
      })

      return processedResults
    } finally {
      this.activeSearchSignature = ''
    }
  }

  validateSearch() {
    const { selectedYear, selectedMake, selectedModel } = this.state
    
    if (this.config.flowType === 'primary') {
      return selectedYear.value && selectedMake.value && selectedModel.value
    } else {
      return selectedMake.value && selectedModel.value
    }
  }

  generateSearchSignature() {
    const { selectedYear, selectedMake, selectedModel, 
            selectedGeneration, selectedModification } = this.state
    
    return JSON.stringify({
      year: selectedYear.value,
      make: selectedMake.value,
      model: selectedModel.value,
      generation: selectedGeneration.value,
      modification: selectedModification.value
    })
  }

  buildSearchParams() {
    const params = {
      make: this.state.selectedMake.value,
      model: this.state.selectedModel.value
    }

    if (this.state.selectedYear.value) {
      params.year = this.state.selectedYear.value
    }

    if (this.state.selectedGeneration.value) {
      params.generation = this.state.selectedGeneration.value
    }

    if (this.state.selectedModification.value) {
      params.modification = this.state.selectedModification.value
    }

    // Add region and brand filters
    Object.assign(params, this.getFilterParams())

    return params
  }

  processResults(data) {
    if (!data?.data) return []
    
    return data.data.map(item => ({
      ...item,
      displayName: this.formatResultName(item)
    }))
  }

  formatResultName(item) {
    return `${item.make} ${item.model} ${item.year || ''}`
  }

  getFilterParams() {
    // Delegate to filter builder
    return {}
  }
}
```

### Phase 5: Refactor Main Store

#### 5.1 New Simplified Main Store
```javascript
// src/stores/finder.js (refactored)
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { createVehicleState } from './modules/state'
import { VehicleLoader } from './modules/vehicle-loader'
import { SearchExecutor } from './modules/search-executor'
import { useApiClient } from '@/composables/useApiClient'
import { useSearchHistory } from '@/composables/useSearchHistory'
import { useAnalytics } from '@/composables/useAnalytics'
import { useWidgetEvents } from '@/composables/useWidgetEvents'

export const useFinderStore = defineStore('finder', () => {
  // Configuration
  const config = ref({})
  
  // Results
  const results = ref([])
  const loadingResults = ref(false)
  const error = ref(null)
  
  // Initialize modules
  const vehicleState = createVehicleState()
  let apiClient = null
  let vehicleLoader = null
  let searchExecutor = null
  let searchHistory = null
  let analytics = null
  let widgetEvents = null

  // Computed properties
  const flowType = computed(() => config.value.flowType || 'primary')
  const isReady = computed(() => !!config.value.apiUrl)

  // Initialize store
  function initialize(widgetConfig) {
    config.value = widgetConfig
    
    // Initialize services
    apiClient = useApiClient(widgetConfig)
    vehicleLoader = new VehicleLoader(apiClient, vehicleState, widgetConfig)
    widgetEvents = useWidgetEvents()
    searchExecutor = new SearchExecutor(apiClient, vehicleState, widgetConfig, widgetEvents)
    
    // Initialize optional services
    if (widgetConfig.search_history) {
      searchHistory = useSearchHistory(widgetConfig.id)
      searchHistory.configure(widgetConfig.search_history)
    }
    
    if (widgetConfig.analytics) {
      analytics = useAnalytics()
      analytics.initialize(widgetConfig.analytics)
    }
  }

  // Load initial data
  async function loadInitialData() {
    try {
      if (flowType.value === 'primary') {
        await vehicleLoader.loadYears()
      } else {
        await vehicleLoader.loadMakes()
      }
      
      widgetEvents.emit('ready:data')
    } catch (err) {
      error.value = err
      widgetEvents.emit('error', err)
    }
  }

  // Search function
  async function search() {
    loadingResults.value = true
    error.value = null
    
    try {
      results.value = await searchExecutor.searchByVehicle()
      
      // Save to history
      if (searchHistory) {
        searchHistory.addSearch({
          year: vehicleState.selectedYear.value,
          make: vehicleState.selectedMake.value,
          model: vehicleState.selectedModel.value,
          results: results.value.length
        })
      }
      
      // Track analytics
      if (analytics) {
        analytics.track('search:complete', {
          resultCount: results.value.length
        })
      }
    } catch (err) {
      error.value = err
      widgetEvents.emit('error', err)
    } finally {
      loadingResults.value = false
    }
  }

  // Reset function
  function reset() {
    vehicleState.resetSelection()
    vehicleState.resetOptions()
    results.value = []
    error.value = null
  }

  return {
    // Config
    config,
    flowType,
    isReady,
    
    // State (spread from vehicle state)
    ...vehicleState,
    
    // Results
    results,
    loadingResults,
    error,
    
    // Methods
    initialize,
    loadInitialData,
    loadYears: (make, model) => vehicleLoader.loadYears(make, model),
    loadMakes: (year) => vehicleLoader.loadMakes(year),
    loadModels: (make, year) => vehicleLoader.loadModels(make, year),
    loadGenerations: (make, model) => vehicleLoader.loadGenerations(make, model),
    loadModifications: (make, model, yearOrGen) => 
      vehicleLoader.loadModifications(make, model, yearOrGen),
    search,
    reset,
    
    // History
    getSearchHistory: () => searchHistory?.getHistory() || []
  }
})
```

## Implementation Strategy

### Phase-by-Phase Rollout

#### Week 1: Foundation
- Day 1-2: Create API client module and tests
- Day 3-4: Create state management module
- Day 5: Integration testing

#### Week 2: Core Modules
- Day 1-2: Create vehicle loader module
- Day 3-4: Create search executor module
- Day 5: Update main store

#### Week 3: Testing & Optimization
- Day 1-2: Comprehensive unit tests
- Day 3: Integration testing
- Day 4-5: Performance optimization

### Migration Path

1. **Create new modules alongside existing code**
2. **Add feature flags for gradual rollout**
3. **Run both implementations in parallel**
4. **Validate behavior matches**
5. **Switch to new implementation**
6. **Remove old code**

### Feature Flag Implementation
```javascript
// Enable gradual rollout
const USE_REFACTORED_STORE = 
  window.FinderV2Config?.useRefactoredStore ?? false

export const useFinderStore = USE_REFACTORED_STORE 
  ? useFinderStoreRefactored 
  : useFinderStoreLegacy
```

## Testing Strategy

### Unit Tests
```javascript
// tests/stores/modules/api.test.js
describe('ApiClient', () => {
  it('deduplicates concurrent requests', async () => {
    const api = new ApiClient(config)
    
    // Make two identical requests
    const req1 = api.get('year', { make: 'toyota' })
    const req2 = api.get('year', { make: 'toyota' })
    
    // Should return same promise
    expect(req1).toBe(req2)
  })
})

// tests/stores/modules/vehicle-loader.test.js
describe('VehicleLoader', () => {
  it('loads years with proper transformation', async () => {
    const loader = new VehicleLoader(mockApi, state, config)
    
    mockApi.get.mockResolvedValue({
      data: [{ year: 2023 }, { year: 2022 }]
    })
    
    const years = await loader.loadYears()
    
    expect(years).toEqual([
      { value: 2023, label: '2023' },
      { value: 2022, label: '2022' }
    ])
  })
})
```

### Integration Tests
```javascript
// tests/stores/finder.integration.test.js
describe('Refactored Finder Store', () => {
  it('maintains backward compatibility', async () => {
    const legacyStore = useFinderStoreLegacy()
    const refactoredStore = useFinderStoreRefactored()
    
    // Initialize both
    legacyStore.initialize(config)
    refactoredStore.initialize(config)
    
    // Load data
    await legacyStore.loadYears()
    await refactoredStore.loadYears()
    
    // Compare results
    expect(refactoredStore.years).toEqual(legacyStore.years)
  })
})
```

## Success Metrics

### Code Quality Metrics
- **File Size**: < 200 lines per module
- **Cyclomatic Complexity**: < 10 per function
- **Test Coverage**: > 90% per module
- **Coupling**: Low coupling between modules

### Performance Metrics
- **Bundle Size**: Potential for lazy loading saves ~30KB
- **Initial Load**: Faster due to smaller initial bundle
- **Memory Usage**: Reduced due to better garbage collection

### Maintainability Metrics
- **Time to Add Feature**: 50% reduction
- **Bug Fix Time**: 40% reduction
- **Code Review Time**: 30% reduction
- **Onboarding Time**: 60% reduction

## Risk Mitigation

### Potential Risks
1. **Breaking Changes**: Mitigated by feature flags
2. **Performance Regression**: Mitigated by benchmarking
3. **Missing Functionality**: Mitigated by comprehensive testing
4. **Integration Issues**: Mitigated by parallel running

### Rollback Plan
```javascript
// Quick rollback mechanism
if (window.FinderV2Config?.emergencyRollback) {
  // Force use of legacy store
  window.FinderV2Config.useRefactoredStore = false
}
```

## Expected Outcomes

### Before Refactoring
- 818 lines in single file
- High complexity (>30 cyclomatic)
- Difficult to test
- Hard to maintain
- Slow onboarding

### After Refactoring
- ~150 lines main store
- 6 focused modules (50-200 lines each)
- Low complexity (<10 cyclomatic)
- Easy to test (90%+ coverage)
- Easy to maintain
- Fast onboarding

## Next Steps

1. **Review and approve plan** with team
2. **Create feature branch** for refactoring
3. **Begin Phase 1** with API client extraction
4. **Set up monitoring** for A/B testing
5. **Plan gradual rollout** schedule

---

**Recommendation**: Start with Phase 1 (API Client) as it's the most independent module and provides immediate benefits for testing and reusability.