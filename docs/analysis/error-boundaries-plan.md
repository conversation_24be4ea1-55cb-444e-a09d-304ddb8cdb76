# 🛡️ Error Boundaries Implementation Plan for Finder-v2 Widget

## Overview

Error boundaries are crucial for preventing widget crashes from affecting the entire page. Vue 3 provides error handling mechanisms that we'll leverage to create robust error boundaries for the Finder-v2 widget.

## Current State Analysis

### Existing Error Handling
- **No error boundaries**: Application crashes propagate to parent page
- **No global error handler**: Unhandled errors go to browser console
- **No fallback UI**: Users see blank widget on errors
- **No error reporting**: No telemetry for production issues
- **No recovery mechanisms**: Page refresh required after crashes

### Risk Assessment
| Risk Level | Issue | Impact |
|------------|-------|--------|
| 🔴 High | API failures crash widget | Complete widget failure |
| 🔴 High | Component errors propagate | Parent page affected |
| 🟡 Medium | No error recovery | Poor user experience |
| 🟡 Medium | Silent failures | Issues go undetected |
| 🟢 Low | Console errors | Developer experience only |

## Vue 3 Error Handling Mechanisms

### Available APIs
1. **app.config.errorHandler**: Global error handler for entire app
2. **onErrorCaptured**: Composition API lifecycle hook
3. **errorCaptured**: Options API lifecycle hook
4. **Suspense + Error Boundary**: Async component error handling
5. **Try-catch in async functions**: Manual error handling

## Implementation Strategy

### 🎯 Goals
1. **Isolate Failures**: Prevent widget errors from affecting parent page
2. **Graceful Degradation**: Show meaningful fallback UI on errors
3. **Error Recovery**: Allow users to retry without page refresh
4. **Error Reporting**: Track errors for debugging and monitoring
5. **Developer Experience**: Clear error messages in development

## Detailed Implementation Plan

### Phase 1: Global Error Handler

#### 1.1 Create Global Error Handler
```javascript
// src/utils/errorHandler.js
export class WidgetErrorHandler {
  constructor(config = {}) {
    this.errors = []
    this.maxErrors = config.maxErrors || 10
    this.isDevelopment = import.meta.env.DEV
    this.errorCallback = config.onError || null
  }

  handleError(error, instance, info) {
    // Log error details
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      component: instance?.$options.name || 'Unknown',
      info: info,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // Store error
    this.errors.push(errorInfo)
    if (this.errors.length > this.maxErrors) {
      this.errors.shift()
    }

    // Development logging
    if (this.isDevelopment) {
      console.group('🔴 Widget Error')
      console.error('Error:', error)
      console.log('Component:', errorInfo.component)
      console.log('Info:', info)
      console.log('Stack:', error.stack)
      console.groupEnd()
    }

    // Production error reporting
    if (!this.isDevelopment) {
      this.reportError(errorInfo)
    }

    // Call custom error callback
    if (this.errorCallback) {
      this.errorCallback(errorInfo)
    }

    // Emit error event for parent page
    this.emitErrorEvent(errorInfo)
  }

  reportError(errorInfo) {
    // Send to error tracking service
    if (window.FinderV2Config?.errorReportingUrl) {
      fetch(window.FinderV2Config.errorReportingUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorInfo)
      }).catch(() => {
        // Silently fail error reporting
      })
    }
  }

  emitErrorEvent(errorInfo) {
    // Emit custom event for parent page
    window.dispatchEvent(new CustomEvent('finderv2:error', {
      detail: {
        message: errorInfo.message,
        component: errorInfo.component,
        recoverable: this.isRecoverable(errorInfo)
      }
    }))
  }

  isRecoverable(errorInfo) {
    // Determine if error is recoverable
    const nonRecoverable = [
      'Maximum call stack',
      'out of memory',
      'SecurityError'
    ]
    return !nonRecoverable.some(pattern => 
      errorInfo.message.includes(pattern)
    )
  }

  clearErrors() {
    this.errors = []
  }

  getErrors() {
    return [...this.errors]
  }
}
```

#### 1.2 Update main.js
```javascript
// src/main.js
import { createApp } from 'vue'
import { WidgetErrorHandler } from './utils/errorHandler'

const app = createApp(FinderV2Widget)

// Initialize error handler
const errorHandler = new WidgetErrorHandler({
  maxErrors: 20,
  onError: (error) => {
    // Custom error handling
    if (error.message.includes('Network')) {
      // Show network error UI
    }
  }
})

// Set global error handler
app.config.errorHandler = (error, instance, info) => {
  errorHandler.handleError(error, instance, info)
  // Return false to prevent default logging
  return false
}

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  errorHandler.handleError(
    new Error(event.reason?.message || event.reason || 'Unhandled Promise Rejection'),
    null,
    'unhandledrejection'
  )
  event.preventDefault()
})
```

### Phase 2: Component Error Boundaries

#### 2.1 Create ErrorBoundary Component
```vue
<!-- src/components/ErrorBoundary.vue -->
<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-container">
      <!-- Error Icon -->
      <div class="error-icon">
        <svg class="w-12 h-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
      </div>

      <!-- Error Message -->
      <div class="error-content">
        <h3 class="error-title">{{ errorTitle }}</h3>
        <p class="error-message">{{ errorMessage }}</p>
        
        <!-- Retry Button -->
        <button 
          v-if="showRetry"
          @click="retry"
          class="retry-button"
        >
          {{ retryText }}
        </button>

        <!-- Details (Development Only) -->
        <details v-if="isDevelopment && error" class="error-details">
          <summary>Error Details</summary>
          <pre>{{ error.stack }}</pre>
        </details>
      </div>
    </div>
  </div>
  
  <!-- Normal slot content -->
  <slot v-else />
</template>

<script>
import { ref, onErrorCaptured, computed } from 'vue'

export default {
  name: 'ErrorBoundary',
  props: {
    fallback: {
      type: String,
      default: 'Something went wrong'
    },
    canRetry: {
      type: Boolean,
      default: true
    },
    onError: {
      type: Function,
      default: null
    },
    errorTitle: {
      type: String,
      default: 'Oops! Something went wrong'
    },
    retryText: {
      type: String,
      default: 'Try Again'
    }
  },
  setup(props, { emit }) {
    const hasError = ref(false)
    const error = ref(null)
    const isDevelopment = import.meta.env.DEV

    const errorMessage = computed(() => {
      if (!error.value) return props.fallback
      
      // User-friendly error messages
      const errorMap = {
        'Network': 'Unable to connect. Please check your internet connection.',
        'API': 'Service temporarily unavailable. Please try again later.',
        'Timeout': 'Request timed out. Please try again.',
        'Permission': 'You don\'t have permission to access this resource.',
        'NotFound': 'The requested resource was not found.'
      }

      for (const [key, message] of Object.entries(errorMap)) {
        if (error.value.message?.includes(key)) {
          return message
        }
      }

      return props.fallback
    })

    const showRetry = computed(() => {
      return props.canRetry && hasError.value
    })

    // Capture errors from child components
    onErrorCaptured((err, instance, info) => {
      console.error('Error caught by boundary:', err)
      
      hasError.value = true
      error.value = err

      // Call custom error handler
      if (props.onError) {
        props.onError(err, instance, info)
      }

      // Emit error event
      emit('error', {
        error: err,
        instance,
        info
      })

      // Prevent error propagation
      return false
    })

    const retry = () => {
      hasError.value = false
      error.value = null
      emit('retry')
    }

    return {
      hasError,
      error,
      errorMessage,
      showRetry,
      retry,
      isDevelopment
    }
  }
}
</script>

<style scoped>
.error-boundary {
  @apply p-8;
}

.error-container {
  @apply max-w-md mx-auto text-center;
}

.error-icon {
  @apply mb-4 flex justify-center;
}

.error-content {
  @apply space-y-4;
}

.error-title {
  @apply text-xl font-semibold text-gray-900;
}

.error-message {
  @apply text-gray-600;
}

.retry-button {
  @apply px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors;
}

.error-details {
  @apply mt-4 text-left;
}

.error-details summary {
  @apply cursor-pointer text-sm text-gray-500;
}

.error-details pre {
  @apply mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto;
}
</style>
```

#### 2.2 Create Specialized Error Boundaries

```vue
<!-- src/components/ApiErrorBoundary.vue -->
<template>
  <ErrorBoundary
    :can-retry="true"
    :error-title="errorTitle"
    :fallback="fallbackMessage"
    @retry="handleRetry"
    @error="handleApiError"
  >
    <slot />
  </ErrorBoundary>
</template>

<script>
import { computed } from 'vue'
import ErrorBoundary from './ErrorBoundary.vue'
import { useFinderStore } from '@/stores/finder'

export default {
  name: 'ApiErrorBoundary',
  components: { ErrorBoundary },
  setup() {
    const store = useFinderStore()

    const errorTitle = computed(() => {
      if (store.isOffline) {
        return 'No Internet Connection'
      }
      return 'Unable to Load Data'
    })

    const fallbackMessage = computed(() => {
      if (store.isOffline) {
        return 'Please check your internet connection and try again.'
      }
      return 'We\'re having trouble loading the data. Please try again.'
    })

    const handleRetry = async () => {
      // Reset store state
      store.resetFilters()
      // Reload initial data
      await store.loadInitialData()
    }

    const handleApiError = ({ error }) => {
      // Log API errors specifically
      console.error('API Error:', error)
      
      // Track API failures
      if (window.FinderV2Config?.analytics) {
        window.FinderV2Config.analytics.track('api_error', {
          endpoint: error.config?.url,
          status: error.response?.status,
          message: error.message
        })
      }
    }

    return {
      errorTitle,
      fallbackMessage,
      handleRetry,
      handleApiError
    }
  }
}
</script>
```

### Phase 3: Integration with Existing Components

#### 3.1 Wrap Main Component
```vue
<!-- src/components/FinderV2Widget.vue -->
<template>
  <ErrorBoundary 
    class="finder-v2-widget"
    @error="handleWidgetError"
  >
    <!-- Existing widget content -->
    <div class="widget-container">
      <ApiErrorBoundary>
        <SearchForm />
      </ApiErrorBoundary>
      
      <ApiErrorBoundary>
        <ResultsList />
      </ApiErrorBoundary>
    </div>
  </ErrorBoundary>
</template>
```

#### 3.2 Add to Critical Components
```vue
<!-- Wrap dropdowns -->
<ErrorBoundary :can-retry="false">
  <YearDropdown />
</ErrorBoundary>

<!-- Wrap async components -->
<Suspense>
  <template #default>
    <ErrorBoundary>
      <AsyncResultsView />
    </ErrorBoundary>
  </template>
  <template #fallback>
    <LoadingSpinner />
  </template>
</Suspense>
```

### Phase 4: Error Recovery Strategies

#### 4.1 Automatic Recovery
```javascript
// src/composables/useErrorRecovery.js
export function useErrorRecovery() {
  const maxRetries = 3
  const retryDelay = 1000

  const withRetry = async (fn, options = {}) => {
    const { 
      retries = maxRetries, 
      delay = retryDelay,
      backoff = true 
    } = options

    let lastError

    for (let i = 0; i < retries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        
        if (i < retries - 1) {
          const waitTime = backoff ? delay * Math.pow(2, i) : delay
          await new Promise(resolve => setTimeout(resolve, waitTime))
        }
      }
    }

    throw lastError
  }

  const withFallback = async (fn, fallbackValue) => {
    try {
      return await fn()
    } catch (error) {
      console.warn('Using fallback value due to error:', error)
      return fallbackValue
    }
  }

  const withCircuitBreaker = (() => {
    const failures = new Map()
    const threshold = 5
    const timeout = 60000 // 1 minute

    return async (key, fn) => {
      const failureCount = failures.get(key) || 0
      
      if (failureCount >= threshold) {
        const lastFailure = failures.get(`${key}_time`)
        if (Date.now() - lastFailure < timeout) {
          throw new Error('Circuit breaker open')
        }
        // Reset after timeout
        failures.delete(key)
        failures.delete(`${key}_time`)
      }

      try {
        const result = await fn()
        failures.delete(key) // Reset on success
        return result
      } catch (error) {
        failures.set(key, failureCount + 1)
        failures.set(`${key}_time`, Date.now())
        throw error
      }
    }
  })()

  return {
    withRetry,
    withFallback,
    withCircuitBreaker
  }
}
```

### Phase 5: Testing Strategy

#### 5.1 Unit Tests
```javascript
// tests/error-boundary.test.js
import { mount } from '@vue/test-utils'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

describe('ErrorBoundary', () => {
  it('catches and displays child component errors', async () => {
    const ThrowError = {
      setup() {
        throw new Error('Test error')
      },
      template: '<div>Should not render</div>'
    }

    const wrapper = mount(ErrorBoundary, {
      slots: {
        default: ThrowError
      }
    })

    expect(wrapper.text()).toContain('Something went wrong')
    expect(wrapper.find('.retry-button').exists()).toBe(true)
  })

  it('recovers when retry is clicked', async () => {
    const wrapper = mount(ErrorBoundary)
    
    // Simulate error
    wrapper.vm.hasError = true
    await wrapper.vm.$nextTick()
    
    // Click retry
    await wrapper.find('.retry-button').trigger('click')
    
    expect(wrapper.vm.hasError).toBe(false)
  })
})
```

#### 5.2 Integration Tests
```javascript
// tests/widget-error-handling.test.js
describe('Widget Error Handling', () => {
  it('continues working when one component fails', async () => {
    // Mount widget
    const widget = await mountWidget()
    
    // Cause error in search form
    await widget.searchForm.simulateError()
    
    // Results should still be visible
    expect(widget.results.isVisible()).toBe(true)
    
    // Error boundary should show
    expect(widget.searchForm.hasErrorBoundary()).toBe(true)
  })
})
```

## Rollout Plan

### Week 1: Foundation
- Day 1-2: Implement global error handler
- Day 3-4: Create ErrorBoundary component
- Day 5: Add unit tests

### Week 2: Integration
- Day 1-2: Wrap main components
- Day 3-4: Add specialized boundaries
- Day 5: Integration testing

### Week 3: Enhancement
- Day 1-2: Add recovery strategies
- Day 3: Error reporting setup
- Day 4-5: Production testing

## Monitoring & Metrics

### Success Metrics
- **Error Recovery Rate**: > 80% of errors recovered without refresh
- **User Impact**: < 5% of users experience unrecoverable errors
- **MTTR**: < 30 seconds mean time to recovery
- **Error Visibility**: 100% of errors tracked and reported

### Monitoring Setup
```javascript
// Track error metrics
window.FinderV2Metrics = {
  errors: {
    total: 0,
    recovered: 0,
    fatal: 0,
    byComponent: {},
    byType: {}
  }
}
```

## Configuration Options

### Widget Configuration
```javascript
window.FinderV2Config = {
  errorHandling: {
    enabled: true,
    maxRetries: 3,
    retryDelay: 1000,
    showDetails: false, // Show error details in production
    reportingUrl: '/api/errors',
    fallbackUI: 'minimal', // 'minimal' | 'full' | 'none'
    autoRecover: true
  }
}
```

## Best Practices

### Do's ✅
1. Always wrap async operations in error boundaries
2. Provide meaningful error messages to users
3. Log errors with sufficient context
4. Allow users to recover from errors
5. Test error scenarios thoroughly

### Don'ts ❌
1. Don't expose sensitive information in error messages
2. Don't let errors propagate to parent page
3. Don't retry indefinitely
4. Don't ignore recurring errors
5. Don't show technical stack traces to users

## Expected Outcomes

### Before Implementation
- Widget crashes affect entire page
- No error visibility
- Users must refresh page
- Silent failures common

### After Implementation
- Isolated error handling
- 100% error visibility
- In-place recovery options
- Improved user experience
- Reduced support tickets

---

**Next Steps**: Begin with Phase 1 (Global Error Handler) to establish foundation for comprehensive error handling.