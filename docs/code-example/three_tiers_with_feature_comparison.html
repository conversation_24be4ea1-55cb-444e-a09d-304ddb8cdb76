<div class="isolate overflow-hidden">
  <div class="flow-root bg-gray-900 pt-24 pb-16 sm:pt-32 lg:pb-0">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="relative z-10">
        <h2 class="mx-auto max-w-4xl text-center text-5xl font-semibold tracking-tight text-balance text-white sm:text-6xl">Pricing that grows with you</h2>
        <p class="mx-auto mt-6 max-w-2xl text-center text-lg font-medium text-pretty text-gray-400 sm:text-xl/8">Choose an affordable plan that’s packed with the best features for engaging your audience, creating customer loyalty, and driving sales.</p>
        <div class="mt-16 flex justify-center">
          <fieldset aria-label="Payment frequency">
            <div class="grid grid-cols-2 gap-x-1 rounded-full bg-white/5 p-1 text-center text-xs/5 font-semibold text-white">
              <!-- Checked: "bg-indigo-500" -->
              <label class="cursor-pointer rounded-full px-2.5 py-1">
                <input type="radio" name="frequency" value="monthly" class="sr-only">
                <span>Monthly</span>
              </label>
              <!-- Checked: "bg-indigo-500" -->
              <label class="cursor-pointer rounded-full px-2.5 py-1">
                <input type="radio" name="frequency" value="annually" class="sr-only">
                <span>Annually</span>
              </label>
            </div>
          </fieldset>
        </div>
      </div>
      <div class="relative mx-auto mt-10 grid max-w-md grid-cols-1 gap-y-8 lg:mx-0 lg:-mb-14 lg:max-w-none lg:grid-cols-3">
        <svg viewBox="0 0 1208 1024" aria-hidden="true" class="absolute -bottom-48 left-1/2 h-[64rem] -translate-x-1/2 translate-y-1/2 [mask-image:radial-gradient(closest-side,white,transparent)] lg:-top-48 lg:bottom-auto lg:translate-y-0">
          <ellipse cx="604" cy="512" fill="url(#d25c25d4-6d43-4bf9-b9ac-1842a30a4867)" rx="604" ry="512" />
          <defs>
            <radialGradient id="d25c25d4-6d43-4bf9-b9ac-1842a30a4867">
              <stop stop-color="#7775D6" />
              <stop offset="1" stop-color="#E935C1" />
            </radialGradient>
          </defs>
        </svg>
        <div class="hidden lg:absolute lg:inset-x-px lg:top-4 lg:bottom-0 lg:block lg:rounded-t-2xl lg:bg-gray-800/80 lg:ring-1 lg:ring-white/10" aria-hidden="true"></div>
        <div class="relative rounded-2xl bg-gray-800/80 ring-1 ring-white/10 lg:bg-transparent lg:pb-14 lg:ring-0">
          <div class="p-8 lg:pt-12 xl:p-10 xl:pt-14">
            <h3 id="tier-starter" class="text-sm/6 font-semibold text-white">Starter</h3>
            <div class="flex flex-col gap-6 sm:flex-row sm:items-end sm:justify-between lg:flex-col lg:items-stretch">
              <div class="mt-2 flex items-center gap-x-4">
                <!-- Price, update based on frequency toggle state -->
                <p class="text-4xl font-semibold tracking-tight text-white">$19</p>
                <div class="text-sm">
                  <p class="text-white">USD</p>
                  <!-- Payment frequency, update based on frequency toggle state -->
                  <p class="text-gray-400">Billed monthly</p>
                </div>
              </div>
              <a href="#" aria-describedby="tier-starter" class="rounded-md bg-white/10 px-3 py-2 text-center text-sm/6 font-semibold text-white hover:bg-white/20 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white">Buy this plan</a>
            </div>
            <div class="mt-8 flow-root sm:mt-10">
              <ul role="list" class="-my-2 divide-y divide-white/5 border-t border-white/5 text-sm/6 text-white lg:border-t-0">
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Custom domains
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Edge content delivery
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Advanced analytics
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="relative z-10 rounded-2xl bg-white ring-1 shadow-xl ring-gray-900/10">
          <div class="p-8 lg:pt-12 xl:p-10 xl:pt-14">
            <h3 id="tier-scale" class="text-sm/6 font-semibold text-gray-900">Scale</h3>
            <div class="flex flex-col gap-6 sm:flex-row sm:items-end sm:justify-between lg:flex-col lg:items-stretch">
              <div class="mt-2 flex items-center gap-x-4">
                <!-- Price, update based on frequency toggle state -->
                <p class="text-4xl font-semibold tracking-tight text-gray-900">$99</p>
                <div class="text-sm">
                  <p class="text-gray-900">USD</p>
                  <!-- Payment frequency, update based on frequency toggle state -->
                  <p class="text-gray-500">Billed monthly</p>
                </div>
              </div>
              <a href="#" aria-describedby="tier-scale" class="rounded-md bg-indigo-600 px-3 py-2 text-center text-sm/6 font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Buy this plan</a>
            </div>
            <div class="mt-8 flow-root sm:mt-10">
              <ul role="list" class="-my-2 divide-y divide-gray-900/5 border-t border-gray-900/5 text-sm/6 text-gray-600 lg:border-t-0">
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Custom domains
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Edge content delivery
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Advanced analytics
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Quarterly workshops
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Single sign-on (SSO)
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Priority phone support
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="relative rounded-2xl bg-gray-800/80 ring-1 ring-white/10 lg:bg-transparent lg:pb-14 lg:ring-0">
          <div class="p-8 lg:pt-12 xl:p-10 xl:pt-14">
            <h3 id="tier-growth" class="text-sm/6 font-semibold text-white">Growth</h3>
            <div class="flex flex-col gap-6 sm:flex-row sm:items-end sm:justify-between lg:flex-col lg:items-stretch">
              <div class="mt-2 flex items-center gap-x-4">
                <!-- Price, update based on frequency toggle state -->
                <p class="text-4xl font-semibold tracking-tight text-white">$49</p>
                <div class="text-sm">
                  <p class="text-white">USD</p>
                  <!-- Payment frequency, update based on frequency toggle state -->
                  <p class="text-gray-400">Billed monthly</p>
                </div>
              </div>
              <a href="#" aria-describedby="tier-growth" class="rounded-md bg-white/10 px-3 py-2 text-center text-sm/6 font-semibold text-white hover:bg-white/20 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white">Buy this plan</a>
            </div>
            <div class="mt-8 flow-root sm:mt-10">
              <ul role="list" class="-my-2 divide-y divide-white/5 border-t border-white/5 text-sm/6 text-white lg:border-t-0">
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Custom domains
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Edge content delivery
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Advanced analytics
                </li>
                <li class="flex gap-x-3 py-2">
                  <svg class="h-6 w-5 flex-none text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                  </svg>
                  Quarterly workshops
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="relative bg-gray-50 lg:pt-14">
    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <!-- Feature comparison (up to lg) -->
      <section aria-labelledby="mobile-comparison-heading" class="lg:hidden">
        <h2 id="mobile-comparison-heading" class="sr-only">Feature comparison</h2>

        <div class="mx-auto max-w-2xl space-y-16">
          <div class="border-t border-gray-900/10">
            <div class="-mt-px w-72 border-t-2 border-transparent pt-10 md:w-80">
              <h3 class="text-sm/6 font-semibold text-gray-900">Starter</h3>
              <p class="mt-1 text-sm/6 text-gray-600">Everything you need to get started.</p>
            </div>

            <div class="mt-10 space-y-10">
              <div>
                <h4 class="text-sm/6 font-semibold text-gray-900">Features</h4>
                <div class="relative mt-6">
                  <!-- Fake card background -->
                  <div aria-hidden="true" class="absolute inset-y-0 right-0 hidden w-1/2 rounded-lg bg-white shadow-xs sm:block"></div>

                  <div class="relative rounded-lg bg-white ring-1 shadow-xs ring-gray-900/10 sm:rounded-none sm:bg-transparent sm:ring-0 sm:shadow-none">
                    <dl class="divide-y divide-gray-200 text-sm/6">
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Edge content delivery</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Custom domains</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <span class="text-gray-900">1</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Team members</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <span class="text-gray-900">3</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Single sign-on (SSO)</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <!-- Fake card border -->
                  <div aria-hidden="true" class="pointer-events-none absolute inset-y-0 right-0 hidden w-1/2 rounded-lg ring-1 ring-gray-900/10 sm:block"></div>
                </div>
              </div>
              <div>
                <h4 class="text-sm/6 font-semibold text-gray-900">Reporting</h4>
                <div class="relative mt-6">
                  <!-- Fake card background -->
                  <div aria-hidden="true" class="absolute inset-y-0 right-0 hidden w-1/2 rounded-lg bg-white shadow-xs sm:block"></div>

                  <div class="relative rounded-lg bg-white ring-1 shadow-xs ring-gray-900/10 sm:rounded-none sm:bg-transparent sm:ring-0 sm:shadow-none">
                    <dl class="divide-y divide-gray-200 text-sm/6">
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Advanced analytics</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Basic reports</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Professional reports</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Custom report builder</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <!-- Fake card border -->
                  <div aria-hidden="true" class="pointer-events-none absolute inset-y-0 right-0 hidden w-1/2 rounded-lg ring-1 ring-gray-900/10 sm:block"></div>
                </div>
              </div>
              <div>
                <h4 class="text-sm/6 font-semibold text-gray-900">Support</h4>
                <div class="relative mt-6">
                  <!-- Fake card background -->
                  <div aria-hidden="true" class="absolute inset-y-0 right-0 hidden w-1/2 rounded-lg bg-white shadow-xs sm:block"></div>

                  <div class="relative rounded-lg bg-white ring-1 shadow-xs ring-gray-900/10 sm:rounded-none sm:bg-transparent sm:ring-0 sm:shadow-none">
                    <dl class="divide-y divide-gray-200 text-sm/6">
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">24/7 online support</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Quarterly workshops</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Priority phone support</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">1:1 onboarding tour</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <!-- Fake card border -->
                  <div aria-hidden="true" class="pointer-events-none absolute inset-y-0 right-0 hidden w-1/2 rounded-lg ring-1 ring-gray-900/10 sm:block"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="border-t border-gray-900/10">
            <div class="-mt-px w-72 border-t-2 border-indigo-600 pt-10 md:w-80">
              <h3 class="text-sm/6 font-semibold text-indigo-600">Scale</h3>
              <p class="mt-1 text-sm/6 text-gray-600">Added flexibility at scale.</p>
            </div>

            <div class="mt-10 space-y-10">
              <div>
                <h4 class="text-sm/6 font-semibold text-gray-900">Features</h4>
                <div class="relative mt-6">
                  <!-- Fake card background -->
                  <div aria-hidden="true" class="absolute inset-y-0 right-0 hidden w-1/2 rounded-lg bg-white shadow-xs sm:block"></div>

                  <div class="relative rounded-lg bg-white ring-2 shadow-xs ring-indigo-600 sm:rounded-none sm:bg-transparent sm:ring-0 sm:shadow-none">
                    <dl class="divide-y divide-gray-200 text-sm/6">
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Edge content delivery</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Custom domains</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <span class="font-semibold text-indigo-600">Unlimited</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Team members</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <span class="font-semibold text-indigo-600">Unlimited</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Single sign-on (SSO)</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <!-- Fake card border -->
                  <div aria-hidden="true" class="pointer-events-none absolute inset-y-0 right-0 hidden w-1/2 rounded-lg ring-2 ring-indigo-600 sm:block"></div>
                </div>
              </div>
              <div>
                <h4 class="text-sm/6 font-semibold text-gray-900">Reporting</h4>
                <div class="relative mt-6">
                  <!-- Fake card background -->
                  <div aria-hidden="true" class="absolute inset-y-0 right-0 hidden w-1/2 rounded-lg bg-white shadow-xs sm:block"></div>

                  <div class="relative rounded-lg bg-white ring-2 shadow-xs ring-indigo-600 sm:rounded-none sm:bg-transparent sm:ring-0 sm:shadow-none">
                    <dl class="divide-y divide-gray-200 text-sm/6">
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Advanced analytics</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Basic reports</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Professional reports</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Custom report builder</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <!-- Fake card border -->
                  <div aria-hidden="true" class="pointer-events-none absolute inset-y-0 right-0 hidden w-1/2 rounded-lg ring-2 ring-indigo-600 sm:block"></div>
                </div>
              </div>
              <div>
                <h4 class="text-sm/6 font-semibold text-gray-900">Support</h4>
                <div class="relative mt-6">
                  <!-- Fake card background -->
                  <div aria-hidden="true" class="absolute inset-y-0 right-0 hidden w-1/2 rounded-lg bg-white shadow-xs sm:block"></div>

                  <div class="relative rounded-lg bg-white ring-2 shadow-xs ring-indigo-600 sm:rounded-none sm:bg-transparent sm:ring-0 sm:shadow-none">
                    <dl class="divide-y divide-gray-200 text-sm/6">
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">24/7 online support</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Quarterly workshops</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Priority phone support</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">1:1 onboarding tour</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <!-- Fake card border -->
                  <div aria-hidden="true" class="pointer-events-none absolute inset-y-0 right-0 hidden w-1/2 rounded-lg ring-2 ring-indigo-600 sm:block"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="border-t border-gray-900/10">
            <div class="-mt-px w-72 border-t-2 border-transparent pt-10 md:w-80">
              <h3 class="text-sm/6 font-semibold text-gray-900">Growth</h3>
              <p class="mt-1 text-sm/6 text-gray-600">All the extras for your growing team.</p>
            </div>

            <div class="mt-10 space-y-10">
              <div>
                <h4 class="text-sm/6 font-semibold text-gray-900">Features</h4>
                <div class="relative mt-6">
                  <!-- Fake card background -->
                  <div aria-hidden="true" class="absolute inset-y-0 right-0 hidden w-1/2 rounded-lg bg-white shadow-xs sm:block"></div>

                  <div class="relative rounded-lg bg-white ring-1 shadow-xs ring-gray-900/10 sm:rounded-none sm:bg-transparent sm:ring-0 sm:shadow-none">
                    <dl class="divide-y divide-gray-200 text-sm/6">
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Edge content delivery</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Custom domains</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <span class="text-gray-900">3</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Team members</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <span class="text-gray-900">20</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Single sign-on (SSO)</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <!-- Fake card border -->
                  <div aria-hidden="true" class="pointer-events-none absolute inset-y-0 right-0 hidden w-1/2 rounded-lg ring-1 ring-gray-900/10 sm:block"></div>
                </div>
              </div>
              <div>
                <h4 class="text-sm/6 font-semibold text-gray-900">Reporting</h4>
                <div class="relative mt-6">
                  <!-- Fake card background -->
                  <div aria-hidden="true" class="absolute inset-y-0 right-0 hidden w-1/2 rounded-lg bg-white shadow-xs sm:block"></div>

                  <div class="relative rounded-lg bg-white ring-1 shadow-xs ring-gray-900/10 sm:rounded-none sm:bg-transparent sm:ring-0 sm:shadow-none">
                    <dl class="divide-y divide-gray-200 text-sm/6">
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Advanced analytics</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Basic reports</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Professional reports</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Custom report builder</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <!-- Fake card border -->
                  <div aria-hidden="true" class="pointer-events-none absolute inset-y-0 right-0 hidden w-1/2 rounded-lg ring-1 ring-gray-900/10 sm:block"></div>
                </div>
              </div>
              <div>
                <h4 class="text-sm/6 font-semibold text-gray-900">Support</h4>
                <div class="relative mt-6">
                  <!-- Fake card background -->
                  <div aria-hidden="true" class="absolute inset-y-0 right-0 hidden w-1/2 rounded-lg bg-white shadow-xs sm:block"></div>

                  <div class="relative rounded-lg bg-white ring-1 shadow-xs ring-gray-900/10 sm:rounded-none sm:bg-transparent sm:ring-0 sm:shadow-none">
                    <dl class="divide-y divide-gray-200 text-sm/6">
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">24/7 online support</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Quarterly workshops</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                          </svg>
                          <span class="sr-only">Yes</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">Priority phone support</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                      <div class="flex items-center justify-between px-4 py-3 sm:grid sm:grid-cols-2 sm:px-0">
                        <dt class="pr-4 text-gray-600">1:1 onboarding tour</dt>
                        <dd class="flex items-center justify-end sm:justify-center sm:px-4">
                          <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                            <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                          </svg>
                          <span class="sr-only">No</span>
                        </dd>
                      </div>
                    </dl>
                  </div>

                  <!-- Fake card border -->
                  <div aria-hidden="true" class="pointer-events-none absolute inset-y-0 right-0 hidden w-1/2 rounded-lg ring-1 ring-gray-900/10 sm:block"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Feature comparison (lg+) -->
      <section aria-labelledby="comparison-heading" class="hidden lg:block">
        <h2 id="comparison-heading" class="sr-only">Feature comparison</h2>

        <div class="grid grid-cols-4 gap-x-8 border-t border-gray-900/10 before:block">
          <div aria-hidden="true" class="-mt-px">
            <div class="border-t-2 border-transparent pt-10">
              <p class="text-sm/6 font-semibold text-gray-900">Starter</p>
              <p class="mt-1 text-sm/6 text-gray-600">Everything you need to get started.</p>
            </div>
          </div>
          <div aria-hidden="true" class="-mt-px">
            <div class="border-t-2 border-indigo-600 pt-10">
              <p class="text-sm/6 font-semibold text-indigo-600">Scale</p>
              <p class="mt-1 text-sm/6 text-gray-600">Added flexibility at scale.</p>
            </div>
          </div>
          <div aria-hidden="true" class="-mt-px">
            <div class="border-t-2 border-transparent pt-10">
              <p class="text-sm/6 font-semibold text-gray-900">Growth</p>
              <p class="mt-1 text-sm/6 text-gray-600">All the extras for your growing team.</p>
            </div>
          </div>
        </div>

        <div class="-mt-6 space-y-16">
          <div>
            <h3 class="text-sm/6 font-semibold text-gray-900">Features</h3>
            <div class="relative -mx-8 mt-10">
              <!-- Fake card backgrounds -->
              <div class="absolute inset-x-8 inset-y-0 grid grid-cols-4 gap-x-8 before:block" aria-hidden="true">
                <div class="size-full rounded-lg bg-white shadow-xs"></div>
                <div class="size-full rounded-lg bg-white shadow-xs"></div>
                <div class="size-full rounded-lg bg-white shadow-xs"></div>
              </div>

              <table class="relative w-full border-separate border-spacing-x-8">
                <thead>
                  <tr class="text-left">
                    <th scope="col">
                      <span class="sr-only">Feature</span>
                    </th>
                    <th scope="col">
                      <span class="sr-only">Starter tier</span>
                    </th>
                    <th scope="col">
                      <span class="sr-only">Scale tier</span>
                    </th>
                    <th scope="col">
                      <span class="sr-only">Growth tier</span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">
                      Edge content delivery
                      <div class="absolute inset-x-8 mt-3 h-px bg-gray-200"></div>
                    </th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">
                      Custom domains
                      <div class="absolute inset-x-8 mt-3 h-px bg-gray-200"></div>
                    </th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <span class="text-sm/6 text-gray-900">1</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <span class="text-sm/6 font-semibold text-indigo-600">Unlimited</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <span class="text-sm/6 text-gray-900">3</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">
                      Team members
                      <div class="absolute inset-x-8 mt-3 h-px bg-gray-200"></div>
                    </th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <span class="text-sm/6 text-gray-900">3</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <span class="text-sm/6 font-semibold text-indigo-600">Unlimited</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <span class="text-sm/6 text-gray-900">20</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">Single sign-on (SSO)</th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>

              <!-- Fake card borders -->
              <div class="pointer-events-none absolute inset-x-8 inset-y-0 grid grid-cols-4 gap-x-8 before:block" aria-hidden="true">
                <div class="rounded-lg ring-1 ring-gray-900/10"></div>
                <div class="rounded-lg ring-2 ring-indigo-600"></div>
                <div class="rounded-lg ring-1 ring-gray-900/10"></div>
              </div>
            </div>
          </div>
          <div>
            <h3 class="text-sm/6 font-semibold text-gray-900">Reporting</h3>
            <div class="relative -mx-8 mt-10">
              <!-- Fake card backgrounds -->
              <div class="absolute inset-x-8 inset-y-0 grid grid-cols-4 gap-x-8 before:block" aria-hidden="true">
                <div class="size-full rounded-lg bg-white shadow-xs"></div>
                <div class="size-full rounded-lg bg-white shadow-xs"></div>
                <div class="size-full rounded-lg bg-white shadow-xs"></div>
              </div>

              <table class="relative w-full border-separate border-spacing-x-8">
                <thead>
                  <tr class="text-left">
                    <th scope="col">
                      <span class="sr-only">Feature</span>
                    </th>
                    <th scope="col">
                      <span class="sr-only">Starter tier</span>
                    </th>
                    <th scope="col">
                      <span class="sr-only">Scale tier</span>
                    </th>
                    <th scope="col">
                      <span class="sr-only">Growth tier</span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">
                      Advanced analytics
                      <div class="absolute inset-x-8 mt-3 h-px bg-gray-200"></div>
                    </th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">
                      Basic reports
                      <div class="absolute inset-x-8 mt-3 h-px bg-gray-200"></div>
                    </th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">
                      Professional reports
                      <div class="absolute inset-x-8 mt-3 h-px bg-gray-200"></div>
                    </th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">Custom report builder</th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>

              <!-- Fake card borders -->
              <div class="pointer-events-none absolute inset-x-8 inset-y-0 grid grid-cols-4 gap-x-8 before:block" aria-hidden="true">
                <div class="rounded-lg ring-1 ring-gray-900/10"></div>
                <div class="rounded-lg ring-2 ring-indigo-600"></div>
                <div class="rounded-lg ring-1 ring-gray-900/10"></div>
              </div>
            </div>
          </div>
          <div>
            <h3 class="text-sm/6 font-semibold text-gray-900">Support</h3>
            <div class="relative -mx-8 mt-10">
              <!-- Fake card backgrounds -->
              <div class="absolute inset-x-8 inset-y-0 grid grid-cols-4 gap-x-8 before:block" aria-hidden="true">
                <div class="size-full rounded-lg bg-white shadow-xs"></div>
                <div class="size-full rounded-lg bg-white shadow-xs"></div>
                <div class="size-full rounded-lg bg-white shadow-xs"></div>
              </div>

              <table class="relative w-full border-separate border-spacing-x-8">
                <thead>
                  <tr class="text-left">
                    <th scope="col">
                      <span class="sr-only">Feature</span>
                    </th>
                    <th scope="col">
                      <span class="sr-only">Starter tier</span>
                    </th>
                    <th scope="col">
                      <span class="sr-only">Scale tier</span>
                    </th>
                    <th scope="col">
                      <span class="sr-only">Growth tier</span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">
                      24/7 online support
                      <div class="absolute inset-x-8 mt-3 h-px bg-gray-200"></div>
                    </th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">
                      Quarterly workshops
                      <div class="absolute inset-x-8 mt-3 h-px bg-gray-200"></div>
                    </th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">
                      Priority phone support
                      <div class="absolute inset-x-8 mt-3 h-px bg-gray-200"></div>
                    </th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <th scope="row" class="w-1/4 py-3 pr-4 text-left text-sm/6 font-normal text-gray-900">1:1 onboarding tour</th>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Yes</span>
                      </span>
                    </td>
                    <td class="relative w-1/4 px-4 py-0 text-center">
                      <span class="relative size-full py-3">
                        <svg class="mx-auto size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                          <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                        </svg>
                        <span class="sr-only">No</span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>

              <!-- Fake card borders -->
              <div class="pointer-events-none absolute inset-x-8 inset-y-0 grid grid-cols-4 gap-x-8 before:block" aria-hidden="true">
                <div class="rounded-lg ring-1 ring-gray-900/10"></div>
                <div class="rounded-lg ring-2 ring-indigo-600"></div>
                <div class="rounded-lg ring-1 ring-gray-900/10"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
