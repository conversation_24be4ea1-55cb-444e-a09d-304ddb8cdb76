# Finder-v2 Widget Deployment Modes

## Quick Reference

The finder-v2 widget supports two deployment modes to manage console.log statements:

### Production Mode (Default)
```bash
./deploy-finder-v2.sh       # Default to production
./deploy-finder-v2.sh prod  # Explicit production
```
- ✅ Console.logs are **REMOVED**
- ✅ Smaller bundle size (~5KB savings)
- ✅ Optimized for production use
- ✅ No debug output in browser console

### Development Mode
```bash
./deploy-finder-v2.sh dev   # Development build
```
- ✅ Console.logs are **PRESERVED**
- ✅ Debug output visible in browser console
- ✅ Easier debugging and troubleshooting
- ⚠️ Slightly larger bundle size

## Visual Indicators

When running the deployment script, you'll see clear indicators of which mode is being used:

**Production Mode:**
```
📦 Build Mode: PRODUCTION (console.logs removed)
   Mode: PRODUCTION (console.logs removed)
```
(Displayed in magenta/purple color)

**Development Mode:**
```
📦 Build Mode: DEVELOPMENT (console.logs preserved)
   Mode: DEVELOPMENT (console.logs preserved)
```
(Displayed in cyan color)

## How It Works

### Technical Implementation

1. **Vite Configuration** (`vite.config.js`):
   - Checks the build mode
   - In production: Sets `drop_console: true` in terser options
   - In development: Sets `drop_console: false`

2. **Deployment Script**:
   - Accepts mode parameter: `dev` or `prod`
   - Development: Runs `npm run build -- --mode development`
   - Production: Runs `npm run build`

3. **Bundle Size Impact**:
   - Development build: ~298KB (with console.logs)
   - Production build: ~293KB (without console.logs)
   - Savings: ~5KB

## Use Cases

### When to Use Development Mode

Use `./deploy-finder-v2.sh dev` when:
- 🔍 Debugging issues in the widget
- 📊 Monitoring API responses and data flow
- 🚧 Developing new features
- 📝 Tracking execution flow
- 🐛 Troubleshooting production issues locally

### When to Use Production Mode

Use `./deploy-finder-v2.sh prod` (or default) when:
- 🚀 Deploying to staging/production
- 📦 Creating final builds
- ⚡ Optimizing performance
- 🔒 Removing debug information
- 📉 Minimizing bundle size

## Console.log Best Practices

### In Your Code

You can use console.logs freely in development:

```javascript
// These will be automatically removed in production
console.log('🚀 Widget initialized', config)
console.warn('⚠️ Missing configuration', missingFields)
console.error('❌ API call failed', error)
console.debug('🔍 Debug info:', debugData)
```

### Alternative: Development Logger

For explicit development-only logging:

```javascript
import { devLog, devWarn, devError } from '@/utils/logger.js'

// These only execute in development mode
devLog('Widget state updated', state)
devWarn('Deprecated method used')
devError('Validation failed', errors)
```

## Verification

### Check if Console.logs are Present

**Production Build:**
```bash
# Build for production
./deploy-finder-v2.sh prod

# Check for console.logs (should return nothing)
grep "console.log" src/apps/widgets/finder_v2/static/finder_v2/js/finder-v2-app.js
```

**Development Build:**
```bash
# Build for development
./deploy-finder-v2.sh dev

# Check for console.logs (should find matches)
grep "console.log" src/apps/widgets/finder_v2/static/finder_v2/js/finder-v2-app.js
```

## Enhanced Script

An enhanced version is also available with more features:
```bash
./deploy-finder-v2-enhanced.sh      # Production (default)
./deploy-finder-v2-enhanced.sh dev  # Development
./deploy-finder-v2-enhanced.sh prod # Production
```

Features:
- Bundle size reporting
- Detailed build information
- Color-coded output by mode
- Build time statistics

## Summary

- **Default behavior**: Production mode (console.logs removed)
- **Development debugging**: Use `dev` mode to keep console.logs
- **No code changes needed**: Just choose the appropriate build mode
- **Automatic optimization**: Production builds are always optimized
- **Clear visual feedback**: Color-coded output shows current mode