# User Cleanup Management Command

## Summary
Removes spam accounts while preserving legitimate users. The command deletes users who:
- are not Staff
- are not Superuser
- do not own any widget configuration (`widgets.widgetconfig`)

All other users are kept.

## Command
```bash
# Preview deletions (no changes)
python manage.py cleanup_inactive_users --dry-run

# Perform deletions
python manage.py cleanup_inactive_users
```

## Running in Docker (development/staging only)

### docker-compose (recommended)
```bash
# Dry-run (recommended first)
docker-compose exec web /root/.pyenv/versions/3.12.0/bin/python /code/manage.py cleanup_inactive_users --dry-run

# Execute cleanup
docker-compose exec web /root/.pyenv/versions/3.12.0/bin/python /code/manage.py cleanup_inactive_users

# Optional (not recommended): allow emails during deletion
docker-compose exec web /root/.pyenv/versions/3.12.0/bin/python /code/manage.py cleanup_inactive_users --allow-email
```

If you prefer loading pyenv shims via login shell:
```bash
docker-compose exec web bash -lc 'cd /code && python manage.py cleanup_inactive_users --dry-run'
docker-compose exec web bash -lc 'cd /code && python manage.py cleanup_inactive_users'
```

### docker (named container)
```bash
# Replace ws_services with your container name
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python /code/manage.py cleanup_inactive_users --dry-run
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python /code/manage.py cleanup_inactive_users
```

## Running on AWS EC2 (Production, Poetry only)

1) SSH to the instance:
```bash
ssh ec2-user@<your-ec2-host>
```

2) Navigate to the project root (replace with your actual path, e.g., `/srv/wheel-size-services`):
```bash
cd <project_root>
```

3) (Optional) Enable maintenance mode:
```bash
poetry run python manage.py maintenance_mode on
```

4) Dry-run the cleanup:
```bash
poetry run python manage.py cleanup_inactive_users --dry-run
```

5) Execute the cleanup:
```bash
poetry run python manage.py cleanup_inactive_users
```

6) (Optional) Disable maintenance mode:
```bash
poetry run python manage.py maintenance_mode off
```

Notes:
- If your production Poetry setup uses a shell session instead of `poetry run`:
  ```bash
  cd <project_root>
  poetry shell
  python manage.py cleanup_inactive_users --dry-run
  python manage.py cleanup_inactive_users
  exit
  ```
- If Poetry is configured with `virtualenvs.create false`, you can run `python manage.py ...` directly (system Python), but `poetry run` is recommended to ensure the correct environment.

## Safety Notes
- Ownership is determined by `WidgetConfig.user` (related name `widgets`). Users with at least one related `WidgetConfig` are preserved.
- Staff and Superuser accounts are always preserved.
- Use `--dry-run` first in production to verify impact.
- Email sending is disabled by default during deletions via `EMAIL_BACKEND` override. Use `--allow-email` to opt-in (not recommended).

## Implementation Location
- Command: `src/apps/portal/management/commands/cleanup_inactive_users.py`

## Progress
- [x] Implemented
- [ ] Run in staging
- [ ] Run in production (with `--dry-run` first)
- [ ] Schedule periodic maintenance (optional)
