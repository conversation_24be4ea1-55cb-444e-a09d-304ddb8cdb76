# Widget API 404 Error - Root Cause Analysis and Fix

## Problem Summary
After implementing Phase 3-4 of the security enhancements, the widget API started returning 404 errors for all endpoints.

## Root Cause
The 404 error was actually a security measure - when CSRF validation fails, the `WsProtectMixin` raises `Http404` to hide the real reason (security through obscurity).

### Issue Chain:
1. **URL Routing Conflict**: The main widget URLs were matching before API proxy URLs
   - Pattern: `r'^%s/' % WidgetType.slug_url_pattern` was catching UUID widgets
   - This pattern includes `[a-z0-9]{32}` which matches UUIDs
   - Fixed by adding negative lookahead: `r'^%s/(?!api/)'` in main/urls.py

2. **CSRF Validation Failure**: The real issue was CSRF validation
   - Browser requests were missing proper headers
   - Referer header validation was failing
   - The mixin returns 404 to hide security failures

3. **Challenge Token Issue**: For development (HTTP) mode
   - crypto.subtle API not available in insecure contexts
   - Development bypass token wasn't being added to axios headers
   - Fixed by adding `axios.defaults.headers.common['X-Challenge-Token'] = 'development-bypass'`

## Fixes Applied

### 1. URL Pattern Fix
```python
# src/apps/widgets/main/urls.py
# Before:
re_path(r'^%s/' % WidgetType.slug_url_pattern, include(widget_urlpatterns)),

# After - exclude API paths:
re_path(r'^%s/(?!api/)' % WidgetType.slug_url_pattern, include(widget_urlpatterns)),
```

### 2. Development Mode Bypass
```javascript
// src/apps/widgets/finder_v2/app/src/composables/useBotProtection.js
if (window.location.protocol === 'http:') {
  console.warn('Running in HTTP mode - skipping challenge verification for development')
  challengeSolved.value = true
  challengeToken.value = 'development-bypass'
  
  // Add development bypass token to axios headers
  axios.defaults.headers.common['X-Challenge-Token'] = 'development-bypass'
  
  return true
}
```

### 3. Server-Side Development Bypass
```python
# src/apps/widgets/api_proxy/views.py
if settings.DEBUG and challenge_token == 'development-bypass':
    self._debug_log("Development mode - bypassing challenge verification")
    # Skip challenge validation in development
```

## Testing the Fix

### Manual Test
```bash
curl -X GET "http://development.local:8000/widget/{uuid}/api/yr" \
  -H "X-CSRF-TOKEN: {token}" \
  -H "X-Challenge-Token: development-bypass" \
  -H "User-Agent: Mozilla/5.0" \
  -H "Referer: http://development.local:8000/widget/{uuid}?config" \
  --cookie "sessionid={session_id}"
```

### Debug Process
```python
# Check if widget exists
from src.apps.widgets.common.models.config import WidgetConfig
widget = WidgetConfig.objects.get(uuid='...')
print(f'Widget type: {widget.type}')

# Check URL resolution
from django.urls import resolve
match = resolve('/widget/{uuid}/api/yr')
print('Resolved to:', match.view_name)
```

## Key Learnings

1. **Security Through Obscurity**: The 404 error was intentional to hide CSRF failures
2. **URL Order Matters**: More specific patterns must come before general ones
3. **Development vs Production**: crypto.subtle availability differs between HTTP/HTTPS
4. **Header Requirements**: Widget API requires proper CSRF token, referer, and challenge token

## Status
✅ Fixed - Widget API now works correctly with proper headers and development bypass