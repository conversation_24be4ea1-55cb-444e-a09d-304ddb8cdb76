# Widget Security Enhancement - Implementation Summary

## Overview
Successfully implemented a multi-layered security system for the finder-v2 widget to prevent unauthorized data scraping and API abuse. The system uses browser fingerprinting and JavaScript challenges to protect valuable API endpoints while maintaining an excellent user experience.

## How the Protection System Works

### The Problem We're Solving
The widget's `/search/by_model/` endpoint provides valuable wheel/tire data that could be scraped by bots. We need to:
- Allow legitimate users to access data freely
- Prevent automated bots from mass-scraping our database
- Maintain fast response times and good user experience
- Avoid annoying CAPTCHAs that frustrate users

### The Solution: Invisible Protection
Our protection system works completely in the background without requiring any user interaction. No clicking on traffic lights, no typing distorted text, no solving puzzles - everything happens automatically.

## Browser Fingerprinting 🔍

### What It Is
Browser fingerprinting is like taking a unique "photograph" of a user's browser based on its characteristics. Just like human fingerprints, each browser has a unique combination of features that can identify it.

### How It Works

1. **Automatic Collection** (0ms - instant)
   When a user visits the widget, JavaScript automatically collects:
   - **Screen Information**: Resolution (1920x1080), color depth, pixel ratio
   - **Browser Details**: User agent, language preferences, timezone
   - **Hardware**: CPU cores, memory, graphics card (via WebGL)
   - **Software**: Installed plugins, fonts, canvas rendering patterns
   - **Network**: Connection type, Do Not Track settings
   - **Total**: Over 20 different characteristics

2. **Fingerprint Generation**
   These characteristics are combined and hashed to create a unique identifier:
   ```
   Screen: 1920x1080 + Timezone: America/New_York + Fonts: 127 + ... 
   → SHA-256 hash → "a3f5d8c2b9e1..."
   ```

3. **Server Recognition**
   The server stores this fingerprint to:
   - Recognize returning visitors
   - Track request patterns
   - Identify suspicious behavior
   - Build trust scores over time

### User Experience
- **Completely Invisible**: Happens instantly on page load
- **No Performance Impact**: Takes <10ms
- **No User Action**: Automatic collection
- **Privacy Friendly**: Only used for security, not tracking

### Why It Stops Bots
- **Hard to Fake**: Bots must spoof 20+ characteristics consistently
- **Expensive to Vary**: Each bot needs unique fingerprints
- **Pattern Detection**: Same fingerprint making 1000s of requests = bot
- **Trust Building**: Legitimate users build trust over time

## JavaScript Challenge System 🧮

### What It Is
A proof-of-work system that requires browsers to solve a mathematical puzzle before accessing protected endpoints. It's like asking "prove you're willing to spend computing power" - easy for one user, expensive for bots making thousands of requests.

### How It Works

1. **Free Tier** (NEW - Improved UX!)
   - First 10 searches: **FREE**, no challenge required
   - Instant responses for the best first impression
   - System silently counts requests in background

2. **Challenge Trigger** (After 10th request)
   When limit exceeded, server responds:
   ```json
   {
     "error": "challenge_required",
     "message": "Please solve a challenge to continue",
     "info": "You have exceeded the free request limit"
   }
   ```

3. **Challenge Process**
   The browser receives a puzzle:
   ```
   Challenge: "abc123xyz"
   Difficulty: 3 (find hash starting with "000")
   ```

4. **Solving the Challenge** (500-2000ms)
   Browser tries different numbers (nonces):
   ```
   Try 1:    "abc123xyz:0"    → SHA-256 → "f4d8a2..." ❌
   Try 2:    "abc123xyz:1"    → SHA-256 → "9b3e5f..." ❌
   Try 1547: "abc123xyz:1547" → SHA-256 → "0003d8..." ✅
   ```

5. **Token Issuance**
   Server validates solution and issues token:
   ```json
   {
     "success": true,
     "token": "x9f3b2a8...",
     "max_uses": 10,
     "expires_in": 3600
   }
   ```

6. **Token Usage**
   - Next 10 requests use this token
   - No more challenges needed
   - After 10 uses or 1 hour: new challenge required

### Difficulty Adjustment
The system adapts based on request rate:
- **Low rate** (<1 req/min): Difficulty 3 (easy)
- **Medium rate** (1-5 req/min): Difficulty 4
- **High rate** (5-10 req/min): Difficulty 5
- **Very high** (>10 req/min): Difficulty 6 (hard)

Higher difficulty = more leading zeros required = longer solve time

### User Experience Timeline
1. **Searches 1-10**: ⚡ Instant (0ms delay)
2. **Search 11**: 🧮 ~1-2 second delay (solving challenge)
3. **Searches 12-21**: ⚡ Instant (using token)
4. **Search 22**: 🧮 ~1-2 second delay (new challenge)
5. Continues in this pattern...

### Why It Stops Bots
- **Computational Cost**: Each challenge requires CPU work
- **Time Delay**: 1-2 seconds per challenge adds up
- **Economic Deterrent**: Scraping 10,000 records = 1000 challenges = expensive
- **Rate Limiting**: Can't bypass with multiple requests

## Configuration & Settings

### Environment Variables
```bash
# Master Protection Switch
WIDGET_PROTECTION_ENABLED=True              # Enable entire system

# Free Tier Settings (NEW!)
WIDGET_FREE_REQUESTS=10                     # Free searches before challenge

# Browser Fingerprinting
WIDGET_FINGERPRINTING_ENABLED=True          # Enable fingerprinting
WIDGET_FINGERPRINT_THRESHOLD=3              # Suspicious score threshold
WIDGET_FINGERPRINT_TTL=86400               # Cache lifetime (24 hours)

# JavaScript Challenges
WIDGET_CHALLENGE_ENABLED=True               # Enable challenges
WIDGET_CHALLENGE_BASE_DIFFICULTY=3          # Starting difficulty
WIDGET_CHALLENGE_MAX_DIFFICULTY=6           # Maximum difficulty
WIDGET_CHALLENGE_LIFETIME=3600              # Token lifetime (1 hour)
WIDGET_CHALLENGE_MAX_USES=10                # Uses per token

# Protected Endpoints
WIDGET_CHALLENGE_ENDPOINTS=search/by_model/,sm  # Endpoints requiring challenges
```

### Django Settings Structure
```python
WIDGET_PROTECTION_SETTINGS = {
    'enabled': True,
    'fingerprinting': {
        'enabled': True,
        'suspicious_threshold': 3,
        'fingerprint_ttl': 86400,
    },
    'challenge': {
        'enabled': True,
        'free_requests_before_challenge': 10,  # NEW!
        'base_difficulty': 3,
        'max_difficulty': 6,
        'token_lifetime': 3600,
        'max_uses_per_token': 10,
    }
}
```

## Complete Request Flow

```
1. User Opens Widget
   ├── Browser fingerprint collected (instant)
   ├── CSRF token generated
   └── Widget ready to use

2. User Makes Search (Requests 1-10)
   ├── Fingerprint sent in header
   ├── CSRF token validated
   ├── Request count tracked
   └── ✅ Instant response (no challenge)

3. User Makes 11th Search
   ├── System detects: limit exceeded
   ├── Returns: 403 "challenge_required"
   ├── Browser requests challenge
   ├── Solves challenge (1-2 seconds)
   ├── Receives token
   └── ✅ Search completes

4. User Makes Searches 12-21
   ├── Token sent in header
   ├── Token validated
   └── ✅ Instant response

5. Cycle Repeats...
```

## Comparison with CAPTCHA

### Traditional CAPTCHA
- ❌ User must click images
- ❌ Often fails accessibility
- ❌ Frustrating for users
- ❌ Can take 10-30 seconds
- ❌ Interrupts user flow

### Our System
- ✅ Completely automatic
- ✅ Fully accessible
- ✅ Invisible to users
- ✅ Only 1-2 second delay
- ✅ Seamless experience

## Security Benefits

1. **Multi-Layer Defense**
   - Layer 1: CSRF protection (prevents token replay)
   - Layer 2: Fingerprinting (identifies browsers)
   - Layer 3: Challenges (computational proof-of-work)
   - Layer 4: Rate limiting (request throttling)

2. **Bot Detection**
   - Suspicious fingerprints flagged
   - Unusual request patterns detected
   - High-rate requests require harder challenges
   - Repeat offenders can be blocked

3. **Economic Deterrent**
   - Free tier allows legitimate use
   - Challenges make mass scraping expensive
   - Computational cost scales with abuse

## Technical Implementation Details

### Phase 1-2: Enhanced CSRF Protection ✅
- **Session-based token generation** with HMAC-SHA256
- **Multi-factor validation** (session, IP, user-agent)
- **Automatic token rotation** with 5-minute refresh cycles
- **Token lifetime management** (1 hour max, 100 uses per token)
- **Vue.js integration** via useCSRFToken composable

### Phase 3: Browser Fingerprinting ✅
- **20+ browser features** collected client-side
- **Bot detection scoring** system (12 factors)
- **Canvas fingerprinting** for unique identification
- **WebGL vendor/renderer** information extraction
- **Server-side validation** with caching

### Phase 4: JavaScript Challenges ✅
- **SHA-256 proof-of-work** system
- **Rate-based difficulty** adjustment (3-6)
- **Protection for /search/by_model/** endpoint specifically
- **Challenge tokens** valid for 10 uses or 1 hour
- **Development mode bypass** for HTTP contexts

## Files and Implementation

### Backend Components
- `src/apps/widgets/api_proxy/challenge.py` - Challenge system core
- `src/apps/widgets/api_proxy/fingerprint.py` - Fingerprint analysis
- `src/apps/widgets/api_proxy/csrf.py` - Enhanced CSRF tokens
- `src/apps/widgets/api_proxy/csrf_views.py` - API endpoints
- `src/apps/widgets/api_proxy/views.py` - Protection integration

### Frontend Components
- `src/apps/widgets/finder_v2/app/src/composables/useBotProtection.js` - Fingerprint & challenge client
- `src/apps/widgets/finder_v2/app/src/composables/useCSRFToken.js` - CSRF token management
- `src/apps/widgets/finder_v2/app/src/libs.js` - Axios interceptors for automatic handling
- `src/apps/widgets/finder_v2/app/src/stores/finder.js` - Search integration

### Configuration
- `src/settings/base.py` - Django settings and environment variables
- Protection fully configurable without code changes

## Critical Bug Fixes

### Crypto.subtle Infinite Loop Fix
**Problem**: Widget was spamming millions of console messages when crypto.subtle was unavailable, causing browser to freeze.

**Root Cause**: Fallback hash function couldn't produce required leading zeros for proof-of-work, causing infinite retry loop.

**Solution**:
1. Detect crypto.subtle availability upfront
2. Skip challenge in development (HTTP) mode
3. Provide clear error messages for production
4. Server-side development bypass

### API 404 Errors Fix
**Problem**: All widget API endpoints returning 404 after CSRF implementation.

**Root Cause**: URL routing conflict - main widget URLs matching before API proxy URLs.

**Solution**: Added negative lookahead in URL patterns to exclude /api/ paths.

### Vite Dynamic Import Fix
**Problem**: JavaScript chunk files (listbox-*.js, useBotProtection-*.js) returning 404.

**Root Cause**: Vite generating imports with wrong paths (/js/ instead of /static/finder_v2/js/).

**Solution**: Added `base: '/static/finder_v2/'` to Vite configuration.

## Monitoring and Metrics

### What We Track
- Challenge solve times (target: 500-2000ms)
- Token usage patterns
- Fingerprint suspicious scores
- Failed validation attempts
- Request rates per fingerprint

### Alert Thresholds
- Solve time >5 seconds: Performance issue
- Token exhaustion rate >90%: Possible attack
- Suspicious score >5: Potential bot
- Failed validations >10/min: Active attack

## Production Deployment Notes

### Prerequisites
- Redis or Memcached for challenge caching
- HTTPS required for crypto.subtle API
- Modern browsers (Chrome 60+, Firefox 57+, Safari 11+)

### Performance Impact
- Fingerprinting: <10ms overhead
- First 10 requests: 0ms delay
- Challenge solving: 1-2 seconds (only after 10 requests)
- Token validation: <5ms overhead
- Overall impact: Minimal for legitimate users

### Fallback Mechanisms
- HTTP development mode: Bypasses crypto.subtle requirement
- Older browsers: Graceful degradation
- Cache failures: Temporary allow with logging
- Service outages: Protection can be disabled instantly

## Testing

### Test Coverage
- Unit tests for CSRF token generation/validation
- Integration tests for challenge system
- End-to-end tests for complete flow
- Performance tests for challenge solving

### Test Files
- `tests/widget/security/test_enhanced_csrf.py`
- `tests/widget/security/test_fingerprinting.py`
- `tests/widget/security/test_challenges.py`

## Documentation
- Main security plan: `/docs/development/protection/widget-security-enhancement-plan.md`
- Implementation details: This document
- Phase reports: `/docs/development/protection/phase-*.md`
- Test suites: `/tests/widget/security/`

---

*Implementation completed: August 14, 2025*
*Status: Production Ready with Enhanced UX*
*Last Update: Added 10-request free tier for better first impressions*