# Enhanced CSRF Widget Integration Guide

## Issue Summary

After implementing Phase 1 of the enhanced CSRF protection, the finder-v2 widget API endpoints started returning 404 errors. This was caused by the enhanced CSRF validation being enabled while the widget JavaScript was still using the legacy User-Agent based tokens.

## Root Cause

1. **Enhanced CSRF Enabled**: `use_enhanced_csrf: True` in settings
2. **Widget Using Legacy Tokens**: The finder-v2 widget JavaScript generates tokens using the old User-Agent algorithm
3. **Validation Mismatch**: Server expects session-based tokens but receives User-Agent based tokens
4. **Result**: CSRF validation fails → 404 error (security measure to hide API endpoints)

## Temporary Solution

Disabled enhanced CSRF in `src/settings/dev_docker.py`:
```python
WIDGET_CSRF_SETTINGS = {
    'use_enhanced_csrf': False,  # TEMPORARILY DISABLED
    # ... other settings remain
}
```

## Permanent Solution Required

To fully enable enhanced CSRF protection, the finder-v2 widget needs to be updated:

### 1. Widget Initialization
The widget needs to request an initial session-based token when loading:

```javascript
// In finder-v2 widget initialization
async function initializeWidget() {
    // Get initial CSRF token from server
    const response = await fetch(`/widget/${widgetUuid}/api/init-token/`, {
        credentials: 'include',  // Include cookies for session
        headers: {
            'X-Widget-UUID': widgetUuid
        }
    });
    
    const data = await response.json();
    window.FinderV2Config.csrfToken = data.token;
    
    // Use the enhanced CSRF composable
    const { csrfToken, refreshToken } = useCSRFToken();
}
```

### 2. API Request Headers
Update all API requests to use the session-based token:

```javascript
// Instead of generating token from User-Agent
const oldToken = generateTokenFromUserAgent();

// Use the session-based token
const enhancedToken = window.FinderV2Config.csrfToken;

// Add to request headers
headers['X-CSRF-TOKEN'] = enhancedToken;
```

### 3. Token Refresh
Implement automatic token refresh as defined in the Vue composable:

```javascript
// Already implemented in src/apps/widgets/finder_v2/app/src/composables/useCSRFToken.js
// Just needs to be integrated into the widget
```

## Migration Path

### Phase 1: Preparation (Completed)
- ✅ Enhanced CSRF implementation complete
- ✅ Backend support ready
- ✅ Vue composable created
- ✅ Widget configuration updated

### Phase 2: Widget Update (Completed - 2025-08-14)
- ✅ Update widget initialization to use useCSRFToken composable
- ✅ Integrate enhanced CSRF token management in FinderV2Widget.vue
- ✅ Add enhancedCSRF flag to widget configuration template
- ✅ Update main.js to handle legacy vs enhanced tokens
- ✅ Test with enhanced CSRF enabled locally

### Phase 3: Gradual Rollout (In Progress)
- ✅ Enable enhanced CSRF in development
- ✅ Verify API endpoints reject invalid tokens (404 response)
- ✅ Test all widget functionality with real session tokens
- [ ] Deploy to staging with feature flag
- [ ] Monitor for issues
- [ ] Enable in production

### Phase 4: Browser Fingerprinting (Completed - 2025-08-14)
- ✅ Implement BrowserFingerprint class for server-side validation
- ✅ Add client-side feature collection in useBotProtection composable
- ✅ Create human verification endpoint with challenge support
- ✅ Integrate fingerprinting into widget initialization
- ✅ Configure CORS headers for cross-origin fingerprint submission

## Testing Checklist

### With Legacy CSRF (Completed)
- ✅ Widget loads successfully
- ✅ API endpoints respond with 200
- ✅ Cross-domain requests work
- ✅ Token validation passes

### With Enhanced CSRF (Completed - 2025-08-14)
- ✅ Widget requests initial token on load
- ✅ Session cookie is set
- ✅ API requests include session token (verified with curl)
- ✅ Token refresh endpoint working (`/widget/api/refresh-token/`)
- ✅ CORS headers configured for cross-origin access
- ✅ Rate limiting per token functions (100 requests max)

## Configuration Options

### Development Testing
```python
# Enable for testing
WIDGET_CSRF_SETTINGS = {
    'use_enhanced_csrf': True,
    'debug_csrf_validation': True,  # See validation logs
    # ...
}
```

### Production Rollout
```python
# Use feature flag for gradual rollout
WIDGET_CSRF_SETTINGS = {
    'use_enhanced_csrf': FEATURE_FLAGS.get('ENHANCED_CSRF', False),
    'fallback_to_legacy': True,  # Allow legacy tokens as fallback
    # ...
}
```

## Browser Compatibility

The enhanced CSRF system requires:
- Session cookies support (all modern browsers)
- sessionStorage API (IE8+, all modern browsers)
- fetch API or axios (polyfill available for older browsers)

## Security Benefits

Once fully implemented:
1. **Session Binding**: Tokens tied to user sessions
2. **Rotation**: Automatic token refresh reduces exposure
3. **Rate Limiting**: Per-token limits prevent abuse
4. **Better Protection**: Against CSRF attacks and scraping
5. **Audit Trail**: Complete tracking of token usage

## Next Steps

1. **Immediate**: Keep enhanced CSRF disabled until widget is updated
2. **Short-term**: Update finder-v2 widget to support enhanced tokens
3. **Medium-term**: Test thoroughly and enable in development
4. **Long-term**: Roll out to production with monitoring

## References

- Phase 1 Implementation: `/docs/development/protection/phase-1-completion-report.md`
- Security Plan: `/docs/development/protection/widget-security-enhancement-plan.md`
- Vue Composable: `/src/apps/widgets/finder_v2/app/src/composables/useCSRFToken.js`
- CSRF Module: `/src/apps/widgets/api_proxy/csrf.py`

---

**Status**: Widget working with enhanced CSRF and browser fingerprinting enabled
**Phase 1-3 Completed**: Enhanced CSRF, Token Rotation, Browser Fingerprinting
**Next Phase**: JavaScript Challenges and Behavioral Analysis
**Timeline**: Phase 4 implementation scheduled for next sprint