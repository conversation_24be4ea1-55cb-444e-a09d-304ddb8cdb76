# Finder-v2 Translation System Documentation

## Overview

The finder-v2 widget uses a simplified translation system that stores all custom text phrases in a single JSON object within the widget's configuration. This approach provides a clean, maintainable way to customize widget text without requiring separate database models or complex translation management.

## Architecture

### Storage Structure

Translations are stored in the widget's `raw_params` under the path:
```
raw_params → interface → translation
```

### Data Format

The translation object is a simple JSON structure with key-value pairs:
```json
{
  "year_label": "Year",
  "make_label": "Make",
  "model_label": "Model",
  "generation_label": "Generation", 
  "modification_label": "Modification",
  "select_year": "Select Year",
  "select_make": "Select Make",
  "select_model": "Select Model",
  "select_generation": "Select Generation",
  "select_modification": "Select Modification",
  "loading": "Loading...",
  "loading_results": "Loading results...",
  "no_results": "No results found. Please try different search criteria.",
  "search_button": "Unlock More Insights at Wheel-Size.com"
}
```

## File Structure

### Backend Files

#### 1. Forms Configuration
**File**: `src/apps/widgets/finder_v2/forms.py`

**Purpose**: Handles the translation field in the admin interface

**Key Components**:
- `translation` field using `WsJsonFormField` for JSON validation
- `_get_default_translation()` method providing default English values
- `_ensure_dict()` helper for proper DefaultJson serialization
- Form validation and data processing

```python
translation = WsJsonFormField(
    label=_('Translations (JSON)'),
    required=False,
    widget=forms.Textarea(attrs={
        'rows': 10,
        'class': 'form-control font-mono text-sm',
        'placeholder': _('Enter JSON translations here'),
        'style': 'resize: vertical; min-height: 200px;'
    }),
    help_text=_('JSON object with translation keys. Leave empty to use defaults.')
)
```

#### 2. Default Configuration
**File**: `src/apps/widgets/finder_v2/default_config/config.py`

**Purpose**: Defines the default translation structure

**Why Important**: The translation object must be included in the default configuration to allow the `DefaultJson` wrapper to properly access saved translation data.

```python
"translation": {
    "year_label": "Year",
    "make_label": "Make",
    # ... all translation keys with English defaults
}
```

### Frontend Files

#### 3. Vue.js Main Component
**File**: `src/apps/widgets/finder_v2/app/src/components/FinderV2Widget.vue`

**Purpose**: Extracts translation from configuration and passes to child components

**Key Implementation**:
```javascript
const translation = computed(() => {
  return config.translation || {
    // fallback defaults
  }
})
```

#### 4. Vehicle Search Component  
**File**: `src/apps/widgets/finder_v2/app/src/components/VehicleSearch.vue`

**Purpose**: Uses translation props for form labels and placeholders

**Key Implementation**:
```vue
<label class="form-label">{{ translation.year_label || 'Year' }}</label>
:placeholder="translation.select_year || 'Select Year'"
```

#### 5. Results Display Component
**File**: `src/apps/widgets/finder_v2/app/src/components/ResultsDisplay.vue`

**Purpose**: Uses translation props for loading messages and button text

**Key Implementation**:
```vue
<span>{{ translation.loading_results || 'Loading results...' }}</span>
<p>{{ translation.no_results || 'No results found...' }}</p>
```

### Template Files

#### 6. Widget Configuration Interface
**File**: `src/templates/widgets/finder_v2/interface.html`

**Purpose**: Provides the admin interface for editing translations

**Features**:
- JSON textarea with syntax highlighting
- Client-side validation with real-time feedback
- "Restore Defaults" button
- Visual error/success indicators

#### 7. Widget Iframe Template
**File**: `src/templates/widgets/finder_v2/iframe/page.html`

**Purpose**: Passes translation data to the Vue.js application

**Key Implementation**:
```javascript
translation: {{ config.params.interface.translation|jsonify|safe|default:"null" }},
```

## Translation Keys Reference

### Form Labels
- `year_label`: Label for year selection field
- `make_label`: Label for make selection field  
- `model_label`: Label for model selection field
- `generation_label`: Label for generation selection field
- `modification_label`: Label for modification selection field

### Placeholder Text
- `select_year`: Placeholder for year dropdown
- `select_make`: Placeholder for make dropdown
- `select_model`: Placeholder for model dropdown
- `select_generation`: Placeholder for generation dropdown
- `select_modification`: Placeholder for modification dropdown

### Status Messages
- `loading`: General loading indicator text
- `loading_results`: Text shown while search results are loading
- `no_results`: Message shown when no results are found

### Interface Elements
- `search_button`: Text for the main action button

## Usage Examples

### Basic Translation Override

To change the widget language to Russian:
```json
{
  "year_label": "Год",
  "make_label": "Марка",
  "model_label": "Модель",
  "select_year": "Выберите год",
  "select_make": "Выберите марку",
  "loading": "Загрузка...",
  "no_results": "Результаты не найдены. Попробуйте изменить критерии поиска."
}
```

### Partial Translation

You can override only specific keys - others will use defaults:
```json
{
  "year_label": "Anno",
  "make_label": "Marca", 
  "select_year": "Seleziona anno"
}
```

### Custom Branding

Customize messaging for specific clients:
```json
{
  "search_button": "Find Your Perfect Wheels",
  "no_results": "No matches found. Contact our experts for help!"
}
```

## Client-Side Validation

The translation textarea includes comprehensive JavaScript validation:

### Real-time Validation
- Validates JSON syntax as user types
- Shows visual feedback with green/red borders
- Displays specific error messages for syntax issues

### Validation Rules
- Must be valid JSON syntax
- Must be an object (not array, string, or null)
- Empty values are allowed (will use defaults)

### Form Submission Protection
- Prevents saving if JSON is invalid
- Shows alert dialog with error description
- Automatically focuses the problematic field

## Data Flow

1. **Configuration**: User enters JSON in admin interface
2. **Validation**: Client-side JavaScript validates syntax
3. **Storage**: Data saved to `raw_params.interface.translation`
4. **Retrieval**: Django template passes data to Vue.js
5. **Application**: Vue components use translation with fallbacks

## Error Handling

### Backend Fallbacks
- Invalid/missing translation data falls back to defaults
- `DefaultJson` wrapper handles missing keys gracefully
- Form validation ensures data integrity

### Frontend Fallbacks  
- Vue components use `||` operators for fallback defaults
- Missing translation keys display English defaults
- Malformed data doesn't break widget functionality

## Best Practices

### Translation Management
1. Always provide fallback text in Vue components
2. Test with empty/partial translation objects
3. Use consistent key naming conventions
4. Document any new translation keys added

### Performance Considerations
1. Translation object is cached in widget configuration
2. No runtime API calls for translation data
3. Vue reactivity ensures efficient updates

### Maintenance
1. Keep default translations in sync between backend and frontend
2. Update documentation when adding new translatable text
3. Test client-side validation with various JSON scenarios

## Migration from Legacy System

The finder-v2 translation system replaces individual translation fields with a unified JSON approach. Benefits include:

- **Simplified Configuration**: Single field instead of multiple inputs
- **Better Internationalization**: Easy to add complete language packs
- **Enhanced Validation**: Real-time JSON syntax checking
- **Improved Maintainability**: Centralized translation management

## Troubleshooting

### Common Issues

**Translation not appearing in widget:**
- Check that translation key exists in default configuration
- Verify JSON syntax is valid
- Ensure Vue.js build has been regenerated

**DefaultJson access errors:**
- Verify translation object exists in `FINDER_V2_DEFAULT_CONFIG`
- Check that `config.params.interface.translation` path is accessible

**Form validation errors:**
- Use browser dev tools to check JavaScript console
- Verify translation textarea has proper `id` attribute
- Check that validation functions are properly initialized

### Debugging Steps

1. Check Django logs for backend errors
2. Inspect browser console for JavaScript errors
3. Verify database content in Django admin
4. Test with simple JSON objects first
5. Rebuild Vue.js application if changes don't appear