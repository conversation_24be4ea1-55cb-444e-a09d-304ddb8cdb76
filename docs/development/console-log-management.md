# Console.log Management Strategy for Finder-v2 Widget

## Overview
This document describes how console.log statements are managed in the finder-v2 widget to maintain development debugging capabilities while optimizing production bundle size.

## Implementation

### 1. Automatic Removal in Production Builds

The Vite configuration (`vite.config.js`) is set up to automatically remove console statements in production builds:

```javascript
export default defineConfig(({ mode }) => ({
  build: {
    terserOptions: {
      compress: {
        // Only remove console.logs in production builds
        drop_console: mode === 'production',
        drop_debugger: true
      }
    }
  }
}))
```

### 2. Build Modes

- **Development build** (`npm run dev`): All console.logs are preserved
- **Production build** (`npm run build`): All console.logs are removed automatically

### 3. Development Logger Utility

A custom logger utility is available at `src/utils/logger.js` that provides development-only logging methods:

```javascript
import { devLog, devWarn, devError, devDebug } from '@/utils/logger.js'

// These only log in development mode
devLog('Debug message')
devWarn('Warning message')
devError('Error message')
devDebug('Detailed debug info')
```

### 4. Using Console.log

You have three options for logging:

#### Option 1: Use regular console.log (Recommended for simplicity)
```javascript
// Just use console.log normally - it will be automatically removed in production
console.log('This message appears in development only')
console.warn('This warning appears in development only')
console.error('This error appears in development only')
```

#### Option 2: Use the logger utility (Recommended for explicit intent)
```javascript
import { devLog } from '@/utils/logger.js'

// Makes it clear this is development-only logging
devLog('Initializing component with config:', config)
```

#### Option 3: Use import.meta.env.DEV check (For conditional logic)
```javascript
if (import.meta.env.DEV) {
  console.log('Development mode active')
  // Additional development-only code
}
```

## Bundle Size Impact

- **With console.logs**: ~298KB (current)
- **Without console.logs**: ~293KB (saves ~5KB)

The savings are modest but worth implementing since it's automatic and requires no code changes.

## Testing

### Verify console.log removal in production:
```bash
# Build for production
npm run build

# Check if console.logs are present (should return nothing)
grep "console.log" dist/js/finder-v2-app.js
```

### Test development mode:
```bash
# Run development server
npm run dev

# Console.logs should appear in browser console
```

## Best Practices

1. **Use descriptive messages**: Since they're removed in production, be verbose in development
   ```javascript
   console.log('🚀 Store initialized with config:', config)
   console.log('📊 API response received:', response.data)
   ```

2. **Use appropriate log levels**:
   - `console.log()` - General information
   - `console.warn()` - Warnings that don't break functionality
   - `console.error()` - Errors and exceptions
   - `console.debug()` - Detailed debugging information

3. **Clean up before committing**: While console.logs are removed in production, avoid committing excessive debugging logs

4. **Use the logger utility for important logs**: If a log message is particularly important for development, use the logger utility to make the intent clear

## Production Considerations

- All console methods are removed: `log`, `warn`, `error`, `debug`, `table`, `time`, `timeEnd`
- `console.error` in catch blocks will also be removed - ensure proper error handling
- The removal happens at build time, so there's zero runtime overhead

## Alternative Configuration

If you want to keep certain console methods in production (e.g., `console.error`), modify `vite.config.js`:

```javascript
terserOptions: {
  compress: {
    // Remove only console.log and console.debug in production
    pure_funcs: mode === 'production' ? ['console.log', 'console.debug'] : [],
    drop_debugger: true
  }
}
```

This provides more granular control over which console methods are removed.