Finder-v2: Paid Subscription Behavior and WS Button Visibility

Overview
- When a widget has an active paid subscription (`subscription.is_paid`), the promotional button linking to Wheel-Size.com must not be shown inside the finder-v2 results view.
- For non‑paid widgets, the button visibility is configurable via the interface config block `interface.blocks.button_to_ws.hide`.

Admin Source of Truth
- Checkbox: subscription-0-is_paid on the widget config admin page.
- URL example: `http://development.local:8000/admin/widgets/widgetconfig/<UUID>/change/`

Frontend Behavior
- Finder-v2 receives the paid flag as a top-level field in the runtime config object: `window.FinderV2Config.subscriptionPaid`.
- Visibility rule:
  - If `subscriptionPaid === true`: the button is always hidden.
  - Else: use `interface.blocks.button_to_ws.hide` (false = show, true = hide).

Implementation Details
- Template that builds runtime config for the iframe:
  - File: `src/templates/widgets/finder_v2/iframe/page.html`
  - Adds `subscriptionPaid` to `window.FinderV2Config` from `config.subscription.is_paid`.
  - Ensures `interface.blocks.button_to_ws.hide` is present in the runtime config, and forces it to `true` for paid widgets.
- Results view logic:
  - File: `src/apps/widgets/finder_v2/app/src/components/ResultsDisplay.vue`
  - Computed values:
    - `subscriptionPaid`: derives from `config.subscriptionPaid` (or fallback under `config.widgetConfig.subscriptionPaid` for local dev).
    - `buttonHideSetting`: reads `interface.blocks.button_to_ws.hide` (fallback to `widgetConfig.blocks` for local dev).
    - `showWheelSizeButton`: returns `false` when `subscriptionPaid` is true; otherwise returns `!buttonHideSetting`.

Config Fields
- Top-level runtime config (iframe):
  - `subscriptionPaid: boolean`
  - `utm: string` (used for WS link tracking)
  - `interface.blocks.button_to_ws.hide: boolean`

Admin Forms and Defaults
- Default configuration stores the value at: `interface.blocks.button_to_ws.hide`.
- In admin UI a checkbox labeled “Show "See on Wheel-Size.com" button” maps to the inverse of the stored flag (show vs hide).
- For paid subscriptions the runtime enforces hide, regardless of the form value.

Testing Steps
1) Set a widget to paid in admin (subscription-0-is_paid = checked).
2) Open the widget iframe page: `http://development.local:8000/widget/finder-v2/?config`.
3) Perform a search; verify the button is not rendered.
4) Uncheck is_paid, save. In interface, set “Show button” on or off and verify the button shows/hides accordingly for non‑paid widgets.

Notes
- The button label can be customized via translations key `search_button`.
- GA/analytics context includes `subscription_paid` when available for event enrichment.
- In local dev playground (`app/index.html`) a nested `widgetConfig` is still supported; the implementation reads both top-level and `widgetConfig.*` for compatibility.






