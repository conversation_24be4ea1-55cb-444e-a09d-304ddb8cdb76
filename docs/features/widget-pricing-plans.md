# Widget Pricing Plans: Free vs Paid Versions
Last Modified: 2025-08-15 16:00 UTC+6

## Overview
Wheel-Size Services widgets are offered under two pricing tiers:

- **Free**: Requires visible attribution and includes Wheel-Size.com branding in the widget output. Intended for public, search-accessible pages.
- **Paid**: Removes all attribution/branding requirements, unlocks additional visual themes (including car imagery), and allows outbound links inside Custom Output Templates.

This document defines the requirements, benefits, enforcement, and developer implementation guidelines for both tiers.

## Free Version Requirements

### Required Attribution Link
- The host page embedding the widget must include a visible HTML link positioned close to the widget:

```html
<a title="Wheel fitment and tire size guide and knowledge base" href="https://www.wheel-size.com">Wheel-Size.com</a>
```

### HTML Example (Recommended Placement)
```html
<!-- Finder‑v2 embed container -->
<div id="ws-finder-v2-widget"></div>

<!-- Required attribution: place immediately above or below the widget container on the host page -->
<div class="ws-attribution" style="font-size:14px; line-height:20px; margin-top:8px;">
  <a title="Wheel fitment and tire size guide and knowledge base" href="https://www.wheel-size.com">Wheel-Size.com</a>
  <!-- Do NOT add rel="nofollow" and do NOT hide this element -->
  <!-- Keep this in the host DOM, not inside the iframe, to ensure crawler visibility -->
  <!-- Adjust spacing via CSS, but keep the link clearly visible near the widget -->
  <!-- Example minimal CSS (optional): .ws-attribution a { color: #0f62fe; text-decoration: underline; } -->
  
  <!-- Optional: Attribution can be placed above the widget instead of below, as long as it remains close and visible. -->
  <!-- Note: Avoid placing the attribution only in a distant footer or behind tabs/accordions. -->
  
  <!-- The attribution link text must remain "Wheel-Size.com" as shown. -->
  <!-- The title attribute must remain exactly as specified above. -->
  <!-- The href must remain exactly https://www.wheel-size.com -->
  <!-- The link must be crawlable and indexable. -->
  <!-- The link must not be password-protected or hidden. -->
</div>
```

### Technical Requirements
- **Visibility and size**:
  - Minimum font size: **14px** (16px recommended for accessibility).
  - The link must be visible on page load; no hidden, off-screen, or overlayed elements; no `display:none`, `visibility:hidden`, or zero-size containers.
- **Accessibility**:
  - Must be keyboard focusable and announced by screen readers.
  - Ensure sufficient color contrast (WCAG 2.1 AA). Underline or clear visual affordance is recommended.
- **Crawler access**:
  - The link must be accessible to search engine crawlers (place it in the host page DOM near the widget, not only inside the iframe).
  - Do not block via `robots` meta tags or HTTP headers.
  - The link must not include `rel="nofollow"` (also avoid `ugc` or `sponsored`).
- **Positioning**:
  - Place the link immediately **above or below** the widget container, in the same section. Do not bury it in footers or separate pages.

### Usage Restrictions
- Pages using the free widget must be publicly accessible. **No password-protected** or intranet-only areas are permitted for the free tier.
- The free tier **does not allow links** in the Custom Output Template (see Implementation Guidelines for enforcement).

### Visual Indicator (Widget Output Branding)
- The free version displays a small button that links to `wheel-size.com` in the widget results view.
- Behavior details and the controlling flags are documented in `docs/features/finder-v2-paid-subscription-behavior.md`.

## Paid Version Benefits
- **No attribution required**: The external attribution link and any in-widget branding requirements are removed.
- **Brand-free widget output**: The small button linking to Wheel-Size.com is hidden and cannot be forced on for paid subscriptions.
- **Additional themes for output**: Includes extended visual themes and the ability to use car images to enhance presentation.
- **Custom Output Template links allowed**: The paid tier permits outbound links inside the Custom Output Template (e.g., to the client’s website, product pages, and wheel fitment content).

## Terms of Service (Enforcement and Penalties)
- **Compliance**:
  - Free-tier users must maintain the required attribution link and keep pages publicly accessible.
  - Free-tier users must not include outbound links in Custom Output Templates.
  - Paid-tier users may remove attribution and use outbound links in templates.
- **Detection**:
  - Automated and manual reviews may verify attribution presence, visibility, and crawler accessibility.
  - System-level checks may enforce in-widget button visibility for non‑paid widgets.
- **Violations** (examples):
  - Missing/hidden attribution on free widgets.
  - Using `rel="nofollow"` (or `ugc`/`sponsored`) on the required link.
  - Hiding the link via CSS or placing it far from the widget.
  - Using links inside Custom Output Templates on free tier.
  - Embedding the free widget on password-protected pages.
- **Penalties** (progressive):
  1) Notice and remediation window.
  2) Automatic restoration of in-widget branding and/or temporary widget disablement.
  3) Suspension of domain(s) and/or API access until compliance or upgrade.
  4) Repeated violations may result in permanent bans.

## Implementation Guidelines (For Developers)

### 1) Configuration and Runtime Flags
- Back end stores the subscription status on the widget configuration (admin checkbox `subscription.is_paid`).
- The iframe page injects the paid flag into the runtime config as `window.FinderV2Config.subscriptionPaid`.
- Front end consumes the flag to control both attribution-related UI and template capabilities.

References:
- Admin source of truth and runtime wiring: `docs/features/finder-v2-paid-subscription-behavior.md`.

### 2) In-Widget Button Visibility (Free vs Paid)
- Logic (summarized):
  - If `subscriptionPaid === true`: do not render the small button linking to Wheel-Size.com.
  - Else: follow `interface.blocks.button_to_ws.hide` (false = show, true = hide), ensuring defaults favor showing in free tier.

### 3) Custom Output Template Link Policy
- Enforcement requirement:
  - **Free tier**: disallow outbound links (`<a ...>`) in the Custom Output Template.
  - **Paid tier**: allow outbound links to client resources (respect target/rel best practices, excluding `nofollow` requirement which applies only to the free attribution link).

Suggested safeguards:
- Server-side form validation (Django): when `subscription.is_paid` is false, reject templates containing `<a` tags or strip them during save.
- Front-end rendering guard (Vue): when `subscriptionPaid` is false, sanitize rendered HTML to remove/neutralize anchors (convert `<a>` to `<span>`).

### 4) Attribution Link (Host Page) Guidance
- Documentation and examples for integrators must include the exact required link and placement guidance (see above HTML example).
- Stress that the attribution must be in the host DOM (outside the iframe) to ensure crawler access.

### 5) QA and Analytics
- QA checklist for free widgets:
  - Attribution link present, visible, ≥14px, keyboard focusable, near the widget, and crawlable.
  - In-widget small button visible for non‑paid widgets and hidden for paid widgets.
  - Custom Output Template contains no `<a>` tags (free tier) or contains allowed links (paid tier).
- Analytics: include a `subscription_paid` flag in relevant widget events for observability and audits.

### 6) Security & Accessibility Notes
- Continue to follow XSS-safe rendering in the template engine and sanitize user-provided HTML.
- Maintain accessible names and roles; verify focus order around the attribution link.
- Ensure any additional paid themes (including images of cars) meet performance and accessibility standards (alt text, responsive loading).

---

If you have questions about enforcement details or need to onboard a new domain, contact the Wheel-Size Services team.


