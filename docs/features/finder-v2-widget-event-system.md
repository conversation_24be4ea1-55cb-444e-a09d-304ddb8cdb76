### Finder‑v2 Widget Event System (Vue 3)

A modern event system for the Finder‑v2 widget that preserves full compatibility with the legacy WheelSizeWidgets JavaScript API while leveraging Vue 3 Composition API patterns.

- Implemented via composable: `src/apps/widgets/finder_v2/app/src/composables/useWidgetEvents.js`
- Integrated in store: `src/apps/widgets/finder_v2/app/src/stores/finder.js`
- Bootstrapped in app entry: `src/apps/widgets/finder_v2/app/src/main.js`
- Deployed using: `./deploy-finder-v2.sh`

#### Goals
- Maintain backward compatibility with `WheelSizeWidgets.create(...).on('event', ...)` used by client sites.
- Provide structured payloads for lifecycle and interaction events.
- Safe cross‑origin postMessage behavior and robust error handling.
- Include context with every event: config, selections, widget meta.

#### Transport & Envelope
Events are sent with the legacy envelope to parent pages via `window.parent.postMessage`:

```json
{
  "src": "<child iframe url>",
  "type": "<event type>",
  "data": {
    "context": {
      "config": {},
      "widgetUuid": "<uuid>",
      "widgetType": "finder-v2",
      "flowType": "primary|alternative|year_select",
      "selections": {
        "year": "<slug>",
        "make": "<slug>",
        "model": "<slug>",
        "generation": "<slug>",
        "modification": "<slug>"
      }
    }
  }
}
```

Notes
- Parent pages keep using the legacy `WheelSizeWidgets` host script which routes messages back to widget instances and triggers `widget.on(type, cb)`.
- Target origin is `'*'` for compatibility; production origin acceptance is handled by the host script.

#### Event Types
- ready:document — emitted when DOM is ready.
- ready:window — emitted once after window load AND initial data load complete.
- change:year|make|model|generation|modification — payload: `{ value: { slug, title } }`.
- search:start — before `searchByVehicle()`; payload includes selections.
- search:complete — after results load; includes counts and timings.
- search:error — with `error_message`.
- results:display — after DOM renders results.

Value object contract for change events:

```ts
interface SelectionValue { slug: string; title: string }
```

#### results:display Event Payload (Important)
- **When fired**: after search results are rendered in the widget’s DOM (post‑render), and after we hint the parent iframe to resize.
- **Payload**:

```json
{
  "type": "results:display",
  "data": {
    "results_count": 3,
    "context": {
      "config": { /* widget config */ },
      "widgetUuid": "...",
      "widgetType": "finder-v2",
      "flowType": "primary|alternative|year_select",
      "selections": {
        "year": "<slug>",
        "make": "<slug>",
        "model": "<slug>",
        "generation": "<slug>",
        "modification": "<slug>"
      }
    }
  }
}
```

Notes
- This event does NOT include full results data to keep messages light; results are rendered inside the widget. Parent pages can use `results_count` and `context` (config, flowType, selections, etc.) for analytics and orchestration.

Example parent listener
```js
widget.on('results:display', function(e) {
  console.log('Results displayed. Count:', e.data.results_count)
  console.log('Selections:', e.data.context.selections)
})
```

#### Integration Points
- `useWidgetEvents` composable provides: `emit`, `setConfig`, `setContextProvider`, `markInitialDataLoaded`, `onBeforeSend`, `onAfterSend`.
- `finder.js` wires selection watchers and search lifecycle to dispatch events and supplies context.
- `main.js` ensures the composable is instantiated so readiness events are guaranteed.

#### Output Template Data Model (Used by Results Rendering)
Each rendered item (passed to the template engine) has the following fields (subset shown, based on Finder API v2 `search_by_model` response):
- **make**: `{ slug, name }`
- **model**: `{ slug, name }`
- **generation**: `{ slug, name, start, end, platform, bodies[] }`
- **start_year**, **end_year**: numeric
- **name** / **trim** / **body**: modification/trim descriptors (if provided)
- **engine**: `{ fuel, capacity, type, power: { hp, PS, kW }, code }`
- **regions**: `string[]`
- **technical**: key specs (e.g., `bolt_pattern`, `stud_holes`, `pcd`, `centre_bore`, `wheel_fasteners`, …)
- **wheels**: array of fitment options
  - flags: `is_stock`, `is_recommended_for_winter`, `is_runflat_tires`, `is_extra_load_tires`, `is_pressed_steel_rims`, `showing_fp_only`
  - **front** and **rear** blocks with fields such as `rim`, `rim_diameter`, `rim_width`, `rim_offset`, `tire`, `tire_full`, and basic pressure metrics

Template engine capabilities
- **variables**: `{{ path.to.value }}`
- **loops**: `{% for item in array %}...{% endfor %}`
- **conditionals**: `{% if condition %}...{% else %}...{% endif %}`
- **simple ternary** inside variables: `{{ condition ? 'A' : 'B' }}`

Common variable examples
- `{{ make.name }} {{ model.name }} ({{ start_year }}–{{ end_year }})`
- `{{ generation.name }}` (if present)
- `{{ engine.power.hp }}` (if present)

Loop example with conditions
```html
{% for wheel in wheels %}
  {{ wheel.is_stock ? 'OE' : 'AM' }}:
  Front {{ wheel.front.tire }} – {{ wheel.front.rim }}
  {% if wheel.rear.tire %}
    • Rear {{ wheel.rear.tire }} – {{ wheel.rear.rim }}
  {% endif %}
{% endfor %}
```

Full sample template
```html
<div class="ws-result">
  <h3>{{ make.name }} {{ model.name }} ({{ start_year }}–{{ end_year }})</h3>
  {% if generation.name %}
    <div>Generation: {{ generation.name }}</div>
  {% endif %}
  {% if engine.type %}
    <div>Engine: {{ engine.type }} {{ engine.capacity }}L • {{ engine.power.hp }} hp</div>
  {% endif %}
  <div class="ws-fitments">
    {% for wheel in wheels %}
      <div class="ws-fitment">
        <strong>{{ wheel.is_stock ? 'OE' : 'AM' }}</strong>
        <div>Front: {{ wheel.front.tire }} – {{ wheel.front.rim }}</div>
        {% if wheel.rear.tire %}
          <div>Rear: {{ wheel.rear.tire }} – {{ wheel.rear.rim }}</div>
        {% endif %}
      </div>
    {% endfor %}
  </div>
</div>
```

#### Parent Page Usage

```html
<script src="//services.wheel-size.com/static/widget/code/local/ws-widget.js"></script>
<script>
  var widget = WheelSizeWidgets.create('#ws-widget', { uuid: 'your-uuid', type: 'finder-v2' });
  widget.on('ready:window', console.log);
  widget.on('change:make', (e) => console.log('Make', e.data.value));
  widget.on('search:start', console.log);
  widget.on('search:complete', console.log);
  widget.on('search:error', console.error);
  widget.on('results:display', console.log);
</script>
```

#### Testing & Deployment
- Preview: `http://development.local:8000/widget/finder-v2/?config`
- Build & deploy in dev: `./deploy-finder-v2.sh`

#### Error Handling
- `postMessage` wrapped with try/catch; `ready:window` gated; payloads JSON‑serializable.

#### Change Log
- Added `useWidgetEvents` and integrated events in `finder.js` and `main.js`.


