# Google Analytics 4 Widget Analytics Setup Guide

Last Modified: 2025-08-04 20:30 UTC+6

This comprehensive guide covers the complete setup and configuration of Google Analytics 4 (GA4) for the finder-v2 widget, including measurement ID configuration, custom dimensions setup, and detailed reporting configurations.

## Table of Contents

1. [GA4 Measurement ID Configuration](#ga4-measurement-id-configuration)
2. [GA4 Property Setup](#ga4-property-setup)
3. [Custom Dimensions Configuration](#custom-dimensions-configuration)
4. [Custom Reports Configuration](#custom-reports-configuration)
5. [Real-Time Monitoring Setup](#real-time-monitoring-setup)
6. [Advanced Analytics Queries](#advanced-analytics-queries)
7. [Automated Alerts Configuration](#automated-alerts-configuration)
8. [Performance Monitoring Dashboards](#performance-monitoring-dashboards)
9. [Troubleshooting Guide](#troubleshooting-guide)

## GA4 Measurement ID Configuration

The GA4 measurement ID can be configured in multiple ways with a priority hierarchy system.

### Configuration Priority Order

1. **Widget Configuration Object** (Highest Priority)
2. **Direct GA4 Property** (Second Priority)  
3. **Global FinderV2Config** (Third Priority)
4. **Default Value** (Fallback)

### Method 1: Widget Configuration Object (Recommended)

```javascript
// When creating a widget instance
const widgetConfig = {
  uuid: 'widget-123',
  analytics: {
    measurementId: 'G-YOUR-ACTUAL-ID',
    debugMode: false,
    enabled: true
  },
  // ... other widget configuration
}
```

### Method 2: Direct GA4 Property

```javascript
const widgetConfig = {
  uuid: 'widget-123',
  ga4MeasurementId: 'G-YOUR-ACTUAL-ID',
  // ... other widget configuration
}
```

### Method 3: Global FinderV2Config

```javascript
window.FinderV2Config = {
  analytics: {
    measurementId: 'G-YOUR-ACTUAL-ID',
    debugMode: false
  },
  // ... other global configuration
}
```

### Method 4: Update Default Value (For Global Deployment)

Edit the file: `src/apps/widgets/finder_v2/app/src/composables/useAnalytics.js`

```javascript
// Line 20: Change from
const measurementId = ref('G-XXXXXXXXXX') // To be configured with actual GA4 property ID

// To your actual GA4 measurement ID:
const measurementId = ref('G-YOUR-ACTUAL-ID') // Your actual GA4 property ID
```

### Environment-Specific Configuration

```javascript
// Development Environment
const widgetConfig = {
  analytics: {
    measurementId: 'G-DEV-MEASUREMENT-ID',
    debugMode: true,
    enabled: true
  }
}

// Production Environment
const widgetConfig = {
  analytics: {
    measurementId: 'G-PROD-MEASUREMENT-ID',
    debugMode: false,
    enabled: true
  }
}

// Staging Environment
const widgetConfig = {
  analytics: {
    measurementId: 'G-STAGING-MEASUREMENT-ID',
    debugMode: false,
    enabled: true
  }
}
```

## GA4 Property Setup

### Step 1: Create GA4 Property

1. **Go to Google Analytics 4**
2. **Click "Admin" → "Create Property"**
3. **Configure Property Settings:**

```yaml
Property Name: "WheelSize Finder-v2 Widget Analytics"
Reporting Time Zone: Your preferred timezone
Currency: Your preferred currency
Industry Category: "Technology"
Business Size: Select appropriate size
```

### Step 2: Create Data Stream

1. **Go to Property → Data Streams → Add Stream → Web**
2. **Configure Web Stream:**

```yaml
Website URL: https://services.wheel-size.com
Stream Name: "Finder-v2 Widget Stream"
Enhanced Measurement: Enable (recommended)
```

### Step 3: Enhanced Measurement Configuration

Enable these enhanced measurement events:

```yaml
✅ Page views
✅ Scrolls  
✅ Outbound clicks
✅ Site search
✅ Video engagement
✅ File downloads
✅ Form interactions
```

## Custom Dimensions Configuration

### Required Custom Dimensions

Create these custom dimensions in your GA4 property:

**Go to: Configure → Custom Definitions → Custom Dimensions → Create Custom Dimensions**

#### Core Widget Dimensions

```yaml
# Dimension 1: Widget Instance ID
Dimension Name: "Widget UUID"
Scope: Event
Description: "Unique identifier for each widget instance"
Event Parameter: "widget_uuid"

# Dimension 2: Widget Type
Dimension Name: "Widget Type" 
Scope: Event
Description: "Type of widget (finder-v2)"
Event Parameter: "widget_type"

# Dimension 3: Client Website
Dimension Name: "Client Hostname"
Scope: Event  
Description: "Hostname of the client website where widget is embedded"
Event Parameter: "client_hostname"

# Dimension 4: Search Flow Type
Dimension Name: "Search Flow Type"
Scope: Event
Description: "Type of search flow (primary, secondary, year_select)"
Event Parameter: "flow_type"

# Dimension 5: API Version
Dimension Name: "API Version"
Scope: Event
Description: "API version used by the widget"
Event Parameter: "api_version"
```

#### User Interaction Dimensions

```yaml
# Dimension 6: Interaction Type
Dimension Name: "Interaction Type"
Scope: Event
Description: "Type of user interaction (year_selected, make_selected, etc.)"
Event Parameter: "interaction_type"

# Dimension 7: Search Type
Dimension Name: "Search Type" 
Scope: Event
Description: "Type of search performed (search_complete, search_initiate)"
Event Parameter: "search_type"

# Dimension 8: Error Type
Dimension Name: "Error Type"
Scope: Event
Description: "Category of errors encountered"
Event Parameter: "error_type"
```

#### Advanced Dimensions

```yaml
# Dimension 9: Theme Name
Dimension Name: "Widget Theme"
Scope: Event
Description: "Visual theme applied to the widget"
Event Parameter: "theme_name"

# Dimension 10: User Profile UUID
Dimension Name: "User Profile UUID"
Scope: User
Description: "User profile identifier (when available)"
Event Parameter: "user_profile_uuid"

# Dimension 11: Search Results Count
Dimension Name: "Results Count"
Scope: Event
Description: "Number of search results returned"
Event Parameter: "results_count"

# Dimension 12: Flow Step
Dimension Name: "Flow Step"
Scope: Event
Description: "Current step in the search flow (1-5)"
Event Parameter: "flow_step"
```

### Custom Metrics Configuration

Create these custom metrics for performance tracking:

```yaml
# Metric 1: Search Completion Time
Metric Name: "Search Duration"
Scope: Event
Description: "Time taken to complete search in milliseconds"
Event Parameter: "search_completion_time"
Unit: Milliseconds

# Metric 2: API Response Time
Metric Name: "API Response Time"
Scope: Event  
Description: "API response time in milliseconds"
Event Parameter: "api_response_time"
Unit: Milliseconds

# Metric 3: Session Duration
Metric Name: "Widget Session Duration"
Scope: Event
Description: "Duration of widget session in milliseconds"
Event Parameter: "session_duration"
Unit: Milliseconds
```

## Custom Reports Configuration

### Report 1: Real-Time Widget Activity Dashboard

**Navigation: Reports → Library → Create Report → Detail Report**

```yaml
Report Name: "Widget Real-Time Activity"
Report Type: Detail Report

Dimensions:
  Primary: Event name
  Secondary: Client hostname (Custom)
  Additional: Widget UUID (Custom), Widget type (Custom)

Metrics:
  - Event count
  - Active users  
  - Events per user
  - Average engagement time

Filters:
  - Event name contains "widget_"
  - Widget type exactly matches "finder-v2"

Date Range: Last 30 minutes
Refresh Rate: 1 minute
```

### Report 2: Client Website Usage Analysis

```yaml
Report Name: "Widget Usage by Client Website"
Report Type: Detail Report

Dimensions:
  Primary: Client hostname (Custom)
  Secondary: Widget UUID (Custom)
  Additional: Search flow type (Custom), Widget theme (Custom)

Metrics:
  - Users
  - Sessions
  - Event count
  - Bounce rate
  - Average session duration
  - Events per session

Filters:
  - Event name begins with "widget_"
  - Client hostname does not equal "development.local"

Date Range: Last 7 days
Comparison: Previous period
```

### Report 3: User Interaction Funnel Analysis

```yaml
Report Name: "Widget Interaction Funnel"
Report Type: Funnel Exploration

Funnel Steps:
  Step 1: widget_load (Widget Loaded)
  Step 2: widget_ready (Widget Ready)  
  Step 3: widget_interaction (User Interaction)
  Step 4: search_initiate (Search Started)
  Step 5: search_complete (Search Completed)

Breakdown Dimensions:
  - Client hostname (Custom)
  - Search flow type (Custom)
  - Device category

Date Range: Last 30 days
```

### Report 4: Performance Metrics Dashboard

```yaml
Report Name: "Widget Performance Metrics"
Report Type: Detail Report

Dimensions:
  Primary: Client hostname (Custom)
  Secondary: API version (Custom)
  Additional: Search type (Custom)

Metrics:
  - Event count
  - Search Duration (Custom Metric)
  - API Response Time (Custom Metric)
  - Results Count (Custom Dimension as Metric)

Calculated Metrics:
  - Average Search Success Rate: search_complete / search_initiate * 100
  - Error Rate: widget_error / total_events * 100
  - User Engagement Score: events_per_session * average_engagement_time

Filters:
  - Event name in (widget_search, search_complete, search_initiate, widget_error)

Date Range: Last 7 days
```

### Report 5: Error Analysis Dashboard

```yaml
Report Name: "Widget Error Analysis"
Report Type: Detail Report

Dimensions:
  Primary: Error type (Custom)
  Secondary: Client hostname (Custom)
  Additional: API version (Custom), Widget UUID (Custom)

Metrics:
  - Event count
  - Affected users
  - Sessions with errors

Filters:
  - Event name equals "widget_error"

Date Range: Last 24 hours
Sort: Event count (Descending)
```

## Real-Time Monitoring Setup

### Real-Time Report Customization

**Go to: Reports → Realtime → Customize**

#### Card 1: Active Widget Sessions

```yaml
Card Name: "Active Widget Sessions"
Primary Dimension: Event name
Filter: Event name contains "widget_"
Metric: Active users (last 30 minutes)
```

#### Card 2: Client Websites with Active Widgets

```yaml
Card Name: "Active Client Websites"  
Primary Dimension: Client hostname (Custom)
Filter: Event name equals "widget_load"
Metric: Active users (last 30 minutes)
Sort: Active users (Descending)
```

#### Card 3: Current Search Activity

```yaml
Card Name: "Live Search Activity"
Primary Dimension: Search type (Custom)  
Filter: Event name equals "widget_search"
Metric: Event count (last 5 minutes)
```

#### Card 4: Real-Time Errors

```yaml
Card Name: "Live Error Tracking"
Primary Dimension: Error type (Custom)
Filter: Event name equals "widget_error"  
Metric: Event count (last 30 minutes)
Alert Threshold: > 5 events per 30 minutes
```

### Advanced Real-Time Exploration

**Go to: Explore → Create Exploration → Free Form**

```yaml
Exploration Name: "Widget Performance Real-Time Dashboard"

Variables:
  Dimensions:
    - Event name
    - Client hostname (Custom)  
    - Widget UUID (Custom)
    - Interaction type (Custom)
    - Hour
    - Minute
    - Device category
    - Country

  Metrics:
    - Event count
    - Active users
    - Events per user
    - Average engagement time
    - Search Duration (Custom)
    - API Response Time (Custom)

Filters:
  - Event name matches regex: "widget_.*"
  - Widget type = "finder-v2"

Visualizations:
  1. Time Series Chart: Event count by minute (last 2 hours)
  2. Bar Chart: Active widgets by client hostname  
  3. Pie Chart: Interaction types distribution
  4. Geographic Map: Widget usage by country
  5. Data Table: Top performing widgets by engagement
```

## Advanced Analytics Queries

### BigQuery Integration (If Enabled)

#### Query 1: Real-Time Widget Events

```sql
-- Real-time widget activity (last hour)
SELECT
  event_name,
  event_timestamp,
  user_pseudo_id,
  (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'client_hostname') as client_website,
  (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'widget_uuid') as widget_id,
  (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'interaction_type') as interaction,
  (SELECT value.int_value FROM UNNEST(event_params) WHERE key = 'search_completion_time') as search_time_ms,
  device.category as device_type,
  geo.country
FROM `your-project.analytics_XXXXX.events_*`
WHERE event_name LIKE 'widget_%'
  AND event_timestamp >= UNIX_MICROS(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR))
  AND _TABLE_SUFFIX = FORMAT_DATE('%Y%m%d', CURRENT_DATE())
ORDER BY event_timestamp DESC
LIMIT 1000
```

#### Query 2: Widget Performance Analysis

```sql
-- Widget performance metrics by client website
SELECT
  (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'client_hostname') as client_website,
  COUNT(CASE WHEN event_name = 'widget_load' THEN 1 END) as widget_loads,
  COUNT(CASE WHEN event_name = 'search_initiate' THEN 1 END) as searches_started,
  COUNT(CASE WHEN event_name = 'search_complete' THEN 1 END) as searches_completed,
  COUNT(CASE WHEN event_name = 'widget_error' THEN 1 END) as errors,
  AVG(CASE 
    WHEN event_name = 'search_complete' 
    THEN (SELECT value.int_value FROM UNNEST(event_params) WHERE key = 'search_completion_time')
  END) as avg_search_time_ms,
  SAFE_DIVIDE(
    COUNT(CASE WHEN event_name = 'search_complete' THEN 1 END),
    COUNT(CASE WHEN event_name = 'search_initiate' THEN 1 END)
  ) * 100 as search_success_rate
FROM `your-project.analytics_XXXXX.events_*`
WHERE event_name IN ('widget_load', 'search_initiate', 'search_complete', 'widget_error')
  AND _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY))
  AND FORMAT_DATE('%Y%m%d', CURRENT_DATE())
GROUP BY client_website
HAVING widget_loads > 10
ORDER BY searches_completed DESC
```

#### Query 3: User Journey Analysis

```sql
-- User interaction flow analysis
WITH user_sessions AS (
  SELECT
    user_pseudo_id,
    (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'widget_uuid') as widget_id,
    event_name,
    event_timestamp,
    (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'interaction_type') as interaction_type,
    ROW_NUMBER() OVER (
      PARTITION BY user_pseudo_id, 
      (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'widget_uuid')
      ORDER BY event_timestamp
    ) as step_number
  FROM `your-project.analytics_XXXXX.events_*`
  WHERE event_name LIKE 'widget_%'
    AND _TABLE_SUFFIX = FORMAT_DATE('%Y%m%d', CURRENT_DATE())
)
SELECT
  step_number,
  event_name,
  interaction_type,
  COUNT(*) as event_count,
  COUNT(DISTINCT user_pseudo_id) as unique_users
FROM user_sessions
WHERE step_number <= 10
GROUP BY step_number, event_name, interaction_type
ORDER BY step_number, event_count DESC
```

### GA4 Data API Queries

#### Python Script for Custom Reporting

```python
# Google Analytics Data API v1 - Widget Analytics
from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.analytics.data_v1beta.types import (
    DateRange,
    Dimension,
    Metric,
    RunReportRequest,
    Filter,
    FilterExpression,
)

def get_widget_analytics_report(property_id, start_date, end_date):
    """Get comprehensive widget analytics report"""
    
    client = BetaAnalyticsDataClient()
    
    request = RunReportRequest(
        property=f"properties/{property_id}",
        dimensions=[
            Dimension(name="eventName"),
            Dimension(name="customEvent:client_hostname"),
            Dimension(name="customEvent:widget_uuid"),
            Dimension(name="customEvent:interaction_type"),
            Dimension(name="deviceCategory"),
            Dimension(name="country"),
        ],
        metrics=[
            Metric(name="eventCount"),
            Metric(name="activeUsers"),
            Metric(name="eventsPerUser"),
            Metric(name="averageSessionDuration"),
        ],
        date_ranges=[DateRange(start_date=start_date, end_date=end_date)],
        dimension_filter=FilterExpression(
            filter=Filter(
                field_name="eventName",
                string_filter=Filter.StringFilter(
                    match_type=Filter.StringFilter.MatchType.BEGINS_WITH,
                    value="widget_"
                )
            )
        ),
        limit=10000
    )
    
    response = client.run_report(request)
    return response

# Usage example
report = get_widget_analytics_report("123456789", "2024-01-01", "2024-01-31")
for row in report.rows:
    print(f"Event: {row.dimension_values[0].value}, "
          f"Client: {row.dimension_values[1].value}, "
          f"Count: {row.metric_values[0].value}")
```

## Automated Alerts Configuration

### Intelligence Alerts Setup

**Go to: Configure → Custom Insights**

#### Alert 1: Widget Error Spike Detection

```yaml
Alert Name: "Widget Error Spike"
Alert Type: Anomaly Detection

Conditions:
  - Metric: Event count
  - Event filter: Event name = "widget_error"  
  - Time period: Last 1 hour
  - Threshold: > 50 events OR > 200% increase from baseline
  - Sensitivity: Medium

Notifications:
  - Email: <EMAIL>
  - Slack: #widget-alerts
  - Frequency: Immediate
```

#### Alert 2: New Client Website Detection

```yaml
Alert Name: "New Client Website Detected"
Alert Type: Custom Condition

Conditions:
  - Metric: Active users
  - Dimension filter: Client hostname (Custom) = new value
  - Time period: Last 24 hours
  - Threshold: > 5 active users from new hostname

Notifications:
  - Email: <EMAIL>
  - Report: Include client hostname and usage stats
  - Frequency: Daily digest
```

#### Alert 3: Widget Usage Drop

```yaml
Alert Name: "Widget Usage Significant Drop"
Alert Type: Anomaly Detection

Conditions:
  - Metric: Event count
  - Event filter: Event name = "widget_load"
  - Time period: Last 4 hours  
  - Threshold: < 70% of expected volume
  - Baseline: Previous 7 days same time

Notifications:
  - Email: <EMAIL>
  - Priority: High
  - Include: Top affected client hostnames
```

#### Alert 4: Search Success Rate Drop

```yaml
Alert Name: "Search Success Rate Below Threshold"
Alert Type: Custom Condition

Conditions:
  - Calculated Metric: search_complete / search_initiate * 100
  - Time period: Last 2 hours
  - Threshold: < 85%
  - Minimum search volume: > 100 searches

Notifications:
  - Email: <EMAIL>
  - Slack: #product-alerts
  - Include: Breakdown by client hostname
```

### Custom Alert Scripts

#### Webhook Integration for Slack

```javascript
// Google Cloud Function for GA4 Webhook Alerts
const { WebClient } = require('@slack/web-api');
const slack = new WebClient(process.env.SLACK_TOKEN);

exports.handleGA4Alert = async (req, res) => {
  const alertData = req.body;
  
  if (alertData.alert_type === 'widget_error_spike') {
    await slack.chat.postMessage({
      channel: '#widget-alerts',
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '🚨 Widget Error Spike Detected'
          }
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Error Count:* ${alertData.error_count}`
            },
            {
              type: 'mrkdwn', 
              text: `*Time Period:* ${alertData.time_period}`
            },
            {
              type: 'mrkdwn',
              text: `*Top Error Type:* ${alertData.top_error_type}`
            },
            {
              type: 'mrkdwn',
              text: `*Affected Clients:* ${alertData.affected_clients}`
            }
          ]
        },
        {
          type: 'actions',
          elements: [
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'View in GA4'
              },
              url: alertData.ga4_report_url
            }
          ]
        }
      ]
    });
  }
  
  res.status(200).send('Alert processed');
};
```

## Performance Monitoring Dashboards

### Looker Studio Integration

#### Dashboard 1: Executive Widget Overview

```yaml
Dashboard Name: "Widget Analytics Executive Summary"
Data Source: GA4 Property

Scorecard Metrics:
  - Total Active Widgets (Last 30 days)
  - Total Client Websites  
  - Total Search Volume
  - Average Search Success Rate
  - Widget Uptime Percentage

Charts:
  1. Time Series: Daily widget usage trend (30 days)
  2. Geo Map: Widget usage by country
  3. Bar Chart: Top 10 client websites by usage
  4. Pie Chart: Device category distribution  
  5. Line Chart: Search success rate trend
  6. Table: Widget performance by client hostname

Filters:
  - Date range selector
  - Client hostname filter
  - Widget type filter (finder-v2)

Refresh Schedule: Every 15 minutes
```

#### Dashboard 2: Technical Performance Monitoring  

```yaml
Dashboard Name: "Widget Technical Performance Dashboard"
Data Source: GA4 Property + BigQuery

Performance Metrics:
  - Average Search Response Time
  - API Error Rates by Endpoint
  - Widget Load Time Distribution
  - Browser Compatibility Issues
  - Network Performance by Region

Real-Time Monitors:
  1. Current Active Sessions
  2. Live Error Stream (Last 30 minutes)
  3. Performance Alerts Status
  4. API Health Check Status

Advanced Visualizations:
  - Heatmap: Usage intensity by hour/day
  - Scatter Plot: Search time vs. results count
  - Funnel: User interaction flow
  - Sankey Diagram: User journey paths

Alerts Integration:
  - Real-time error notifications
  - Performance threshold warnings  
  - Anomaly detection indicators
```

### Custom Dashboard Creation

#### HTML/JavaScript Real-Time Dashboard

```html
<!DOCTYPE html>
<html>
<head>
    <title>Widget Analytics Real-Time Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div id="dashboard">
        <div class="metrics-row">
            <div class="metric-card">
                <h3>Active Widgets</h3>
                <span id="active-widgets">Loading...</span>
            </div>
            <div class="metric-card">
                <h3>Searches/Hour</h3>
                <span id="searches-hour">Loading...</span>
            </div>
            <div class="metric-card">
                <h3>Error Rate</h3>
                <span id="error-rate">Loading...</span>
            </div>
        </div>
        
        <div class="charts-row">
            <canvas id="usage-chart"></canvas>
            <canvas id="performance-chart"></canvas>
        </div>
        
        <div class="tables-row">
            <div id="top-clients"></div>
            <div id="recent-errors"></div>
        </div>
    </div>

    <script>
        // Google Analytics Data API Integration
        async function fetchWidgetAnalytics() {
            const response = await fetch('/api/widget-analytics');
            const data = await response.json();
            
            // Update metrics
            document.getElementById('active-widgets').textContent = data.active_widgets;
            document.getElementById('searches-hour').textContent = data.searches_per_hour;
            document.getElementById('error-rate').textContent = data.error_rate + '%';
            
            // Update charts
            updateUsageChart(data.usage_timeline);
            updatePerformanceChart(data.performance_metrics);
        }
        
        // Refresh every 30 seconds
        setInterval(fetchWidgetAnalytics, 30000);
        fetchWidgetAnalytics(); // Initial load
    </script>
</body>
</html>
```

## Troubleshooting Guide

### Common Configuration Issues

#### Issue 1: Measurement ID Not Working

**Symptoms:**
- No events appearing in GA4
- Console errors about gtag not defined

**Solutions:**
```javascript
// Check measurement ID configuration
console.log('Current measurement ID:', analytics.measurementId.value);

// Verify gtag loading
if (typeof window.gtag === 'function') {
    console.log('gtag loaded successfully');
} else {
    console.error('gtag not loaded - check measurement ID');
}

// Debug mode activation
const widgetConfig = {
    analytics: {
        measurementId: 'G-YOUR-ID',
        debugMode: true // Enable debug logging
    }
}
```

#### Issue 2: Custom Dimensions Not Populating

**Symptoms:**
- Events tracked but custom dimensions empty
- Parameters not appearing in GA4 reports

**Solutions:**
```javascript
// Verify event parameters
analytics.trackEvent('test_event', {
    custom_param: 'test_value',
    widget_uuid: 'test-123'
});

// Check parameter naming matches GA4 configuration
// GA4 Parameter Name must exactly match event parameter key
```

#### Issue 3: Cross-Domain Tracking Issues

**Symptoms:**
- Client hostname shows as 'unknown'
- Tracking not working in iframe

**Solutions:**
```javascript
// Test hostname detection
const analytics = useAnalytics(config);
console.log('Detected hostname:', analytics.getClientHostname());

// Configure cross-domain tracking
gtag('config', 'G-YOUR-ID', {
    linker: {
        domains: ['your-domain.com', 'client-domain.com']
    }
});
```

### Debug Mode Configuration

```javascript
// Enable comprehensive debugging
const widgetConfig = {
    analytics: {
        measurementId: 'G-YOUR-ID',
        debugMode: true,
        enabled: true
    }
};

// Add debug event listener
window.addEventListener('gtag-debug', (event) => {
    console.log('GA4 Debug Event:', event.detail);
});
```

### Testing Checklist

```markdown
## GA4 Integration Testing Checklist

### Basic Functionality
- [ ] Measurement ID correctly configured
- [ ] gtag script loads without errors
- [ ] Events appear in GA4 Realtime reports
- [ ] Custom dimensions populate correctly

### Widget Events
- [ ] widget_load event fires on widget initialization
- [ ] widget_ready event fires after full load
- [ ] User interactions tracked (year, make, model selection)
- [ ] Search events tracked (initiate, complete)
- [ ] Error events tracked when failures occur
- [ ] widget_unload event fires on page unload

### Cross-Domain Tracking  
- [ ] Client hostname detected correctly
- [ ] Iframe tracking works on test domains
- [ ] PostMessage communication functional
- [ ] Referrer fallback works for cross-origin

### Privacy Compliance
- [ ] Do Not Track header respected
- [ ] Opt-out mechanism functional
- [ ] Debug mode disabled in production
- [ ] No PII collected in events

### Performance
- [ ] Analytics loading is non-blocking
- [ ] No impact on widget load time
- [ ] Memory usage acceptable
- [ ] Error handling graceful
```

### Performance Optimization

```javascript
// Optimize analytics loading
const optimizedAnalytics = {
    // Lazy load gtag script
    loadGtagAsync: async () => {
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }
        return loadGtagScript();
    },
    
    // Batch events to reduce requests
    batchEvents: true,
    batchTimeout: 1000, // ms
    
    // Minimize data collection
    minimalTracking: {
        pageView: false,
        scrollTracking: false,
        outboundLinks: false
    }
};
```

## Conclusion

This comprehensive guide provides everything needed to successfully implement and monitor GA4 analytics for the finder-v2 widget. The configuration supports real-time monitoring, detailed performance analysis, and automated alerting to ensure optimal widget performance across all client websites.

For additional support or advanced configurations, refer to the Google Analytics 4 documentation or contact the development team.

---

**Related Documentation:**
- [Finder-v2 Google Analytics 4 Integration Task](../tasks/finder-v2-google-analytics-4-integration.md)
- [Widget Security Guide](../security/widget-security-guide.md)
- [Finder-v2 Development Guide](../development/README-finder-v2-deployment.md)