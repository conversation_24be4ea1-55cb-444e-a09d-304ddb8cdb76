# GA4 Widget Analytics Quick Reference

## Measurement ID Configuration (Choose One)

### Option 1: Widget Config (Recommended)
```javascript
const widgetConfig = {
  analytics: {
    measurementId: 'G-YOUR-ACTUAL-ID',
    debugMode: false
  }
}
```

### Option 2: Global Config
```javascript
window.FinderV2Config = {
  analytics: { measurementId: 'G-YOUR-ACTUAL-ID' }
}
```

### Option 3: Direct Property
```javascript
const widgetConfig = {
  ga4MeasurementId: 'G-YOUR-ACTUAL-ID'
}
```

## Required GA4 Custom Dimensions

| Parameter Name | Dimension Name | Scope | Description |
|---------------|----------------|-------|-------------|
| `widget_uuid` | Widget UUID | Event | Widget instance ID |
| `client_hostname` | Client Hostname | Event | Client website |
| `widget_type` | Widget Type | Event | finder-v2 |
| `flow_type` | Search Flow Type | Event | primary/secondary |
| `interaction_type` | Interaction Type | Event | User interaction |
| `search_type` | Search Type | Event | Search category |
| `error_type` | Error Type | Event | Error category |
| `api_version` | API Version | Event | API version |

## Key Events Tracked

- `widget_load` - Widget initialization
- `widget_ready` - Widget fully loaded
- `widget_interaction` - User interactions
- `widget_search` - Search events
- `widget_error` - Error events
- `widget_unload` - Session end

## Real-Time Reports Setup

1. **Go to GA4 → Reports → Realtime**
2. **Add cards for:**
   - Active widgets (filter: event_name contains "widget_")
   - Client websites (dimension: client_hostname)
   - Search activity (filter: event_name = "widget_search")
   - Errors (filter: event_name = "widget_error")

## Custom Report Templates

### Widget Performance Report
- **Dimensions:** Client hostname, Widget UUID, Search type
- **Metrics:** Event count, Users, Average session duration
- **Filter:** Event name begins with "widget_"

### Error Analysis Report  
- **Dimensions:** Error type, Client hostname, API version
- **Metrics:** Event count, Affected users
- **Filter:** Event name = "widget_error"

## Debug Mode Testing

```javascript
// Enable debug mode
const widgetConfig = {
  analytics: {
    measurementId: 'G-YOUR-ID',
    debugMode: true
  }
}

// Check if working
console.log('Analytics status:', analytics.getStatus())
```

## Quick Troubleshooting

- **No events:** Check measurement ID configuration
- **Empty dimensions:** Verify parameter names match GA4 setup
- **Cross-domain issues:** Check hostname detection function
- **Performance:** Ensure non-blocking initialization


  📊 Creating Detailed GA4 Custom Reports for Widget Usage

  Here's how to create comprehensive custom reports in GA4 to monitor widget usage
  across client websites:

  Step 1: Create Custom Dimensions in GA4

  First, set up these custom dimensions in your GA4 property:

  1. Go to GA4 → Configure → Custom Definitions → Custom Dimensions
  2. Create these dimensions:

  // Custom Dimensions to Create in GA4:
  {
    "widget_uuid": "Widget Instance ID",           // Dimension 1
    "widget_type": "Widget Type (finder-v2)",     // Dimension 2  
    "client_hostname": "Client Website",          // Dimension 3
    "flow_type": "Search Flow Type",              // Dimension 4
    "api_version": "API Version",                 // Dimension 5
    "theme_name": "Widget Theme",                 // Dimension 6
    "search_type": "Search Type",                 // Dimension 7
    "interaction_type": "User Interaction Type",  // Dimension 8
    "error_type": "Error Category",               // Dimension 9
    "user_profile_uuid": "User Profile ID"        // Dimension 10
  }

  Step 2: Create Real-Time Custom Reports

  Real-Time Widget Activity Report

  1. Go to GA4 → Reports → Library → Create new report
  2. Select "Detail Report"
  3. Configure:

  Report Name: "Widget Real-Time Activity"
  Dimensions:
    - Event name
    - Client hostname (custom dimension)
    - Widget UUID (custom dimension)
    - Widget type (custom dimension)

  Metrics:
    - Event count
    - Active users
    - Session duration

  Filters:
    - Event name contains "widget_"
    - Widget type equals "finder-v2"

  Time Range: Last 30 minutes (for real-time)

  Client Website Usage Report

  Report Name: "Widget Usage by Client Website"
  Dimensions:
    - Client hostname (custom dimension)
    - Widget UUID (custom dimension)
    - Search flow type (custom dimension)

  Metrics:
    - Users
    - Sessions
    - Event count
    - Conversion rate (if goals set)

  Secondary Dimensions:
    - Widget theme (custom dimension)
    - API version (custom dimension)

  Step 3: Advanced Real-Time Dashboard

  Create an Exploration Report for deeper insights:

  1. Go to GA4 → Explore → Create new exploration
  2. Choose "Free Form" exploration
  3. Configure Advanced Report:

  Dashboard Name: "Widget Performance Dashboard"

  Variables:
  Dimensions:
    - Event name
    - Client hostname
    - Widget UUID
    - Search flow type
    - Interaction type
    - Hour of day
    - Device category

  Metrics:
    - Event count
    - Users
    - Average engagement time
    - Events per session
    - Custom metric: Search completion rate

  Filters:
    - Event name matches regex: "widget_.*"
    - Widget type = "finder-v2"

  Visualizations:
  1. Time series: Event count by hour
  2. Bar chart: Usage by client hostname
  3. Pivot table: Interactions by widget UUID
  4. Geographic map: Usage by country

  Step 4: Create Automated Alerts

  Set up Intelligence Alerts for monitoring:

  1. Go to GA4 → Configure → Custom Insights
  2. Create alerts for:

  Alert 1: "Widget Error Spike"
  Condition: widget_error events > 50 in last hour
  Notification: Email alert

  Alert 2: "New Client Website"
  Condition: New client_hostname dimension value detected
  Notification: Slack/Email

  Alert 3: "Usage Drop"
  Condition: widget_load events < 100 in last day
  Notification: Email alert

  Alert 4: "High Search Volume"
  Condition: search_complete events > 1000 in last hour
  Notification: Success notification

  Step 5: Key Reports for Widget Monitoring

  Real-Time Widget Events

  -- GA4 BigQuery Query (if enabled)
  SELECT
    event_name,
    (SELECT value.string_value FROM UNNEST(event_params) WHERE key =
  'client_hostname') as client_website,
    (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'widget_uuid')
   as widget_id,
    (SELECT value.string_value FROM UNNEST(event_params) WHERE key =
  'interaction_type') as interaction,
    event_timestamp,
    user_pseudo_id
  FROM `your-project.analytics_XXXXX.events_*`
  WHERE event_name LIKE 'widget_%'
    AND _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', DATE_SUB(CURRENT_DATE(),
  INTERVAL 1 DAY))
    AND FORMAT_DATE('%Y%m%d', CURRENT_DATE())
  ORDER BY event_timestamp DESC
  LIMIT 1000

  Widget Performance Metrics

  - Search Success Rate: search_complete / search_initiate * 100
  - User Engagement: Average time between widget_load and widget_unload
  - Error Rate: widget_error / total_widget_events * 100
  - Popular Client Websites: Top hostnames by event volume

  Step 6: Real-Time Monitoring Setup

  To see live widget activity:

  1. Use GA4 Realtime Report:
    - Go to GA4 → Reports → Realtime
    - Add custom cards for widget events
    - Filter by event_name contains "widget_"
  2. Create Custom Realtime Cards:
    - Active widgets by client website
    - Current search activity
    - Error events in last 30 minutes
    - New widget installations

  Example Custom Report Configuration

  Here's a complete configuration for a comprehensive widget monitoring report:

  // GA4 Custom Report Configuration
  {
    "reportName": "Finder-v2 Widget Analytics Dashboard",
    "timeRange": "last_7_days",
    "dimensions": [
      "customEvent:client_hostname",
      "customEvent:widget_uuid",
      "customEvent:search_type",
      "customEvent:interaction_type",
      "eventName",
      "deviceCategory",
      "country"
    ],
    "metrics": [
      "eventCount",
      "activeUsers",
      "sessionsPerUser",
      "averageSessionDuration",
      "eventCountPerUser"
    ],
    "filters": [
      {
        "fieldName": "eventName",
        "matchType": "BEGINS_WITH",
        "value": "widget_"
      }
    ],
    "orderBys": [
      {
        "metric": "eventCount",
        "desc": true
      }
    ]
  }

  This setup will give you comprehensive real-time visibility into how your
  finder-v2 widgets are being used across all client websites, including user
  behavior patterns, performance metrics, and error tracking.