<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Implementation Test</title>
    
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Template Configuration - Tab Test</h1>
        
<!-- The Link to Wheel-Size.com. It is required for free usage of services. Feel free to modify css styles if needed -->
<h3><a title="Wheel fitment and tire size guide and knowledge base" href="https://www.wheel-size.com">Wheel-Size.com</a></h3>

<div id="ws-widget-df02fc"></div>

<script src="//development.local/static/widget/code/local/ws-widget.js"></script>
<script>
  var widget = WheelSizeWidgets.create('#ws-widget-df02fc', {
    uuid: '8966286f2ec64c0090e70cec714dbd69',
    type: 'finder-v2'
  });

widget.on('results:display', function(e) {
  console.log(e.data.results_count);
  console.log(e.data.context.selections);
});

</script>



    </div>

</body>
</html>