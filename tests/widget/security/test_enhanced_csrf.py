"""
Unit Tests for Enhanced CSRF Protection

This module tests the enhanced CSRF protection system with session binding,
token rotation, and multi-factor validation.
"""

import time
import hmac
import hashlib
from unittest.mock import Mock, patch, MagicMock

from django.test import TestCase, RequestFactory
from django.conf import settings
from django.core.cache import caches

# Use the same cache as the CSRF module
try:
    cache = caches['csrf_tokens']
except:
    try:
        cache = caches['api_proxy_throttle']
    except:
        from django.core.cache import cache
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.models import User
from django.http import HttpRequest

from src.apps.widgets.api_proxy.csrf import (
    EnhancedCSRFProtection,
    CSRFTokenManager
)


class TestEnhancedCSRFProtection(TestCase):
    """Test suite for EnhancedCSRFProtection class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.factory = RequestFactory()
        
        # Use unique username for each test to avoid conflicts
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        self.user = User.objects.create_user(
            username=f'testuser_{unique_id}',
            email=f'test_{unique_id}@example.com',
            password='testpass123'
        )
        
        # Clear cache before each test
        cache.clear()
    
    def _get_request_with_session(self):
        """Helper to create request with session."""
        request = self.factory.get('/')
        
        # Add session support
        middleware = SessionMiddleware(lambda x: x)
        middleware.process_request(request)
        request.session.save()
        
        # Add standard headers
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 Test Browser'
        request.META['REMOTE_ADDR'] = '*************'
        
        return request
    
    def test_token_generation(self):
        """Test basic token generation."""
        request = self._get_request_with_session()
        
        token = EnhancedCSRFProtection.generate_token(request)
        
        # Verify token format
        self.assertEqual(len(token), 32)
        self.assertTrue(token.isalnum())
        
        # Verify token is stored in cache
        cache_key = f"{EnhancedCSRFProtection.CACHE_PREFIX_TOKEN}:{token[:16]}"
        metadata = cache.get(cache_key)
        
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata['session'], request.session.session_key)
        self.assertEqual(metadata['ip'], '*************')
    
    def test_token_validation_success(self):
        """Test successful token validation."""
        request = self._get_request_with_session()
        
        # Generate token
        token = EnhancedCSRFProtection.generate_token(request)
        
        # Validate token with same request
        is_valid = EnhancedCSRFProtection.validate_token(request, token)
        
        self.assertTrue(is_valid)
    
    def test_token_validation_wrong_session(self):
        """Test token validation fails with different session."""
        request1 = self._get_request_with_session()
        token = EnhancedCSRFProtection.generate_token(request1)
        
        # Create new request with different session
        request2 = self._get_request_with_session()
        
        # Validation should fail
        is_valid = EnhancedCSRFProtection.validate_token(request2, token)
        
        self.assertFalse(is_valid)
    
    def test_token_validation_ip_flexibility(self):
        """Test IP validation allows same subnet."""
        request = self._get_request_with_session()
        request.META['REMOTE_ADDR'] = '*************'
        
        token = EnhancedCSRFProtection.generate_token(request)
        
        # Change IP within same /24 subnet
        request.META['REMOTE_ADDR'] = '*************'
        
        # Should still validate (mobile network flexibility)
        is_valid = EnhancedCSRFProtection.validate_token(request, token)
        
        self.assertTrue(is_valid)
        
        # Change to different subnet
        request.META['REMOTE_ADDR'] = '********'
        
        # Should fail
        is_valid = EnhancedCSRFProtection.validate_token(request, token)
        
        self.assertFalse(is_valid)
    
    def test_token_expiration(self):
        """Test token expiration after lifetime."""
        request = self._get_request_with_session()
        
        token = EnhancedCSRFProtection.generate_token(request)
        
        # Mock time to simulate token expiration
        with patch('src.apps.widgets.api_proxy.csrf.time.time') as mock_time:
            # Set time to after token lifetime
            mock_time.return_value = time.time() + EnhancedCSRFProtection.TOKEN_LIFETIME + 1
            
            is_valid = EnhancedCSRFProtection.validate_token(request, token)
            
            self.assertFalse(is_valid)
    
    def test_token_rate_limiting(self):
        """Test rate limiting per token."""
        request = self._get_request_with_session()
        
        token = EnhancedCSRFProtection.generate_token(request)
        
        # Use token up to rate limit
        for i in range(EnhancedCSRFProtection.MAX_REQUESTS_PER_TOKEN):
            is_valid = EnhancedCSRFProtection.validate_token(request, token)
            self.assertTrue(is_valid, f"Validation {i+1} should succeed")
        
        # Next request should fail due to rate limit
        is_valid = EnhancedCSRFProtection.validate_token(request, token)
        
        self.assertFalse(is_valid)
    
    def test_token_rotation(self):
        """Test token rotation mechanism."""
        request = self._get_request_with_session()
        
        # Generate initial token
        old_token = EnhancedCSRFProtection.generate_token(request)
        
        # Ensure token is valid before rotation
        self.assertTrue(EnhancedCSRFProtection.validate_token(request, old_token))
        
        # Rotate token
        new_token = EnhancedCSRFProtection.rotate_token(request, old_token)
        
        self.assertIsNotNone(new_token)
        # Token rotation creates a new token OR returns the same if still fresh
        self.assertEqual(len(new_token), 32)
        
        # New/rotated token should be valid
        is_valid = EnhancedCSRFProtection.validate_token(request, new_token)
        self.assertTrue(is_valid)
        
        # If tokens are different, old token should have grace period
        if old_token != new_token:
            is_valid = EnhancedCSRFProtection.validate_token(request, old_token)
            self.assertTrue(is_valid, "Old token should have grace period")
    
    def test_token_rotation_limit(self):
        """Test rotation limit per token family."""
        request = self._get_request_with_session()
        
        # Generate initial token
        token = EnhancedCSRFProtection.generate_token(request)
        
        # Ensure initial token is valid
        self.assertTrue(EnhancedCSRFProtection.validate_token(request, token))
        
        # Since tokens within same 5-minute window are identical,
        # we'll test the rotation count limit directly
        cache_key = f"{EnhancedCSRFProtection.CACHE_PREFIX_TOKEN}:{token[:16]}"
        metadata = cache.get(cache_key)
        
        # Manually set rotation count to near limit
        if metadata:
            metadata['rotation_count'] = 9
            cache.set(cache_key, metadata, EnhancedCSRFProtection.TOKEN_LIFETIME)
        
        # First rotation should succeed (count becomes 10)
        rotated1 = EnhancedCSRFProtection.rotate_token(request, token)
        self.assertIsNotNone(rotated1, "Rotation at count 9 should succeed")
        
        # Update metadata for the rotated token to have count 10
        if rotated1 != token:
            # If we got a new token, update its count
            new_cache_key = f"{EnhancedCSRFProtection.CACHE_PREFIX_TOKEN}:{rotated1[:16]}"
            new_metadata = cache.get(new_cache_key)
            if new_metadata:
                new_metadata['rotation_count'] = 10
                cache.set(new_cache_key, new_metadata, EnhancedCSRFProtection.TOKEN_LIFETIME)
        else:
            # Same token returned (fresh), update original metadata
            metadata = cache.get(cache_key)
            if metadata:
                metadata['rotation_count'] = 10
                cache.set(cache_key, metadata, EnhancedCSRFProtection.TOKEN_LIFETIME)
        
        # Next rotation should fail (would exceed limit)
        rotated2 = EnhancedCSRFProtection.rotate_token(request, rotated1)
        self.assertIsNone(rotated2, "Rotation at count 10 should fail (limit exceeded)")
    
    def test_invalid_token_format(self):
        """Test validation rejects invalid token formats."""
        request = self._get_request_with_session()
        
        # Test various invalid formats
        invalid_tokens = [
            '',  # Empty
            'short',  # Too short
            'a' * 50,  # Too long
            'invalid-chars!@#',  # Invalid characters
            None  # None
        ]
        
        for invalid_token in invalid_tokens:
            is_valid = EnhancedCSRFProtection.validate_token(request, invalid_token)
            self.assertFalse(is_valid, f"Token '{invalid_token}' should be invalid")
    
    def test_proxy_headers(self):
        """Test IP extraction from proxy headers."""
        request = self._get_request_with_session()
        
        # Test X-Forwarded-For
        request.META['HTTP_X_FORWARDED_FOR'] = '***********, ***********'
        ip = EnhancedCSRFProtection._get_client_ip(request)
        self.assertEqual(ip, '***********')
        
        # Test X-Real-IP
        del request.META['HTTP_X_FORWARDED_FOR']
        request.META['HTTP_X_REAL_IP'] = '***********'
        ip = EnhancedCSRFProtection._get_client_ip(request)
        self.assertEqual(ip, '***********')
        
        # Test fallback to REMOTE_ADDR
        del request.META['HTTP_X_REAL_IP']
        request.META['REMOTE_ADDR'] = '***********'
        ip = EnhancedCSRFProtection._get_client_ip(request)
        self.assertEqual(ip, '***********')


class TestCSRFTokenManager(TestCase):
    """Test suite for CSRFTokenManager utility class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.factory = RequestFactory()
        cache.clear()
    
    def _get_request_with_session(self):
        """Helper to create request with session."""
        request = self.factory.get('/')
        
        # Add session support
        middleware = SessionMiddleware(lambda x: x)
        middleware.process_request(request)
        request.session.save()
        
        # Add standard headers
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 Test Browser'
        request.META['REMOTE_ADDR'] = '*************'
        
        return request
    
    def test_get_or_create_token(self):
        """Test getting or creating token."""
        request = self._get_request_with_session()
        
        # First call should create token
        token1 = CSRFTokenManager.get_or_create_token(request)
        self.assertEqual(len(token1), 32)
        
        # Token should be in session
        self.assertEqual(request.session.get('widget_csrf_token'), token1)
        
        # Second call with same session should return same valid token
        token2 = CSRFTokenManager.get_or_create_token(request)
        self.assertEqual(token1, token2)
    
    def test_validate_request_token_from_header(self):
        """Test validating token from request header."""
        request = self._get_request_with_session()
        
        # Generate token
        token = CSRFTokenManager.get_or_create_token(request)
        
        # Add token to header
        request.META['HTTP_X_CSRF_TOKEN'] = token
        
        # Validation should succeed
        is_valid = CSRFTokenManager.validate_request_token(request)
        self.assertTrue(is_valid)
        
        # Wrong token should fail
        request.META['HTTP_X_CSRF_TOKEN'] = 'wrong_token_12345678901234567890'
        is_valid = CSRFTokenManager.validate_request_token(request)
        self.assertFalse(is_valid)
    
    def test_validate_request_token_from_post(self):
        """Test validating token from POST data."""
        request = self.factory.post('/', {'csrftoken': ''})
        
        # Add session
        middleware = SessionMiddleware(lambda x: x)
        middleware.process_request(request)
        request.session.save()
        
        # Add headers
        request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 Test Browser'
        request.META['REMOTE_ADDR'] = '*************'
        
        # Generate token
        token = CSRFTokenManager.get_or_create_token(request)
        
        # Add token to POST data
        request.POST = {'csrftoken': token}
        
        # Validation should succeed
        is_valid = CSRFTokenManager.validate_request_token(request)
        self.assertTrue(is_valid)
    
    def test_rotate_request_token(self):
        """Test rotating token from request."""
        request = self._get_request_with_session()
        
        # Generate initial token
        old_token = CSRFTokenManager.get_or_create_token(request)
        
        # Add token to header
        request.META['HTTP_X_CSRF_TOKEN'] = old_token
        
        # Rotate token
        new_token = CSRFTokenManager.rotate_request_token(request)
        
        self.assertIsNotNone(new_token)
        # Note: Token rotation might return the same token if it's still valid
        # The important thing is that the token is valid
        
        # New token should be in session
        self.assertEqual(request.session.get('widget_csrf_token'), new_token)
        
        # Verify the token is valid
        request.META['HTTP_X_CSRF_TOKEN'] = new_token
        self.assertTrue(CSRFTokenManager.validate_request_token(request))


class TestDebugMode(TestCase):
    """Test debug mode functionality."""
    
    def test_debug_mode_detection(self):
        """Test debug mode detection from settings."""
        with self.settings(WIDGET_CSRF_SETTINGS={'debug_csrf_validation': True}):
            self.assertTrue(EnhancedCSRFProtection._is_debug_mode())
        
        with self.settings(WIDGET_CSRF_SETTINGS={'debug_csrf_validation': False}):
            self.assertFalse(EnhancedCSRFProtection._is_debug_mode())
        
        with self.settings(WIDGET_CSRF_SETTINGS={}):
            self.assertFalse(EnhancedCSRFProtection._is_debug_mode())