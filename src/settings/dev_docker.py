from .dev import *
from .env import get_env_variable, get_env_int

ALLOWED_HOSTS = [
    'services.ws.com', 'services.ws.ru', 'development.local', 'development.local:8000'
]

STATIC_ROOT = '/static'

# Fix X-Frame-Options for widget iframe functionality in development
X_FRAME_OPTIONS = 'SAMEORIGIN'

SERVICES_ENV['code_version'] = 'local'

# TailwindCSS configuration for development
USE_TAILWIND_DEBUG_CSS = True  # Use full utility CSS in development

# Docker development-specific widget CSRF settings
# Similar to dev.py but includes Docker-specific hostnames
WIDGET_CSRF_SETTINGS = {
    'ignore_port_in_hostname_check': True,  # Enable port ignoring for Docker development
    # 'trusted_hostnames': [],  # REMOVED: Security risk for client widget installations
    'debug_csrf_validation': True,  # Enable debug logging in Docker development
    
    # Enhanced CSRF Protection Settings (Phase 1 - Testing)
    'use_enhanced_csrf': True,  # ENABLED - Widget now supports enhanced tokens
    'token_lifetime': 3600,  # 1 hour
    'token_rotation_interval': 300,  # 5 minutes
    'max_requests_per_token': 100,  # Rate limit per token
}

# Enable detailed form validation logging for debugging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'src.apps.widgets.main.views.config': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'src.apps.widgets.finder_v2.forms': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.forms': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

DATABASES['default'] = {
    'ENGINE': get_env_variable('DB_ENGINE', default='django.db.backends.postgresql_psycopg2', required=False),
    'NAME': get_env_variable('DB_NAME', default='ws_services_db', required=False),
    'USER': get_env_variable('DB_USER', default='ws_services_user', required=False),
    'PASSWORD': get_env_variable('DB_PASSWORD', default='89JXdCdNUEMKCRLu', required=False),
    'HOST': get_env_variable('DB_HOST', default='postgres15', required=False),
    'PORT': get_env_int('DB_PORT', default=5432),
}
        # 'ENGINE': 'django.db.backends.postgresql_psycopg2',
        # 'NAME': 'tiresvote',
        # 'USER': 'tirereviews',
        # 'PASSWORD': '89JXdCdNUEMKCRLu',
        # 'HOST': 'postgres15',

# Development API configuration - testing fixed proxy with production authentication
REST_PROXY = {
    'HOST': 'https://api3.wheel-size.com',
    'HEADERS': {
        'X-WS-API-SECRET-TOKEN': 'uJnxEaznliaMfXIy',
        'X-AMZN-TRACE-ID': 'CalledFrom=ws-services-dev',
    }
}

# Alternative user_key method (fallback if needed):
# REST_PROXY = {
#     'HOST': 'https://api.wheel-size.com',
#     'HEADERS': {
#         'X-AMZN-TRACE-ID': 'CalledFrom=ws-services-dev',
#     }
# }

# Alternative local API configuration (commented out for calc widget testing)
# REST_PROXY = {
#     'HOST': get_env_variable('REST_PROXY_HOST', default='http://ws_api:9002/v1', required=False)
#     # 'HEADERS': {
#     #     'X-WS-API-SECRET-TOKEN': get_env_variable('REST_PROXY_SECRET_TOKEN', required=False),
#     #     'X-AMZN-TRACE-ID': 'CalledFrom=ws-services-local-docker',
#     # }
# }