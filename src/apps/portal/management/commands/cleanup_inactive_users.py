from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
import contextlib


class Command(BaseCommand):
    help = (
        "Delete spam/inactive user accounts that: "
        "(1) are not staff, (2) are not superusers, and (3) do not own any widget configurations."
    )

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show how many users would be deleted without actually deleting them.",
        )
        parser.add_argument(
            "--allow-email",
            action="store_true",
            help=(
                "Do not override EMAIL_BACKEND. By default the command disables email sending "
                "to prevent any bulk notifications."
            ),
        )

    def handle(self, *args, **options):
        User = get_user_model()

        # Users that are not staff and not superuser
        base_qs = User.objects.filter(is_staff=False, is_superuser=False)

        # Exclude users who own at least one widget configuration
        # Related name: user.widgets (from WidgetConfig.user related_name='widgets')
        candidates = base_qs.filter(widgets__isnull=True)

        count = candidates.count()
        if options.get("dry_run"):
            self.stdout.write(self.style.WARNING(f"[DRY RUN] Users to delete: {count}"))
            return

        # Double-safety: disable email sending during deletion unless explicitly allowed
        email_ctx = (
            contextlib.nullcontext()
            if options.get("allow_email")
            else override_settings(EMAIL_BACKEND="django.core.mail.backends.dummy.EmailBackend")
        )

        with email_ctx:
            deleted_info = candidates.delete()
        self.stdout.write(self.style.SUCCESS(f"Deleted {count} users. Details: {deleted_info}"))


