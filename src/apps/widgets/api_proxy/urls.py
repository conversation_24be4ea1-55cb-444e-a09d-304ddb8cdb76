"""
Widget API Proxy URL Configuration

This module defines URL patterns for widget API endpoints that proxy requests
to the external wheel-size API. It handles routing for both finder widgets
(which use v1 API) and other widgets (which use v2 API).

URL Structure:
- /widget/finder/api/mk → FinderWidgetProxyView → api3.wheel-size.com/v1/makes/
- /widget/calc/api/mk → WidgetProxyView → api3.wheel-size.com/v2/makes/
- /widget/api/mk → WidgetProxyView → api3.wheel-size.com/v2/makes/
"""

from django.urls import path, re_path, include
from django.http import Http404

from src.apps.widgets.api_proxy.views import WidgetProxyView, FinderWidgetProxyView, FinderV2WidgetProxyView
from src.apps.widgets.widget_type import WidgetType
from src.apps.widgets.common.models import WidgetConfig

# Import CSRF management views if available
try:
    from src.apps.widgets.api_proxy.csrf_views import (
        CSRFTokenRefreshView, 
        CSRFTokenValidateView,
        VerifyHumanView,
        FingerprintStatusView,
        RequestChallengeView,
        VerifyChallengeView
    )
    CSRF_VIEWS_AVAILABLE = True
except ImportError:
    CSRF_VIEWS_AVAILABLE = False


def dynamic_widget_api_view(request, widget_slug, **kwargs):
    """
    Dynamic API view that routes to the correct proxy view based on widget type.

    This function determines whether a UUID widget is a finder widget or another
    widget type and routes the request to the appropriate proxy view:
    - Finder widgets → FinderWidgetProxyView (v1 API)
    - Other widgets → WidgetProxyView (v2 API)

    Args:
        request: Django HttpRequest object
        widget_slug: Widget UUID or type slug
        **kwargs: Additional URL parameters

    Returns:
        HttpResponse: Response from the appropriate proxy view
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"dynamic_widget_api_view called with widget_slug={widget_slug}, path={request.path}")
    
    # Check if this is a UUID widget (32 hex characters)
    if len(widget_slug) == 32 and all(c in '0123456789abcdef' for c in widget_slug):
        try:
            # Get widget configuration from database
            widget_config = WidgetConfig.objects.get(uuid=widget_slug)
            logger.info(f"Found widget config: type={widget_config.type}")

            # Route finder widgets to FinderWidgetProxyView (v1 API)
            if widget_config.type == 'finder':
                # Get the source parameter from the URL path
                source = request.resolver_match.url_name
                source_mapping = {
                    'countries': '__cc/',
                    'markets': '__mr/',
                    'makes': 'makes/',
                    'models': 'models/',
                    'years': 'years/',
                    'search-by-model': 'search/by_model/',
                    'tire-widths': '__tw/',
                    'aspect-ratios': '__ar/',
                    'rim-diameters': '__rd/',
                    'search-by-tire': 'search/by_tire/',
                    'rim-widths': '__rw/',
                    'bolt-patterns': '__bp/',
                    'search-by-rim': 'search/by_rim/',
                }

                if source in source_mapping:
                    view = FinderWidgetProxyView.as_view(source=source_mapping[source])
                    return view(request, widget_slug=widget_slug, **kwargs)

            # Route finder-v2 widgets to FinderV2WidgetProxyView (v2 API)
            elif widget_config.type == 'finder-v2':
                # Get the source parameter from the URL path
                source = request.resolver_match.url_name
                logger.info(f"Finder-v2 widget detected, source={source}")
                source_mapping = {
                    'countries': '__cc/',
                    'regions': 'regions/',
                    'makes': 'makes/',
                    'models': 'models/',
                    'years': 'years/',
                    'modifications': 'modifications/',  # New for v2 API
                    'generations': 'generations/',      # New for v2 API alternative flow
                    'search-by-model': 'search/by_model/',
                    'tire-widths': '__tw/',
                    'aspect-ratios': '__ar/',
                    'rim-diameters': '__rd/',
                    'search-by-tire': 'search/by_tire/',
                    'rim-widths': '__rw/',
                    'bolt-patterns': '__bp/',
                    'search-by-rim': 'search/by_rim/',
                }

                if source in source_mapping:
                    view = FinderV2WidgetProxyView.as_view(source=source_mapping[source])
                    logger.info(f"Returning FinderV2WidgetProxyView for source={source}")
                    return view(request, widget_slug=widget_slug, **kwargs)
                else:
                    logger.warning(f"Source '{source}' not found in finder-v2 source_mapping")

        except WidgetConfig.DoesNotExist:
            logger.error(f"WidgetConfig not found for UUID: {widget_slug}")
            pass

    # Default to WidgetProxyView for non-finder widgets or if lookup fails
    # This maintains backward compatibility for calc widgets and other widget types
    source = request.resolver_match.url_name
    source_mapping = {
        'countries': '__cc/',
        'markets': '__mr/',
        'makes': 'makes/',
        'models': 'models/',
        'years': 'years/',
        'modifications': 'modifications/',    # Added for v2 API compatibility
        'generations': 'generations/',       # Added for v2 API compatibility (alternative flow)
        'search-by-model': 'search/by_model/',
        'tire-widths': '__tw/',
        'aspect-ratios': '__ar/',
        'rim-diameters': '__rd/',
        'search-by-tire': 'search/by_tire/',
        'rim-widths': '__rw/',
        'bolt-patterns': '__bp/',
        'search-by-rim': 'search/by_rim/',
    }

    if source in source_mapping:
        view = WidgetProxyView.as_view(source=source_mapping[source])
        return view(request, widget_slug=widget_slug, **kwargs)

    raise Http404("Widget API endpoint not found")


# Finder widget API endpoints (use v1 API via FinderWidgetProxyView)
# These endpoints are specifically for the finder widget which requires v1 API
finder_api_urlpatterns = [
    # Location and market endpoints
    re_path(r'cc$', FinderWidgetProxyView.as_view(source='__cc/'), name='countries'),
    re_path(r'mr$', FinderWidgetProxyView.as_view(source='__mr/'), name='markets'),

    # Vehicle search endpoints (by make/model/year)
    re_path(r'mk$', FinderWidgetProxyView.as_view(source='makes/'), name='makes'),
    re_path(r'ml$', FinderWidgetProxyView.as_view(source='models/'), name='models'),
    re_path(r'yr$', FinderWidgetProxyView.as_view(source='years/'), name='years'),
    re_path(r'sm$', FinderWidgetProxyView.as_view(source='search/by_model/'), name='search-by-model'),

    # Tire search endpoints (by tire dimensions)
    re_path(r'tw$', FinderWidgetProxyView.as_view(source='__tw/'), name='tire-widths'),
    re_path(r'ar$', FinderWidgetProxyView.as_view(source='__ar/'), name='aspect-ratios'),
    re_path(r'rd$', FinderWidgetProxyView.as_view(source='__rd/'), name='rim-diameters'),
    re_path(r'st$', FinderWidgetProxyView.as_view(source='search/by_tire/'), name='search-by-tire'),

    # Rim search endpoints (by rim specifications)
    re_path(r'rw$', FinderWidgetProxyView.as_view(source='__rw/'), name='rim-widths'),
    re_path(r'bp$', FinderWidgetProxyView.as_view(source='__bp/'), name='bolt-patterns'),
    re_path(r'sr$', FinderWidgetProxyView.as_view(source='search/by_rim/'), name='search-by-rim'),
]

# Finder-v2 widget API endpoints (use v2 API via FinderV2WidgetProxyView)
# These endpoints are specifically for the finder-v2 widget which uses v2 API with enhanced functionality
finder_v2_api_urlpatterns = [
    # Location and market endpoints
    re_path(r'cc$', FinderV2WidgetProxyView.as_view(source='__cc/'), name='countries'),
    re_path(r'rg$', FinderV2WidgetProxyView.as_view(source='regions/'), name='regions'),

    # Vehicle search endpoints (by make/model/year) - v2 API
    re_path(r'mk$', FinderV2WidgetProxyView.as_view(source='makes/'), name='makes'),
    re_path(r'ml$', FinderV2WidgetProxyView.as_view(source='models/'), name='models'),
    re_path(r'yr$', FinderV2WidgetProxyView.as_view(source='years/'), name='years'),
    re_path(r'md$', FinderV2WidgetProxyView.as_view(source='modifications/'), name='modifications'),
    re_path(r'gn$', FinderV2WidgetProxyView.as_view(source='generations/'), name='generations'),
    re_path(r'sm$', FinderV2WidgetProxyView.as_view(source='search/by_model/'), name='search-by-model'),

    # Tire search endpoints (by tire dimensions) - v2 API
    re_path(r'tw$', FinderV2WidgetProxyView.as_view(source='__tw/'), name='tire-widths'),
    re_path(r'ar$', FinderV2WidgetProxyView.as_view(source='__ar/'), name='aspect-ratios'),
    re_path(r'rd$', FinderV2WidgetProxyView.as_view(source='__rd/'), name='rim-diameters'),
    re_path(r'st$', FinderV2WidgetProxyView.as_view(source='search/by_tire/'), name='search-by-tire'),

    # Rim search endpoints (by rim specifications) - v2 API
    re_path(r'rw$', FinderV2WidgetProxyView.as_view(source='__rw/'), name='rim-widths'),
    re_path(r'bp$', FinderV2WidgetProxyView.as_view(source='__bp/'), name='bolt-patterns'),
    re_path(r'sr$', FinderV2WidgetProxyView.as_view(source='search/by_rim/'), name='search-by-rim'),
]

# Default API endpoints (for calc widgets and other non-finder widgets)
# These endpoints use v2 API via WidgetProxyView
api_urlpatterns = [
    # Location and market endpoints
    re_path(r'cc$', WidgetProxyView.as_view(source='__cc/'), name='countries'),
    re_path(r'mr$', WidgetProxyView.as_view(source='__mr/'), name='markets'),
    re_path(r'rg$', WidgetProxyView.as_view(source='regions/'), name='regions'),

    # Vehicle search endpoints (by make/model/year)
    re_path(r'mk$', WidgetProxyView.as_view(source='makes/'), name='makes'),
    re_path(r'ml$', WidgetProxyView.as_view(source='models/'), name='models'),
    re_path(r'yr$', WidgetProxyView.as_view(source='years/'), name='years'),
    re_path(r'sm$', WidgetProxyView.as_view(source='search/by_model/'), name='search-by-model'),

    # Tire search endpoints (by tire dimensions)
    re_path(r'tw$', WidgetProxyView.as_view(source='__tw/'), name='tire-widths'),
    re_path(r'ar$', WidgetProxyView.as_view(source='__ar/'), name='aspect-ratios'),
    re_path(r'rd$', WidgetProxyView.as_view(source='__rd/'), name='rim-diameters'),
    re_path(r'st$', WidgetProxyView.as_view(source='search/by_tire/'), name='search-by-tire'),

    # Rim search endpoints (by rim specifications)
    re_path(r'rw$', WidgetProxyView.as_view(source='__rw/'), name='rim-widths'),
    re_path(r'bp$', WidgetProxyView.as_view(source='__bp/'), name='bolt-patterns'),
    re_path(r'sr$', WidgetProxyView.as_view(source='search/by_rim/'), name='search-by-rim'),
]

# Dynamic UUID widget API endpoints that route based on widget type
# These endpoints check the widget type in the database and route accordingly
dynamic_uuid_api_urlpatterns = [
    # Location and market endpoints
    re_path(r'cc$', dynamic_widget_api_view, name='countries'),
    re_path(r'rg$', dynamic_widget_api_view, name='regions'),

    # Vehicle search endpoints (by make/model/year)
    re_path(r'mk$', dynamic_widget_api_view, name='makes'),
    re_path(r'ml$', dynamic_widget_api_view, name='models'),
    re_path(r'yr$', dynamic_widget_api_view, name='years'),
    re_path(r'md$', dynamic_widget_api_view, name='modifications'),  # New for finder-v2
    re_path(r'gn$', dynamic_widget_api_view, name='generations'),    # New for finder-v2 alternative flow
    re_path(r'sm$', dynamic_widget_api_view, name='search-by-model'),

    # Tire search endpoints (by tire dimensions)
    re_path(r'tw$', dynamic_widget_api_view, name='tire-widths'),
    re_path(r'ar$', dynamic_widget_api_view, name='aspect-ratios'),
    re_path(r'rd$', dynamic_widget_api_view, name='rim-diameters'),
    re_path(r'st$', dynamic_widget_api_view, name='search-by-tire'),

    # Rim search endpoints (by rim specifications)
    re_path(r'rw$', dynamic_widget_api_view, name='rim-widths'),
    re_path(r'bp$', dynamic_widget_api_view, name='bolt-patterns'),
    re_path(r'sr$', dynamic_widget_api_view, name='search-by-rim'),
]

# CSRF token management endpoints
csrf_urlpatterns = []
if CSRF_VIEWS_AVAILABLE:
    csrf_urlpatterns = [
        path('refresh-token/', CSRFTokenRefreshView.as_view(), name='csrf-refresh'),
        path('validate-token/', CSRFTokenValidateView.as_view(), name='csrf-validate'),
        path('verify-human/', VerifyHumanView.as_view(), name='verify-human'),
        path('fingerprint-status/', FingerprintStatusView.as_view(), name='fingerprint-status'),
        path('request-challenge/', RequestChallengeView.as_view(), name='request-challenge'),
        path('verify-challenge/', VerifyChallengeView.as_view(), name='verify-challenge'),
    ]

# Main URL patterns - ORDER IS CRITICAL
# These patterns are processed in order, so more specific patterns must come first
urlpatterns = [
    # CSRF token management endpoints (if available)
    # Pattern: /widget/api/refresh-token/ → CSRFTokenRefreshView
    re_path(r'^api/', include(csrf_urlpatterns)),
    
    # Finder widget specific API URLs (use v1 API endpoints)
    # Pattern: /widget/finder/api/mk → FinderWidgetProxyView
    re_path(r'^(?P<widget_slug>finder)/api/', include(finder_api_urlpatterns)),

    # Finder-v2 widget specific API URLs (use v2 API endpoints)
    # Pattern: /widget/finder-v2/api/mk → FinderV2WidgetProxyView
    re_path(r'^(?P<widget_slug>finder-v2)/api/', include(finder_v2_api_urlpatterns)),

    # Calc widget specific API URLs (use default v2 API endpoints)
    # Pattern: /widget/calc/api/mk → WidgetProxyView
    re_path(r'^(?P<widget_slug>calc)/api/', include(api_urlpatterns)),

    # UUID widget specific API URLs (dynamic routing based on widget type)
    # Pattern: /widget/abc123.../api/mk → dynamic_widget_api_view → FinderWidgetProxyView or FinderV2WidgetProxyView or WidgetProxyView
    re_path(r'^(?P<widget_slug>[a-z0-9]{32})/api/', include(dynamic_uuid_api_urlpatterns)),

    # Global API URLs (for /widget/api/... patterns)
    # Pattern: /widget/api/mk → WidgetProxyView
    # This is a fallback for direct API access without widget slug
    re_path(r'^api/', include(api_urlpatterns)),
]
