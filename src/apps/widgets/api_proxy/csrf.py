"""
Enhanced CSRF Protection for Widget API

This module provides enhanced CSRF protection with session binding,
token rotation, and multi-factor validation for widget API requests.
"""

import hashlib
import hmac
import time
import secrets
import logging
from typing import Dict, Optional, Tuple

from django.conf import settings
from django.core.cache import caches
from django.http import HttpRequest

# Use a specific cache for CSRF tokens
# Falls back to default cache if csrf_tokens cache doesn't exist
try:
    cache = caches['csrf_tokens']
except:
    try:
        # Try to use the api_proxy_throttle cache which uses LocMemCache
        cache = caches['api_proxy_throttle']
    except:
        # Fall back to default cache
        from django.core.cache import cache

logger = logging.getLogger(__name__)


class EnhancedCSRFProtection:
    """
    Enhanced CSRF protection with session binding and rotation.
    
    Features:
    - Session-based token generation
    - Automatic token rotation
    - IP validation with mobile network support
    - Rate limiting per token
    - Multi-factor validation
    """
    
    # Token configuration
    TOKEN_LIFETIME = 3600  # 1 hour
    TOKEN_ROTATION = 300   # 5 minutes for fresh token
    TOKEN_LENGTH = 32      # Token string length
    MAX_REQUESTS_PER_TOKEN = 100  # Rate limit per token
    
    # Cache key prefixes
    CACHE_PREFIX_TOKEN = "csrf_token"
    CACHE_PREFIX_USAGE = "csrf_usage"
    
    @classmethod
    def generate_token(cls, request: HttpRequest) -> str:
        """
        Generate a secure CSRF token with multiple factors.
        
        Args:
            request: Django HttpRequest object
            
        Returns:
            str: Generated CSRF token (32 characters)
        """
        try:
            # Ensure session exists
            session_key = cls._ensure_session(request)
            
            # Collect entropy sources
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            remote_addr = cls._get_client_ip(request)
            timestamp = int(time.time() // cls.TOKEN_ROTATION)
            
            # Add widget-specific context if available
            widget_uuid = ''
            if hasattr(request, 'config') and request.config:
                widget_uuid = str(request.config.uuid)
            elif hasattr(request, 'widget_uuid'):
                widget_uuid = str(request.widget_uuid)
            
            # Generate token with HMAC
            secret_key = settings.SECRET_KEY.encode()
            message = f"{session_key}:{user_agent}:{remote_addr}:{timestamp}:{widget_uuid}"
            
            token_hash = hmac.new(
                secret_key,
                message.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # Store token metadata for validation
            cache_key = f"{cls.CACHE_PREFIX_TOKEN}:{token_hash[:16]}"
            metadata = {
                'session': session_key,
                'ip': remote_addr,
                'user_agent_hash': hashlib.md5(user_agent.encode()).hexdigest()[:16],
                'created': time.time(),
                'widget': widget_uuid,
                'rotation_count': 0
            }
            
            cache.set(cache_key, metadata, cls.TOKEN_LIFETIME)
            
            # Log token generation (debug mode only)
            if cls._is_debug_mode():
                logger.debug(f"Generated CSRF token for session {session_key[:8]}...")
            
            return token_hash[:cls.TOKEN_LENGTH]
            
        except Exception as e:
            logger.error(f"Failed to generate CSRF token: {e}")
            # Fallback to random token if generation fails
            return secrets.token_hex(cls.TOKEN_LENGTH // 2)
    
    @classmethod
    def validate_token(cls, request: HttpRequest, token: str) -> bool:
        """
        Validate CSRF token with multiple checks.
        
        Args:
            request: Django HttpRequest object
            token: CSRF token to validate
            
        Returns:
            bool: True if token is valid, False otherwise
        """
        try:
            # Basic token format validation
            if not token or len(token) != cls.TOKEN_LENGTH:
                cls._log_validation_failure(request, "Invalid token format")
                return False
            
            # Retrieve token metadata
            cache_key = f"{cls.CACHE_PREFIX_TOKEN}:{token[:16]}"
            metadata = cache.get(cache_key)
            
            if not metadata:
                cls._log_validation_failure(request, "Token not found")
                return False
            
            # Validate session
            current_session = request.session.session_key
            if metadata['session'] != current_session:
                cls._log_validation_failure(request, "Session mismatch")
                return False
            
            # Validate IP (with flexibility for mobile networks)
            client_ip = cls._get_client_ip(request)
            if not cls._validate_ip(metadata['ip'], client_ip):
                cls._log_validation_failure(request, f"IP mismatch: {metadata['ip']} vs {client_ip}")
                return False
            
            # Validate User-Agent hash (allows minor UA changes)
            current_ua_hash = hashlib.md5(
                request.META.get('HTTP_USER_AGENT', '').encode()
            ).hexdigest()[:16]
            if metadata.get('user_agent_hash') and metadata['user_agent_hash'] != current_ua_hash:
                # Log but don't fail - UAs can change legitimately
                logger.info(f"User-Agent changed for token {token[:8]}...")
            
            # Check token age
            token_age = time.time() - metadata['created']
            if token_age > cls.TOKEN_LIFETIME:
                cls._log_validation_failure(request, f"Token expired (age: {token_age}s)")
                return False
            
            # Rate limit token usage
            if not cls._check_rate_limit(token):
                cls._log_validation_failure(request, "Token rate limit exceeded")
                return False
            
            # Update last used timestamp
            metadata['last_used'] = time.time()
            cache.set(cache_key, metadata, cls.TOKEN_LIFETIME - int(token_age))
            
            return True
            
        except Exception as e:
            logger.error(f"Token validation error: {e}")
            return False
    
    @classmethod
    def rotate_token(cls, request: HttpRequest, old_token: str) -> Optional[str]:
        """
        Rotate CSRF token while maintaining session binding.
        
        Note: If the current token is still fresh (within rotation window),
        the same token may be returned. This is intentional to avoid
        unnecessary token churn.
        
        Args:
            request: Django HttpRequest object
            old_token: Current token to rotate
            
        Returns:
            Optional[str]: New or current token if successful, None otherwise
        """
        try:
            # Validate old token first
            if not cls.validate_token(request, old_token):
                return None
            
            # Get old token metadata
            old_cache_key = f"{cls.CACHE_PREFIX_TOKEN}:{old_token[:16]}"
            metadata = cache.get(old_cache_key)
            
            if not metadata:
                return None
            
            # Check rotation limit (max 10 rotations per token family)
            rotation_count = metadata.get('rotation_count', 0)
            if rotation_count >= 10:
                logger.warning(f"Token rotation limit exceeded for session {metadata['session'][:8]}...")
                return None
            
            # Check if token is still fresh (within rotation window)
            token_age = time.time() - metadata.get('created', 0)
            if token_age < cls.TOKEN_ROTATION:
                # Token is still fresh, return it as-is
                # This is not a failed rotation, just unnecessary
                logger.debug(f"Token still fresh ({token_age:.1f}s old), returning same token")
                return old_token
            
            # Generate new token (will be different due to timestamp change)
            new_token = cls.generate_token(request)
            
            # If somehow we got the same token (edge case at window boundary), that's OK
            if new_token == old_token:
                logger.debug("Generated same token at rotation window boundary")
                return new_token
            
            # Update rotation count in new token metadata
            new_cache_key = f"{cls.CACHE_PREFIX_TOKEN}:{new_token[:16]}"
            new_metadata = cache.get(new_cache_key)
            if new_metadata:
                new_metadata['rotation_count'] = rotation_count + 1
                new_metadata['rotated_from'] = old_token[:8]
                cache.set(new_cache_key, new_metadata, cls.TOKEN_LIFETIME)
            
            # Invalidate old token with grace period (30 seconds)
            metadata['rotated'] = True
            metadata['rotated_to'] = new_token[:8]
            cache.set(old_cache_key, metadata, 30)
            
            logger.info(f"Token rotated successfully for session {metadata['session'][:8]}...")
            
            return new_token
            
        except Exception as e:
            logger.error(f"Token rotation error: {e}")
            return None
    
    @classmethod
    def _ensure_session(cls, request: HttpRequest) -> str:
        """
        Ensure session exists and return session key.
        
        Args:
            request: Django HttpRequest object
            
        Returns:
            str: Session key
        """
        if not request.session.session_key:
            request.session.create()
        return request.session.session_key
    
    @classmethod
    def _get_client_ip(cls, request: HttpRequest) -> str:
        """
        Get real client IP from request.
        
        Handles proxy headers and load balancers.
        
        Args:
            request: Django HttpRequest object
            
        Returns:
            str: Client IP address
        """
        # Check for proxy headers in order of preference
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            # Take first IP from comma-separated list
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            # Check other common proxy headers
            ip = (request.META.get('HTTP_X_REAL_IP') or
                  request.META.get('HTTP_CLIENT_IP') or
                  request.META.get('REMOTE_ADDR', ''))
        
        return ip
    
    @classmethod
    def _validate_ip(cls, stored_ip: str, current_ip: str) -> bool:
        """
        Validate IP with flexibility for mobile networks.
        
        Allows same /24 subnet for mobile networks that may change IPs.
        
        Args:
            stored_ip: Originally stored IP address
            current_ip: Current request IP address
            
        Returns:
            bool: True if IPs match or are in same subnet
        """
        # Exact match
        if stored_ip == current_ip:
            return True
        
        # Allow same /24 subnet for mobile networks
        try:
            stored_parts = stored_ip.split('.')[:3]
            current_parts = current_ip.split('.')[:3]
            
            if len(stored_parts) == 3 and len(current_parts) == 3:
                return stored_parts == current_parts
        except:
            pass
        
        return False
    
    @classmethod
    def _check_rate_limit(cls, token: str) -> bool:
        """
        Check and update rate limit for token usage.
        
        Args:
            token: CSRF token
            
        Returns:
            bool: True if within rate limit, False otherwise
        """
        usage_key = f"{cls.CACHE_PREFIX_USAGE}:{token}"
        usage_count = cache.get(usage_key, 0)
        
        if usage_count >= cls.MAX_REQUESTS_PER_TOKEN:
            return False
        
        # Increment usage count with 60 second TTL
        cache.set(usage_key, usage_count + 1, 60)
        
        return True
    
    @classmethod
    def _log_validation_failure(cls, request: HttpRequest, reason: str):
        """
        Log CSRF validation failure for monitoring.
        
        Args:
            request: Django HttpRequest object
            reason: Failure reason
        """
        if cls._is_debug_mode():
            client_ip = cls._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')[:50]
            logger.debug(f"CSRF validation failed: {reason} | IP: {client_ip} | UA: {user_agent}")
    
    @classmethod
    def _is_debug_mode(cls) -> bool:
        """
        Check if CSRF debug mode is enabled.
        
        Returns:
            bool: True if debug mode is enabled
        """
        csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
        return csrf_settings.get('debug_csrf_validation', False)


class CSRFTokenManager:
    """
    Manager class for CSRF token operations in views.
    
    Provides simplified interface for token management in Django views.
    """
    
    @staticmethod
    def get_or_create_token(request: HttpRequest) -> str:
        """
        Get existing token or create new one if needed.
        
        Args:
            request: Django HttpRequest object
            
        Returns:
            str: CSRF token
        """
        # Check for existing token in session
        token_key = 'widget_csrf_token'
        token = request.session.get(token_key)
        
        # Validate existing token
        if token and EnhancedCSRFProtection.validate_token(request, token):
            return token
        
        # Generate new token
        token = EnhancedCSRFProtection.generate_token(request)
        request.session[token_key] = token
        
        return token
    
    @staticmethod
    def validate_request_token(request: HttpRequest) -> bool:
        """
        Validate CSRF token from request headers.
        
        Args:
            request: Django HttpRequest object
            
        Returns:
            bool: True if valid, False otherwise
        """
        # Get token from header
        token = request.META.get('HTTP_X_CSRF_TOKEN', '')
        
        if not token:
            # Check for token in POST data as fallback
            token = request.POST.get('csrftoken', '')
        
        return EnhancedCSRFProtection.validate_token(request, token)
    
    @staticmethod
    def rotate_request_token(request: HttpRequest) -> Optional[str]:
        """
        Rotate token from request.
        
        Args:
            request: Django HttpRequest object
            
        Returns:
            Optional[str]: New token if successful
        """
        # Get current token
        old_token = request.META.get('HTTP_X_CSRF_TOKEN', '')
        
        if not old_token:
            old_token = request.session.get('widget_csrf_token', '')
        
        if not old_token:
            return None
        
        # Rotate token
        new_token = EnhancedCSRFProtection.rotate_token(request, old_token)
        
        if new_token:
            request.session['widget_csrf_token'] = new_token
        
        return new_token