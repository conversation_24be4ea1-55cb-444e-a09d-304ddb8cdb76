"""
CSRF Token Management Views

Provides endpoints for enhanced CSRF token operations including
token generation, rotation, and validation for widget security.
"""

import json
import logging
import time

from django.conf import settings
from django.http import JsonResponse, HttpResponseBadRequest
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from src.apps.widgets.api_proxy.csrf import CSRFTokenManager, EnhancedCSRFProtection
from src.apps.widgets.api_proxy.fingerprint import BrowserFingerprint
from src.apps.widgets.api_proxy.challenge import ChallengeSystem

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class CSRFTokenRefreshView(View):
    """
    View for refreshing CSRF tokens for widgets.
    
    This endpoint allows widgets to request new CSRF tokens
    or rotate existing ones while maintaining session binding.
    """
    
    def post(self, request, *args, **kwargs):
        """
        Handle token refresh request.
        
        Request body (JSON):
        {
            "widget_uuid": "widget-uuid",
            "old_token": "current-token" (optional)
        }
        
        Response:
        {
            "token": "new-csrf-token",
            "expires_in": 3600
        }
        """
        try:
            # Parse request body
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return HttpResponseBadRequest(
                    json.dumps({"error": "Invalid JSON"}),
                    content_type="application/json"
                )
            
            # Get widget UUID
            widget_uuid = data.get('widget_uuid', '')
            if widget_uuid:
                request.widget_uuid = widget_uuid
            
            # Check for old token (for rotation)
            old_token = data.get('old_token', '')
            
            if old_token:
                # Rotate existing token
                new_token = CSRFTokenManager.rotate_request_token(request)
                if not new_token:
                    # Rotation failed, generate new token
                    new_token = CSRFTokenManager.get_or_create_token(request)
            else:
                # Generate new token
                new_token = CSRFTokenManager.get_or_create_token(request)
            
            # Return token with metadata
            response = JsonResponse({
                "token": new_token,
                "expires_in": EnhancedCSRFProtection.TOKEN_LIFETIME,
                "rotation_interval": EnhancedCSRFProtection.TOKEN_ROTATION
            })
            
            # Add CORS headers for widget access
            response["Access-Control-Allow-Origin"] = "*"
            response["Access-Control-Allow-Credentials"] = "true"
            
            return response
            
        except Exception as e:
            logger.error(f"Token refresh error: {e}")
            return HttpResponseBadRequest(
                json.dumps({"error": "Token refresh failed"}),
                content_type="application/json"
            )
    
    def options(self, request, *args, **kwargs):
        """
        Handle CORS preflight request.
        """
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, X-CSRF-TOKEN"
        response["Access-Control-Allow-Credentials"] = "true"
        return response


class CSRFTokenValidateView(View):
    """
    View for validating CSRF tokens (debugging/testing).
    
    This endpoint is only available in debug mode.
    """
    
    def post(self, request, *args, **kwargs):
        """
        Validate a CSRF token.
        
        Request body (JSON):
        {
            "token": "csrf-token-to-validate"
        }
        
        Response:
        {
            "valid": true/false,
            "reason": "validation failure reason" (if invalid)
        }
        """
        # Only allow in debug mode
        csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
        if not csrf_settings.get('debug_csrf_validation', False):
            return HttpResponseBadRequest(
                json.dumps({"error": "Endpoint not available"}),
                content_type="application/json"
            )
        
        try:
            # Parse request body
            data = json.loads(request.body)
            token = data.get('token', '')
            
            # Validate token
            is_valid = EnhancedCSRFProtection.validate_token(request, token)
            
            response_data = {"valid": is_valid}
            if not is_valid:
                response_data["reason"] = "Token validation failed"
            
            return JsonResponse(response_data)
            
        except Exception as e:
            logger.error(f"Token validation error: {e}")
            return HttpResponseBadRequest(
                json.dumps({"error": "Validation failed"}),
                content_type="application/json"
            )


@method_decorator(csrf_exempt, name='dispatch')
class VerifyHumanView(View):
    """
    View for human verification with proof-of-work challenge.
    
    This endpoint validates browser fingerprints and challenge solutions
    to ensure requests are coming from real humans, not bots.
    """
    
    def post(self, request, *args, **kwargs):
        """
        Verify human user with fingerprint and challenge.
        
        Request body (JSON):
        {
            "features": {browser feature object},
            "solution": {
                "challenge": "random-string",
                "nonce": 12345,
                "hash": "0000abc123...",
                "duration": 1234,
                "difficulty": 4
            },
            "widget_uuid": "widget-uuid"
        }
        
        Response:
        {
            "success": true/false,
            "token": "verification-token" (if successful),
            "reason": "failure reason" (if failed)
        }
        """
        try:
            # Parse request body
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return HttpResponseBadRequest(
                    json.dumps({"error": "Invalid JSON"}),
                    content_type="application/json"
                )
            
            # Validate browser fingerprint
            fingerprint_result = BrowserFingerprint.validate(request)
            
            # Check if fingerprint is blocked
            if BrowserFingerprint.is_fingerprint_blocked(fingerprint_result['fingerprint']):
                logger.warning(f"Blocked fingerprint attempted verification: {fingerprint_result['fingerprint'][:8]}...")
                return JsonResponse({
                    "success": False,
                    "reason": "Access temporarily restricted"
                }, status=403)
            
            # Check if detected as bot
            if fingerprint_result['is_bot']:
                logger.warning(f"Bot detected during human verification: score={fingerprint_result['suspicious_score']}")
                # Could trigger CAPTCHA here instead of blocking
                return JsonResponse({
                    "success": False,
                    "reason": "Additional verification required",
                    "captcha_required": True
                }, status=403)
            
            # Validate challenge solution (simplified for now)
            solution = data.get('solution', {})
            if solution:
                # Basic validation - in production, verify the hash
                if solution.get('duration', 0) < 10:  # Too fast, likely automated
                    logger.warning("Challenge solved too quickly, possible automation")
                    return JsonResponse({
                        "success": False,
                        "reason": "Challenge validation failed"
                    }, status=400)
            
            # Generate verification token
            import secrets
            verification_token = secrets.token_urlsafe(32)
            
            # Store verification status in cache
            from django.core.cache import cache
            cache_key = f"human_verified:{fingerprint_result['fingerprint']}"
            cache.set(cache_key, {
                'token': verification_token,
                'verified_at': time.time(),
                'widget_uuid': data.get('widget_uuid', '')
            }, 3600)  # Valid for 1 hour
            
            logger.info(f"Human verification successful for fingerprint: {fingerprint_result['fingerprint'][:8]}...")
            
            response = JsonResponse({
                "success": True,
                "token": verification_token
            })
            
            # Add CORS headers
            response["Access-Control-Allow-Origin"] = "*"
            response["Access-Control-Allow-Credentials"] = "true"
            
            return response
            
        except Exception as e:
            logger.error(f"Human verification error: {e}")
            return HttpResponseBadRequest(
                json.dumps({"error": "Verification failed"}),
                content_type="application/json"
            )
    
    def options(self, request, *args, **kwargs):
        """
        Handle CORS preflight request.
        """
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, X-Client-Features, X-Challenge-Token"
        response["Access-Control-Allow-Credentials"] = "true"
        return response


class FingerprintStatusView(View):
    """
    View for checking fingerprint status (debugging/testing).
    
    This endpoint is only available in debug mode.
    """
    
    def get(self, request, *args, **kwargs):
        """
        Get fingerprint status for current request.
        
        Response:
        {
            "fingerprint": "abc123...",
            "is_bot": false,
            "suspicious_score": 0,
            "request_count": 5,
            "is_blocked": false
        }
        """
        # Only allow in debug mode
        csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
        if not csrf_settings.get('debug_csrf_validation', False):
            return HttpResponseBadRequest(
                json.dumps({"error": "Endpoint not available"}),
                content_type="application/json"
            )
        
        try:
            # Get fingerprint validation result
            result = BrowserFingerprint.validate(request)
            
            # Add blocked status
            result['is_blocked'] = BrowserFingerprint.is_fingerprint_blocked(result['fingerprint'])
            
            # Add human verification status
            from django.core.cache import cache
            cache_key = f"human_verified:{result['fingerprint']}"
            verification = cache.get(cache_key)
            result['is_human_verified'] = bool(verification)
            
            return JsonResponse(result)
            
        except Exception as e:
            logger.error(f"Fingerprint status error: {e}")
            return HttpResponseBadRequest(
                json.dumps({"error": "Status check failed"}),
                content_type="application/json"
            )


@method_decorator(csrf_exempt, name='dispatch')
class RequestChallengeView(View):
    """
    View for requesting a proof-of-work challenge.
    
    This endpoint provides challenges specifically for protecting
    the /search/by_model/ endpoint from automated scraping.
    """
    
    def post(self, request, *args, **kwargs):
        """
        Request a new challenge to solve.
        
        Request body (JSON):
        {
            "widget_uuid": "widget-uuid"
        }
        
        Response:
        {
            "challenge": "random-string",
            "difficulty": 4,
            "algorithm": "sha256",
            "target": "0000"
        }
        """
        try:
            # Parse request body
            try:
                data = json.loads(request.body) if request.body else {}
            except json.JSONDecodeError:
                data = {}
            
            # Get browser fingerprint
            fingerprint_result = BrowserFingerprint.validate(request)
            fingerprint = fingerprint_result['fingerprint']
            
            # Track request rate for this fingerprint
            ChallengeSystem.track_request_rate(fingerprint)
            
            # Create challenge with dynamic difficulty
            challenge = ChallengeSystem.create_challenge(request, fingerprint)
            
            response = JsonResponse(challenge)
            
            # Add CORS headers
            response["Access-Control-Allow-Origin"] = "*"
            response["Access-Control-Allow-Credentials"] = "true"
            
            return response
            
        except Exception as e:
            logger.error(f"Challenge request error: {e}")
            return HttpResponseBadRequest(
                json.dumps({"error": "Failed to create challenge"}),
                content_type="application/json"
            )
    
    def options(self, request, *args, **kwargs):
        """Handle CORS preflight request."""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, X-Client-Features"
        response["Access-Control-Allow-Credentials"] = "true"
        return response


@method_decorator(csrf_exempt, name='dispatch')
class VerifyChallengeView(View):
    """
    View for verifying a solved challenge.
    
    Returns a challenge token that can be used for protected API calls.
    """
    
    def post(self, request, *args, **kwargs):
        """
        Verify a solved challenge.
        
        Request body (JSON):
        {
            "solution": {
                "challenge": "random-string",
                "nonce": 12345,
                "hash": "0000abc123...",
                "duration": 1234,
                "difficulty": 4
            },
            "widget_uuid": "widget-uuid"
        }
        
        Response:
        {
            "success": true/false,
            "token": "challenge-token" (if successful),
            "reason": "failure reason" (if failed)
        }
        """
        try:
            # Parse request body
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return HttpResponseBadRequest(
                    json.dumps({"error": "Invalid JSON"}),
                    content_type="application/json"
                )
            
            # Get browser fingerprint
            fingerprint_result = BrowserFingerprint.validate(request)
            fingerprint = fingerprint_result['fingerprint']
            
            # Verify solution
            solution = data.get('solution', {})
            is_valid, result = ChallengeSystem.verify_solution(request, fingerprint, solution)
            
            if is_valid:
                response = JsonResponse({
                    "success": True,
                    "token": result,
                    "max_uses": ChallengeSystem.MAX_USES_PER_CHALLENGE,
                    "expires_in": ChallengeSystem.CHALLENGE_LIFETIME
                })
            else:
                response = JsonResponse({
                    "success": False,
                    "reason": result
                }, status=400)
            
            # Add CORS headers
            response["Access-Control-Allow-Origin"] = "*"
            response["Access-Control-Allow-Credentials"] = "true"
            
            return response
            
        except Exception as e:
            logger.error(f"Challenge verification error: {e}")
            return HttpResponseBadRequest(
                json.dumps({"error": "Verification failed"}),
                content_type="application/json"
            )
    
    def options(self, request, *args, **kwargs):
        """Handle CORS preflight request."""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, X-Client-Features"
        response["Access-Control-Allow-Credentials"] = "true"
        return response