"""
Proof-of-Work Challenge System for Widget Protection

This module implements a proof-of-work challenge system specifically designed
to protect the /search/by_model/ endpoint from automated scraping.
"""
import hashlib
import json
import secrets
import time
from typing import Dict, Optional, Tuple
from django.core.cache import cache
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class ChallengeSystem:
    """
    Proof-of-work challenge system for protecting high-value API endpoints.
    
    Specifically designed to protect /search/by_model/ endpoint while allowing
    normal access to other widget endpoints.
    
    Configuration is loaded from Django settings WIDGET_PROTECTION_SETTINGS.
    """
    
    # Load configuration from Django settings with fallbacks
    _settings = getattr(settings, 'WIDGET_PROTECTION_SETTINGS', {})
    _challenge_settings = _settings.get('challenge', {})
    
    # Challenge configuration from settings
    ENABLED = _settings.get('enabled', True) and _challenge_settings.get('enabled', True)
    BASE_DIFFICULTY = _challenge_settings.get('base_difficulty', 3)
    MAX_DIFFICULTY = _challenge_settings.get('max_difficulty', 6)
    CHALLENGE_LIFETIME = _challenge_settings.get('token_lifetime', 3600)
    MAX_USES_PER_CHALLENGE = _challenge_settings.get('max_uses_per_token', 10)
    SOLVE_TIMEOUT = _challenge_settings.get('solve_timeout', 300)
    PROTECTED_ENDPOINTS = _challenge_settings.get('protected_endpoints', ['search/by_model/', 'sm'])
    ALLOW_DEV_BYPASS = _challenge_settings.get('allow_development_bypass', True)
    
    # Rate thresholds for difficulty adjustment
    RATE_THRESHOLDS = _challenge_settings.get('rate_thresholds', {
        1: 3,   # <1 req/min: difficulty 3
        5: 4,   # 1-5 req/min: difficulty 4  
        10: 5,  # 5-10 req/min: difficulty 5
        999: 6  # >10 req/min: difficulty 6
    })
    
    @classmethod
    def create_challenge(cls, request, fingerprint: str) -> Dict[str, any]:
        """
        Create a new challenge for the client to solve.
        
        Args:
            request: Django request object
            fingerprint: Browser fingerprint
            
        Returns:
            dict: Challenge parameters including difficulty
        """
        # Calculate difficulty based on request rate
        difficulty = cls._calculate_difficulty(fingerprint)
        
        # Generate random challenge string
        challenge = secrets.token_urlsafe(16)
        
        # Store challenge in cache for verification
        challenge_key = f"challenge:{fingerprint}:{challenge}"
        cache_data = {
            'created': time.time(),
            'difficulty': difficulty,
            'fingerprint': fingerprint,
            'ip': cls._get_client_ip(request)
        }
        cache.set(challenge_key, cache_data, cls.SOLVE_TIMEOUT)  # Use configured timeout
        
        # Debug logging
        logger.debug(f"Stored challenge {challenge[:8]}... for fingerprint {fingerprint[:8]}... with key {challenge_key}")
        
        logger.info(f"Created challenge for {fingerprint[:8]}... with difficulty {difficulty}")
        
        return {
            'challenge': challenge,
            'difficulty': difficulty,
            'algorithm': 'sha256',
            'target': '0' * difficulty  # Target prefix
        }
    
    @classmethod
    def verify_solution(cls, request, fingerprint: str, solution: Dict) -> Tuple[bool, Optional[str]]:
        """
        Verify a challenge solution from the client.
        
        Args:
            request: Django request object
            fingerprint: Browser fingerprint
            solution: Solution dict with challenge, nonce, hash, duration
            
        Returns:
            tuple: (is_valid, challenge_token or error_message)
        """
        challenge = solution.get('challenge', '')
        nonce = solution.get('nonce', 0)
        client_hash = solution.get('hash', '')
        duration = solution.get('duration', 0)
        difficulty = solution.get('difficulty', 0)
        
        # Retrieve stored challenge
        challenge_key = f"challenge:{fingerprint}:{challenge}"
        logger.debug(f"Looking for challenge with key: {challenge_key}")
        stored_challenge = cache.get(challenge_key)
        
        if not stored_challenge:
            logger.warning(f"Challenge not found or expired for {fingerprint[:8]}... with key {challenge_key}")
            return False, "Challenge expired or invalid"
        
        # Verify difficulty matches
        if difficulty != stored_challenge['difficulty']:
            logger.warning(f"Difficulty mismatch for {fingerprint[:8]}...")
            return False, "Invalid difficulty"
        
        # Verify the hash
        attempt = f"{challenge}:{nonce}"
        computed_hash = hashlib.sha256(attempt.encode()).hexdigest()
        
        if computed_hash != client_hash:
            logger.warning(f"Hash mismatch for {fingerprint[:8]}...")
            return False, "Invalid hash"
        
        # Verify hash meets difficulty requirement
        target_prefix = '0' * difficulty
        if not computed_hash.startswith(target_prefix):
            logger.warning(f"Hash doesn't meet difficulty for {fingerprint[:8]}...")
            return False, "Hash doesn't meet difficulty requirement"
        
        # Check solve duration (too fast might be pre-computed)
        if duration < 10:  # Less than 10ms is suspicious
            logger.warning(f"Challenge solved too quickly ({duration}ms) for {fingerprint[:8]}...")
            return False, "Challenge solved suspiciously fast"
        
        # Clean up used challenge
        cache.delete(challenge_key)
        
        # Generate challenge token for API access
        challenge_token = secrets.token_urlsafe(32)
        
        # Store challenge token with usage tracking
        token_key = f"challenge_token:{challenge_token}"
        cache.set(token_key, {
            'fingerprint': fingerprint,
            'created': time.time(),
            'uses': 0,
            'max_uses': cls.MAX_USES_PER_CHALLENGE,
            'ip': cls._get_client_ip(request)
        }, cls.CHALLENGE_LIFETIME)
        
        logger.info(f"Challenge verified for {fingerprint[:8]}..., token issued")
        
        return True, challenge_token
    
    @classmethod
    def validate_token(cls, request, token: str) -> bool:
        """
        Validate a challenge token for API access.
        
        Args:
            request: Django request object
            token: Challenge token
            
        Returns:
            bool: True if token is valid and has uses remaining
        """
        if not token:
            return False
        
        token_key = f"challenge_token:{token}"
        token_data = cache.get(token_key)
        
        if not token_data:
            logger.debug(f"Challenge token not found or expired")
            return False
        
        # Check usage limit
        if token_data['uses'] >= token_data['max_uses']:
            logger.info(f"Challenge token exceeded usage limit ({token_data['uses']} uses)")
            cache.delete(token_key)  # Clean up exhausted token
            return False
        
        # Increment usage counter
        token_data['uses'] += 1
        cache.set(token_key, token_data, cls.CHALLENGE_LIFETIME)
        
        logger.debug(f"Challenge token validated (use {token_data['uses']}/{token_data['max_uses']})")
        
        return True
    
    @classmethod
    def requires_challenge(cls, request, endpoint: str) -> bool:
        """
        Check if an endpoint requires challenge verification.
        
        Args:
            request: Django request object
            endpoint: API endpoint path
            
        Returns:
            bool: True if challenge is required
        """
        # Check if challenge system is enabled
        if not cls.ENABLED:
            logger.debug(f"Challenge system disabled, skipping for {endpoint}")
            return False
        
        # Check if endpoint matches protected patterns
        is_protected = False
        for protected in cls.PROTECTED_ENDPOINTS:
            if protected in endpoint:
                is_protected = True
                break
        
        if not is_protected:
            logger.debug(f"Endpoint {endpoint} is not protected - no challenge required")
            return False
        
        # Get fingerprint from request (if available)
        fingerprint = cls._get_fingerprint_from_request(request)
        if not fingerprint:
            # No fingerprint yet, allow first requests
            logger.debug(f"No fingerprint available yet - allowing request")
            return False
        
        # Check request count for this fingerprint
        request_count = cls.get_request_count(fingerprint)
        
        # Load free requests threshold from settings
        free_requests = cls._challenge_settings.get('free_requests_before_challenge', 10)
        
        if request_count < free_requests:
            logger.debug(f"Request #{request_count + 1} for {fingerprint[:8]}... - still within free tier ({free_requests} free requests)")
            # Increment request count for tracking
            cls.track_request(fingerprint, endpoint)
            return False
        
        logger.info(f"Request #{request_count + 1} for {fingerprint[:8]}... exceeds free tier ({free_requests}) - challenge required")
        return True
    
    @classmethod
    def _calculate_difficulty(cls, fingerprint: str) -> int:
        """
        Calculate challenge difficulty based on request rate.
        
        Args:
            fingerprint: Browser fingerprint
            
        Returns:
            int: Difficulty level (3-6)
        """
        # Get request rate for this fingerprint
        rate_key = f"request_rate:{fingerprint}"
        rate_data = cache.get(rate_key, {'count': 0, 'window_start': time.time()})
        
        # Calculate requests per minute
        time_elapsed = time.time() - rate_data.get('window_start', time.time())
        if time_elapsed > 0:
            requests_per_minute = (rate_data['count'] / time_elapsed) * 60
        else:
            requests_per_minute = 0
        
        # Determine difficulty based on rate
        difficulty = cls.BASE_DIFFICULTY
        for threshold, diff in cls.RATE_THRESHOLDS.items():
            if requests_per_minute <= threshold:
                difficulty = diff
                break
        
        logger.debug(f"Calculated difficulty {difficulty} for rate {requests_per_minute:.1f} req/min")
        
        return min(difficulty, cls.MAX_DIFFICULTY)
    
    @classmethod
    def track_request_rate(cls, fingerprint: str):
        """
        Track request rate for difficulty adjustment.
        
        Args:
            fingerprint: Browser fingerprint
        """
        rate_key = f"request_rate:{fingerprint}"
        rate_data = cache.get(rate_key, {'count': 0, 'window_start': time.time()})
        
        # Reset window if it's been more than 5 minutes
        if time.time() - rate_data['window_start'] > 300:
            rate_data = {'count': 0, 'window_start': time.time()}
        
        rate_data['count'] += 1
        cache.set(rate_key, rate_data, 300)  # 5-minute window
    
    @classmethod
    def track_request(cls, fingerprint: str, endpoint: str):
        """
        Track request count for free tier tracking.
        
        Args:
            fingerprint: Browser fingerprint
            endpoint: API endpoint being accessed
        """
        # Track total request count for this fingerprint
        count_key = f"request_count:{fingerprint}"
        count_data = cache.get(count_key, {'count': 0, 'first_request': time.time()})
        
        count_data['count'] += 1
        count_data['last_request'] = time.time()
        
        # Keep count for 24 hours
        cache.set(count_key, count_data, 86400)
        
        logger.debug(f"Tracked request #{count_data['count']} for {fingerprint[:8]}... on {endpoint}")
    
    @classmethod
    def get_request_count(cls, fingerprint: str) -> int:
        """
        Get current request count for a fingerprint.
        
        Args:
            fingerprint: Browser fingerprint
            
        Returns:
            int: Number of requests made
        """
        count_key = f"request_count:{fingerprint}"
        count_data = cache.get(count_key, {'count': 0})
        return count_data.get('count', 0)
    
    @classmethod
    def _get_fingerprint_from_request(cls, request) -> Optional[str]:
        """
        Extract fingerprint from request if available.
        
        Args:
            request: Django request object
            
        Returns:
            str: Fingerprint hash or None
        """
        try:
            # Try to get fingerprint from header
            features_json = request.META.get('HTTP_X_CLIENT_FEATURES', '')
            if features_json:
                features = json.loads(features_json)
                # Generate a simple hash of the features for identification
                import hashlib
                fingerprint_data = json.dumps(features, sort_keys=True)
                return hashlib.sha256(fingerprint_data.encode()).hexdigest()
            
            # Fallback to IP-based fingerprint
            ip = cls._get_client_ip(request)
            if ip:
                return hashlib.sha256(f"ip:{ip}".encode()).hexdigest()
                
        except Exception as e:
            logger.debug(f"Could not extract fingerprint: {e}")
        
        return None
    
    @classmethod
    def _get_client_ip(cls, request) -> str:
        """Get real client IP from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
    
    @classmethod
    def get_challenge_status(cls, token: str) -> Optional[Dict]:
        """
        Get status of a challenge token (for debugging).
        
        Args:
            token: Challenge token
            
        Returns:
            dict: Token status or None
        """
        if not token:
            return None
        
        token_key = f"challenge_token:{token}"
        token_data = cache.get(token_key)
        
        if token_data:
            return {
                'valid': True,
                'uses': token_data['uses'],
                'max_uses': token_data['max_uses'],
                'remaining': token_data['max_uses'] - token_data['uses'],
                'created': token_data['created'],
                'age': time.time() - token_data['created']
            }
        
        return {'valid': False}