"""
Browser fingerprinting for bot detection.

This module provides browser fingerprinting capabilities to detect and prevent
bot access to widget APIs.
"""
import hashlib
import json
import time
from typing import Dict, Any, Optional
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


class BrowserFingerprint:
    """
    Browser fingerprinting for bot detection.
    
    Collects and analyzes browser characteristics to identify bots and scrapers.
    """
    
    FINGERPRINT_TTL = 86400  # 24 hours
    SUSPICIOUS_THRESHOLD = 3  # Score threshold for bot detection
    
    # Known bot patterns in user agents
    BOT_PATTERNS = [
        'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
        'python', 'java/', 'ruby/', 'perl/', 'php/', 'scrapy',
        'headless', 'phantom', 'selenium', 'puppeteer', 'playwright'
    ]
    
    # Expected browser headers
    EXPECTED_HEADERS = [
        'HTTP_ACCEPT',
        'HTTP_ACCEPT_LANGUAGE',
        'HTTP_ACCEPT_ENCODING',
        'HTTP_USER_AGENT'
    ]
    
    @classmethod
    def generate(cls, request) -> str:
        """
        Generate browser fingerprint from request.
        
        Args:
            request: Django request object
            
        Returns:
            str: 32-character fingerprint hash
        """
        features = {
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'accept': request.META.get('HTTP_ACCEPT', ''),
            'accept_language': request.META.get('HTTP_ACCEPT_LANGUAGE', ''),
            'accept_encoding': request.META.get('HTTP_ACCEPT_ENCODING', ''),
            'dnt': request.META.get('HTTP_DNT', ''),
            'connection': request.META.get('HTTP_CONNECTION', ''),
            'sec_fetch_site': request.META.get('HTTP_SEC_FETCH_SITE', ''),
            'sec_fetch_mode': request.META.get('HTTP_SEC_FETCH_MODE', ''),
            'sec_fetch_dest': request.META.get('HTTP_SEC_FETCH_DEST', ''),
            'sec_ch_ua': request.META.get('HTTP_SEC_CH_UA', ''),
            'sec_ch_ua_mobile': request.META.get('HTTP_SEC_CH_UA_MOBILE', ''),
            'sec_ch_ua_platform': request.META.get('HTTP_SEC_CH_UA_PLATFORM', ''),
        }
        
        # Add client-side collected features (sent via headers)
        client_features_header = request.META.get('HTTP_X_CLIENT_FEATURES', '{}')
        try:
            client_features = json.loads(client_features_header)
            features.update(client_features)
        except (json.JSONDecodeError, ValueError):
            logger.debug(f"Invalid client features: {client_features_header}")
        
        # Generate fingerprint hash
        fingerprint_str = json.dumps(features, sort_keys=True)
        fingerprint = hashlib.sha256(fingerprint_str.encode()).hexdigest()[:32]
        
        logger.debug(f"Generated fingerprint: {fingerprint[:8]}... for UA: {features.get('user_agent', '')[:50]}")
        
        return fingerprint
    
    @classmethod
    def validate(cls, request) -> Dict[str, Any]:
        """
        Validate browser fingerprint for bot detection.
        
        Args:
            request: Django request object
            
        Returns:
            dict: Validation result with is_bot flag and suspicious score
        """
        fingerprint = cls.generate(request)
        cache_key = f"fingerprint:{fingerprint}"
        
        # Get or create fingerprint history
        history = cache.get(cache_key, {
            'first_seen': None,
            'request_count': 0,
            'suspicious_score': 0,
            'last_seen': None
        })
        
        # Update history
        current_time = time.time()
        if not history['first_seen']:
            history['first_seen'] = current_time
        history['last_seen'] = current_time
        history['request_count'] += 1
        
        # Calculate suspicious score
        suspicious_indicators = cls._calculate_suspicious_score(request, history)
        history['suspicious_score'] = suspicious_indicators
        
        # Save updated history
        cache.set(cache_key, history, cls.FINGERPRINT_TTL)
        
        # Determine if this is a bot
        is_bot = suspicious_indicators >= cls.SUSPICIOUS_THRESHOLD
        
        if is_bot:
            logger.warning(f"Bot detected - Fingerprint: {fingerprint[:8]}..., Score: {suspicious_indicators}")
        
        return {
            'is_bot': is_bot,
            'suspicious_score': suspicious_indicators,
            'fingerprint': fingerprint,
            'request_count': history['request_count']
        }
    
    @classmethod
    def _calculate_suspicious_score(cls, request, history: Dict[str, Any]) -> int:
        """
        Calculate suspicious score based on various indicators.
        
        Args:
            request: Django request object
            history: Fingerprint history from cache
            
        Returns:
            int: Suspicious score (higher = more likely to be bot)
        """
        suspicious_indicators = 0
        
        # Check 1: Missing expected headers
        missing_headers = 0
        for header in cls.EXPECTED_HEADERS:
            if not request.META.get(header):
                missing_headers += 1
        
        if missing_headers >= 2:
            suspicious_indicators += 1
            logger.debug(f"Missing {missing_headers} expected headers")
        
        # Check 2: Known bot user agent patterns
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        if any(pattern in user_agent for pattern in cls.BOT_PATTERNS):
            suspicious_indicators += 3
            logger.debug(f"Bot pattern detected in user agent: {user_agent[:50]}")
        
        # Check 3: No JavaScript execution (missing client features)
        if not request.META.get('HTTP_X_CLIENT_FEATURES'):
            suspicious_indicators += 2
            logger.debug("No client features - JavaScript not executed")
        
        # Check 4: Suspicious request rate
        if history['request_count'] > 10 and history['first_seen']:
            time_elapsed = time.time() - history['first_seen']
            if time_elapsed > 0:
                requests_per_second = history['request_count'] / time_elapsed
                if requests_per_second > 1:
                    suspicious_indicators += 2
                    logger.debug(f"High request rate: {requests_per_second:.2f} req/s")
        
        # Check 5: Missing referer for API requests
        if '/api/' in request.path and not request.META.get('HTTP_REFERER'):
            suspicious_indicators += 1
            logger.debug("API request without referer")
        
        # Check 6: Suspicious Accept header
        accept_header = request.META.get('HTTP_ACCEPT', '')
        if not accept_header or accept_header == '*/*':
            suspicious_indicators += 1
            logger.debug(f"Suspicious Accept header: {accept_header}")
        
        # Check 7: No Accept-Language header
        if not request.META.get('HTTP_ACCEPT_LANGUAGE'):
            suspicious_indicators += 1
            logger.debug("Missing Accept-Language header")
        
        # Check 8: Command-line tool indicators
        if user_agent.startswith(('curl/', 'wget/', 'python-requests/')):
            suspicious_indicators += 4
            logger.debug(f"Command-line tool detected: {user_agent[:30]}")
        
        return suspicious_indicators
    
    @classmethod
    def is_fingerprint_blocked(cls, fingerprint: str) -> bool:
        """
        Check if a fingerprint is blocked.
        
        Args:
            fingerprint: Fingerprint hash to check
            
        Returns:
            bool: True if fingerprint is blocked
        """
        block_key = f"fingerprint_blocked:{fingerprint}"
        return cache.get(block_key, False)
    
    @classmethod
    def block_fingerprint(cls, fingerprint: str, duration: int = 3600):
        """
        Block a fingerprint temporarily.
        
        Args:
            fingerprint: Fingerprint hash to block
            duration: Block duration in seconds (default: 1 hour)
        """
        block_key = f"fingerprint_blocked:{fingerprint}"
        cache.set(block_key, True, duration)
        logger.info(f"Blocked fingerprint: {fingerprint[:8]}... for {duration} seconds")
    
    @classmethod
    def get_fingerprint_stats(cls, fingerprint: str) -> Optional[Dict[str, Any]]:
        """
        Get statistics for a fingerprint.
        
        Args:
            fingerprint: Fingerprint hash
            
        Returns:
            dict: Fingerprint statistics or None if not found
        """
        cache_key = f"fingerprint:{fingerprint}"
        return cache.get(cache_key)