import fnmatch
from urllib.parse import urlparse

from django.utils import translation
from django.utils.functional import cached_property
from django.views.generic import TemplateView
from django.conf import settings
from django.http import Http404

from src.apps.widgets.common.exceptions import WidgetException, \
    WidgetExceptionResponse, Widget403, Widget404
from ws_live_settings.models import WsLiveSettings


class WidgetView(TemplateView):

    def dispatch(self, request, *args, **kwargs):
        try:
            return super(WidgetView, self).dispatch(request, *args, **kwargs)
        except WidgetException as ex:
            return WidgetExceptionResponse(ex)

    def check_permissions(self, config):
        if config.subscription.deny_iframe():
            raise Widget404(message='Widget Not Found')

        referer = self.request.META.get('HTTP_REFERER')
        if not referer:
            return True

        referer = urlparse(referer)
        if self._is_same_origin_request(referer.hostname):
            return True

        if config.subscription.is_demo:
            raise Widget403(message='This is a demo version '
                                    'of widget configuration')

        if config.subscription.is_trial:
            raise Widget403(message='This is a trial version '
                                    'of widget configuration')

        if config.subscription.is_banned:
            raise Widget403(message='This widget configuration is banned')

        if self.is_allowed_domain(referer.hostname):
            return True

        raise Widget403(message='This site is not authorized '
                                'for widget embedding')

    @cached_property
    def config(self):
        return self.request.config

    def _hostname_match_domain(self, hostname, domain):
        if domain.startswith('*.'):
            simple_domain = domain.replace('*.', '')
            wildcard_domain = domain
        else:
            simple_domain = domain
            wildcard_domain = '*.%s' % domain

        if fnmatch.fnmatch(hostname, domain) or \
                fnmatch.fnmatch(hostname, simple_domain) or \
                fnmatch.fnmatch(hostname, wildcard_domain):
            return True

        return False

    def is_allowed_domain(self, hostname):
        for black_domain in WsLiveSettings.get('WS_DOMAINS_BLACK_LIST',
                                               default=[]):
            if self._hostname_match_domain(hostname, black_domain):
                return False

        for domain in self.config.params['permissions']['domains']:
            if self._hostname_match_domain(hostname, domain):
                return True

        return False

    def _is_same_origin(self):
        """
        Check if the request is from the same origin, with port-aware hostname comparison.

        This method implements the same port-ignoring logic as WsProtectMixin to handle
        development environments where referer might be 'development.local' but request
        host is 'development.local:8000'.

        Returns:
            bool: True if request is from same origin, False otherwise
        """
        # Get CSRF settings for port handling configuration
        csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
        ignore_port = csrf_settings.get('ignore_port_in_hostname_check', False)

        # Get request hostname
        request_host = self.request.META.get('HTTP_HOST', '')

        # Extract request hostname (with or without port based on settings)
        if ignore_port and ':' in request_host:
            request_hostname = request_host.split(':')[0]
        else:
            request_hostname = request_host

        self._debug_log(f"Same-origin check: referer='{request_hostname}' request='{request_hostname}' ignore_port={ignore_port}")

        # Check direct hostname match
        if request_hostname == request_hostname:
            self._debug_log("Same-origin request allowed")
            return True

        # If port ignoring is enabled, also check without port
        if ignore_port and ':' in request_host:
            base_request_hostname = request_host.split(':')[0]
            if request_hostname == base_request_hostname:
                self._debug_log("Same-origin request allowed (port ignored)")
                return True

        self._debug_log(f"Same-origin check failed: '{request_hostname}' != '{request_hostname}'")
        return False

    def _debug_log(self, message):
        """
        Log debug messages for widget iframe permission checking if debug mode is enabled.

        Args:
            message: Debug message to log
        """
        csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
        if csrf_settings.get('debug_csrf_validation', False):
            # Debug logging can be enabled via WIDGET_CSRF_SETTINGS.debug_csrf_validation
            pass

    def get_context_data(self, **kwargs):
        context = super(WidgetView, self).get_context_data(**kwargs)
        context['config'] = self.request.config
        
        # Add enhanced CSRF setting to context
        csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
        context['use_enhanced_csrf'] = csrf_settings.get('use_enhanced_csrf', False)
        
        return context

    def get_template_names(self):
        return [
            self.request.config.widget_type.get_template_name(
                self.request, self.request.config
            )
        ]
