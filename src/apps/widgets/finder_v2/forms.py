import json
import pprint

from django import forms
from django.urls import reverse
from django.test import RequestFactory, Client
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from rest_framework import status

from src.apps.widgets.api_proxy.views import FinderV2WidgetProxyView
from src.apps.widgets.common.forms import FakeModelForm, WidgetPermissionsForm, WidgetCommonForm, \
    WidgetInterfaceForm, WidgetConfigForm, DemoWidgetCommonForm
from src.apps.widgets.common.fields import WsJsonFormField


class DemoCompatibleJSONField(forms.CharField):
    """
    Custom field that handles both comma-separated strings (demo template)
    and JSON arrays (storage format). Inherits from CharField to avoid
    JSONField's automatic JSON parsing issues.
    """

    def __init__(self, **kwargs):
        # Set default widget to TextInput for demo template compatibility
        kwargs.setdefault('widget', forms.TextInput)
        super().__init__(**kwargs)

    def to_python(self, value):
        """
        Convert the value to Python format (list).
        Handles both comma-separated strings and JSON arrays.
        """
        if not value:
            return []

        if isinstance(value, list):
            return value

        if isinstance(value, str):
            # Try JSON first
            try:
                parsed = json.loads(value)
                if isinstance(parsed, list):
                    return parsed
                else:
                    return [parsed] if parsed else []
            except (json.JSONDecodeError, TypeError):
                # Not JSON, treat as comma-separated string
                if ',' in value:
                    return [item.strip() for item in value.split(',') if item.strip()]
                elif value.strip():
                    return [value.strip()]
                else:
                    return []

        return []

    def prepare_value(self, value):
        """
        Convert the value for display in the form field.
        This is called when rendering the form field in templates.
        CRITICAL: Always return comma-separated string format for demo template compatibility.
        """
        # Removed verbose logging to reduce noise in development logs
        # For debugging, uncomment the following lines:
        # import logging
        # logger = logging.getLogger(__name__)
        # logger.debug(f"PREPARE_VALUE: Input value: {value} (type: {type(value)})")

        # Handle None or empty values
        if not value:
            return ''
        
        if isinstance(value, list):
            # Handle nested list format from Django form processing
            if len(value) == 1 and isinstance(value[0], str):
                # Single item list containing a string (possibly JSON)
                single_value = value[0]

                try:
                    # Try to parse the inner string as JSON
                    parsed = json.loads(single_value)
                    if isinstance(parsed, list):
                        result = ','.join(str(item) for item in parsed) if parsed else ''
                        return result
                    else:
                        result = str(parsed) if parsed else ''
                        return result
                except (json.JSONDecodeError, TypeError):
                    # Not JSON, treat as regular string
                    return single_value
            else:
                # Regular list - convert to comma-separated string
                result = ','.join(str(item) for item in value) if value else ''
                return result
            
        elif isinstance(value, str):
            # Handle string data (could be JSON or comma-separated)
            if not value.strip():
                return ''

            try:
                # Try to parse as JSON first
                parsed = json.loads(value)

                if isinstance(parsed, list):
                    # Convert JSON array to comma-separated string for demo template
                    result = ','.join(str(item) for item in parsed) if parsed else ''
                    return result
                else:
                    # Single value
                    result = str(parsed) if parsed else ''
                    return result
            except (json.JSONDecodeError, TypeError):
                # Not JSON, assume it's already comma-separated string
                return value
        else:
            # Convert any other type to string
            return str(value)

    def validate(self, value):
        """
        Custom validation to ensure the value is a valid list.
        """
        super().validate(value)
        if value and not isinstance(value, list):
            raise forms.ValidationError('Value must be a list.')

    def clean(self, value):
        """
        Clean the value and convert it to the proper format.
        """
        value = self.to_python(value)
        self.validate(value)
        return value
from src.apps.widgets.finder_v2.default_config.themes import FinderV2Themes
from src.apps.widgets.finder_v2.models import FinderV2InterfaceTabs


class ContentFilterForm(FakeModelForm):
    CHOICES = (
        ('brands', _('Brands')),
        ('brands_exclude', _('Exclude brands')),
    )

    brands = DemoCompatibleJSONField(label=_('Brands'), required=False)
    brands_exclude = DemoCompatibleJSONField(label=_('Exclude brands'), required=False)

    by = forms.ChoiceField(choices=CHOICES, required=False)

    regions = DemoCompatibleJSONField(label=_('Regions'), required=False)

    only_oem = forms.BooleanField(label=_('Show only Original Equipment (OE) modifications'),
                                  initial=False,
                                  required=False)

    filter_choices = [field[0] for field in CHOICES]

    # Use DefaultJson wrapper for proper data handling
    use_default_json = True

    def __getitem__(self, name):
        """Override to ensure BoundField.value() returns correct format for JSON fields."""
        bound_field = super().__getitem__(name)
        
        # For DemoCompatibleJSONField, ensure the value is always in the correct format
        if isinstance(self.fields[name], DemoCompatibleJSONField):
            original_value_method = bound_field.value
            
            def custom_value():
                # Get the raw value using original method
                raw_value = original_value_method()
                # Apply prepare_value to ensure correct format
                return self.fields[name].prepare_value(raw_value)
            
            # Replace the value method with our custom one
            bound_field.value = custom_value
        
        return bound_field

    def __init__(self, *args, **kwargs):
        import logging
        import threading
        logger = logging.getLogger(__name__)

        # Get thread/request ID for correlation
        thread_id = threading.get_ident()

        self.widget = kwargs.pop('widget', None)

        # Only log in DEBUG mode to reduce noise
        from django.conf import settings
        if settings.DEBUG:
            logger.debug(f"ContentFilterForm.__init__ called with {len(args)} args, {len(kwargs)} kwargs")

        # CRITICAL: Fix malformed JSON data BEFORE Django form initialization
        # Data can be passed as args[0] OR kwargs['data']
        data = None
        if args and len(args) > 0:
            data = args[0]
        elif 'data' in kwargs:
            data = kwargs['data']

        if data is not None and hasattr(data, 'items'):

            # Process all JSON fields that might have malformed data
            import json
            json_fields = ['content-regions', 'content-brands', 'content-brands_exclude']

            for field_name in json_fields:
                if field_name in data:
                    raw_field_data = data[field_name]

                    # Handle list data (Django QueryDict format)
                    if isinstance(raw_field_data, list) and len(raw_field_data) > 0:
                        raw_string = raw_field_data[0]

                        if raw_string and isinstance(raw_string, str):
                            try:
                                # Try to parse as JSON first
                                parsed = json.loads(raw_string)
                                # Already valid JSON, no action needed
                            except json.JSONDecodeError:
                                # Handle malformed JSON data
                                if ',' in raw_string:
                                    # Handle comma-separated values like "[],usdm,cdm"
                                    parts = [part.strip() for part in raw_string.split(',') if part.strip() and part.strip() != '[]']
                                    if parts:
                                        fixed_json = json.dumps(parts)
                                        if settings.DEBUG:
                                            logger.debug(f"Fixed malformed JSON in {field_name}: '{raw_string}' -> '{fixed_json}'")

                                            # Create mutable copy and fix the data
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Making data mutable for {field_name}")

                                            # CRITICAL: Create a new mutable QueryDict if needed
                                            from django.http import QueryDict
                                            if isinstance(data, QueryDict) and not data._mutable:
                                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict")
                                                new_data = data.copy()
                                                new_data._mutable = True
                                                data = new_data
                                                # Update the reference in args or kwargs
                                                if args and len(args) > 0:
                                                    args = (data,) + args[1:]
                                                elif 'data' in kwargs:
                                                    kwargs['data'] = data
                                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Updated data reference")
                                            elif hasattr(data, '_mutable'):
                                                data._mutable = True
                                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Data._mutable set to True")

                                            data[field_name] = [fixed_json]
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: DATA FIXED - new {field_name}: {data[field_name]}")
                                        else:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: No valid parts found in {field_name}, setting to empty array")

                                            # CRITICAL: Create a new mutable QueryDict if needed
                                            from django.http import QueryDict
                                            if isinstance(data, QueryDict) and not data._mutable:
                                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for empty array")
                                                new_data = data.copy()
                                                new_data._mutable = True
                                                data = new_data
                                                # Update the reference in args or kwargs
                                                if args and len(args) > 0:
                                                    args = (data,) + args[1:]
                                                elif 'data' in kwargs:
                                                    kwargs['data'] = data
                                            elif hasattr(data, '_mutable'):
                                                data._mutable = True

                                            data[field_name] = ['[]']
                            else:
                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Skipping {field_name} - not a valid string: '{raw_string}' (type: {type(raw_string)})")
                        elif isinstance(raw_field_data, str):
                            # Handle case where data is directly a string (not in a list)
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Processing direct string for {field_name}: '{raw_field_data}'")
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Attempting JSON validation for {field_name}: '{raw_field_data}'")
                            try:
                                # Try to parse as JSON first
                                parsed = json.loads(raw_field_data)
                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: {field_name} data is already valid JSON: {parsed}")
                            except json.JSONDecodeError as e:
                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: MALFORMED JSON DETECTED in {field_name}: '{raw_field_data}' - Error: {e}")

                                # Handle specific case like "[],usdm,cdm"
                                if ',' in raw_field_data:
                                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Processing comma-separated values in {field_name}")
                                    parts = [part.strip() for part in raw_field_data.split(',') if part.strip() and part.strip() != '[]']
                                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Extracted parts from {field_name}: {parts}")
                                    if parts:
                                        fixed_json = json.dumps(parts)
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: FIXING MALFORMED DATA in {field_name}: '{raw_field_data}' -> '{fixed_json}'")

                                        # Create mutable copy and fix the data
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Making data mutable for {field_name}")

                                        # CRITICAL: Create a new mutable QueryDict if needed
                                        from django.http import QueryDict
                                        if isinstance(data, QueryDict) and not data._mutable:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for string data")
                                            new_data = data.copy()
                                            new_data._mutable = True
                                            data = new_data
                                            # Update the reference in args or kwargs
                                            if args and len(args) > 0:
                                                args = (data,) + args[1:]
                                            elif 'data' in kwargs:
                                                kwargs['data'] = data
                                        elif hasattr(data, '_mutable'):
                                            data._mutable = True
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Data._mutable set to True")

                                        data[field_name] = [fixed_json]  # Store as list for Django QueryDict compatibility
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: DATA FIXED - new {field_name}: {data[field_name]}")
                                    else:
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: No valid parts found in {field_name}, setting to empty array")

                                        # CRITICAL: Create a new mutable QueryDict if needed
                                        from django.http import QueryDict
                                        if isinstance(data, QueryDict) and not data._mutable:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for string empty array")
                                            new_data = data.copy()
                                            new_data._mutable = True
                                            data = new_data
                                            # Update the reference in args or kwargs
                                            if args and len(args) > 0:
                                                args = (data,) + args[1:]
                                            elif 'data' in kwargs:
                                                kwargs['data'] = data
                                        elif hasattr(data, '_mutable'):
                                            data._mutable = True

                                        data[field_name] = ['[]']
                                else:
                                    # Single value or other malformed data
                                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Processing single value in {field_name}")
                                    if raw_field_data and raw_field_data != '[]':
                                        fixed_json = json.dumps([raw_field_data])
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: FIXING SINGLE VALUE in {field_name}: '{raw_field_data}' -> '{fixed_json}'")

                                        # CRITICAL: Create a new mutable QueryDict if needed
                                        from django.http import QueryDict
                                        if isinstance(data, QueryDict) and not data._mutable:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for string single value")
                                            new_data = data.copy()
                                            new_data._mutable = True
                                            data = new_data
                                            # Update the reference in args or kwargs
                                            if args and len(args) > 0:
                                                args = (data,) + args[1:]
                                            elif 'data' in kwargs:
                                                kwargs['data'] = data
                                        elif hasattr(data, '_mutable'):
                                            data._mutable = True

                                        data[field_name] = [fixed_json]
                                    else:
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Empty {field_name} value, setting to empty array")

                                        # CRITICAL: Create a new mutable QueryDict if needed
                                        from django.http import QueryDict
                                        if isinstance(data, QueryDict) and not data._mutable:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for string empty value")
                                            new_data = data.copy()
                                            new_data._mutable = True
                                            data = new_data
                                            # Update the reference in args or kwargs
                                            if args and len(args) > 0:
                                                args = (data,) + args[1:]
                                            elif 'data' in kwargs:
                                                kwargs['data'] = data
                                        elif hasattr(data, '_mutable'):
                                            data._mutable = True

                                        data[field_name] = ['[]']
                        else:
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Skipping {field_name} - unexpected data type: {raw_field_data} (type: {type(raw_field_data)})")

        super(ContentFilterForm, self).__init__(*args, **kwargs)

        # CRITICAL FIX: Set field initial values after form initialization
        # This ensures that unbound forms (GET requests) display saved values correctly
        # The demo template expects comma-separated values, not JSON arrays
        if not self.is_bound and self.initial:
            # Set initial values on the actual form fields for proper template display
            for field_name in ['regions', 'brands', 'brands_exclude']:
                if field_name in self.initial and field_name in self.fields:
                    initial_value = self.initial[field_name]

                    # Convert JSON array to comma-separated string for demo template
                    if isinstance(initial_value, list):
                        if initial_value:  # Non-empty list
                            comma_separated = ','.join(initial_value)
                            self.fields[field_name].initial = comma_separated
                            # Keep form.initial in sync so BoundField.value() reflects the same value
                            self.initial[field_name] = comma_separated
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Set field initial for {field_name}: {initial_value} -> '{comma_separated}'")
                        else:  # Empty list
                            self.fields[field_name].initial = ''
                            self.initial[field_name] = ''
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Set field initial for {field_name}: {initial_value} -> ''")
                    else:
                        # Already string or other format
                        self.fields[field_name].initial = initial_value
                        self.initial[field_name] = initial_value
                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Set field initial for {field_name}: {initial_value}")

        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: ContentFilterForm.__init__ completed")
        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: self.data after init: {dict(self.data) if hasattr(self, 'data') and self.data else 'No data'}")

        # Debug field values for template display
        if not self.is_bound:
            for field_name in ['regions', 'brands', 'brands_exclude']:
                if field_name in self.fields:
                    field_value = self[field_name].value()
                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Field {field_name} value(): {field_value} (type: {type(field_value)})")

    def clean_regions(self):
        """
        Custom validation for regions field to handle JSON string conversion.

        The AngularJS TagChoiceCtrl sends regions data as a JSON string (e.g., '["region1", "region2"]'),
        but Django's JSONField expects parsed JSON data. This method converts the JSON string
        to a proper Python list for form validation.
        """
        import json
        import logging
        import threading
        logger = logging.getLogger(__name__)

        thread_id = threading.get_ident()

        regions_data = self.cleaned_data.get('regions')
        logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: clean_regions() called")
        logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: regions_data from cleaned_data: {regions_data} (type: {type(regions_data)})")

        # Also log the raw form data for comparison
        if hasattr(self, 'data') and 'content-regions' in self.data:
            raw_regions = self.data['content-regions']
            logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Raw form data content-regions: {raw_regions} (type: {type(raw_regions)})")

        # If regions_data is already a list, return it as-is
        if isinstance(regions_data, list):
            logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Regions data is already a list: {regions_data}")
            return regions_data

        # If regions_data is a string, try to parse it as JSON
        if isinstance(regions_data, str):
            logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Attempting to parse regions string: '{regions_data}'")
            try:
                parsed_regions = json.loads(regions_data)
                if isinstance(parsed_regions, list):
                    logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Successfully parsed regions JSON string: {parsed_regions}")
                    return parsed_regions
                else:
                    logger.warning(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Parsed regions data is not a list: {parsed_regions}")
                    return []
            except json.JSONDecodeError as e:
                logger.error(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Failed to parse regions JSON string '{regions_data}': {e}")
                return []

        # If regions_data is None or empty, return empty list
        if not regions_data:
            logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Regions data is empty, returning empty list")
            return []

        # Fallback: return empty list for any other data type
        logger.warning(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Unexpected regions data type {type(regions_data)}: {regions_data}")
        return []

    def clean_brands(self):
        """
        Custom validation for brands field to handle JSON string conversion.

        Similar to clean_regions, this handles the brands field which also uses TagChoiceCtrl.
        """
        import json
        import logging
        logger = logging.getLogger(__name__)

        brands_data = self.cleaned_data.get('brands')
        logger.debug(f"Raw brands data: {brands_data} (type: {type(brands_data)})")

        # If brands_data is already a list, return it as-is
        if isinstance(brands_data, list):
            return brands_data

        # If brands_data is a string, try to parse it as JSON
        if isinstance(brands_data, str):
            try:
                parsed_brands = json.loads(brands_data)
                if isinstance(parsed_brands, list):
                    return parsed_brands
                else:
                    return []
            except json.JSONDecodeError:
                return []

        # If brands_data is None or empty, return empty list
        if not brands_data:
            return []

        # Fallback: return empty list for any other data type
        return []

    def clean_only_oem(self):
        """
        Custom validation for only_oem field.
        
        Since the UI no longer shows this field, always return the default value (False)
        to show all modifications (both OE and aftermarket).
        """
        # Always return False to show all modifications, since the UI is hidden
        return False

    def clean_brands_exclude(self):
        """
        Custom validation for brands_exclude field to handle JSON string conversion.

        Similar to clean_regions, this handles the brands_exclude field.
        """
        import json
        import logging
        logger = logging.getLogger(__name__)

        brands_exclude_data = self.cleaned_data.get('brands_exclude')
        logger.debug(f"Raw brands_exclude data: {brands_exclude_data} (type: {type(brands_exclude_data)})")

        # If brands_exclude_data is already a list, return it as-is
        if isinstance(brands_exclude_data, list):
            return brands_exclude_data

        # If brands_exclude_data is a string, try to parse it as JSON
        if isinstance(brands_exclude_data, str):
            try:
                parsed_brands_exclude = json.loads(brands_exclude_data)
                if isinstance(parsed_brands_exclude, list):
                    return parsed_brands_exclude
                else:
                    return []
            except json.JSONDecodeError:
                return []

        # If brands_exclude_data is None or empty, return empty list
        if not brands_exclude_data:
            return []

        # Fallback: return empty list for any other data type
        return []

    def clean(self):
        """
        Override clean method to handle JSON string conversion for all fields.

        The issue is that Django's JSONField validation happens before our custom
        clean_* methods, so we need to preprocess the raw form data here.
        """
        import json
        import logging
        import threading
        logger = logging.getLogger(__name__)

        thread_id = threading.get_ident()

        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: ContentFilterForm.clean() called")
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Raw form data: {dict(self.data) if hasattr(self, 'data') and self.data else 'No data'}")
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Current errors before clean: {self.errors}")

        # NOTE: This clean() method is called AFTER individual field validation
        # So if there are already errors from WsJsonFormField validation, they will be here
        if self.errors:
            logger.error(f"🔍 FORM CLEAN [Thread {thread_id}]: ERRORS ALREADY EXIST BEFORE CLEAN: {self.errors}")

        # Preprocess JSON fields before Django's field validation
        json_fields = ['regions', 'brands', 'brands_exclude']

        for field_name in json_fields:
            form_field_name = f'content-{field_name}'  # Form field names have 'content-' prefix
            if form_field_name in self.data:
                raw_value = self.data[form_field_name]
                logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Processing {form_field_name}: {raw_value} (type: {type(raw_value)})")

                if isinstance(raw_value, list) and len(raw_value) > 0:
                    raw_string = raw_value[0]  # Django form data comes as lists
                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Raw string for {form_field_name}: '{raw_string}' (type: {type(raw_string)})")

                    # Check if it's already valid JSON
                    if raw_string:
                        # Try to parse as JSON first
                        try:
                            json.loads(raw_string)
                            # If it parses successfully, it's already valid JSON
                            logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: {form_field_name} data is already valid JSON: {raw_string}")
                        except json.JSONDecodeError:
                            # It's not valid JSON, determine the format and fix it
                            logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: NON-JSON {form_field_name} DATA DETECTED: '{raw_string}'")

                            # Handle comma-separated values from demo template (e.g., "usdm,cdm")
                            if ',' in raw_string and not raw_string.startswith('['):
                                # This is comma-separated values from demo template
                                parts = [part.strip() for part in raw_string.split(',') if part.strip()]
                                if parts:
                                    fixed_json = json.dumps(parts)
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: CONVERTING COMMA-SEPARATED {form_field_name}: '{raw_string}' -> '{fixed_json}'")
                                    # Update the form data
                                    self.data = self.data.copy()  # Make it mutable
                                    self.data[form_field_name] = [fixed_json]
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: DATA UPDATED - {form_field_name}: {self.data[form_field_name]}")
                                else:
                                    # No valid parts, set to empty array
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: No valid parts found in comma-separated {form_field_name}, setting to empty array")
                                    self.data = self.data.copy()
                                    self.data[form_field_name] = ['[]']
                            # Handle malformed JSON with mixed comma-separated and arrays (e.g., "[],usdm,cdm")
                            elif ',' in raw_string and '[' in raw_string:
                                # Split by comma and filter out empty/invalid parts
                                parts = [part.strip() for part in raw_string.split(',') if part.strip() and part.strip() != '[]']
                                if parts:
                                    # Create proper JSON array
                                    fixed_json = json.dumps(parts)
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: FIXING MALFORMED JSON {form_field_name}: '{raw_string}' -> '{fixed_json}'")
                                    # Update the form data
                                    self.data = self.data.copy()  # Make it mutable
                                    self.data[form_field_name] = [fixed_json]
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: DATA UPDATED - {form_field_name}: {self.data[form_field_name]}")
                                else:
                                    # No valid parts, set to empty array
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: No valid parts found in malformed {form_field_name}, setting to empty array")
                                    self.data = self.data.copy()
                                    self.data[form_field_name] = ['[]']
                            else:
                                # Single value, wrap in array
                                if raw_string and raw_string != '[]':
                                    fixed_json = json.dumps([raw_string])
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Fixed single {form_field_name} value: '{raw_string}' -> '{fixed_json}'")
                                    self.data = self.data.copy()
                                    self.data[form_field_name] = [fixed_json]
                                else:
                                    # Empty or just "[]", set to empty array
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Empty {form_field_name} value, setting to empty array")
                                    self.data = self.data.copy()
                                    self.data[form_field_name] = ['[]']

        # Now call the parent clean method
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Calling super().clean()")
        cleaned_data = super().clean()
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Cleaned data: {cleaned_data}")
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Form errors after clean: {self.errors}")

        return cleaned_data

    def decompose_to_initial(self):
        import logging
        import threading
        logger = logging.getLogger(__name__)
        thread_id = threading.get_ident()
        
        
        # Only log in DEBUG mode to reduce noise
        from django.conf import settings
        if settings.DEBUG:
            logger.debug(f"Starting data decomposition for ContentFilterForm")

        # Handle cases where self.instance might be None or missing keys
        if not self.instance:
            default_initial = {
                'regions': [],
                'only_oem': False,
                'by': '',
                'brands': [],
                'brands_exclude': [],
            }
            return default_initial

        # Debug instance content only in DEBUG mode
        if settings.DEBUG:
            logger.debug(f"Instance content available with {len(self.instance.keys()) if hasattr(self.instance, 'keys') else 0} keys")

        # With use_default_json=True, self.instance is a DefaultJson object
        # Extract data safely with default values
        try:
            # DefaultJson.get signature is get(key, default=None)
            # so we must use keyword arg to avoid TypeError when default is list
            regions = self.instance.get('regions', default=[])
        except (KeyError, TypeError):
            regions = []

        try:
            only_oem = self.instance['only_oem']
        except (KeyError, TypeError):
            only_oem = False

        # CRITICAL FIX: Sanitize corrupted JSON data
        # The data may have been over-escaped during previous saves, so we need to clean it
        regions = self._sanitize_json_field(regions, 'regions')
        
        # Convert regions to comma-separated format for demo template
        if isinstance(regions, list):
            regions_for_template = ','.join(regions)
        else:
            regions_for_template = regions or ''

        initial = {
            # Always store regions initial as comma-separated string for template compatibility
            'regions': regions_for_template,
            'only_oem': only_oem,
        }

        # Safely update with filter data
        try:
            filter_data = self.instance['filter']
            logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Raw filter_data: {filter_data} (type: {type(filter_data)})")
            
            if filter_data:
                # Add filter fields with defaults and sanitize them
                # CRITICAL FIX: Use .get() with proper DefaultJson syntax
                try:
                    by_value = filter_data.get('by', default='') or ''
                    brands_raw = filter_data.get('brands', default=[]) or []
                    brands_exclude_raw = filter_data.get('brands_exclude', default=[]) or []
                    
                    logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Raw filter values:")
                    logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   by = '{by_value}'")
                    logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   brands = {brands_raw} (type: {type(brands_raw)})")
                    logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   brands_exclude = {brands_exclude_raw} (type: {type(brands_exclude_raw)})")
                    
                    initial['by'] = by_value
                    initial['brands'] = self._sanitize_json_field(brands_raw, 'brands')
                    initial['brands_exclude'] = self._sanitize_json_field(brands_exclude_raw, 'brands_exclude')
                except Exception as filter_access_error:
                    logger.error(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Filter access error: {filter_access_error}")
                    # Set defaults if individual field access fails
                    initial['by'] = ''
                    initial['brands'] = []
                    initial['brands_exclude'] = []
            else:
                logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Filter data is empty/None")
                initial['by'] = ''
                initial['brands'] = []
                initial['brands_exclude'] = []
                
        except (KeyError, TypeError, AttributeError) as e:
            logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Error accessing filter data: {e}")
            # Set defaults if filter data is missing or invalid
            initial['by'] = ''
            initial['brands'] = []
            initial['brands_exclude'] = []

        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Final initial data:")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   regions = {initial['regions']}")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   only_oem = {initial['only_oem']}")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   by = '{initial['by']}'")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   brands = {initial['brands']}")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   brands_exclude = {initial['brands_exclude']}")

        return initial

    def _sanitize_json_field(self, field_value, field_name):
        """
        Sanitize JSON field data that may have been corrupted by over-escaping.

        This fixes data corruption issues where JSON arrays were repeatedly escaped,
        resulting in strings like: ['["[\\"[\\\\\\"usdm\\\\\\"", "\\\\\\"cdm\\\\\\"]\\"]", "cdm", "mxndm"]']

        Args:
            field_value: The potentially corrupted field value
            field_name: Name of the field (for logging)

        Returns:
            list: Clean list of values
        """
        import json
        import logging
        import threading
        logger = logging.getLogger(__name__)
        thread_id = threading.get_ident()

        # If it's already a proper list, return it
        if isinstance(field_value, list) and all(isinstance(item, str) for item in field_value):
            # Check if any items look like escaped JSON
            needs_cleaning = any('\\' in item or item.startswith('[') for item in field_value)
            if not needs_cleaning:
                        # Field is already clean
                return field_value

        # Sanitize the field value

        # Try to extract clean values from corrupted data
        clean_values = []

        if isinstance(field_value, list):
            for item in field_value:
                if isinstance(item, str):
                    # Try to parse nested JSON strings
                    current = item
                    max_iterations = 10  # Prevent infinite loops
                    iteration = 0

                    while iteration < max_iterations:
                        try:
                            # Try to parse as JSON
                            parsed = json.loads(current)
                            if isinstance(parsed, list):
                                # Successfully parsed to a list
                                clean_values.extend(parsed)
                                break
                            elif isinstance(parsed, str):
                                # Still a string, try parsing again
                                current = parsed
                                iteration += 1
                            else:
                                # Not a list or string, add as-is
                                clean_values.append(str(parsed))
                                break
                        except json.JSONDecodeError:
                            # Not valid JSON, treat as a plain string
                            if current and current not in ['[]', '""']:
                                clean_values.append(current)
                            break
                else:
                    # Not a string, add as-is
                    clean_values.append(str(item))
        elif isinstance(field_value, str):
            # Single string value
            if field_value and field_value not in ['[]', '""']:
                try:
                    parsed = json.loads(field_value)
                    if isinstance(parsed, list):
                        clean_values = parsed
                    else:
                        clean_values = [str(parsed)]
                except json.JSONDecodeError:
                    clean_values = [field_value]

        # Remove duplicates while preserving order
        seen = set()
        result = []
        for value in clean_values:
            if value not in seen and value:  # Skip empty values
                seen.add(value)
                result.append(value)

        # Only log sanitization in DEBUG mode
        from django.conf import settings
        if settings.DEBUG and field_value != result:
            logger.debug(f"Sanitized {field_name}: {len(str(field_value))} chars -> {len(result)} items")
        return result

    def compose_to_save(self, data):
        import logging
        import threading
        logger = logging.getLogger(__name__)
        thread_id = threading.get_ident()
        
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Starting data composition")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Input data type: {type(data)}")
        
        # CRITICAL FIX: Use self.cleaned_data when form is bound and valid
        # instead of relying on DefaultJson wrapper which doesn't have processed form data
        if hasattr(self, 'cleaned_data') and self.cleaned_data and self.is_bound:
            logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Using cleaned_data from bound form")
            print(f"🎯 CONTENT FIX: Using cleaned_data instead of DefaultJson wrapper")
            
            # Extract from cleaned_data which has the processed JSON arrays
            by_value = self.cleaned_data.get('by', '')
            brands_cleaned = self.cleaned_data.get('brands', [])
            brands_exclude_cleaned = self.cleaned_data.get('brands_exclude', [])
            regions_cleaned = self.cleaned_data.get('regions', [])
            only_oem_value = self.cleaned_data.get('only_oem', False)
            
            print(f"🎯 CONTENT FIX: Cleaned data values:")
            print(f"🎯 CONTENT FIX:   by_value = {by_value}")
            print(f"🎯 CONTENT FIX:   brands_cleaned = {brands_cleaned}")
            print(f"🎯 CONTENT FIX:   brands_exclude_cleaned = {brands_exclude_cleaned}")
            print(f"🎯 CONTENT FIX:   regions_cleaned = {regions_cleaned}")
            print(f"🎯 CONTENT FIX:   only_oem_value = {only_oem_value}")
            
            # Process the cleaned data through ensure_list to convert JSON strings to lists
            brands_clean = self._ensure_list_format(brands_cleaned, 'brands')
            brands_exclude_clean = self._ensure_list_format(brands_exclude_cleaned, 'brands_exclude')
            regions_clean = self._ensure_list_format(regions_cleaned, 'regions')
            
        else:
            logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Falling back to DefaultJson data")
            print(f"🎯 CONTENT FALLBACK: Using DefaultJson wrapper (form not bound or no cleaned_data)")
            
            # Debug what data is actually available
            print(f"🎯 CONTENT DATA DEBUG: Raw data type: {type(data)}")
            if hasattr(data, 'keys'):
                available_keys = list(data.keys()) if hasattr(data.keys, '__call__') else 'Cannot call keys()'
                print(f"🎯 CONTENT DATA DEBUG: Available keys: {available_keys}")
            else:
                print(f"🎯 CONTENT DATA DEBUG: Data has no keys method")
            
            # Try to see what the data contains
            print(f"🎯 CONTENT DATA DEBUG: Data repr: {repr(data)[:300]}")
            
            # With use_default_json=True, data is a DefaultJson object
            # Use DefaultJson.get() method with proper signature: get(key, default=None)
            
            # Extract and log the form data
            by_value = data.get('by', '')
            brands_value = data.get('brands', [])
            brands_exclude_value = data.get('brands_exclude', [])
            regions_value = data.get('regions', [])
            only_oem_value = data.get('only_oem', False)
            
            print(f"🎯 CONTENT DATA DEBUG: Raw extracted values:")
            print(f"🎯 CONTENT DATA DEBUG:   by_value = {by_value}")
            print(f"🎯 CONTENT DATA DEBUG:   brands_value = {brands_value}")
            print(f"🎯 CONTENT DATA DEBUG:   brands_exclude_value = {brands_exclude_value}")
            print(f"🎯 CONTENT DATA DEBUG:   regions_value = {regions_value}")
            print(f"🎯 CONTENT DATA DEBUG:   only_oem_value = {only_oem_value}")
            
            # Process through ensure_list helper
            brands_clean = self._ensure_list_format(brands_value, 'brands')
            brands_exclude_clean = self._ensure_list_format(brands_exclude_value, 'brands_exclude')
            regions_clean = self._ensure_list_format(regions_value, 'regions')
        
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Cleaned values:")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   brands_clean = {brands_clean}")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   brands_exclude_clean = {brands_exclude_clean}")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   regions_clean = {regions_clean}")
        
        # CRITICAL FIX: Explicitly save all filter fields to ensure they persist
        filter = {
            'by': by_value,
            'brands': brands_clean,
            'brands_exclude': brands_exclude_clean,
        }

        # Get existing filter keys to preserve any additional fields
        existing_filter = {}
        if self.instance:
            try:
                existing_filter = self.instance['filter']
                logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Found existing filter: {existing_filter}")
                
                if hasattr(existing_filter, 'keys'):
                    # Handle DefaultJson object
                    for key in existing_filter.keys():
                        if key not in ['by', 'brands', 'brands_exclude']:  # Skip fields we already set
                            existing_value = data.get(key, [])
                            if not isinstance(existing_value, (list, tuple)):
                                existing_value = []
                            filter[key] = existing_value
                            logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Added existing field '{key}' = {existing_value}")
                elif isinstance(existing_filter, dict):
                    # Handle regular dict
                    for key in existing_filter.keys():
                        if key not in ['by', 'brands', 'brands_exclude']:  # Skip fields we already set
                            existing_value = data.get(key, [])
                            if not isinstance(existing_value, (list, tuple)):
                                existing_value = []
                            filter[key] = existing_value
                            logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Added existing field '{key}' = {existing_value}")
            except (KeyError, AttributeError, TypeError) as e:
                logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: No existing filter data: {e}")
                # No existing filter data, just use the fields we set above
                pass

        final_data = {
            'only_oem': only_oem_value,
            'regions': regions_clean,
            'filter': filter,
        }

        # Enhanced logging for debugging save issues
        print(f"🎯 CONTENT SAVE DEBUG: Final composed data:")
        print(f"🎯 CONTENT SAVE DEBUG:   only_oem = {final_data['only_oem']}")
        print(f"🎯 CONTENT SAVE DEBUG:   regions = {final_data['regions']} (length: {len(final_data['regions'])})")
        print(f"🎯 CONTENT SAVE DEBUG:   filter = {final_data['filter']}")
        print(f"🎯 CONTENT SAVE DEBUG:   filter.brands = {final_data['filter'].get('brands', 'NOT_FOUND')} (length: {len(final_data['filter'].get('brands', []))})")
        print(f"🎯 CONTENT SAVE DEBUG:   filter.brands_exclude = {final_data['filter'].get('brands_exclude', 'NOT_FOUND')} (length: {len(final_data['filter'].get('brands_exclude', []))})")
        
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Final composed data:")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   only_oem = {final_data['only_oem']}")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   regions = {final_data['regions']}")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   filter = {final_data['filter']}")
        
        return final_data

    def _ensure_list_format(self, value, field_name):
        """
        Ensure value is a proper list of strings, not nested JSON or other formats.
        This fixes the issue where demo form saves ["[\"a\", \"b\"]"] instead of ["a", "b"].
        """
        import logging
        import json
        logger = logging.getLogger(__name__)
        
        logger.debug(f"🔍 ENSURE_LIST: Processing {field_name}: {value} (type: {type(value)})")
        
        if not value:
            return []
            
        if isinstance(value, list):
            # Process each item in the list
            result = []
            for item in value:
                if isinstance(item, str):
                    # Try to parse as JSON if it looks like JSON
                    if item.startswith('[') and item.endswith(']'):
                        try:
                            parsed = json.loads(item)
                            if isinstance(parsed, list):
                                result.extend(parsed)
                            else:
                                result.append(str(parsed))
                        except json.JSONDecodeError:
                            # Not valid JSON, treat as string
                            result.append(item)
                    else:
                        # Regular string, add it
                        result.append(item)
                else:
                    # Not a string, convert to string
                    result.append(str(item))
            
            logger.debug(f"🔍 ENSURE_LIST: {field_name} result: {result}")
            return result
        elif isinstance(value, str):
            # Single string value, try to parse as JSON or split by comma
            if value.startswith('[') and value.endswith(']'):
                try:
                    parsed = json.loads(value)
                    if isinstance(parsed, list):
                        return parsed
                    else:
                        return [str(parsed)]
                except json.JSONDecodeError:
                    return [value]
            else:
                return [value]
        else:
            return [str(value)]

    @cached_property
    def choices(self):
        """
        Get choices data for finder-v2 widget configuration.

        Uses FinderV2WidgetProxyView to ensure v2 API endpoints are used,
        which are required for the finder-v2 widget functionality.
        """
        if not self.widget:
            return {
                'brands': [],
                'regions': [],
            }

        brands_url = reverse('widget-api:makes', kwargs={'widget_slug': self.widget.slug})
        regions_url = reverse('widget-api:regions', kwargs={'widget_slug': self.widget.slug})
        
        brands_choices = self.get_choices(brands_url)
        regions_choices = self.get_choices(regions_url)

        # Parse JSON strings to Python objects for template use
        try:
            brands_data = json.loads(brands_choices)
            brands_list = brands_data.get('data', []) if isinstance(brands_data, dict) else []
        except json.JSONDecodeError:
            brands_list = []
            
        try:
            regions_data = json.loads(regions_choices)
            regions_list = regions_data.get('data', []) if isinstance(regions_data, dict) else []
        except json.JSONDecodeError:
            regions_list = []

        return {
            'brands': brands_list,
            'regions': regions_list,
        }

    def get_choices(self, url, json_result=True):
        """
        Get choices data directly from Wheel Fitment API for form initialization.

        This method bypasses the widget API proxy and calls the external Wheel Fitment API
        directly using the REST_PROXY configuration. This avoids CSRF protection issues
        since these are internal form initialization calls, not user-initiated requests.

        Args:
            url (str): Widget API endpoint URL (used to determine the external API endpoint).
            json_result (bool): Whether to return a raw JSON string or parsed data.

        Returns:
            str or list: JSON string or parsed data depending on json_result.
        """
        import requests
        from django.conf import settings

        try:
            # Map widget API endpoints to external API endpoints
            endpoint_mapping = {
                'mk': 'makes',        # /api/mk -> makes API
                'rg': 'regions'       # /api/rg -> regions API
            }
            
            # Extract endpoint type from URL
            endpoint_type = None
            for widget_endpoint, api_endpoint in endpoint_mapping.items():
                if widget_endpoint in url:
                    endpoint_type = api_endpoint
                    break
            
            if not endpoint_type:
                return '[]' if json_result else []

            # Get REST_PROXY configuration
            rest_proxy = getattr(settings, 'REST_PROXY', {})
            api_host = rest_proxy.get('HOST', 'https://api3.wheel-size.com')
            api_headers = rest_proxy.get('HEADERS', {})
            
            # Ensure v2 API endpoint for finder-v2
            if not api_host.endswith('/v2'):
                api_host = api_host.rstrip('/') + '/v2'
            
            # Build the external API URL
            external_url = f"{api_host}/{endpoint_type}/"
            
            # Make direct request to external API
            response = requests.get(
                external_url,
                headers=api_headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.text
            else:
                result = '[]'
                
        except Exception as e:
            # Handle any exceptions during API calls
            result = '[]'

        return result if json_result else json.loads(result)


class FinderV2InterfaceForm(WidgetInterfaceForm):
    TABS = FinderV2InterfaceTabs.TABS
    TAB_CHOICES = FinderV2InterfaceTabs.TAB_CHOICES

    FLOW_TYPE_CHOICES = (
        ('primary', _('Primary Flow (Year → Make → Model → Modifications)')),
        ('alternative', _('Alternative Flow (Make → Model → Generation → Modifications)')),
        ('year_select', _('Year Selection Flow (Make → Model → Years → Modifications)')),
    )

    # Hidden fields - finder-v2 only has one tab, so these are automatically set
    # Finder-v2 has a single hard-coded tab ("by_vehicle"), but Django still
    # constructs these hidden fields at validation time.  Mark them optional
    # and give sane defaults so the form remains valid even when the inputs
    # are omitted from POST data (or removed from the template altogether).

    tabs = forms.MultipleChoiceField(
        choices=TAB_CHOICES,
        widget=forms.MultipleHiddenInput(),
        required=False,
        initial=['by_vehicle'],
    )

    primary_tab = forms.ChoiceField(
        choices=TAB_CHOICES,
        widget=forms.HiddenInput(),
        required=False,
        initial='by_vehicle',
    )
    button_to_ws = forms.BooleanField(required=False,
                                      label='Show "See on Wheel-Size.com" button')
    flow_type = forms.ChoiceField(choices=FLOW_TYPE_CHOICES,
                                  initial='primary',
                                  label=_('API Flow Type'),
                                  help_text=_('Choose the search flow for vehicle selection'))
    
    output_template = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 15,
            'class': 'form-control font-mono text-sm',
            'placeholder': _('Loading default template...'),
            'style': 'resize: vertical; min-height: 300px;'
        }),
        label=_('Custom Output Template'),
        help_text=_('HTML template with placeholders. Supports TailwindCSS classes and loops. Pre-populated with default template - modify as needed.'),
        required=False,
        initial=''  # Will be set by _get_default_template() in decompose_to_initial
    )

    # IMPORTANT: Interface data should be saved exactly as provided, without
    # merging with initial/default values.  Using DefaultJson here was
    # swallowing the freshly-submitted `output_template` when it happened to be
    # identical to the default, resulting in an **empty string** being written
    # to the database.  Disabling the DefaultJson wrapping ensures
    # `cleaned_data` is passed straight through to `compose_to_save()`.
    use_default_json = False

    translation = WsJsonFormField(
        label=_('Translations (JSON)'),
        required=False,
        widget=forms.Textarea(attrs={
            'rows': 10,
            'class': 'form-control font-mono text-sm',
            'placeholder': _('Enter JSON translations here'),
            'style': 'resize: vertical; min-height: 200px;'
        }),
        help_text=_('JSON object with translation keys. Leave empty to use defaults.')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Drop the height field to prevent it rendering in the UI
        self.fields.pop('height', None)

    def decompose_to_initial(self):
        # Width comes from stored config; height intentionally omitted
        import logging
        logger = logging.getLogger(__name__)
        
        # Log the entire instance structure to understand the data
        logger.info(f"🎯 DECOMPOSE_TO_INITIAL: Instance type: {type(self.instance)}")
        logger.info(f"🎯 DECOMPOSE_TO_INITIAL: Instance keys: {list(self.instance.keys()) if isinstance(self.instance, dict) else 'Not a dict'}")
        
        if isinstance(self.instance, dict) and 'display' in self.instance:
            logger.info(f"🎯 DECOMPOSE_TO_INITIAL: Display keys: {list(self.instance['display'].keys())}")
            logger.info(f"🎯 DECOMPOSE_TO_INITIAL: Display structure: {self.instance['display']}")
        
        try:
            width_val = self.instance['dimensions']['width']
        except Exception:
            width_val = "100%"  # Updated default width for responsive layout

        # Get template value from stored configuration - this is the critical fix
        try:
            template_val = self.instance['display']['output_template']
            # Ensure we actually use the stored value, not just validate it exists
            if not template_val or template_val.strip() == '':
                template_val = self._get_default_template()
        except (KeyError, TypeError):
            template_val = self._get_default_template()

        # Get flow_type from saved configuration
        try:
            flow_type_val = self.instance['flow_type']
        except (KeyError, TypeError):
            flow_type_val = 'primary'  # Default flow type

        # Get button_to_ws from saved configuration
        # Note: The stored value is 'hide' but the form field is the inverse (show)
        try:
            button_hidden = self.instance['blocks']['button_to_ws']['hide']
            button_to_ws_val = not button_hidden  # Form field is inverse of stored value
        except (KeyError, TypeError):
            button_to_ws_val = False  # Default to not showing the button

        # Get translation from stored configuration
        try:
            translation_val = self.instance['translation']
            logger.info(f"🌐 TRANSLATION: Raw stored value: {translation_val}")
            logger.info(f"🌐 TRANSLATION: Type: {type(translation_val)}")
            
            # Convert DefaultJson or other objects to regular dict
            translation_val = self._ensure_dict(translation_val)
            logger.info(f"🌐 TRANSLATION: After _ensure_dict conversion: {translation_val}")
                
            if not translation_val:
                logger.info("🌐 TRANSLATION: Using defaults because translation_val is empty")
                translation_val = self._get_default_translation()
            else:
                logger.info(f"🌐 TRANSLATION: Using stored translation: {translation_val}")
        except (KeyError, TypeError) as e:
            logger.info(f"🌐 TRANSLATION: Exception occurred ({e}), using defaults")
            translation_val = self._get_default_translation()
        
        initial = {
            'width': width_val,
            'output_template': template_val,
            'flow_type': flow_type_val,
            'button_to_ws': button_to_ws_val,
            'translation': translation_val,  # Pass dict directly, WsJsonFormField will handle conversion
            # no height field
        }

        return initial

    def compose_to_save(self, data):
        # Dimensions: only width retained; height forced to blank for auto-resize
        dimensions = {
            'width': data.get('width', '') or '',
            'height': '',
        }

        # Get template value and handle empty/default cases
        raw_template = data.get('output_template') or data.get('interface-output_template')
        template_value = (raw_template or '').strip()
        
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"🎯 COMPOSE_TO_SAVE: Raw data keys: {list(data.keys())}")
        logger.info(f"🎯 COMPOSE_TO_SAVE: Raw output_template type: {type(raw_template)}")
        logger.info(f"🎯 COMPOSE_TO_SAVE: Raw output_template length: {len(raw_template) if raw_template is not None else 'None'}")
        logger.info(f"🎯 COMPOSE_TO_SAVE: Processed template_value length: {len(template_value)}")
        logger.info(f"🎯 COMPOSE_TO_SAVE: Template preview: {template_value[:100]}")
        
        result = {
            'dimensions': dimensions,
            'display': {
                'output_template': template_value
            },
            'flow_type': data['flow_type'],
            'tabs': {
                'visible': ['by_vehicle'],
                'primary': 'by_vehicle',
            },
            'blocks': {
                'button_to_ws': {
                    'hide': not data.get('button_to_ws', False),
                }
            },
            'translation': self._ensure_dict(data.get('translation')) or {},
        }
        
        # Debug logging for translation save
        logger.info(f"🌐 SAVE: Raw translation data: {data.get('translation')}")
        logger.info(f"🌐 SAVE: Processed translation: {result['translation']}")
        
        return result

    def clean_primary_tab(self):
        # Finder-v2 only has one tab (by_vehicle), so validation is simplified
        # Always return the single available tab regardless of form input
        return 'by_vehicle'

    def clean_tabs(self):
        # Finder-v2 only has one tab (by_vehicle), so always return it
        # This ensures consistency regardless of form input
        return ['by_vehicle']

    def clean_output_template(self):
        """Validate output template syntax and security"""
        from django.core.exceptions import ValidationError
        import logging
        
        logger = logging.getLogger(__name__)
        
        template = self.cleaned_data.get('output_template', '').strip()
        logger.info(f"🎯 CLEAN_OUTPUT_TEMPLATE: Received template length: {len(template)}")
        logger.info(f"🎯 CLEAN_OUTPUT_TEMPLATE: Template preview: {template[:100]}")
        
        if not template:
            logger.info("🎯 CLEAN_OUTPUT_TEMPLATE: Template is empty, returning empty")
            return template
        
        # Basic syntax validation
        try:
            self._validate_template_syntax(template)
            logger.info("🎯 CLEAN_OUTPUT_TEMPLATE: Validation passed")
        except ValidationError as e:
            logger.error(f"🎯 CLEAN_OUTPUT_TEMPLATE: Validation failed: {e.message}")
            raise forms.ValidationError(f'Template syntax error: {e.message}')
        
        logger.info(f"🎯 CLEAN_OUTPUT_TEMPLATE: Returning template length: {len(template)}")
        return template

    def _validate_template_syntax(self, template):
        """Basic template syntax validation"""
        from django.core.exceptions import ValidationError
        
        # Check for balanced tags
        for_count = template.count('{% for')
        endfor_count = template.count('{% endfor')
        if for_count != endfor_count:
            raise ValidationError('Unmatched for/endfor tags')
        
        if_count = template.count('{% if')
        endif_count = template.count('{% endif')
        if if_count != endif_count:
            raise ValidationError('Unmatched if/endif tags')
        
        # Check for dangerous content (basic XSS prevention)
        dangerous_patterns = ['<script', 'javascript:', 'onclick=', 'onerror=']
        template_lower = template.lower()
        for pattern in dangerous_patterns:
            if pattern in template_lower:
                raise ValidationError(f'Potentially unsafe content detected: {pattern}')

    def _get_default_template(self):
        """Return default template for pre-population"""
        return '''<div class="space-y-4">
  <h3 class="text-lg font-semibold mb-2 text-main-color">
    {{ make.name }} {{ model.name }} ({{ start_year }}-{{ end_year }})
  </h3>
  {% for wheel in wheels %}
    <div class="p-3 rounded-md border {% if wheel.is_stock %}border-primary-color bg-primary-color/10{% else %}border-secondary-color{% endif %}">
      <div class="font-medium uppercase tracking-wide text-sm mb-1 {% if wheel.is_stock %}text-primary-color{% else %}text-secondary-color{% endif %}">
        {% if wheel.is_stock %}OE option{% else %}After-market option{% endif %}
      </div>
      <div class="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span class="text-secondary-color">Front:</span>
          <span class="text-main-color">{{ wheel.front.tire }} – {{ wheel.front.rim }}</span>
        </div>
        {% if not wheel.showing_fp_only %}
          {% if wheel.rear.tire %}
            <div>
              <span class="text-secondary-color">Rear:</span>
              <span class="text-main-color">{{ wheel.rear.tire }} – {{ wheel.rear.rim }}</span>
            </div>
          {% endif %}
        {% endif %}
      </div>
    </div>
  {% endfor %}
</div>'''

    def _get_default_translation(self):
        return {
            "year_label": "Year",
            "make_label": "Make", 
            "model_label": "Model",
            "generation_label": "Generation",
            "modification_label": "Modification",
            "select_year": "Select Year",
            "select_make": "Select Make",
            "select_model": "Select Model", 
            "select_generation": "Select Generation",
            "select_modification": "Select Modification",
            "loading": "Loading...",
            "loading_results": "Loading results...",
            "no_results": "No results found. Please try different search criteria.",
            "search_button": "Unlock More Insights at Wheel-Size.com"
        }

    def _ensure_dict(self, value):
        """Convert DefaultJson or other objects to regular dict to avoid serialization issues."""
        if value is None:
            return {}
        
        # Handle DefaultJson objects specifically
        if hasattr(value, 'primary') and hasattr(value, 'default'):
            # This is a DefaultJson object - get the primary data directly
            return value.primary if isinstance(value.primary, dict) else {}
            
        if isinstance(value, dict):
            return value
            
        # Try to convert other objects to dict as fallback
        try:
            return dict(value)
        except (TypeError, ValueError):
            return {}


class FinderV2ThemeForm(FakeModelForm):
    """
    Comprehensive theme form for Finder-v2 widget.

    This form provides full theme customization including:
    - Predefined theme selection with gallery
    - Color customization (primary, secondary, accent, background, text)
    - Typography settings (font family, font size)
    - Visual effects (border radius, shadow intensity)
    - Advanced configuration options
    - Import/export functionality
    """
    
    # Theme selection
    predefined_theme = forms.ChoiceField(
        choices=[],
        required=False,
        label=_('Predefined Theme'),
        help_text=_('Select a predefined theme as a starting point'),
        widget=forms.Select(attrs={'class': 'form-control theme-selector'})
    )
    
    # Theme identification
    theme_name = forms.CharField(
        max_length=100,
        required=False,
        label=_('Theme Name'),
        help_text=_('Name for this theme configuration'),
        initial='Custom Theme',
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    # Color customization
    primary_color = forms.CharField(
        max_length=7,
        required=False,
        label=_('Primary Color'),
        help_text=_('Primary brand color (buttons, links, highlights). Use in templates: text-primary-color, bg-primary-color, border-primary-color'),
        widget=forms.TextInput(attrs={
            'type': 'color',
            'class': 'form-control color-picker',
            'data-field': 'primary'
        })
    )
    
    secondary_color = forms.CharField(
        max_length=7,
        required=False,
        label=_('Secondary Color'),
        help_text=_('Secondary color (borders, dividers, subtle elements). Use in templates: text-secondary-color, bg-secondary-color, border-secondary-color'),
        widget=forms.TextInput(attrs={
            'type': 'color',
            'class': 'form-control color-picker',
            'data-field': 'secondary'
        })
    )
    
    accent_color = forms.CharField(
        max_length=7,
        required=False,
        label=_('Accent Color'),
        help_text=_('Accent color (success states, calls-to-action). Use in templates: text-accent-color, bg-accent-color, border-accent-color'),
        widget=forms.TextInput(attrs={
            'type': 'color',
            'class': 'form-control color-picker',
            'data-field': 'accent'
        })
    )
    
    background_color = forms.CharField(
        max_length=7,
        required=False,
        label=_('Background Color'),
        help_text=_('Background color for the widget. Use in templates: text-background-color, bg-background-color, border-background-color'),
        widget=forms.TextInput(attrs={
            'type': 'color',
            'class': 'form-control color-picker',
            'data-field': 'background'
        })
    )
    
    text_color = forms.CharField(
        max_length=7,
        required=False,
        label=_('Text Color'),
        help_text=_('Primary text color. Use in templates: text-main-color, bg-main-color, border-main-color'),
        widget=forms.TextInput(attrs={
            'type': 'color',
            'class': 'form-control color-picker',
            'data-field': 'text'
        })
    )
    
    # Typography settings
    font_family = forms.ChoiceField(
        choices=[
            ('system-ui', _('System UI')),
            ('Inter', _('Inter')),
            ('Roboto', _('Roboto')),
            ('Open Sans', _('Open Sans')),
            ('Lato', _('Lato')),
            ('Montserrat', _('Montserrat')),
            ('Poppins', _('Poppins')),
            ('custom', _('Custom (specify in advanced config)')),
        ],
        required=False,
        label=_('Font Family'),
        help_text=_('Font family for text elements'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    base_font_size = forms.ChoiceField(
        choices=[
            ('14px', _('Small (14px)')),
            ('16px', _('Medium (16px)')),
            ('18px', _('Large (18px)')),
            ('20px', _('Extra Large (20px)')),
        ],
        required=False,
        label=_('Base Font Size'),
        help_text=_('Base font size for the widget'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    # Visual effects
    border_radius = forms.ChoiceField(
        choices=[
            ('0', _('None (0px)')),
            ('0.25rem', _('Small (4px)')),
            ('0.375rem', _('Medium (6px)')),
            ('0.5rem', _('Large (8px)')),
            ('0.75rem', _('Extra Large (12px)')),
            ('1rem', _('Round (16px)')),
        ],
        required=False,
        label=_('Border Radius'),
        help_text=_('Border radius for form elements and containers'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    shadow_intensity = forms.ChoiceField(
        choices=[
            ('none', _('None')),
            ('light', _('Light')),
            ('medium', _('Medium')),
            ('heavy', _('Heavy')),
        ],
        required=False,
        label=_('Shadow Intensity'),
        help_text=_('Shadow intensity for elevated elements'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    # Advanced styling controls
    font_weight = forms.ChoiceField(
        choices=[
            ('300', _('Light (300)')),
            ('400', _('Normal (400)')),
            ('500', _('Medium (500)')),
            ('600', _('Semi-bold (600)')),
            ('700', _('Bold (700)')),
        ],
        required=False,
        label=_('Font Weight'),
        help_text=_('Font weight for text elements'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    line_height = forms.ChoiceField(
        choices=[
            ('1.2', _('Tight (1.2)')),
            ('1.4', _('Snug (1.4)')),
            ('1.5', _('Normal (1.5)')),
            ('1.6', _('Relaxed (1.6)')),
            ('1.8', _('Loose (1.8)')),
        ],
        required=False,
        label=_('Line Height'),
        help_text=_('Line height for text elements'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    letter_spacing = forms.ChoiceField(
        choices=[
            ('-0.025em', _('Tighter (-0.025em)')),
            ('0', _('Normal (0)')),
            ('0.025em', _('Wide (0.025em)')),
            ('0.05em', _('Wider (0.05em)')),
            ('0.1em', _('Widest (0.1em)')),
        ],
        required=False,
        label=_('Letter Spacing'),
        help_text=_('Letter spacing for text elements'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    element_padding = forms.ChoiceField(
        choices=[
            ('0.25rem', _('Extra Small (4px)')),
            ('0.5rem', _('Small (8px)')),
            ('0.75rem', _('Medium (12px)')),
            ('1rem', _('Large (16px)')),
            ('1.25rem', _('Extra Large (20px)')),
        ],
        required=False,
        label=_('Element Padding'),
        help_text=_('Padding for form elements and buttons'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    element_margin = forms.ChoiceField(
        choices=[
            ('0.25rem', _('Extra Small (4px)')),
            ('0.5rem', _('Small (8px)')),
            ('0.75rem', _('Medium (12px)')),
            ('1rem', _('Large (16px)')),
            ('1.25rem', _('Extra Large (20px)')),
        ],
        required=False,
        label=_('Element Margin'),
        help_text=_('Margin between elements'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    border_width = forms.ChoiceField(
        choices=[
            ('0', _('None (0px)')),
            ('1px', _('Thin (1px)')),
            ('2px', _('Medium (2px)')),
            ('3px', _('Thick (3px)')),
            ('4px', _('Extra Thick (4px)')),
        ],
        required=False,
        label=_('Border Width'),
        help_text=_('Border width for form elements'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    animation_speed = forms.ChoiceField(
        choices=[
            ('0.1s', _('Fast (0.1s)')),
            ('0.2s', _('Quick (0.2s)')),
            ('0.3s', _('Normal (0.3s)')),
            ('0.5s', _('Slow (0.5s)')),
            ('0.8s', _('Very Slow (0.8s)')),
        ],
        required=False,
        label=_('Animation Speed'),
        help_text=_('Speed of hover and transition animations'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    hover_effect = forms.ChoiceField(
        choices=[
            ('darken', _('Darken')),
            ('lighten', _('Lighten')),
            ('brightness', _('Brightness')),
            ('opacity', _('Opacity')),
            ('scale', _('Scale')),
            ('shadow', _('Shadow')),
        ],
        required=False,
        label=_('Hover Effect'),
        help_text=_('Effect applied on hover interactions'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    # Advanced configuration
    advanced_config = forms.CharField(
        required=False,
        label=_('Advanced Configuration'),
        help_text=_('Advanced theme configuration in JSON format'),
        widget=forms.Textarea(attrs={
            'rows': 8,
            'class': 'form-control font-mono',
            'placeholder': _('{\n  "hover_effects": true,\n  "transition_timing": "ease-in-out",\n  "animation_speed": "0.2s"\n}')
        })
    )
    
    # Import/Export
    theme_import = forms.FileField(
        required=False,
        label=_('Import Theme'),
        help_text=_('Import theme configuration from JSON file'),
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.json',
            'data-action': 'import-theme'
        })
    )
    
    use_default_json = False

    def __init__(self, *args, **kwargs):
        import logging
        from django.conf import settings
        logger = logging.getLogger(__name__)

        # Only log in DEBUG mode to reduce noise
        if settings.DEBUG:
            logger.debug(f"FinderV2ThemeForm.__init__ called with {len(args)} args, {len(kwargs)} kwargs")

        self.widget_config = kwargs.pop('widget_config', None) or kwargs.pop('widget', None)
        super(FinderV2ThemeForm, self).__init__(*args, **kwargs)

        # Import predefined themes
        from .default_config.predefined_themes import PredefinedThemes
        
        # Set predefined theme choices
        theme_choices = [('', _('Custom Theme'))] + PredefinedThemes.get_theme_choices()
        self.fields['predefined_theme'].choices = theme_choices
        
        # Set default values
        self.fields['predefined_theme'].initial = PredefinedThemes.DEFAULT_THEME
        
        # Set up form validation
        self._setup_field_validation()

    def _setup_field_validation(self):
        """Setup custom field validation"""
        # Add custom CSS classes for real-time validation
        color_fields = ['primary_color', 'secondary_color', 'accent_color', 'background_color', 'text_color']
        
        for field_name in color_fields:
            if field_name in self.fields:
                self.fields[field_name].widget.attrs.update({
                    'data-validate': 'color',
                    'pattern': '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$'
                })

    def clean_advanced_config(self):
        """Validate advanced configuration JSON"""
        import json
        from .utils.theme_validator import ThemeValidator
        
        advanced_config = self.cleaned_data.get('advanced_config', '').strip()
        
        if not advanced_config:
            return {}
        
        try:
            config_data = json.loads(advanced_config)
            if not isinstance(config_data, dict):
                raise forms.ValidationError(_('Advanced configuration must be a JSON object'))
            
            # Validate configuration using ThemeValidator
            validator = ThemeValidator()
            allowed_keys = {
                'custom_font_family', 'custom_font_sizes', 'responsive_overrides',
                'animation_speed', 'transition_timing', 'hover_effects',
                'focus_styles', 'custom_shadows', 'gradient_colors'
            }
            
            for key in config_data.keys():
                if key not in allowed_keys:
                    raise forms.ValidationError(f'Unknown configuration key: {key}')
            
            return config_data
            
        except json.JSONDecodeError as e:
            raise forms.ValidationError(f'Invalid JSON format: {e}')

    def clean(self):
        """Validate complete theme configuration"""
        import logging, pprint
        logger = logging.getLogger(__name__)
        logger.debug("🎨 THEME CLEAN: raw POST data: %s", pprint.pformat(self.data if hasattr(self, 'data') else {}))
        cleaned_data = super().clean()
        logger.debug("🎨 THEME CLEAN: cleaned_data after super: %s", pprint.pformat(cleaned_data))
        
        # Handle predefined theme selection without clobbering user-provided values
        predefined_theme = cleaned_data.get('predefined_theme')
        if predefined_theme and predefined_theme != '':
            from .default_config.predefined_themes import PredefinedThemes
            theme_data = PredefinedThemes.get_theme(predefined_theme) or {}

            # Only populate theme_name if the user did not supply their own
            if not self.data.get('theme-theme_name') and theme_data.get('name'):
                cleaned_data['theme_name'] = theme_data['name']

            # Record selection for compose_to_save but keep user customisations
            self._selected_predefined_theme = predefined_theme
        
        # Build theme data for validation
        theme_data = {
            'colors': {
                'primary': cleaned_data.get('primary_color', '#3B82F6'),
                'secondary': cleaned_data.get('secondary_color', '#6B7280'),
                'accent': cleaned_data.get('accent_color', '#10B981'),
                'background': cleaned_data.get('background_color', '#FFFFFF'),
                'text': cleaned_data.get('text_color', '#1F2937'),
            },
            'typography': {
                'font_family': cleaned_data.get('font_family', 'system-ui'),
                'base_font_size': cleaned_data.get('base_font_size', '16px'),
            },
            'effects': {
                'border_radius': cleaned_data.get('border_radius', '0.375rem'),
                'shadow_intensity': cleaned_data.get('shadow_intensity', 'medium'),
            },
            'advanced_config': cleaned_data.get('advanced_config', {}),
        }
        
        # Validate theme (skip validation if ThemeValidator doesn't exist)
        try:
            from .utils.theme_validator import ThemeValidator
            validator = ThemeValidator()
            validation_result = validator.validate_theme(theme_data)
            
            if not validation_result['valid']:
                for error in validation_result['errors']:
                    self.add_error(None, error)
            
            # Add warnings as form messages (if supported)
            if validation_result['warnings']:
                for warning in validation_result['warnings']:
                    # Add warning to form (will be displayed in template)
                    if not hasattr(self, '_warnings'):
                        self._warnings = []
                    self._warnings.append(warning)
        except ImportError:
            # ThemeValidator not available, skip validation
            pass
        logger.debug("🎨 THEME CLEAN: final cleaned_data: %s", pprint.pformat(cleaned_data))
        
        return cleaned_data

    def decompose_to_initial(self):
        """Extract theme data for form initialization"""
        # Check if widget has existing theme
        if self.widget_config and hasattr(self.widget_config, 'theme'):
            theme = self.widget_config.theme
            
            return {
                'predefined_theme': '',  # TODO: Re-enable after migration - getattr(theme, 'predefined_theme', ''),
                'theme_name': theme.theme_name,
                'primary_color': theme.primary_color,
                'secondary_color': theme.secondary_color,
                'accent_color': theme.accent_color,
                'background_color': theme.background_color,
                'text_color': theme.text_color,
                'font_family': theme.font_family,
                'base_font_size': theme.base_font_size,
                'font_weight': getattr(theme, 'font_weight', '400'),
                'line_height': getattr(theme, 'line_height', '1.5'),
                'letter_spacing': getattr(theme, 'letter_spacing', '0'),
                'element_padding': getattr(theme, 'element_padding', '0.75rem'),
                'element_margin': getattr(theme, 'element_margin', '0.5rem'),
                'border_radius': theme.border_radius,
                'border_width': getattr(theme, 'border_width', '1px'),
                'shadow_intensity': theme.shadow_intensity,
                'animation_speed': getattr(theme, 'animation_speed', '0.3s'),
                'hover_effect': getattr(theme, 'hover_effect', 'darken'),
                'advanced_config': json.dumps(theme.advanced_config, indent=2) if theme.advanced_config else '',
            }
        else:
            # Use default theme
            from .default_config.predefined_themes import PredefinedThemes
            default_theme = PredefinedThemes.get_theme(PredefinedThemes.DEFAULT_THEME)
            
            return {
                'predefined_theme': PredefinedThemes.DEFAULT_THEME,
                'primary_color': default_theme['colors']['primary'],
                'secondary_color': default_theme['colors']['secondary'],
                'accent_color': default_theme['colors']['accent'],
                'background_color': default_theme['colors']['background'],
                'text_color': default_theme['colors']['text'],
                'font_family': default_theme['typography']['font_family'],
                'base_font_size': default_theme['typography']['base_font_size'],
                'border_radius': default_theme['effects']['border_radius'],
                'shadow_intensity': default_theme['effects']['shadow_intensity'],
                'advanced_config': json.dumps(default_theme.get('advanced_config', {}), indent=2),
            }

    def compose_to_save(self, data):
        """Compose theme data for saving (deferred until after widget config is saved)"""
        import logging
        from django.conf import settings
        logger = logging.getLogger(__name__)

        # Only log in DEBUG mode to reduce noise
        if settings.DEBUG:
            logger.debug(f"Theme compose_to_save called with data type {type(data)}")
            logger.debug(f"Widget config: {self.widget_config}")
        
        # Store theme data for later saving (after widget config is saved)
        self._theme_data_to_save = data
        logger.info("🎨 THEME COMPOSE_TO_SAVE: Stored theme data for deferred saving")
        
        # Return theme data for legacy compatibility without saving to database
        return {
            'theme_name': data.get('predefined_theme', 'custom'),
            'colors': {
                'primary': data.get('primary_color', '#3B82F6'),
                'secondary': data.get('secondary_color', '#6B7280'),
                'accent': data.get('accent_color', '#10B981'),
                'background': data.get('background_color', '#FFFFFF'),
                'text': data.get('text_color', '#1F2937'),
            },
            'typography': {
                'font_family': data.get('font_family', 'system-ui'),
                'base_font_size': data.get('base_font_size', '16px'),
            },
            'effects': {
                'border_radius': data.get('border_radius', '0.375rem'),
                'shadow_intensity': data.get('shadow_intensity', 'medium'),
            }
        }
    
    def save_theme_after_config(self, widget_config):
        """Save theme to database after widget config is saved"""
        import logging
        logger = logging.getLogger(__name__)
        
        if not hasattr(self, '_theme_data_to_save'):
            logger.warning("🎨 SAVE_THEME_AFTER_CONFIG: No theme data to save")
            return
            
        data = self._theme_data_to_save
        logger.info(f"🎨 SAVE_THEME_AFTER_CONFIG: Saving theme for widget {widget_config.uuid}")
        
        # Now we can safely save the theme since the widget config exists
        try:
            theme = widget_config.theme
            logger.info(f"Updating existing theme for widget {widget_config.uuid}")
            
            # Update existing theme
            theme.primary_color = data.get('primary_color', '#3B82F6')
            theme.secondary_color = data.get('secondary_color', '#6B7280')
            theme.accent_color = data.get('accent_color', '#10B981')
            theme.background_color = data.get('background_color', '#FFFFFF')
            theme.text_color = data.get('text_color', '#1F2937')
            theme.font_family = data.get('font_family', 'system-ui')
            theme.base_font_size = data.get('base_font_size', '16px')
            theme.font_weight = data.get('font_weight', '400')
            theme.line_height = data.get('line_height', '1.5')
            theme.letter_spacing = data.get('letter_spacing', '0')
            theme.element_padding = data.get('element_padding', '0.75rem')
            theme.element_margin = data.get('element_margin', '0.5rem')
            theme.border_radius = data.get('border_radius', '0.375rem')
            theme.border_width = data.get('border_width', '1px')
            theme.shadow_intensity = data.get('shadow_intensity', 'medium')
            theme.animation_speed = data.get('animation_speed', '0.3s')
            theme.hover_effect = data.get('hover_effect', 'darken')
            theme.advanced_config = data.get('advanced_config', {})
            
            # Update theme name and predefined theme selection
            predefined_theme = data.get('predefined_theme')
            if predefined_theme:
                from .default_config.predefined_themes import PredefinedThemes
                theme_data = PredefinedThemes.get_theme(predefined_theme)
                theme.theme_name = theme_data['name']
                # TODO: Re-enable after migration - theme.predefined_theme = predefined_theme
            elif data.get('theme_name'):
                theme.theme_name = data.get('theme_name')
                # TODO: Re-enable after migration - theme.predefined_theme = ''  # Clear predefined theme for custom themes
            else:
                theme.theme_name = 'Custom'
                # TODO: Re-enable after migration - theme.predefined_theme = ''
            
            theme.save()
            logger.info(f"Successfully updated theme for widget {widget_config.uuid}")
            
        except (AttributeError, Exception) as e:
            # Create new theme
            logger.info(f"Creating new theme for widget {widget_config.uuid}: {e}")
            from ..common.models import WidgetTheme
            
            predefined_theme = data.get('predefined_theme')
            if predefined_theme:
                from .default_config.predefined_themes import PredefinedThemes
                theme_data = PredefinedThemes.get_theme(predefined_theme)
                theme_name = theme_data['name']
                logger.info(f"Creating predefined theme: {theme_name}")
            else:
                theme_name = data.get('theme_name', 'Custom')
                logger.info(f"Creating custom theme: {theme_name}")
            
            # Create the theme with comprehensive logging
            theme_data = {
                'widget': widget_config,  # Use the passed widget_config
                'theme_name': theme_name,
                # TODO: Re-enable after migration - 'predefined_theme': predefined_theme or '',
                'primary_color': data.get('primary_color', '#3B82F6'),
                'secondary_color': data.get('secondary_color', '#6B7280'),
                'accent_color': data.get('accent_color', '#10B981'),
                'background_color': data.get('background_color', '#FFFFFF'),
                'text_color': data.get('text_color', '#1F2937'),
                'font_family': data.get('font_family', 'system-ui'),
                'base_font_size': data.get('base_font_size', '16px'),
                'font_weight': data.get('font_weight', '400'),
                'line_height': data.get('line_height', '1.5'),
                'letter_spacing': data.get('letter_spacing', '0'),
                'element_padding': data.get('element_padding', '0.75rem'),
                'element_margin': data.get('element_margin', '0.5rem'),
                'border_radius': data.get('border_radius', '0.375rem'),
                'border_width': data.get('border_width', '1px'),
                'shadow_intensity': data.get('shadow_intensity', 'medium'),
                'animation_speed': data.get('animation_speed', '0.3s'),
                'hover_effect': data.get('hover_effect', 'darken'),
                'advanced_config': data.get('advanced_config', {})
            }
            
            logger.info(f"Theme data to create: font_size={theme_data['base_font_size']}, primary={theme_data['primary_color']}")
            
            new_theme = WidgetTheme.objects.create(**theme_data)
            logger.info(f"Successfully created theme {new_theme.id} for widget {widget_config.uuid}")
    
    def get_theme_gallery_data(self):
        """Get theme gallery data for JavaScript"""
        from .default_config.predefined_themes import PredefinedThemes
        return PredefinedThemes.get_theme_gallery()
    
    def export_theme_json(self):
        """Export current theme as JSON"""
        if not self.is_valid():
            return None
        
        theme_data = {
            'theme_name': self.cleaned_data.get('predefined_theme', 'custom'),
            'colors': {
                'primary': self.cleaned_data.get('primary_color'),
                'secondary': self.cleaned_data.get('secondary_color'),
                'accent': self.cleaned_data.get('accent_color'),
                'background': self.cleaned_data.get('background_color'),
                'text': self.cleaned_data.get('text_color'),
            },
            'typography': {
                'font_family': self.cleaned_data.get('font_family'),
                'base_font_size': self.cleaned_data.get('base_font_size'),
            },
            'effects': {
                'border_radius': self.cleaned_data.get('border_radius'),
                'shadow_intensity': self.cleaned_data.get('shadow_intensity'),
            },
            'advanced_config': self.cleaned_data.get('advanced_config', {}),
            'exported_at': timezone.now().isoformat(),
        }
        
        return json.dumps(theme_data, indent=2)
    
    def import_theme_json(self, json_data):
        """Import theme from JSON data"""
        try:
            theme_data = json.loads(json_data)
            
            # Validate imported data
            from .utils.theme_validator import ThemeValidator
            validator = ThemeValidator()
            validation_result = validator.validate_theme(theme_data)
            
            if not validation_result['valid']:
                return False, validation_result['errors']
            
            # Apply imported data to form
            if 'colors' in theme_data:
                for color_name, color_value in theme_data['colors'].items():
                    field_name = f"{color_name}_color"
                    if field_name in self.fields:
                        self.initial[field_name] = color_value
            
            if 'typography' in theme_data:
                for typo_name, typo_value in theme_data['typography'].items():
                    if typo_name in self.fields:
                        self.initial[typo_name] = typo_value
            
            if 'effects' in theme_data:
                for effect_name, effect_value in theme_data['effects'].items():
                    if effect_name in self.fields:
                        self.initial[effect_name] = effect_value
            
            if 'advanced_config' in theme_data:
                self.initial['advanced_config'] = json.dumps(theme_data['advanced_config'], indent=2)
            
            return True, []
            
        except json.JSONDecodeError as e:
            return False, [f'Invalid JSON format: {e}']
        except Exception as e:
            return False, [f'Import failed: {e}']


class FinderV2SearchHistoryForm(FakeModelForm):
    """
    Form for configuring search history settings in Finder V2 widget.
    
    Allows administrators to control search history behavior including:
    - Enable/disable search history functionality
    - Configure storage limits and display options
    - Set user experience preferences
    """
    
    enabled = forms.BooleanField(
        required=False,
        initial=True,
        label=_('Enable Search History'),
        help_text=_('Allow users to save and re-execute recent searches')
    )
    
    max_items = forms.IntegerField(
        min_value=1,
        max_value=50,
        initial=10,
        required=False,
        label=_('Maximum Stored Searches'),
        help_text=_('Maximum number of searches to store in browser localStorage (1-50)')
    )
    
    display_items = forms.IntegerField(
        min_value=1,
        max_value=20,
        initial=5,
        required=False,
        label=_('Default Display Items'),
        help_text=_('Number of searches to show by default (1-20)')
    )
    
    auto_expand = forms.BooleanField(
        required=False,
        initial=False,
        label=_('Auto-expand Panel'),
        help_text=_('Automatically expand search history panel when searches are available')
    )
    
    show_timestamps = forms.BooleanField(
        required=False,
        initial=True,
        label=_('Show Timestamps'),
        help_text=_('Display relative timestamps for each search (e.g., "2 hours ago")')
    )
    
    use_default_json = True

    def clean_max_items(self):
        """Validate max_items range"""
        max_items = self.cleaned_data.get('max_items')
        if max_items is not None:
            if max_items < 1 or max_items > 50:
                raise forms.ValidationError(_('Maximum items must be between 1 and 50'))
        return max_items or 10

    def clean_display_items(self):
        """Validate display_items range and ensure it's not greater than max_items"""
        display_items = self.cleaned_data.get('display_items')
        max_items = self.cleaned_data.get('max_items', 10)
        
        if display_items is not None:
            if display_items < 1 or display_items > 20:
                raise forms.ValidationError(_('Display items must be between 1 and 20'))
            
            if display_items > max_items:
                raise forms.ValidationError(_('Display items cannot exceed maximum stored searches'))
        
        return display_items or 5

    def decompose_to_initial(self):
        """Extract search history configuration for form initialization"""
        import logging
        logger = logging.getLogger(__name__)

        logger.debug("🔍 SEARCH_HISTORY DECOMPOSE: Starting decomposition")

        # Default values from the default config
        default_initial = {
            'enabled': True,
            'max_items': 10,
            'display_items': 5,
            'auto_expand': False,
            'show_timestamps': True,
        }

        # Handle cases where self.instance might be None
        if not self.instance:
            logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: No instance, returning defaults: {default_initial}")
            return default_initial

        logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Instance type: {type(self.instance)}")

        # Extract saved search history configuration values
        # Note: self.instance IS the search_history section, not the full config
        # Use try/except blocks to safely access configuration data
        try:
            enabled_val = self.instance['enabled']
            logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Found saved enabled: {enabled_val}")
        except (KeyError, TypeError):
            enabled_val = default_initial['enabled']
            logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Using default enabled: {enabled_val}")

        try:
            # Handle both camelCase (maxItems) and snake_case (max_items) for backward compatibility
            max_items_val = self.instance.get('maxItems') or self.instance.get('max_items')
            if max_items_val is not None:
                max_items_val = int(max_items_val)
                logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Found saved max_items: {max_items_val}")
            else:
                raise KeyError("No max_items found")
        except (KeyError, TypeError, ValueError):
            max_items_val = default_initial['max_items']
            logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Using default max_items: {max_items_val}")

        try:
            # Handle both camelCase (displayItems) and snake_case (display_items) for backward compatibility
            display_items_val = self.instance.get('displayItems') or self.instance.get('display_items')
            if display_items_val is not None:
                display_items_val = int(display_items_val)
                logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Found saved display_items: {display_items_val}")
            else:
                raise KeyError("No display_items found")
        except (KeyError, TypeError, ValueError):
            display_items_val = default_initial['display_items']
            logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Using default display_items: {display_items_val}")

        try:
            # Handle both camelCase (autoExpand) and snake_case (auto_expand) for backward compatibility
            auto_expand_val = self.instance.get('autoExpand')
            if auto_expand_val is None:
                auto_expand_val = self.instance.get('auto_expand')
            if auto_expand_val is not None:
                logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Found saved auto_expand: {auto_expand_val}")
            else:
                raise KeyError("No auto_expand found")
        except (KeyError, TypeError):
            auto_expand_val = default_initial['auto_expand']
            logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Using default auto_expand: {auto_expand_val}")

        try:
            # Handle both camelCase (showTimestamps) and snake_case (show_timestamps) for backward compatibility
            show_timestamps_val = self.instance.get('showTimestamps')
            if show_timestamps_val is None:
                show_timestamps_val = self.instance.get('show_timestamps')
            if show_timestamps_val is not None:
                logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Found saved show_timestamps: {show_timestamps_val}")
            else:
                raise KeyError("No show_timestamps found")
        except (KeyError, TypeError):
            show_timestamps_val = default_initial['show_timestamps']
            logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Using default show_timestamps: {show_timestamps_val}")

        # Compose the final initial data
        final_initial = {
            'enabled': bool(enabled_val),
            'max_items': int(max_items_val),
            'display_items': int(display_items_val),
            'auto_expand': bool(auto_expand_val),
            'show_timestamps': bool(show_timestamps_val),
        }

        logger.debug(f"🔍 SEARCH_HISTORY DECOMPOSE: Final initial data: {final_initial}")
        return final_initial

    def compose_to_save(self, data):
        """Compose search history configuration for saving"""
        import logging
        logger = logging.getLogger(__name__)
        
        logger.debug(f"🔍 SEARCH_HISTORY COMPOSE: Starting composition")
        logger.debug(f"🔍 SEARCH_HISTORY COMPOSE: Input data: {data}")
        
        # Extract form data with defaults and ensure proper types
        # Handle both DefaultJson objects and regular dicts
        try:
            enabled = data['enabled'] if 'enabled' in data else True
        except (KeyError, TypeError):
            enabled = True

        try:
            max_items = int(data['max_items']) if 'max_items' in data and data['max_items'] else 10
        except (KeyError, TypeError, ValueError):
            max_items = 10

        try:
            display_items = int(data['display_items']) if 'display_items' in data and data['display_items'] else 5
        except (KeyError, TypeError, ValueError):
            display_items = 5

        try:
            auto_expand = data['auto_expand'] if 'auto_expand' in data else False
        except (KeyError, TypeError):
            auto_expand = False

        try:
            show_timestamps = data['show_timestamps'] if 'show_timestamps' in data else True
        except (KeyError, TypeError):
            show_timestamps = True
        
        # Ensure display_items doesn't exceed max_items
        if display_items > max_items:
            original_display_items = display_items
            display_items = max_items
            logger.warning(f"🔍 SEARCH_HISTORY COMPOSE: Adjusted display_items from {original_display_items} to {display_items} (max_items limit)")
        
        final_data = {
            'enabled': bool(enabled),
            'maxItems': int(max_items),  # Note: camelCase for frontend consumption
            'displayItems': int(display_items),  # Note: camelCase for frontend consumption
            'autoExpand': bool(auto_expand),  # Note: camelCase for frontend consumption
            'showTimestamps': bool(show_timestamps),  # Note: camelCase for frontend consumption
        }

        logger.debug(f"🔍 SEARCH_HISTORY COMPOSE: Final composed data: {final_data}")
        return final_data


class FinderV2ConfigForm(WidgetConfigForm):
    form_classes = {
        'config': WidgetCommonForm,
        'theme': FinderV2ThemeForm,
        'content': ContentFilterForm,
        'interface': FinderV2InterfaceForm,
        'search_history': FinderV2SearchHistoryForm,
        'permissions': WidgetPermissionsForm,
    }


class FinderV2DemoConfigForm(WidgetConfigForm):
    form_classes = {
        'config': DemoWidgetCommonForm,
        'theme': FinderV2ThemeForm,
        'content': ContentFilterForm,
        'interface': FinderV2InterfaceForm,
        'search_history': FinderV2SearchHistoryForm,
    }



