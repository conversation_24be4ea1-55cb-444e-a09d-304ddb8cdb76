"""
Template Engine for Finder-v2 Widget
Python implementation of the JavaScript template engine for server-side preview rendering.
Supports Django-like template syntax with variables, loops, and conditionals.
"""

import re
import json
import html
from typing import Any, Dict, List, Union


class TemplateEngine:
    """
    Lightweight template engine that mimics the JavaScript version.
    Supports basic Django template syntax for secure template rendering.
    """
    
    def __init__(self):
        # Regex patterns for template syntax
        self.variable_pattern = re.compile(r'\{\{\s*([^}]+)\s*\}\}')
        self.for_pattern = re.compile(r'\{\%\s*for\s+(\w+)\s+in\s+(\w+(?:\.\w+)*)\s*\%\}(.*?)\{\%\s*endfor\s*\%\}', re.DOTALL)
        self.if_pattern = re.compile(r'\{\%\s*if\s+([^%]+)\%\}(.*?)(?:\{\%\s*else\s*\%\}(.*?))?\{\%\s*endif\s*\%\}', re.DOTALL)
    
    def render_template(self, template: str, data: Dict[str, Any]) -> str:
        """
        Main entry point for template rendering.
        Processes template with data and returns HTML string.
        """
        if not template or not isinstance(template, str):
            return '<div class="text-gray-500">No template provided</div>'
        
        try:
            # Use a different approach - process template tags holistically
            result = self._process_template_recursive(template, data)
            return result
        except Exception as e:
            return f'<div class="text-red-600 bg-red-50 p-4 border border-red-200 rounded">Template Error: {html.escape(str(e))}</div>'
    
    def _process_template_recursive(self, template: str, data: Dict[str, Any]) -> str:
        """
        Process template recursively, handling all template tags properly.
        This ensures correct processing order regardless of nesting.
        """
        # Process loops first (outermost structure)
        template = self._process_all_loops(template, data)
        
        # Then process all conditionals
        template = self._process_all_conditionals(template, data)
        
        # Finally process variables
        template = self.process_variables(template, data)
        
        return template
    
    def _process_all_loops(self, template: str, data: Dict[str, Any]) -> str:
        """Process all for loops in the template"""
        while '{% for ' in template:
            loop_start = template.find('{% for ')
            if loop_start == -1:
                break
                
            # Find the matching endfor
            endfor_pos = self._find_matching_endfor(template, loop_start)
            if endfor_pos == -1:
                # Malformed template
                return template[:loop_start] + '<span class="text-red-500">[Missing {% endfor %}]</span>' + template[loop_start:]
            
            # Extract and process this loop
            before = template[:loop_start]
            loop_block = template[loop_start:endfor_pos + len('{% endfor %}')]
            after = template[endfor_pos + len('{% endfor %}'):]
            
            processed_loop = self._process_single_loop(loop_block, data)
            template = before + processed_loop + after
        
        return template
    
    def _process_all_conditionals(self, template: str, data: Dict[str, Any]) -> str:
        """Process all conditionals in the template"""  
        while '{% if ' in template:
            if_start = template.find('{% if ')
            if if_start == -1:
                break
                
            # Find the matching endif
            endif_pos = self._find_matching_endif(template, if_start)
            if endif_pos == -1:
                # Malformed template
                return template[:if_start] + '<span class="text-red-500">[Missing {% endif %}]</span>' + template[if_start:]
            
            # Extract and process this conditional
            before = template[:if_start]
            conditional_block = template[if_start:endif_pos + len('{% endif %}')]
            after = template[endif_pos + len('{% endif %}'):]
            
            processed_conditional = self._process_single_conditional(conditional_block, data)
            template = before + processed_conditional + after
        
        return template
    
    def _find_matching_endfor(self, template: str, for_start: int) -> int:
        """Find the matching {% endfor %} for a given {% for %} position"""
        depth = 1  # We start with depth 1 since we already found the first {% for %}
        pos = for_start + len('{% for ')  # Start after the opening for tag
        
        while pos < len(template):
            next_for = template.find('{% for ', pos)
            next_endfor = template.find('{% endfor %}', pos)
            
            if next_endfor == -1:
                return -1  # No endfor found
            
            if next_for != -1 and next_for < next_endfor:
                # Found a nested for before the next endfor
                depth += 1
                pos = next_for + len('{% for ')
            else:
                # Found an endfor
                depth -= 1
                if depth == 0:
                    return next_endfor  # This is our matching endfor
                else:
                    pos = next_endfor + len('{% endfor %}')
        
        return -1  # No matching endfor found
    
    def _process_single_loop(self, loop_block: str, data: Dict[str, Any]) -> str:
        """Process a single for loop block"""
        # Parse the for loop structure
        for_match = re.match(r'{% for (\w+) in (\w+(?:\.\w+)*) %}', loop_block)
        if not for_match:
            return loop_block
        
        item_var = for_match.group(1).strip()
        array_path = for_match.group(2).strip()
        content_start = for_match.end()
        endfor_pos = loop_block.rfind('{% endfor %}')
        
        loop_content = loop_block[content_start:endfor_pos]
        
        try:
            array_data = self.get_nested_value(data, array_path)
            if not isinstance(array_data, list):
                return '<div class="text-gray-400">No items to display</div>'
            
            result_parts = []
            for item in array_data:
                # Create new context with loop item
                loop_context = data.copy()
                loop_context[item_var] = item
                
                # Process the loop content completely with the loop context
                # First process any nested loops
                processed_content = self._process_all_loops(loop_content, loop_context)
                # Then process conditionals
                processed_content = self._process_all_conditionals(processed_content, loop_context)
                # Finally process variables
                processed_content = self.process_variables(processed_content, loop_context)
                result_parts.append(processed_content)
            
            return ''.join(result_parts)
            
        except Exception as e:
            return f'<div class="text-red-600">Loop error: {html.escape(str(e))}</div>'
    
    def process_variables(self, template: str, data: Dict[str, Any]) -> str:
        """Process variable substitutions like {{ variable.path }}"""
        def replace_variable(match):
            var_path = match.group(1).strip()
            try:
                value = self.get_nested_value(data, var_path)
                if value is None:
                    return f'<span class="text-gray-400" title="Missing: {var_path}">—</span>'
                return html.escape(str(value))
            except Exception:
                return f'<span class="text-gray-400" title="Error: {var_path}">—</span>'
        
        return self.variable_pattern.sub(replace_variable, template)
    
    
    def _find_matching_endif(self, template: str, if_start: int) -> int:
        """Find the matching {% endif %} for a given {% if %} position"""
        depth = 1  # We start with depth 1 since we already found the first {% if %}
        pos = if_start + len('{% if ')  # Start after the opening if tag
        
        while pos < len(template):
            # Look for if/endif tags
            next_if = template.find('{% if ', pos)
            next_endif = template.find('{% endif %}', pos)
            
            if next_endif == -1:
                return -1  # No endif found
            
            if next_if != -1 and next_if < next_endif:
                # Found a nested if before the next endif
                depth += 1
                pos = next_if + len('{% if ')
            else:
                # Found an endif
                depth -= 1
                if depth == 0:
                    return next_endif  # This is our matching endif
                else:
                    pos = next_endif + len('{% endif %}')
        
        return -1  # No matching endif found
    
    def _process_single_conditional(self, conditional_block: str, data: Dict[str, Any]) -> str:
        """Process a single conditional block"""
        # Parse the conditional structure
        if_match = re.match(r'{% if ([^%]+) %}', conditional_block)
        if not if_match:
            return conditional_block
        
        condition = if_match.group(1).strip()
        content_start = if_match.end()
        
        # Find else clause if it exists
        else_pos = self._find_else_at_level(conditional_block, content_start)
        endif_pos = conditional_block.rfind('{% endif %}')
        
        if else_pos != -1:
            if_content = conditional_block[content_start:else_pos]
            else_content = conditional_block[else_pos + len('{% else %}'):endif_pos]
        else:
            if_content = conditional_block[content_start:endif_pos]
            else_content = ''
        
        try:
            # Handle negation
            negate = False
            if condition.startswith('not '):
                negate = True
                condition = condition[4:].strip()
            
            # Evaluate condition
            condition_value = self.evaluate_condition(condition, data)
            if negate:
                condition_value = not condition_value
            
            # Return appropriate content and recursively process it
            if condition_value:
                result = if_content
            else:
                result = else_content
            
            # Recursively process the selected content for nested conditionals
            return self._process_template_recursive(result, data)
            
        except Exception as e:
            return f'<div class="text-red-600">Condition error: {html.escape(str(e))}</div>'
    
    def _find_else_at_level(self, template: str, start_pos: int) -> int:
        """Find {% else %} at the current nesting level"""
        depth = 0
        pos = start_pos
        
        while pos < len(template):
            next_if = template.find('{% if ', pos)
            next_else = template.find('{% else %}', pos)
            next_endif = template.find('{% endif %}', pos)
            
            # Find the next closest tag
            candidates = [(tag_pos, tag_type) for tag_pos, tag_type in [
                (next_if, 'if'), (next_else, 'else'), (next_endif, 'endif')
            ] if tag_pos != -1]
            
            if not candidates:
                break
            
            next_pos, next_type = min(candidates)
            
            if next_type == 'if':
                depth += 1
                pos = next_pos + len('{% if ')
            elif next_type == 'endif':
                if depth == 0:
                    break  # We've reached the end of our conditional
                depth -= 1
                pos = next_pos + len('{% endif %}')
            elif next_type == 'else':
                if depth == 0:
                    return next_pos  # This is our else at the current level
                pos = next_pos + len('{% else %}')
        
        return -1
    
    def evaluate_condition(self, condition: str, data: Dict[str, Any]) -> bool:
        """Evaluate a condition expression"""
        # Simple boolean evaluation for common cases
        condition = condition.strip()
        
        # Handle direct boolean values
        if condition.lower() == 'true':
            return True
        elif condition.lower() == 'false':
            return False
        
        # Handle variable path evaluation
        try:
            value = self.get_nested_value(data, condition)
            
            # Convert to boolean
            if isinstance(value, bool):
                return value
            elif isinstance(value, (int, float)):
                return value != 0
            elif isinstance(value, str):
                return value.strip() != ''
            elif isinstance(value, (list, dict)):
                return len(value) > 0
            elif value is None:
                return False
            else:
                return bool(value)
                
        except Exception:
            return False
    
    def get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """
        Get a nested value from data using dot notation.
        Example: 'make.name' returns data['make']['name']
        """
        if not path or not isinstance(data, dict):
            return None
        
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            elif isinstance(current, list) and key.isdigit():
                try:
                    current = current[int(key)]
                except (IndexError, ValueError):
                    return None
            else:
                return None
        
        return current
    
    def validate_template_syntax(self, template: str) -> Dict[str, Any]:
        """
        Validate template syntax and return validation results.
        Used by Django forms for template validation.
        """
        errors = []
        warnings = []
        
        if not template or not isinstance(template, str):
            errors.append("Template cannot be empty")
            return {'valid': False, 'errors': errors, 'warnings': warnings}
        
        # Check for balanced tags
        for_count = len(re.findall(r'\{\%\s*for\s+', template))
        endfor_count = len(re.findall(r'\{\%\s*endfor\s*\%\}', template))
        if for_count != endfor_count:
            errors.append("Unbalanced for/endfor tags")
        
        if_count = len(re.findall(r'\{\%\s*if\s+', template))
        endif_count = len(re.findall(r'\{\%\s*endif\s*\%\}', template))
        if if_count != endif_count:
            errors.append("Unbalanced if/endif tags")
        
        # Check for dangerous content
        dangerous_patterns = [
            r'<script[^>]*>',
            r'javascript:',
            r'on\w+\s*=',
            r'eval\s*\(',
            r'document\s*\.',
            r'window\s*\.',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, template, re.IGNORECASE):
                errors.append(f"Potentially dangerous content detected: {pattern}")
        
        # Check for common template issues
        if '{{' in template and '}}' not in template:
            warnings.append("Incomplete variable syntax detected")
        
        if '{%' in template and '%}' not in template:
            warnings.append("Incomplete tag syntax detected")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }


# Helper functions for Django integration
def render_template_with_data(template: str, data: Dict[str, Any]) -> str:
    """Convenience function for rendering templates"""
    engine = TemplateEngine()
    return engine.render_template(template, data)


def validate_template(template: str) -> Dict[str, Any]:
    """Convenience function for template validation"""
    engine = TemplateEngine()
    return engine.validate_template_syntax(template)