{"name": "finder-v2-widget", "version": "2.0.0", "description": "Finder-v2 Widget - Vue 3 + TailwindCSS v4.1.8 + v2 API", "type": "module", "sideEffects": false, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts --fix", "lint:check": "eslint . --ext .vue,.js,.ts", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"vue": "^3.4.0", "pinia": "^2.1.7", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "axios": "^1.6.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@tailwindcss/vite": "^4.1.8", "tailwindcss": "^4.1.8", "vite": "^5.0.0", "terser": "^5.24.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "@vue/eslint-config-prettier": "^9.0.0", "prettier": "^3.1.0", "vitest": "^1.0.0", "@vue/test-utils": "^2.4.0", "jsdom": "^23.0.0", "@vitest/coverage-v8": "^1.0.0"}, "author": "<PERSON>", "license": "ISC"}