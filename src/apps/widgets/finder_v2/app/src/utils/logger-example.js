/**
 * Example usage of the logger utility
 * This file demonstrates different ways to use logging in development
 */

import { devLog, devWarn, devError, devDebug } from './logger.js'
// Or import the default logger object
import logger from './logger.js'

// Example 1: Simple logging that only appears in development
export function exampleFunction() {
  devLog('This message only appears in development')
  
  // Example 2: Using the logger object
  logger.log('Starting process...')
  logger.warn('This is a warning')
  logger.error('This is an error')
  
  // Example 3: Performance timing (dev only)
  logger.time('API Call')
  // ... some async operation
  logger.timeEnd('API Call')
  
  // Example 4: Conditional logging based on import.meta.env
  if (import.meta.env.DEV) {
    console.log('Development mode - detailed logging enabled')
    console.table({ data: 'some debug data' })
  }
  
  // Example 5: Using regular console.log (will be removed in production)
  console.log('This will be automatically removed in production builds')
}

// Example in a Vue component
export const componentExample = {
  setup() {
    devLog('Component initialized')
    
    const handleClick = () => {
      devDebug('Button clicked', { timestamp: Date.now() })
    }
    
    return { handleClick }
  }
}

// Example in store
export const storeExample = {
  initialize(config) {
    devLog('🚀 Store initializing with config:', config)
    
    if (!config.apiUrl) {
      devWarn('⚠️ No API URL provided in config')
    }
    
    try {
      // ... some operation
    } catch (error) {
      devError('❌ Store initialization failed:', error)
    }
  }
}