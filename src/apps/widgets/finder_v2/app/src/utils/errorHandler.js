/**
 * Widget Error Handler
 * Centralized error handling for the Finder-v2 widget
 */

export class WidgetErrorHandler {
  constructor(config = {}) {
    this.errors = []
    this.maxErrors = config.maxErrors || 10
    this.isDevelopment = import.meta.env.DEV
    this.errorCallback = config.onError || null
    this.errorReportingUrl = config.errorReportingUrl || window.FinderV2Config?.errorReportingUrl
    this.enableReporting = config.enableReporting !== false
  }

  /**
   * Handle an error from Vue's error handler
   */
  handleError(error, instance, info) {
    // Create error info object
    const errorInfo = this.createErrorInfo(error, instance, info)

    // Store error
    this.storeError(errorInfo)

    // Log in development
    if (this.isDevelopment) {
      this.logDevelopmentError(errorInfo)
    }

    // Report in production
    if (!this.isDevelopment && this.enableReporting) {
      this.reportError(errorInfo)
    }

    // Call custom error callback
    if (this.errorCallback) {
      this.errorCallback(errorInfo)
    }

    // Emit error event for parent page
    this.emitErrorEvent(errorInfo)

    return errorInfo
  }

  /**
   * Create structured error information
   */
  createErrorInfo(error, instance, info) {
    return {
      message: error?.message || 'Unknown error',
      stack: error?.stack,
      component: instance?.$options?.name || instance?.type?.name || 'Unknown',
      info: info,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      online: navigator.onLine,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      config: this.sanitizeConfig(window.FinderV2Config),
      type: this.classifyError(error),
      recoverable: this.isRecoverable(error)
    }
  }

  /**
   * Store error in memory
   */
  storeError(errorInfo) {
    this.errors.push(errorInfo)
    
    // Keep only the last N errors
    if (this.errors.length > this.maxErrors) {
      this.errors.shift()
    }
  }

  /**
   * Log error in development mode
   */
  logDevelopmentError(errorInfo) {
    console.group('🔴 Widget Error')
    console.error('Error:', errorInfo.message)
    console.log('Component:', errorInfo.component)
    console.log('Type:', errorInfo.type)
    console.log('Recoverable:', errorInfo.recoverable)
    console.log('Info:', errorInfo.info)
    if (errorInfo.stack) {
      console.log('Stack:', errorInfo.stack)
    }
    console.log('Full Details:', errorInfo)
    console.groupEnd()
  }

  /**
   * Report error to tracking service
   */
  async reportError(errorInfo) {
    if (!this.errorReportingUrl) {
      return
    }

    try {
      const response = await fetch(this.errorReportingUrl, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'X-Widget-Version': window.FinderV2Config?.version || 'unknown'
        },
        body: JSON.stringify({
          ...errorInfo,
          // Remove potentially sensitive data
          stack: this.sanitizeStackTrace(errorInfo.stack)
        })
      })

      if (!response.ok) {
        console.warn('Failed to report error:', response.statusText)
      }
    } catch (err) {
      // Silently fail error reporting
      console.warn('Error reporting failed:', err)
    }
  }

  /**
   * Emit custom event for parent page integration
   */
  emitErrorEvent(errorInfo) {
    window.dispatchEvent(new CustomEvent('finderv2:error', {
      detail: {
        message: errorInfo.message,
        component: errorInfo.component,
        type: errorInfo.type,
        recoverable: errorInfo.recoverable,
        timestamp: errorInfo.timestamp
      }
    }))
  }

  /**
   * Classify error type
   */
  classifyError(error) {
    const message = error?.message || ''
    const name = error?.name || ''

    if (message.includes('Network') || message.includes('fetch')) {
      return 'network'
    }
    if (message.includes('API') || error?.response) {
      return 'api'
    }
    if (message.includes('Timeout') || name.includes('Timeout')) {
      return 'timeout'
    }
    if (message.includes('Permission') || message.includes('denied')) {
      return 'permission'
    }
    if (name === 'TypeError') {
      return 'type'
    }
    if (name === 'SyntaxError') {
      return 'syntax'
    }
    if (name === 'ReferenceError') {
      return 'reference'
    }
    
    return 'unknown'
  }

  /**
   * Determine if error is recoverable
   */
  isRecoverable(error) {
    const message = error?.message || ''
    
    // Non-recoverable error patterns
    const nonRecoverable = [
      'Maximum call stack',
      'out of memory',
      'SecurityError',
      'CSP',
      'Infinite'
    ]
    
    return !nonRecoverable.some(pattern => 
      message.includes(pattern)
    )
  }

  /**
   * Sanitize configuration for error reporting
   */
  sanitizeConfig(config) {
    if (!config) return null

    const sanitized = { ...config }
    
    // Remove sensitive fields
    delete sanitized.apiKey
    delete sanitized.apiSecret
    delete sanitized.token
    delete sanitized.password
    delete sanitized.credentials

    return sanitized
  }

  /**
   * Sanitize stack trace to remove sensitive information
   */
  sanitizeStackTrace(stack) {
    if (!stack) return null

    // Remove local file paths and sensitive patterns
    return stack
      .replace(/file:\/\/[^\s]+/g, '[local-file]')
      .replace(/https?:\/\/[^\/]+\/api\/[^\s]+/g, '[api-endpoint]')
      .replace(/token=[^\s&]+/g, 'token=[redacted]')
      .replace(/key=[^\s&]+/g, 'key=[redacted]')
  }

  /**
   * Clear all stored errors
   */
  clearErrors() {
    this.errors = []
  }

  /**
   * Get all stored errors
   */
  getErrors() {
    return [...this.errors]
  }

  /**
   * Get errors by type
   */
  getErrorsByType(type) {
    return this.errors.filter(error => error.type === type)
  }

  /**
   * Get error statistics
   */
  getStatistics() {
    const stats = {
      total: this.errors.length,
      byType: {},
      byComponent: {},
      recoverable: 0,
      nonRecoverable: 0
    }

    this.errors.forEach(error => {
      // Count by type
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
      
      // Count by component
      stats.byComponent[error.component] = (stats.byComponent[error.component] || 0) + 1
      
      // Count recoverable
      if (error.recoverable) {
        stats.recoverable++
      } else {
        stats.nonRecoverable++
      }
    })

    return stats
  }
}

// Create and export singleton instance
export const errorHandler = new WidgetErrorHandler()

// Export for testing
export default WidgetErrorHandler