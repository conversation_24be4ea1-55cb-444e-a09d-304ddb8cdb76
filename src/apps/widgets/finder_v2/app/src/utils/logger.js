/**
 * Development logger utility
 * Provides logging methods that are automatically removed in production builds
 */

const isDevelopment = import.meta.env.DEV

/**
 * Development-only console.log
 * Automatically removed in production builds
 */
export const devLog = (...args) => {
  if (isDevelopment) {
    console.log(...args)
  }
}

/**
 * Development-only console.warn
 * Automatically removed in production builds
 */
export const devWarn = (...args) => {
  if (isDevelopment) {
    console.warn(...args)
  }
}

/**
 * Development-only console.error
 * Automatically removed in production builds
 */
export const devError = (...args) => {
  if (isDevelopment) {
    console.error(...args)
  }
}

/**
 * Development-only console.debug
 * Automatically removed in production builds
 */
export const devDebug = (...args) => {
  if (isDevelopment) {
    console.debug(...args)
  }
}

/**
 * Development-only console.table
 * Automatically removed in production builds
 */
export const devTable = (...args) => {
  if (isDevelopment) {
    console.table(...args)
  }
}

/**
 * Development-only performance timing
 * Automatically removed in production builds
 */
export const devTime = (label) => {
  if (isDevelopment) {
    console.time(label)
  }
}

export const devTimeEnd = (label) => {
  if (isDevelopment) {
    console.timeEnd(label)
  }
}

// Export a default logger object for convenience
export default {
  log: devLog,
  warn: devWarn,
  error: devError,
  debug: devDebug,
  table: devTable,
  time: devTime,
  timeEnd: devTimeEnd
}