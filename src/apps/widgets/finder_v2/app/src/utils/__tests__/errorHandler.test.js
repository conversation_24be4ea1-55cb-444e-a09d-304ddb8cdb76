import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { WidgetErrorHandler } from '../errorHandler'

describe('WidgetErrorHandler', () => {
  let errorHandler
  let originalEnv

  beforeEach(() => {
    errorHandler = new WidgetErrorHandler()
    originalEnv = import.meta.env.DEV
    vi.clearAllMocks()
  })

  afterEach(() => {
    Object.defineProperty(import.meta.env, 'DEV', {
      value: originalEnv,
      writable: true
    })
  })

  describe('Error Handling', () => {
    it('creates error info with all required fields', () => {
      const error = new Error('Test error')
      const instance = { $options: { name: 'TestComponent' } }
      const info = 'setup'

      const errorInfo = errorHandler.handleError(error, instance, info)

      expect(errorInfo).toMatchObject({
        message: 'Test error',
        component: 'TestComponent',
        info: 'setup',
        online: navigator.onLine,
        recoverable: true
      })
      expect(errorInfo.timestamp).toBeDefined()
      expect(errorInfo.userAgent).toBeDefined()
      expect(errorInfo.url).toBeDefined()
    })

    it('stores errors up to max limit', () => {
      const handler = new WidgetErrorHandler({ maxErrors: 3 })

      for (let i = 0; i < 5; i++) {
        handler.handleError(new Error(`Error ${i}`), null, 'test')
      }

      expect(handler.getErrors()).toHaveLength(3)
      expect(handler.getErrors()[0].message).toBe('Error 2')
      expect(handler.getErrors()[2].message).toBe('Error 4')
    })

    it('calls custom error callback', () => {
      const onError = vi.fn()
      const handler = new WidgetErrorHandler({ onError })
      const error = new Error('Test error')

      handler.handleError(error, null, 'test')

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Test error'
        })
      )
    })

    it('emits custom event for parent page', () => {
      const eventListener = vi.fn()
      window.addEventListener('finderv2:error', eventListener)

      const error = new Error('Test error')
      errorHandler.handleError(error, null, 'test')

      expect(eventListener).toHaveBeenCalled()
      const detail = eventListener.mock.calls[0][0].detail
      expect(detail.message).toBe('Test error')
      expect(detail.recoverable).toBeDefined()

      window.removeEventListener('finderv2:error', eventListener)
    })
  })

  describe('Error Classification', () => {
    it('classifies network errors correctly', () => {
      const networkError = new Error('Network request failed')
      const errorInfo = errorHandler.handleError(networkError, null, 'test')
      expect(errorInfo.type).toBe('network')
    })

    it('classifies API errors correctly', () => {
      const apiError = new Error('API request failed')
      const errorInfo = errorHandler.handleError(apiError, null, 'test')
      expect(errorInfo.type).toBe('api')
    })

    it('classifies timeout errors correctly', () => {
      const timeoutError = new Error('Request Timeout')
      const errorInfo = errorHandler.handleError(timeoutError, null, 'test')
      expect(errorInfo.type).toBe('timeout')
    })

    it('classifies permission errors correctly', () => {
      const permissionError = new Error('Permission denied')
      const errorInfo = errorHandler.handleError(permissionError, null, 'test')
      expect(errorInfo.type).toBe('permission')
    })

    it('classifies type errors correctly', () => {
      const typeError = new TypeError('Cannot read property')
      const errorInfo = errorHandler.handleError(typeError, null, 'test')
      expect(errorInfo.type).toBe('type')
    })

    it('classifies unknown errors correctly', () => {
      const unknownError = new Error('Something went wrong')
      const errorInfo = errorHandler.handleError(unknownError, null, 'test')
      expect(errorInfo.type).toBe('unknown')
    })
  })

  describe('Error Recovery Assessment', () => {
    it('identifies recoverable errors', () => {
      const recoverableError = new Error('Network error')
      const errorInfo = errorHandler.handleError(recoverableError, null, 'test')
      expect(errorInfo.recoverable).toBe(true)
    })

    it('identifies non-recoverable errors', () => {
      const nonRecoverableErrors = [
        new Error('Maximum call stack exceeded'),
        new Error('out of memory'),
        new Error('SecurityError: Blocked by CSP')
      ]

      nonRecoverableErrors.forEach(error => {
        const errorInfo = errorHandler.handleError(error, null, 'test')
        expect(errorInfo.recoverable).toBe(false)
      })
    })
  })

  describe('Development Mode', () => {
    it('logs detailed error info in development', () => {
      Object.defineProperty(import.meta.env, 'DEV', {
        value: true,
        writable: true
      })

      const consoleSpy = vi.spyOn(console, 'group').mockImplementation(() => {})
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      const handler = new WidgetErrorHandler()
      handler.handleError(new Error('Dev error'), null, 'test')

      expect(consoleSpy).toHaveBeenCalledWith('🔴 Widget Error')
      expect(consoleErrorSpy).toHaveBeenCalled()
      expect(consoleLogSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
      consoleErrorSpy.mockRestore()
      consoleLogSpy.mockRestore()
    })

    it('does not report errors in development', async () => {
      Object.defineProperty(import.meta.env, 'DEV', {
        value: true,
        writable: true
      })

      const fetchSpy = vi.spyOn(global, 'fetch')
      
      const handler = new WidgetErrorHandler({
        errorReportingUrl: '/api/errors'
      })
      
      handler.handleError(new Error('Dev error'), null, 'test')

      expect(fetchSpy).not.toHaveBeenCalled()

      fetchSpy.mockRestore()
    })
  })

  describe('Production Mode', () => {
    it('reports errors in production', async () => {
      Object.defineProperty(import.meta.env, 'DEV', {
        value: false,
        writable: true
      })

      const fetchSpy = vi.spyOn(global, 'fetch').mockResolvedValue({
        ok: true
      })

      window.FinderV2Config = {
        errorReportingUrl: '/api/errors',
        version: '1.0.0'
      }

      const handler = new WidgetErrorHandler()
      handler.handleError(new Error('Prod error'), null, 'test')

      expect(fetchSpy).toHaveBeenCalledWith('/api/errors', expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'X-Widget-Version': '1.0.0'
        })
      }))

      fetchSpy.mockRestore()
      delete window.FinderV2Config
    })

    it('handles error reporting failures gracefully', async () => {
      Object.defineProperty(import.meta.env, 'DEV', {
        value: false,
        writable: true
      })

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      const fetchSpy = vi.spyOn(global, 'fetch').mockRejectedValue(new Error('Network error'))

      window.FinderV2Config = {
        errorReportingUrl: '/api/errors'
      }

      const handler = new WidgetErrorHandler()
      handler.handleError(new Error('Prod error'), null, 'test')

      // Wait for async operation
      await new Promise(resolve => setTimeout(resolve, 10))

      expect(consoleSpy).toHaveBeenCalledWith('Error reporting failed:', expect.any(Error))

      consoleSpy.mockRestore()
      fetchSpy.mockRestore()
      delete window.FinderV2Config
    })
  })

  describe('Data Sanitization', () => {
    it('sanitizes configuration data', () => {
      window.FinderV2Config = {
        apiKey: 'secret-key',
        apiSecret: 'secret',
        token: 'auth-token',
        password: 'password123',
        publicData: 'safe-data'
      }

      const errorInfo = errorHandler.handleError(new Error('Test'), null, 'test')

      expect(errorInfo.config.apiKey).toBeUndefined()
      expect(errorInfo.config.apiSecret).toBeUndefined()
      expect(errorInfo.config.token).toBeUndefined()
      expect(errorInfo.config.password).toBeUndefined()
      expect(errorInfo.config.publicData).toBe('safe-data')

      delete window.FinderV2Config
    })

    it('sanitizes stack traces', () => {
      const handler = new WidgetErrorHandler()
      const stack = `Error: Test
        at file:///Users/<USER>/project/file.js:10:5
        at https://example.com/api/endpoint?token=secret&key=apikey
        at Function.call`

      const sanitized = handler.sanitizeStackTrace(stack)

      expect(sanitized).not.toContain('file:///Users')
      expect(sanitized).not.toContain('token=secret')
      expect(sanitized).not.toContain('key=apikey')
      expect(sanitized).toContain('[local-file]')
      expect(sanitized).toContain('token=[redacted]')
      expect(sanitized).toContain('key=[redacted]')
    })
  })

  describe('Error Management', () => {
    it('clears all errors', () => {
      errorHandler.handleError(new Error('Error 1'), null, 'test')
      errorHandler.handleError(new Error('Error 2'), null, 'test')
      
      expect(errorHandler.getErrors()).toHaveLength(2)
      
      errorHandler.clearErrors()
      
      expect(errorHandler.getErrors()).toHaveLength(0)
    })

    it('gets errors by type', () => {
      errorHandler.handleError(new Error('Network error'), null, 'test')
      errorHandler.handleError(new Error('API error'), null, 'test')
      errorHandler.handleError(new Error('Network failed'), null, 'test')

      const networkErrors = errorHandler.getErrorsByType('network')
      expect(networkErrors).toHaveLength(2)
      expect(networkErrors.every(e => e.type === 'network')).toBe(true)
    })

    it('provides error statistics', () => {
      errorHandler.handleError(new Error('Network error'), { $options: { name: 'Comp1' } }, 'test')
      errorHandler.handleError(new Error('API error'), { $options: { name: 'Comp2' } }, 'test')
      errorHandler.handleError(new Error('Network failed'), { $options: { name: 'Comp1' } }, 'test')
      errorHandler.handleError(new Error('Maximum call stack'), null, 'test')

      const stats = errorHandler.getStatistics()

      expect(stats.total).toBe(4)
      expect(stats.byType.network).toBe(2)
      expect(stats.byType.api).toBe(1)
      expect(stats.byComponent.Comp1).toBe(2)
      expect(stats.byComponent.Comp2).toBe(1)
      expect(stats.recoverable).toBe(3)
      expect(stats.nonRecoverable).toBe(1)
    })
  })

  describe('Edge Cases', () => {
    it('handles null error gracefully', () => {
      const errorInfo = errorHandler.handleError(null, null, 'test')
      expect(errorInfo.message).toBe('Unknown error')
      expect(errorInfo.component).toBe('Unknown')
    })

    it('handles error without message', () => {
      const error = {}
      const errorInfo = errorHandler.handleError(error, null, 'test')
      expect(errorInfo.message).toBe('Unknown error')
    })

    it('handles instance without name', () => {
      const instance = {}
      const errorInfo = errorHandler.handleError(new Error('Test'), instance, 'test')
      expect(errorInfo.component).toBe('Unknown')
    })

    it('handles instance with type name', () => {
      const instance = { type: { name: 'TypeComponent' } }
      const errorInfo = errorHandler.handleError(new Error('Test'), instance, 'test')
      expect(errorInfo.component).toBe('TypeComponent')
    })
  })
})