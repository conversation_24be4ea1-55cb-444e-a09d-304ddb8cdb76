/**
 * LocalStorage Manager
 * Coordinates localStorage usage between API Cache and Search History features
 */

export class LocalStorageManager {
  constructor() {
    this.searchHistoryPrefix = 'finder_v2_search_history_'
    this.apiCachePrefix = 'ws_finder_cache_v1_'
    this.cacheMetaKey = 'ws_finder_cache_meta'
    this.maxTotalSize = 8 * 1024 * 1024 // 8MB total for both features
    
    // Storage allocation limits (simplified approach)
    this.limits = {
      searchHistory: 50 * 1024,    // 50KB for search history
      apiCache: 500 * 1024,         // 500KB for API cache
      reserved: 7.45 * 1024 * 1024  // Rest reserved for other uses
    }
  }

  /**
   * Get current storage usage breakdown
   * @returns {Object} Storage usage statistics
   */
  getStorageUsage() {
    let searchHistorySize = 0
    let apiCacheSize = 0
    let otherSize = 0
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (!key) continue
      
      const value = localStorage.getItem(key)
      const size = (key.length + (value ? value.length : 0)) * 2 // UTF-16 encoding
      
      if (key.startsWith(this.searchHistoryPrefix)) {
        searchHistorySize += size
      } else if (key.startsWith(this.apiCachePrefix) || key === this.cacheMetaKey) {
        apiCacheSize += size
      } else {
        otherSize += size
      }
    }
    
    const totalUsed = searchHistorySize + apiCacheSize + otherSize
    
    return {
      searchHistory: searchHistorySize,
      apiCache: apiCacheSize,
      other: otherSize,
      total: totalUsed,
      available: this.maxTotalSize - totalUsed,
      utilization: {
        searchHistory: this.formatPercent(searchHistorySize, this.limits.searchHistory),
        apiCache: this.formatPercent(apiCacheSize, this.limits.apiCache),
        total: this.formatPercent(totalUsed, this.maxTotalSize)
      }
    }
  }

  /**
   * Check if there's enough space for new data
   * @param {number} requiredSpace - Space needed in bytes
   * @param {string} feature - Feature requesting space ('cache' or 'history')
   * @returns {boolean} True if space is available or can be made available
   */
  hasSpace(requiredSpace, feature = 'cache') {
    const usage = this.getStorageUsage()
    
    // Check if space is immediately available
    if (usage.available >= requiredSpace) {
      return true
    }
    
    // Check if feature is within its limit
    if (feature === 'cache' && usage.apiCache + requiredSpace <= this.limits.apiCache) {
      return true
    }
    
    if (feature === 'history' && usage.searchHistory + requiredSpace <= this.limits.searchHistory) {
      return true
    }
    
    return false
  }

  /**
   * Intelligent cleanup when approaching limits
   * @param {number} requiredSpace - Space needed in bytes
   * @returns {boolean} True if enough space was freed
   */
  cleanupStorage(requiredSpace) {
    const usage = this.getStorageUsage()
    
    if (usage.available >= requiredSpace) {
      return true
    }
    
    console.log('🧹 Starting intelligent storage cleanup...')
    console.log('Current usage:', this.formatStorageUsage(usage))
    
    // Prioritize keeping search history (smaller, more valuable)
    // Clean old API cache entries first
    let spaceFreed = 0
    
    // Step 1: Remove expired cache entries
    spaceFreed += this.cleanupExpiredCache()
    
    if (usage.available + spaceFreed >= requiredSpace) {
      console.log(`✅ Freed ${this.formatBytes(spaceFreed)} by removing expired entries`)
      return true
    }
    
    // Step 2: Remove oldest cache entries
    spaceFreed += this.cleanupOldCache(requiredSpace - spaceFreed)
    
    if (usage.available + spaceFreed >= requiredSpace) {
      console.log(`✅ Freed ${this.formatBytes(spaceFreed)} total`)
      return true
    }
    
    console.warn('⚠️ Could not free enough space')
    return false
  }

  /**
   * Clean up expired cache entries
   * @private
   * @returns {number} Bytes freed
   */
  cleanupExpiredCache() {
    const now = Date.now()
    let bytesFreed = 0
    const keysToRemove = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (!key || !key.startsWith(this.apiCachePrefix)) continue
      
      try {
        const value = localStorage.getItem(key)
        const entry = JSON.parse(value)
        
        if (entry.timestamp && entry.ttl && (now - entry.timestamp > entry.ttl)) {
          keysToRemove.push(key)
          bytesFreed += (key.length + value.length) * 2
        }
      } catch (e) {
        // Invalid entry, remove it
        keysToRemove.push(key)
      }
    }
    
    // Remove expired entries
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    if (keysToRemove.length > 0) {
      console.log(`🗑️ Removed ${keysToRemove.length} expired cache entries`)
    }
    
    return bytesFreed
  }

  /**
   * Clean up oldest cache entries
   * @private
   * @param {number} targetSpace - Space to free in bytes
   * @returns {number} Bytes freed
   */
  cleanupOldCache(targetSpace) {
    const cacheEntries = []
    
    // Collect all cache entries with their metadata
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (!key || !key.startsWith(this.apiCachePrefix)) continue
      
      try {
        const value = localStorage.getItem(key)
        const entry = JSON.parse(value)
        
        cacheEntries.push({
          key,
          timestamp: entry.timestamp || 0,
          size: (key.length + value.length) * 2
        })
      } catch (e) {
        // Invalid entry
      }
    }
    
    // Sort by timestamp (oldest first)
    cacheEntries.sort((a, b) => a.timestamp - b.timestamp)
    
    let bytesFreed = 0
    const keysToRemove = []
    
    for (const entry of cacheEntries) {
      if (bytesFreed >= targetSpace) break
      
      keysToRemove.push(entry.key)
      bytesFreed += entry.size
    }
    
    // Remove selected entries
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    if (keysToRemove.length > 0) {
      console.log(`🗑️ Removed ${keysToRemove.length} old cache entries`)
    }
    
    return bytesFreed
  }

  /**
   * Get storage statistics for monitoring
   * @returns {Object} Detailed storage statistics
   */
  getDetailedStats() {
    const usage = this.getStorageUsage()
    const cacheCount = this.countEntriesByPrefix(this.apiCachePrefix)
    const historyCount = this.countEntriesByPrefix(this.searchHistoryPrefix)
    
    return {
      summary: {
        totalUsed: this.formatBytes(usage.total),
        totalAvailable: this.formatBytes(usage.available),
        utilizationPercent: this.formatPercent(usage.total, this.maxTotalSize)
      },
      searchHistory: {
        size: this.formatBytes(usage.searchHistory),
        limit: this.formatBytes(this.limits.searchHistory),
        utilization: usage.utilization.searchHistory,
        entryCount: historyCount
      },
      apiCache: {
        size: this.formatBytes(usage.apiCache),
        limit: this.formatBytes(this.limits.apiCache),
        utilization: usage.utilization.apiCache,
        entryCount: cacheCount
      },
      other: {
        size: this.formatBytes(usage.other),
        description: 'Other localStorage usage'
      }
    }
  }

  /**
   * Count entries by prefix
   * @private
   */
  countEntriesByPrefix(prefix) {
    let count = 0
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        count++
      }
    }
    return count
  }

  /**
   * Format bytes for display
   * @private
   */
  formatBytes(bytes) {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`
  }

  /**
   * Format percentage
   * @private
   */
  formatPercent(value, total) {
    if (total === 0) return '0%'
    return `${((value / total) * 100).toFixed(1)}%`
  }

  /**
   * Format storage usage for logging
   * @private
   */
  formatStorageUsage(usage) {
    return {
      searchHistory: this.formatBytes(usage.searchHistory),
      apiCache: this.formatBytes(usage.apiCache),
      other: this.formatBytes(usage.other),
      total: this.formatBytes(usage.total),
      available: this.formatBytes(usage.available)
    }
  }

  /**
   * Monitor storage usage and warn if approaching limits
   */
  checkStorageHealth() {
    const usage = this.getStorageUsage()
    const totalUtilization = (usage.total / this.maxTotalSize) * 100
    
    if (totalUtilization > 90) {
      console.error('🚨 localStorage critically full:', this.formatPercent(usage.total, this.maxTotalSize))
      return 'critical'
    } else if (totalUtilization > 75) {
      console.warn('⚠️ localStorage getting full:', this.formatPercent(usage.total, this.maxTotalSize))
      return 'warning'
    } else if (totalUtilization > 50) {
      console.log('ℹ️ localStorage usage:', this.formatPercent(usage.total, this.maxTotalSize))
      return 'normal'
    }
    
    return 'healthy'
  }

  /**
   * Clear all API cache entries (preserves search history)
   */
  clearApiCache() {
    const keysToRemove = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.startsWith(this.apiCachePrefix) || key === this.cacheMetaKey)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    console.log(`🗑️ Cleared ${keysToRemove.length} API cache entries`)
    return keysToRemove.length
  }

  /**
   * Clear all search history entries (preserves API cache)
   */
  clearSearchHistory() {
    const keysToRemove = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.searchHistoryPrefix)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    console.log(`🗑️ Cleared ${keysToRemove.length} search history entries`)
    return keysToRemove.length
  }

  /**
   * Export storage state for debugging
   */
  exportStorageState() {
    const state = {
      timestamp: new Date().toISOString(),
      usage: this.getStorageUsage(),
      stats: this.getDetailedStats(),
      health: this.checkStorageHealth(),
      entries: {}
    }
    
    // Collect entry summaries (not full data to avoid large export)
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (!key) continue
      
      const value = localStorage.getItem(key)
      let category = 'other'
      
      if (key.startsWith(this.searchHistoryPrefix)) {
        category = 'searchHistory'
      } else if (key.startsWith(this.apiCachePrefix) || key === this.cacheMetaKey) {
        category = 'apiCache'
      }
      
      if (!state.entries[category]) {
        state.entries[category] = []
      }
      
      state.entries[category].push({
        key: key.substring(0, 50), // Truncate long keys
        size: (key.length + (value ? value.length : 0)) * 2
      })
    }
    
    return state
  }
}

// Export singleton instance
export const localStorageManager = new LocalStorageManager()