/**
 * LocalStorage Cache Implementation
 * Persistent cache using browser localStorage with TTL and size management
 */

export class LocalStorageCache {
  constructor(maxSize = 500 * 1024) { // 500KB default
    this.maxSize = maxSize
    this.prefix = 'ws_finder_cache_v1_'
    this.metadataKey = 'ws_finder_cache_meta'
  }

  /**
   * Get value from cache
   * @param {string} key - Cache key
   * @returns {any} Cached value or null
   */
  get(key) {
    try {
      const stored = localStorage.getItem(this.prefix + key)
      if (!stored) return null
      
      const entry = JSON.parse(stored)
      
      // Check TTL
      if (Date.now() - entry.timestamp > entry.ttl) {
        this.delete(key)
        return null
      }
      
      // Validate data integrity
      if (!this.validateEntry(entry)) {
        this.delete(key)
        return null
      }
      
      // Update last access time in metadata
      this.updateLastAccess(key)
      
      console.log('✅ Storage cache hit:', key)
      return entry.data
    } catch (e) {
      console.error('Cache retrieval error:', e)
      return null
    }
  }

  /**
   * Set value in cache
   * @param {string} key - Cache key
   * @param {any} data - Data to cache
   * @param {number} ttl - Time to live in milliseconds
   */
  set(key, data, ttl = 3600000) {
    try {
      const entry = {
        data,
        timestamp: Date.now(),
        ttl,
        hash: this.generateHash(data)
      }
      
      const serialized = JSON.stringify(entry)
      
      // Check size before storing
      if (serialized.length > this.maxSize / 10) { // Don't let single entry be >10% of cache
        console.warn('Cache entry too large, skipping:', key)
        return false
      }
      
      // Check if we need to evict entries to make space
      if (!this.ensureSpace(serialized.length)) {
        console.warn('Cannot make space for cache entry:', key)
        return false
      }
      
      localStorage.setItem(this.prefix + key, serialized)
      this.updateMetadata(key, serialized.length)
      console.log('💾 Storage cache set:', key)
      return true
    } catch (e) {
      if (e.name === 'QuotaExceededError') {
        this.evictOldest()
        // Retry once
        try {
          localStorage.setItem(this.prefix + key, serialized)
          this.updateMetadata(key, serialized.length)
          return true
        } catch (retryError) {
          console.error('Cache storage failed after eviction:', retryError)
          return false
        }
      }
      console.error('Cache storage error:', e)
      return false
    }
  }

  /**
   * Delete entry from cache
   * @param {string} key - Cache key
   */
  delete(key) {
    localStorage.removeItem(this.prefix + key)
    this.removeFromMetadata(key)
  }

  /**
   * Clear all cache entries
   */
  clear() {
    // Find all cache keys
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.prefix)) {
        keysToRemove.push(key)
      }
    }
    
    // Remove all cache entries
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    // Clear metadata
    localStorage.removeItem(this.metadataKey)
    
    console.log('🗑️ Storage cache cleared')
  }

  /**
   * Validate cache entry integrity
   * @private
   */
  validateEntry(entry) {
    // Check structure
    if (!entry.data || !entry.timestamp || !entry.ttl) {
      return false
    }
    
    // Check data integrity if hash exists
    if (entry.hash) {
      const currentHash = this.generateHash(entry.data)
      if (currentHash !== entry.hash) {
        console.warn('Cache entry failed integrity check')
        return false
      }
    }
    
    return true
  }

  /**
   * Generate simple hash for data validation
   * @private
   */
  generateHash(data) {
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }

  /**
   * Get cache metadata
   * @private
   */
  getMetadata() {
    try {
      const stored = localStorage.getItem(this.metadataKey)
      if (!stored) {
        return {
          version: 'v1',
          entries: [],
          totalSize: 0
        }
      }
      return JSON.parse(stored)
    } catch (e) {
      return {
        version: 'v1',
        entries: [],
        totalSize: 0
      }
    }
  }

  /**
   * Update cache metadata
   * @private
   */
  updateMetadata(key, size) {
    const metadata = this.getMetadata()
    
    // Remove existing entry if it exists
    const existingIndex = metadata.entries.findIndex(e => e.key === key)
    if (existingIndex > -1) {
      metadata.totalSize -= metadata.entries[existingIndex].size
      metadata.entries.splice(existingIndex, 1)
    }
    
    // Add new entry
    metadata.entries.push({
      key,
      size,
      timestamp: Date.now(),
      lastAccess: Date.now()
    })
    
    metadata.totalSize += size
    
    try {
      localStorage.setItem(this.metadataKey, JSON.stringify(metadata))
    } catch (e) {
      console.error('Failed to update metadata:', e)
    }
  }

  /**
   * Remove entry from metadata
   * @private
   */
  removeFromMetadata(key) {
    const metadata = this.getMetadata()
    const index = metadata.entries.findIndex(e => e.key === key)
    if (index > -1) {
      metadata.totalSize -= metadata.entries[index].size
      metadata.entries.splice(index, 1)
      
      try {
        localStorage.setItem(this.metadataKey, JSON.stringify(metadata))
      } catch (e) {
        console.error('Failed to update metadata:', e)
      }
    }
  }

  /**
   * Update last access time
   * @private
   */
  updateLastAccess(key) {
    const metadata = this.getMetadata()
    const entry = metadata.entries.find(e => e.key === key)
    if (entry) {
      entry.lastAccess = Date.now()
      try {
        localStorage.setItem(this.metadataKey, JSON.stringify(metadata))
      } catch (e) {
        // Silent fail - not critical
      }
    }
  }

  /**
   * Ensure space is available for new entry
   * @private
   */
  ensureSpace(requiredSize) {
    const metadata = this.getMetadata()
    
    if (metadata.totalSize + requiredSize <= this.maxSize) {
      return true
    }
    
    // Need to evict entries
    const spaceNeeded = (metadata.totalSize + requiredSize) - this.maxSize
    return this.evictBySize(spaceNeeded)
  }

  /**
   * Evict oldest entries
   * @private
   */
  evictOldest() {
    const metadata = this.getMetadata()
    
    if (metadata.entries.length === 0) return
    
    // Sort by timestamp (oldest first)
    metadata.entries.sort((a, b) => a.timestamp - b.timestamp)
    
    // Evict oldest 20%
    const toEvict = Math.max(1, Math.floor(metadata.entries.length * 0.2))
    
    for (let i = 0; i < toEvict && i < metadata.entries.length; i++) {
      const entry = metadata.entries[i]
      localStorage.removeItem(this.prefix + entry.key)
      console.log('🔄 Storage cache evicted:', entry.key)
    }
    
    // Update metadata
    metadata.entries = metadata.entries.slice(toEvict)
    metadata.totalSize = metadata.entries.reduce((sum, e) => sum + e.size, 0)
    
    try {
      localStorage.setItem(this.metadataKey, JSON.stringify(metadata))
    } catch (e) {
      console.error('Failed to update metadata after eviction:', e)
    }
  }

  /**
   * Evict entries by size
   * @private
   */
  evictBySize(spaceNeeded) {
    const metadata = this.getMetadata()
    
    // Sort by last access (least recently used first)
    metadata.entries.sort((a, b) => a.lastAccess - b.lastAccess)
    
    let spaceFreed = 0
    const keysToEvict = []
    
    for (const entry of metadata.entries) {
      if (spaceFreed >= spaceNeeded) break
      keysToEvict.push(entry.key)
      spaceFreed += entry.size
    }
    
    // Evict selected entries
    keysToEvict.forEach(key => {
      localStorage.removeItem(this.prefix + key)
      console.log('🔄 Storage cache evicted for space:', key)
    })
    
    // Update metadata
    metadata.entries = metadata.entries.filter(e => !keysToEvict.includes(e.key))
    metadata.totalSize = metadata.entries.reduce((sum, e) => sum + e.size, 0)
    
    try {
      localStorage.setItem(this.metadataKey, JSON.stringify(metadata))
    } catch (e) {
      console.error('Failed to update metadata after eviction:', e)
      return false
    }
    
    return spaceFreed >= spaceNeeded
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const metadata = this.getMetadata()
    return {
      entries: metadata.entries.length,
      totalSize: metadata.totalSize,
      maxSize: this.maxSize,
      utilization: `${((metadata.totalSize / this.maxSize) * 100).toFixed(1)}%`
    }
  }

  /**
   * Invalidate all entries for a specific endpoint
   */
  invalidateEndpoint(endpoint) {
    const keysToInvalidate = []
    
    // Find all keys for this endpoint
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.prefix + endpoint)) {
        keysToInvalidate.push(key)
      }
    }
    
    // Remove all matching entries
    keysToInvalidate.forEach(key => {
      const shortKey = key.replace(this.prefix, '')
      this.delete(shortKey)
    })
    
    console.log(`🗑️ Invalidated ${keysToInvalidate.length} cache entries for ${endpoint}`)
  }
}