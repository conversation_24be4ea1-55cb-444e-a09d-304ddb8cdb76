/**
 * Cache Key Generator
 * Generates consistent cache keys from endpoint and parameters
 * Handles filter parameters (region, brands) for widget configurations
 */

export class CacheKeyGenerator {
  /**
   * Generate cache key from endpoint and parameters
   * @param {string} endpoint - API endpoint name
   * @param {Object} params - Request parameters including filters
   * @returns {string} Cache key
   */
  static generate(endpoint, params = {}) {
    // Sort params for consistent keys (including filters)
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, key) => {
        // Skip null, undefined, and empty values
        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
          // Handle array parameters (like brands)
          if (Array.isArray(params[key])) {
            // Sort array values for consistency
            acc[key] = params[key].sort().join(',')
          } else {
            acc[key] = String(params[key])
          }
        }
        return acc
      }, {})
    
    // Create key from endpoint and params
    // Examples:
    // - "year" (no filters)
    // - "year_region_mxndm" (with region filter)
    // - "make_brands_toyota,nissan_region_mxndm" (with multiple filters)
    // - "model_make_toyota_year_2024" (with make and year params)
    
    if (Object.keys(sortedParams).length === 0) {
      return endpoint
    }
    
    const paramString = Object.entries(sortedParams)
      .map(([k, v]) => `${k}_${v}`)
      .join('_')
    
    return `${endpoint}_${paramString}`
  }

  /**
   * Parse cache key back to endpoint and params
   * @param {string} key - Cache key
   * @returns {Object} Parsed endpoint and params
   */
  static parse(key) {
    const parts = key.split('_')
    const endpoint = parts[0]
    const params = {}
    
    // Parse parameters from key
    for (let i = 1; i < parts.length; i += 2) {
      if (i + 1 < parts.length) {
        const paramKey = parts[i]
        const paramValue = parts[i + 1]
        
        // Check if value contains comma (array parameter)
        if (paramValue.includes(',')) {
          params[paramKey] = paramValue.split(',')
        } else {
          params[paramKey] = paramValue
        }
      }
    }
    
    return { endpoint, params }
  }

  /**
   * Generate cache key for search results
   * Special handling for complex search parameters
   */
  static generateSearchKey(searchParams) {
    // Sort all search parameters
    const sorted = Object.keys(searchParams)
      .sort()
      .reduce((acc, key) => {
        if (searchParams[key] !== null && searchParams[key] !== undefined) {
          acc[key] = searchParams[key]
        }
        return acc
      }, {})
    
    // Create hash of search params for shorter key
    const hash = this.hashObject(sorted)
    return `search_${hash}`
  }

  /**
   * Create hash of object for shorter keys
   * @private
   */
  static hashObject(obj) {
    const str = JSON.stringify(obj)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * Generate key with widget configuration filters
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Request parameters
   * @param {Object} widgetConfig - Widget configuration with filters
   * @returns {string} Cache key including filters
   */
  static generateWithConfig(endpoint, params, widgetConfig) {
    const mergedParams = { ...params }
    
    // Add widget configuration filters
    if (widgetConfig) {
      if (widgetConfig.region) {
        mergedParams.region = widgetConfig.region
      }
      
      if (widgetConfig.brands && widgetConfig.brands.length > 0) {
        mergedParams.brands = widgetConfig.brands
      }
      
      if (widgetConfig.excludeBrands && widgetConfig.excludeBrands.length > 0) {
        mergedParams.exclude_brands = widgetConfig.excludeBrands
      }
    }
    
    return this.generate(endpoint, mergedParams)
  }

  /**
   * Check if two cache keys represent the same data
   * @param {string} key1 - First cache key
   * @param {string} key2 - Second cache key
   * @returns {boolean} True if keys are equivalent
   */
  static areKeysEquivalent(key1, key2) {
    if (key1 === key2) return true
    
    const parsed1 = this.parse(key1)
    const parsed2 = this.parse(key2)
    
    // Check endpoint
    if (parsed1.endpoint !== parsed2.endpoint) return false
    
    // Check parameters
    const params1 = JSON.stringify(parsed1.params)
    const params2 = JSON.stringify(parsed2.params)
    
    return params1 === params2
  }
}