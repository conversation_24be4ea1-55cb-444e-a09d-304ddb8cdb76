/**
 * Cache Eviction Manager
 * Manages different eviction strategies for cache storage
 */

export class CacheEvictionManager {
  constructor(storageCache) {
    this.cache = storageCache
  }

  /**
   * LRU (Least Recently Used) eviction
   * @param {number} targetSize - Target size in bytes
   * @returns {number} Number of entries evicted
   */
  evictLRU(targetSize) {
    const metadata = this.cache.getMetadata()
    
    // Sort by last access time (oldest first)
    const entries = [...metadata.entries].sort((a, b) => a.lastAccess - b.lastAccess)
    
    let currentSize = metadata.totalSize
    const keysToEvict = []
    
    for (const entry of entries) {
      if (currentSize <= targetSize) break
      keysToEvict.push(entry.key)
      currentSize -= entry.size
    }
    
    // Evict selected entries
    keysToEvict.forEach(key => {
      this.cache.delete(key)
      console.log('🔄 LRU eviction:', key)
    })
    
    return keysToEvict.length
  }

  /**
   * TTL-based eviction - remove expired entries
   * @returns {number} Number of entries evicted
   */
  evictExpired() {
    const now = Date.now()
    let evicted = 0
    const prefix = this.cache.prefix
    
    // Check all cache entries
    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i)
      if (!key || !key.startsWith(prefix)) continue
      
      try {
        const entry = JSON.parse(localStorage.getItem(key))
        if (entry && entry.timestamp && entry.ttl) {
          if (now - entry.timestamp > entry.ttl) {
            const shortKey = key.replace(prefix, '')
            this.cache.delete(shortKey)
            evicted++
            console.log('⏰ TTL eviction:', shortKey)
          }
        }
      } catch (e) {
        // Invalid entry, remove it
        localStorage.removeItem(key)
        evicted++
      }
    }
    
    return evicted
  }

  /**
   * Size-based eviction - evict entries to stay under max size
   * @param {number} maxSize - Maximum cache size in bytes
   * @returns {number} Number of entries evicted
   */
  evictBySize(maxSize) {
    const metadata = this.cache.getMetadata()
    
    if (metadata.totalSize <= maxSize) return 0
    
    // Sort by size (largest first) and age (oldest first)
    const entries = [...metadata.entries].sort((a, b) => {
      const sizeDiff = b.size - a.size
      if (sizeDiff !== 0) return sizeDiff
      return a.timestamp - b.timestamp
    })
    
    let currentSize = metadata.totalSize
    const keysToEvict = []
    const targetSize = maxSize * 0.8 // Keep 20% buffer
    
    for (const entry of entries) {
      if (currentSize <= targetSize) break
      keysToEvict.push(entry.key)
      currentSize -= entry.size
    }
    
    // Evict selected entries
    keysToEvict.forEach(key => {
      this.cache.delete(key)
      console.log('📏 Size eviction:', key)
    })
    
    return keysToEvict.length
  }

  /**
   * Evict entries for specific endpoint
   * @param {string} endpoint - Endpoint to clear
   * @returns {number} Number of entries evicted
   */
  evictEndpoint(endpoint) {
    const prefix = this.cache.prefix
    const keysToEvict = []
    
    // Find all keys for this endpoint
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix + endpoint)) {
        keysToEvict.push(key)
      }
    }
    
    // Evict all matching entries
    keysToEvict.forEach(key => {
      const shortKey = key.replace(prefix, '')
      this.cache.delete(shortKey)
    })
    
    console.log(`🎯 Endpoint eviction: ${keysToEvict.length} entries for ${endpoint}`)
    return keysToEvict.length
  }

  /**
   * Smart eviction - combines multiple strategies
   * @param {number} requiredSpace - Space needed in bytes
   * @returns {boolean} True if enough space was freed
   */
  smartEvict(requiredSpace) {
    const metadata = this.cache.getMetadata()
    const initialSize = metadata.totalSize
    
    // Step 1: Evict expired entries first
    this.evictExpired()
    
    // Check if we have enough space
    let currentMetadata = this.cache.getMetadata()
    if (currentMetadata.totalSize + requiredSpace <= this.cache.maxSize) {
      return true
    }
    
    // Step 2: Evict least recently used
    const targetSize = this.cache.maxSize - requiredSpace
    this.evictLRU(targetSize)
    
    // Check final result
    currentMetadata = this.cache.getMetadata()
    const spaceFreed = initialSize - currentMetadata.totalSize
    
    console.log(`🧹 Smart eviction freed ${this.formatBytes(spaceFreed)}`)
    
    return currentMetadata.totalSize + requiredSpace <= this.cache.maxSize
  }

  /**
   * Clean up old entries based on age
   * @param {number} maxAge - Maximum age in milliseconds
   * @returns {number} Number of entries evicted
   */
  evictOlderThan(maxAge) {
    const now = Date.now()
    const metadata = this.cache.getMetadata()
    const keysToEvict = []
    
    for (const entry of metadata.entries) {
      if (now - entry.timestamp > maxAge) {
        keysToEvict.push(entry.key)
      }
    }
    
    // Evict old entries
    keysToEvict.forEach(key => {
      this.cache.delete(key)
      console.log('🕰️ Age eviction:', key)
    })
    
    return keysToEvict.length
  }

  /**
   * Format bytes for display
   * @private
   */
  formatBytes(bytes) {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  }

  /**
   * Get eviction statistics
   */
  getStats() {
    const metadata = this.cache.getMetadata()
    const now = Date.now()
    
    // Calculate various statistics
    const expired = metadata.entries.filter(e => {
      try {
        const stored = localStorage.getItem(this.cache.prefix + e.key)
        if (!stored) return true
        const entry = JSON.parse(stored)
        return now - entry.timestamp > entry.ttl
      } catch {
        return true
      }
    }).length
    
    const oldEntries = metadata.entries.filter(e => 
      now - e.timestamp > 24 * 60 * 60 * 1000 // Older than 24 hours
    ).length
    
    const avgSize = metadata.entries.length > 0 
      ? Math.round(metadata.totalSize / metadata.entries.length)
      : 0
    
    return {
      totalEntries: metadata.entries.length,
      expiredEntries: expired,
      oldEntries: oldEntries,
      totalSize: this.formatBytes(metadata.totalSize),
      averageSize: this.formatBytes(avgSize),
      utilizationPercent: ((metadata.totalSize / this.cache.maxSize) * 100).toFixed(1)
    }
  }
}