/**
 * Cached API Client
 * Extends ApiClient with selective caching for year, make, model, generation endpoints
 * Implements the simplified caching strategy from the implementation plan
 */

import { ApiClient } from '../stores/modules/api-client'
import { MemoryCache } from './memory-cache'
import { LocalStorageCache } from './localstorage-cache'
import { <PERSON>ache<PERSON>eyGenerator } from './key-generator'
import { CacheEvictionManager } from './eviction-manager'

export class CachedApiClient extends ApiClient {
  constructor(config, widgetResources, cacheConfig = {}) {
    super(config, widgetResources)
    
    this.cacheConfig = {
      enabled: true,
      memoryMaxSize: 50,              // Reduced - fewer endpoints
      storageMaxSize: 500 * 1024,     // 500KB - much smaller
      defaultTTL: 60 * 60 * 1000,     // 1 hour default
      ...cacheConfig
    }
    
    // ONLY cache selector endpoints - simplified approach
    this.cacheableEndpoints = ['year', 'make', 'model', 'generation']
    
    // TTL configuration for cached endpoints only
    this.endpointTTLs = {
      year: 24 * 60 * 60 * 1000,     // 24 hours - years rarely change
      make: 12 * 60 * 60 * 1000,     // 12 hours - makes change occasionally  
      model: 6 * 60 * 60 * 1000,     // 6 hours - models update more frequently
      generation: 6 * 60 * 60 * 1000, // 6 hours - generation data
      ...cacheConfig.ttls
    }
    
    // Initialize cache layers
    this.memoryCache = new MemoryCache(this.cacheConfig.memoryMaxSize)
    this.storageCache = new LocalStorageCache(this.cacheConfig.storageMaxSize)
    this.evictionManager = new CacheEvictionManager(this.storageCache)
    
    // Cache statistics
    this.stats = {
      hits: 0,
      misses: 0,
      errors: 0,
      bytesServed: 0,
      bytesFetched: 0
    }

    // Widget configuration (for filter support)
    this.widgetConfig = config.widgetConfig || {}
    
    // Initialize cache on creation
    this.initializeCache()
  }

  /**
   * Initialize cache and clean up expired entries
   * @private
   */
  async initializeCache() {
    // Clean up expired entries on startup
    const evicted = this.evictionManager.evictExpired()
    if (evicted > 0) {
      console.log(`🧹 Cleaned up ${evicted} expired cache entries on startup`)
    }
  }

  /**
   * Make an API call with caching support
   * @override
   */
  async call(endpoint, params = {}) {
    // Apply widget configuration filters to params
    const filteredParams = this.applyConfigFilters(params, this.widgetConfig)
    
    // Check if caching is enabled AND endpoint is cacheable
    const shouldCache = this.cacheConfig.enabled && 
                       this.cacheableEndpoints.includes(endpoint)
    
    if (!shouldCache) {
      // Direct API call for non-cacheable endpoints (modifications, search)
      console.log(`⚡ Direct API call (not cached): ${endpoint}`)
      return super.call(endpoint, filteredParams)
    }
    
    const cacheKey = CacheKeyGenerator.generate(endpoint, filteredParams)
    
    // Try memory cache first
    let cached = this.memoryCache.get(cacheKey)
    if (cached) {
      this.stats.hits++
      this.stats.bytesServed += JSON.stringify(cached).length
      console.log(`✅ Cache hit (memory): ${endpoint}`, cacheKey)
      return { data: cached }
    }
    
    // Try localStorage cache
    cached = this.storageCache.get(cacheKey)
    if (cached) {
      this.stats.hits++
      this.stats.bytesServed += JSON.stringify(cached).length
      // Promote to memory cache
      this.memoryCache.set(cacheKey, cached)
      console.log(`✅ Cache hit (storage): ${endpoint}`, cacheKey)
      return { data: cached }
    }
    
    // Cache miss - fetch from API
    this.stats.misses++
    console.log(`❌ Cache miss: ${endpoint}`, cacheKey)
    
    try {
      const response = await super.call(endpoint, filteredParams)
      
      // Cache the response
      const ttl = this.endpointTTLs[endpoint] || this.cacheConfig.defaultTTL
      const responseData = response.data?.data || response.data
      
      // Store in both cache layers
      this.memoryCache.set(cacheKey, responseData, ttl)
      this.storageCache.set(cacheKey, responseData, ttl)
      
      this.stats.bytesFetched += JSON.stringify(responseData).length
      
      console.log(`💾 Cached response for: ${endpoint}`, cacheKey)
      
      return response
    } catch (error) {
      this.stats.errors++
      throw error
    }
  }

  /**
   * Apply widget configuration filters to API params
   * @private
   */
  applyConfigFilters(params, widgetConfig) {
    const filtered = { ...params }
    
    // Add region filter if configured
    if (widgetConfig.region) {
      filtered.region = widgetConfig.region
    }
    
    // Add brand filters if configured
    if (widgetConfig.brands && widgetConfig.brands.length > 0) {
      filtered.brands = widgetConfig.brands
    }
    
    // Add exclude brands if configured
    if (widgetConfig.excludeBrands && widgetConfig.excludeBrands.length > 0) {
      filtered.exclude_brands = widgetConfig.excludeBrands
    }
    
    return filtered
  }

  /**
   * Clear all cache entries
   */
  clearCache() {
    this.memoryCache.clear()
    this.storageCache.clear()
    console.log('🗑️ All cache cleared')
  }

  /**
   * Selective cache invalidation for an endpoint
   */
  invalidateEndpoint(endpoint) {
    // Clear from memory cache
    const memStats = this.memoryCache.getStats()
    const memKeysToDelete = memStats.keys.filter(key => key.startsWith(endpoint))
    memKeysToDelete.forEach(key => this.memoryCache.delete(key))
    
    // Clear from storage cache
    this.storageCache.invalidateEndpoint(endpoint)
    
    console.log(`🗑️ Invalidated cache for endpoint: ${endpoint}`)
  }

  /**
   * Invalidate cache when configuration changes
   */
  invalidateOnConfigChange(newConfig) {
    const oldConfig = this.widgetConfig
    
    // Check if filters have changed
    const filtersChanged = 
      oldConfig.region !== newConfig.region ||
      JSON.stringify(oldConfig.brands) !== JSON.stringify(newConfig.brands) ||
      JSON.stringify(oldConfig.excludeBrands) !== JSON.stringify(newConfig.excludeBrands)
    
    if (filtersChanged) {
      console.log('🔄 Widget configuration changed - clearing affected cache')
      
      // Clear only selector caches affected by filters
      this.cacheableEndpoints.forEach(endpoint => {
        this.invalidateEndpoint(endpoint)
      })
      
      // Update stored config
      this.widgetConfig = newConfig
      
      return true
    }
    
    return false
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const hitRate = this.stats.hits / (this.stats.hits + this.stats.misses) || 0
    
    return {
      ...this.stats,
      hitRate: `${(hitRate * 100).toFixed(1)}%`,
      bandwidthSaved: this.formatBytes(this.stats.bytesServed),
      bandwidthUsed: this.formatBytes(this.stats.bytesFetched),
      memoryCache: this.memoryCache.getStats(),
      storageCache: this.storageCache.getStats(),
      evictionStats: this.evictionManager.getStats()
    }
  }

  /**
   * Format bytes for display
   * @private
   */
  formatBytes(bytes) {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  }

  /**
   * Preload common selector data
   */
  async preloadSelectorData() {
    // Only preload the most common selectors
    const preloadEndpoints = [
      { endpoint: 'year', params: {} },     // Years without filters
      { endpoint: 'make', params: {} }      // All makes (if no brand filter)
    ]
    
    // Skip make preload if brand filter is active
    if (this.widgetConfig.brands && this.widgetConfig.brands.length > 0) {
      preloadEndpoints.pop()
    }
    
    console.log('📦 Preloading selector data...')
    
    const promises = preloadEndpoints.map(({ endpoint, params }) => 
      this.call(endpoint, params).catch(e => {
        console.warn(`Preload failed for ${endpoint}:`, e)
      })
    )
    
    await Promise.all(promises)
    console.log('✅ Selector preload complete')
  }

  /**
   * Check if endpoint is cacheable
   */
  isCacheable(endpoint) {
    return this.cacheableEndpoints.includes(endpoint)
  }

  /**
   * Enable or disable caching
   */
  setCacheEnabled(enabled) {
    this.cacheConfig.enabled = enabled
    console.log(`🎯 Cache ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Run cache maintenance (eviction, cleanup)
   */
  runMaintenance() {
    console.log('🔧 Running cache maintenance...')
    
    // Evict expired entries
    const expired = this.evictionManager.evictExpired()
    
    // Check storage usage and evict if needed
    const stats = this.storageCache.getStats()
    const utilizationPercent = parseFloat(stats.utilization)
    
    if (utilizationPercent > 80) {
      // Evict LRU entries to get back to 60% utilization
      const targetSize = this.cacheConfig.storageMaxSize * 0.6
      const evicted = this.evictionManager.evictLRU(targetSize)
      console.log(`🧹 Evicted ${evicted} LRU entries to reduce storage usage`)
    }
    
    console.log(`✅ Maintenance complete: ${expired} expired entries removed`)
  }

  /**
   * Export cache statistics for monitoring
   */
  exportStats() {
    const stats = this.getCacheStats()
    const timestamp = new Date().toISOString()
    
    return {
      timestamp,
      ...stats,
      configuration: {
        enabled: this.cacheConfig.enabled,
        cacheableEndpoints: this.cacheableEndpoints,
        ttls: this.endpointTTLs,
        memoryMaxSize: this.cacheConfig.memoryMaxSize,
        storageMaxSize: this.formatBytes(this.cacheConfig.storageMaxSize)
      }
    }
  }
}

/**
 * Factory function to create a cached API client instance
 */
export function createCachedApiClient(config, widgetResources, cacheConfig) {
  return new CachedApiClient(config, widgetResources, cacheConfig)
}