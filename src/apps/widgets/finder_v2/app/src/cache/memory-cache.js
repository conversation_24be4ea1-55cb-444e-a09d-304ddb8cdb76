/**
 * Memory Cache Implementation
 * Runtime cache for fast in-memory data access with LRU eviction
 */

export class MemoryCache {
  constructor(maxSize = 50) {
    this.cache = new Map()
    this.maxSize = maxSize
    this.accessOrder = []
  }

  /**
   * Get value from cache
   * @param {string} key - Cache key
   * @returns {any} Cached value or null
   */
  get(key) {
    if (this.cache.has(key)) {
      // Update LRU order
      this.updateAccessOrder(key)
      const entry = this.cache.get(key)
      
      // Check if entry is still valid
      if (this.isValidEntry(entry)) {
        console.log('🎯 Memory cache hit:', key)
        return entry.data
      } else {
        // Remove expired entry
        this.delete(key)
      }
    }
    return null
  }

  /**
   * Set value in cache
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds
   */
  set(key, value, ttl = 3600000) {
    // Evict LRU if at capacity
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }
    
    const entry = {
      data: value,
      timestamp: Date.now(),
      ttl: ttl
    }
    
    this.cache.set(key, entry)
    this.updateAccessOrder(key)
    console.log('💾 Memory cache set:', key)
  }

  /**
   * Delete entry from cache
   * @param {string} key - Cache key
   */
  delete(key) {
    this.cache.delete(key)
    const index = this.accessOrder.indexOf(key)
    if (index > -1) {
      this.accessOrder.splice(index, 1)
    }
  }

  /**
   * Clear all cache entries
   */
  clear() {
    this.cache.clear()
    this.accessOrder = []
    console.log('🗑️ Memory cache cleared')
  }

  /**
   * Update access order for LRU tracking
   * @private
   */
  updateAccessOrder(key) {
    const index = this.accessOrder.indexOf(key)
    if (index > -1) {
      this.accessOrder.splice(index, 1)
    }
    this.accessOrder.push(key)
  }

  /**
   * Evict least recently used entry
   * @private
   */
  evictLRU() {
    if (this.accessOrder.length > 0) {
      const lruKey = this.accessOrder[0]
      this.delete(lruKey)
      console.log('🔄 Memory cache LRU evicted:', lruKey)
    }
  }

  /**
   * Check if cache entry is still valid
   * @private
   */
  isValidEntry(entry) {
    if (!entry || !entry.timestamp || !entry.ttl) {
      return false
    }
    return Date.now() - entry.timestamp <= entry.ttl
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys())
    }
  }
}