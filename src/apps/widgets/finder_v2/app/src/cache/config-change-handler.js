/**
 * Configuration Change Handler
 * Detects widget configuration changes and invalidates affected cache
 */

export class ConfigChangeHandler {
  constructor(cache, widgetId) {
    this.cache = cache
    this.widgetId = widgetId
    this.configKey = `ws_finder_config_${widgetId}`
    this.lastConfig = this.loadConfig()
    
    // Set up periodic config check
    this.checkInterval = null
    this.startConfigMonitoring()
  }

  /**
   * Load current configuration from localStorage or cache
   * @private
   */
  loadConfig() {
    try {
      // Try to get config from localStorage first
      const storedConfig = localStorage.getItem(this.configKey)
      if (storedConfig) {
        return JSON.parse(storedConfig)
      }
    } catch (e) {
      console.error('Failed to load stored config:', e)
    }
    
    // Return current config from cache client
    return this.cache.widgetConfig || {}
  }

  /**
   * Save configuration to localStorage
   * @private
   */
  saveConfig(config) {
    try {
      localStorage.setItem(this.configKey, JSON.stringify(config))
    } catch (e) {
      console.error('Failed to save config:', e)
    }
  }

  /**
   * Check if configuration has changed
   * @param {Object} newConfig - New configuration to check
   * @returns {boolean} True if configuration changed
   */
  checkConfigChange(newConfig) {
    const currentConfig = newConfig || this.cache.widgetConfig
    
    // Check if filters have changed
    if (this.hasFiltersChanged(this.lastConfig, currentConfig)) {
      console.log('🔄 Widget config changed - clearing selector cache')
      console.log('Old config:', this.lastConfig)
      console.log('New config:', currentConfig)
      
      // Invalidate affected cache
      this.cache.invalidateOnConfigChange(currentConfig)
      
      // Update stored config
      this.lastConfig = currentConfig
      this.saveConfig(currentConfig)
      
      // Emit config change event
      this.emitConfigChangeEvent(currentConfig)
      
      return true
    }
    
    return false
  }

  /**
   * Check if filter-related config has changed
   * @private
   */
  hasFiltersChanged(oldConfig, newConfig) {
    // Check region filter
    if (oldConfig.region !== newConfig.region) {
      return true
    }
    
    // Check brand filters
    const oldBrands = JSON.stringify(oldConfig.brands || [])
    const newBrands = JSON.stringify(newConfig.brands || [])
    if (oldBrands !== newBrands) {
      return true
    }
    
    // Check exclude brands
    const oldExclude = JSON.stringify(oldConfig.excludeBrands || [])
    const newExclude = JSON.stringify(newConfig.excludeBrands || [])
    if (oldExclude !== newExclude) {
      return true
    }
    
    // Check other filter-related settings
    if (oldConfig.year !== newConfig.year) {
      return true
    }
    
    return false
  }

  /**
   * Start monitoring configuration changes
   */
  startConfigMonitoring() {
    // Check config every 30 seconds
    this.checkInterval = setInterval(() => {
      this.checkForRemoteConfigChange()
    }, 30000)
    
    // Also listen for storage events (changes from other tabs)
    window.addEventListener('storage', this.handleStorageEvent.bind(this))
    
    console.log('📡 Config monitoring started')
  }

  /**
   * Stop monitoring configuration changes
   */
  stopConfigMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
    
    window.removeEventListener('storage', this.handleStorageEvent.bind(this))
    
    console.log('📡 Config monitoring stopped')
  }

  /**
   * Check for remote configuration changes
   * @private
   */
  async checkForRemoteConfigChange() {
    try {
      // In a real implementation, this would fetch config from server
      // For now, we just check localStorage for changes
      const storedConfig = this.loadConfig()
      
      if (JSON.stringify(storedConfig) !== JSON.stringify(this.lastConfig)) {
        this.checkConfigChange(storedConfig)
      }
    } catch (e) {
      console.error('Failed to check remote config:', e)
    }
  }

  /**
   * Handle storage events from other tabs
   * @private
   */
  handleStorageEvent(event) {
    if (event.key === this.configKey && event.newValue) {
      try {
        const newConfig = JSON.parse(event.newValue)
        console.log('📨 Config change detected from another tab')
        this.checkConfigChange(newConfig)
      } catch (e) {
        console.error('Failed to handle storage event:', e)
      }
    }
  }

  /**
   * Emit custom event for config changes
   * @private
   */
  emitConfigChangeEvent(config) {
    const event = new CustomEvent('finderConfigChanged', {
      detail: {
        widgetId: this.widgetId,
        config: config,
        timestamp: Date.now()
      }
    })
    
    window.dispatchEvent(event)
  }

  /**
   * Update configuration and check for changes
   * @param {Object} newConfig - New configuration
   */
  updateConfig(newConfig) {
    const changed = this.checkConfigChange(newConfig)
    
    if (changed) {
      console.log('✅ Configuration updated and cache invalidated')
    } else {
      console.log('ℹ️ Configuration unchanged, cache preserved')
    }
    
    return changed
  }

  /**
   * Get current configuration
   */
  getCurrentConfig() {
    return this.lastConfig
  }

  /**
   * Force cache invalidation
   */
  forceInvalidation() {
    console.log('⚠️ Forcing cache invalidation')
    
    // Clear all cacheable endpoints
    this.cache.cacheableEndpoints.forEach(endpoint => {
      this.cache.invalidateEndpoint(endpoint)
    })
    
    // Emit force invalidation event
    const event = new CustomEvent('finderCacheInvalidated', {
      detail: {
        widgetId: this.widgetId,
        reason: 'forced',
        timestamp: Date.now()
      }
    })
    
    window.dispatchEvent(event)
  }

  /**
   * Destroy the handler and clean up
   */
  destroy() {
    this.stopConfigMonitoring()
    console.log('🛑 Config change handler destroyed')
  }
}