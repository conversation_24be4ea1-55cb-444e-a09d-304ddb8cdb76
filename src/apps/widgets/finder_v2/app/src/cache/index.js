/**
 * Cache Module Exports
 * Central export point for all caching functionality
 */

export { CachedApiClient, createCachedApiClient } from './cached-api-client.js'
export { MemoryCache } from './memory-cache.js'
export { LocalStorageCache } from './localstorage-cache.js'
export { CacheKeyGenerator } from './key-generator.js'
export { CacheEvictionManager } from './eviction-manager.js'
export { ConfigChangeHandler } from './config-change-handler.js'
export { LocalStorageManager, localStorageManager } from './localstorage-manager.js'

// Export cache utilities for external use
export const cacheUtils = {
  /**
   * Check if caching is supported in the current environment
   */
  isSupported() {
    try {
      const test = '__cache_test__'
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch (e) {
      return false
    }
  },
  
  /**
   * Get cache size information
   */
  getCacheSize() {
    const manager = localStorageManager
    return manager.getStorageUsage()
  },
  
  /**
   * Clear all API cache entries
   */
  clearApiCache() {
    const manager = localStorageManager
    return manager.clearApiCache()
  },
  
  /**
   * Check storage health
   */
  checkHealth() {
    const manager = localStorageManager
    return manager.checkStorageHealth()
  }
}