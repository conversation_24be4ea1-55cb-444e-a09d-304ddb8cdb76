/**
 * Store Index - Refactored Store is Now Default
 * This file manages store selection with the refactored store as default
 * Part of the finder.js store refactoring - All Phases Complete ✅
 * 
 * DEFAULT: The fully refactored store (Phase 3 & 4) is now used by default
 * 
 * To use legacy or earlier versions for testing/comparison:
 * - window.FinderV2Config.useRefactoredStore = 0 (Legacy store)
 * - window.FinderV2Config.useRefactoredStore = 1 (Phase 1)
 * - window.FinderV2Config.useRefactoredStore = 2 (Phase 2)
 * - localStorage.setItem('finder-v2-use-refactored-store', '0') (Legacy)
 * - localStorage.setItem('finder-v2-use-refactored-store', '1') (Phase 1)
 * - localStorage.setItem('finder-v2-use-refactored-store', '2') (Phase 2)
 * - Add ?useRefactoredStore=0 to the URL (Legacy)
 * - Add ?useRefactoredStore=1 to the URL (Phase 1)
 * - Add ?useRefactoredStore=2 to the URL (Phase 2)
 */

import { useFinderStore as useFinderStoreLegacy } from './finder.js'
import { useFinderStoreRefactored } from './finder-refactored.js'
import { useFinderStoreRefactoredV2 } from './finder-refactored-v2.js'
import { useFinderStoreRefactoredFinal } from './finder-refactored-final.js'

/**
 * Get which version of the store should be used
 * @returns {number} 0 for legacy, 1 for Phase 1, 2 for Phase 2, 3 for Final
 */
function getStoreVersion() {
  // Check URL parameter
  const urlParams = new URLSearchParams(window.location.search)
  const urlParam = urlParams.get('useRefactoredStore')
  if (urlParam === '0' || urlParam === 'false') {
    console.log('📦 Using legacy store (URL parameter - explicitly requested)')
    return 0
  } else if (urlParam === '3') {
    console.log('🎯 Using FINAL refactored store (URL parameter)')
    return 3
  } else if (urlParam === '2') {
    console.log('🚀 Using refactored store Phase 2 (URL parameter)')
    return 2
  } else if (urlParam === 'true' || urlParam === '1') {
    console.log('🔧 Using refactored store Phase 1 (URL parameter)')
    return 1
  }

  // Check localStorage
  const localStorageValue = localStorage.getItem('finder-v2-use-refactored-store')
  if (localStorageValue === '0' || localStorageValue === 'false') {
    console.log('📦 Using legacy store (localStorage - explicitly requested)')
    return 0
  } else if (localStorageValue === '3') {
    console.log('🎯 Using FINAL refactored store (localStorage)')
    return 3
  } else if (localStorageValue === '2') {
    console.log('🚀 Using refactored store Phase 2 (localStorage)')
    return 2
  } else if (localStorageValue === 'true' || localStorageValue === '1') {
    console.log('🔧 Using refactored store Phase 1 (localStorage)')
    return 1
  }

  // Check window config
  const windowConfig = window.FinderV2Config?.useRefactoredStore
  if (windowConfig === 0 || windowConfig === false) {
    console.log('📦 Using legacy store (window.FinderV2Config - explicitly requested)')
    return 0
  } else if (windowConfig === 3) {
    console.log('🎯 Using FINAL refactored store (window.FinderV2Config)')
    return 3
  } else if (windowConfig === 2) {
    console.log('🚀 Using refactored store Phase 2 (window.FinderV2Config)')
    return 2
  } else if (windowConfig === true || windowConfig === 1) {
    console.log('🔧 Using refactored store Phase 1 (window.FinderV2Config)')
    return 1
  }

  // Default to FINAL refactored store (Phase 3 & 4 complete)
  console.log('🎯 Using FINAL refactored store (default)')
  return 3
}

/**
 * Export the appropriate store based on feature flag
 */
const storeVersion = getStoreVersion()
export const useFinderStore = 
  storeVersion === 3 ? useFinderStoreRefactoredFinal :
  storeVersion === 2 ? useFinderStoreRefactoredV2 :
  storeVersion === 1 ? useFinderStoreRefactored :
  useFinderStoreLegacy

/**
 * Export all stores explicitly for testing
 */
export { 
  useFinderStoreLegacy, 
  useFinderStoreRefactored, 
  useFinderStoreRefactoredV2,
  useFinderStoreRefactoredFinal 
}

/**
 * Utility to switch stores at runtime (for A/B testing)
 * @param {number|boolean} version - 0/false for legacy, 1/true for Phase 1, 2 for Phase 2, 3 for Final
 */
export function switchStore(version) {
  if (version === 3) {
    localStorage.setItem('finder-v2-use-refactored-store', '3')
    console.log('🎯 Switched to FINAL refactored store')
  } else if (version === 2) {
    localStorage.setItem('finder-v2-use-refactored-store', '2')
    console.log('🚀 Switched to refactored store Phase 2')
  } else if (version === 1 || version === true) {
    localStorage.setItem('finder-v2-use-refactored-store', '1')
    console.log('🔧 Switched to refactored store Phase 1')
  } else {
    localStorage.removeItem('finder-v2-use-refactored-store')
    console.log('📦 Switched to legacy store')
  }
  
  // Note: Page reload required for change to take effect
  console.warn('⚠️ Page reload required for store switch to take effect')
}

/**
 * Get current store type
 * @returns {string} 'legacy', 'refactored-v1', 'refactored-v2', or 'refactored-final'
 */
export function getCurrentStoreType() {
  const version = getStoreVersion()
  return version === 3 ? 'refactored-final' :
         version === 2 ? 'refactored-v2' : 
         version === 1 ? 'refactored-v1' : 
         'legacy'
}