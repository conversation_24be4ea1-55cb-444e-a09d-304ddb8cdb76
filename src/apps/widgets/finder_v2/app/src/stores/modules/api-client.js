/**
 * API Client Module
 * Handles all HTTP requests with deduplication and error handling
 * Part of the finder.js store refactoring - Phase 1
 */

import axios from 'axios'

// Configure axios to serialize arrays as repeated keys (not array[]=)
axios.defaults.paramsSerializer = (params) => {
  const search = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((v) => {
        if (v !== undefined && v !== null && v !== '') {
          search.append(key, v)
        }
      })
    } else if (value !== undefined && value !== null && value !== '') {
      search.append(key, value)
    }
  })
  return search.toString()
}

export class ApiClient {
  constructor(config, widgetResources) {
    this.config = config
    this.widgetResources = widgetResources || {}
    this.ongoingRequests = new Map()
  }

  /**
   * Make an API call with automatic deduplication
   * @param {string} endpoint - The API endpoint name
   * @param {Object} params - Query parameters
   * @returns {Promise} The API response
   */
  async call(endpoint, params = {}) {
    console.log('🔍 API call - endpoint:', endpoint)
    console.log('🔍 API call - widgetResources:', this.widgetResources)
    
    // Get endpoint URL from widget resources
    const resource = this.widgetResources[endpoint]
    console.log('🔍 API call - resource found:', resource)
    
    if (!resource || !resource[1]) {
      console.error('🔍 API endpoint not configured:', endpoint)
      console.error('🔍 Available endpoints:', Object.keys(this.widgetResources))
      throw new Error(`API endpoint not configured: ${endpoint}`)
    }

    let url = resource[1]
    
    // Make relative URLs absolute
    if (url.startsWith('/') && this.config.baseUrl) {
      url = this.config.baseUrl + url
    }
    
    console.log('🔍 API call - URL before request:', url)
    console.log('🔍 API call - config.baseUrl:', this.config.baseUrl)

    // Deduplicate concurrent requests
    const requestKey = this.generateRequestKey(endpoint, params)
    
    // If identical request is already in progress, return the existing promise
    if (this.ongoingRequests.has(requestKey)) {
      return await this.ongoingRequests.get(requestKey)
    }

    // Create and track new request
    const requestPromise = this.executeRequest(url, params)
    this.ongoingRequests.set(requestKey, requestPromise)

    try {
      const result = await requestPromise
      return result
    } finally {
      // Clean up completed request
      this.ongoingRequests.delete(requestKey)
    }
  }

  /**
   * Execute the actual HTTP request
   * @private
   */
  async executeRequest(url, params) {
    console.log('🔍 executeRequest - URL:', url)
    console.log('🔍 executeRequest - params:', params)
    
    try {
      const response = await axios.get(url, { params })
      console.log('🔍 executeRequest - response:', response)
      return response
    } catch (error) {
      console.error('🔍 executeRequest - error:', error)
      // Enhanced error handling
      if (error.response) {
        // Server responded with error status
        const errorMessage = error.response.data?.message || 
                           error.response.data?.error || 
                           `API Error: ${error.response.status}`
        throw new Error(errorMessage)
      } else if (error.request) {
        // Request was made but no response
        throw new Error('Network error: Unable to reach the server')
      } else {
        // Something else happened
        throw new Error(error.message || 'An unexpected error occurred')
      }
    }
  }

  /**
   * Generate a unique key for request deduplication
   * @private
   */
  generateRequestKey(endpoint, params) {
    const sortedParams = Object.keys(params).sort().reduce((result, key) => {
      result[key] = params[key]
      return result
    }, {})
    return `${endpoint}:${JSON.stringify(sortedParams)}`
  }

  /**
   * Check if a request is currently in progress
   */
  isRequestInProgress(endpoint, params = {}) {
    const requestKey = this.generateRequestKey(endpoint, params)
    return this.ongoingRequests.has(requestKey)
  }

  /**
   * Get the number of ongoing requests
   */
  getOngoingRequestCount() {
    return this.ongoingRequests.size
  }

  /**
   * Cancel all ongoing requests (for cleanup)
   */
  cancelAllRequests() {
    this.ongoingRequests.clear()
  }
}

/**
 * Factory function to create an API client instance
 */
export function createApiClient(config, widgetResources) {
  return new ApiClient(config, widgetResources)
}