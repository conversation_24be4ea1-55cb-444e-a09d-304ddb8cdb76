/**
 * State Management Module
 * Centralizes all reactive state and computed properties
 * Part of the finder.js store refactoring - Phase 2
 */

import { ref, computed } from 'vue'

export function createFinderState() {
  // Core configuration
  const config = ref({})
  const outputTemplate = ref('')
  
  // Global loading and error states
  const loading = ref(false)
  const loadingResults = ref(false)
  const error = ref(null)
  const results = ref([])
  
  // Vehicle selection state
  const selectedYear = ref('')
  const selectedMake = ref('')
  const selectedModel = ref('')
  const selectedModification = ref('')
  const selectedGeneration = ref('')
  
  // Available options
  const years = ref([])
  const makes = ref([])
  const models = ref([])
  const modifications = ref([])
  const generations = ref([])
  
  // Individual loading states
  const loadingYears = ref(false)
  const loadingMakes = ref(false)
  const loadingModels = ref(false)
  const loadingGenerations = ref(false)
  const loadingModifications = ref(false)
  
  // State loaded flags for auto-expand behavior
  const stateLoadedYears = ref(false)
  const stateLoadedMakes = ref(false)
  const stateLoadedModels = ref(false)
  const stateLoadedGenerations = ref(false)
  const stateLoadedModifications = ref(false)
  
  // Request tracking for search deduplication
  const activeSearchSignatureInFlight = ref('')
  
  // Computed properties
  const flowType = computed(() => config.value.flowType || 'primary')
  const apiVersion = computed(() => config.value.apiVersion || 'v2')
  const widgetResources = computed(() => config.value.widgetResources || {})
  
  // Computed selection states
  const hasYearSelected = computed(() => !!selectedYear.value)
  const hasMakeSelected = computed(() => !!selectedMake.value)
  const hasModelSelected = computed(() => !!selectedModel.value)
  const hasGenerationSelected = computed(() => !!selectedGeneration.value)
  const hasModificationSelected = computed(() => !!selectedModification.value)
  
  // Computed ready states
  const isConfigured = computed(() => !!config.value.widgetResources)
  const hasResults = computed(() => results.value.length > 0)
  const isSearching = computed(() => loadingResults.value)
  const hasError = computed(() => !!error.value)
  
  // Computed search readiness based on flow type
  const canSearch = computed(() => {
    if (flowType.value === 'primary' || flowType.value === 'year_select') {
      return hasYearSelected.value && hasMakeSelected.value && hasModelSelected.value
    } else if (flowType.value === 'alternative') {
      return hasMakeSelected.value && hasModelSelected.value
    }
    return false
  })
  
  // Reset functions
  function resetVehicleSearch() {
    selectedYear.value = ''
    selectedMake.value = ''
    selectedModel.value = ''
    selectedModification.value = ''
    selectedGeneration.value = ''
    models.value = []
    modifications.value = []
    generations.value = []
    
    // Reset loading states
    loadingModels.value = false
    loadingModifications.value = false
    loadingGenerations.value = false
    
    // Reset state loaded flags
    stateLoadedModels.value = false
    stateLoadedModifications.value = false
    stateLoadedGenerations.value = false
  }
  
  function clearResults() {
    results.value = []
    
    // Trigger iframe resize after clearing results
    setTimeout(() => {
      if (window.parentIFrame) {
        window.parentIFrame.size()
      }
    }, 50)
  }
  
  function clearError() {
    error.value = null
  }
  
  function resetAll() {
    // Reset configuration
    config.value = {}
    outputTemplate.value = ''
    
    // Reset all states
    loading.value = false
    loadingResults.value = false
    error.value = null
    results.value = []
    
    // Reset vehicle search
    resetVehicleSearch()
    
    // Reset all options
    years.value = []
    makes.value = []
    
    // Reset all loading states
    loadingYears.value = false
    loadingMakes.value = false
    
    // Reset all state loaded flags
    stateLoadedYears.value = false
    stateLoadedMakes.value = false
    
    // Reset search tracking
    activeSearchSignatureInFlight.value = ''
  }
  
  // Configuration setters
  function setConfig(newConfig) {
    config.value = newConfig
    outputTemplate.value = newConfig.interface?.outputTemplate || ''
  }
  
  function setError(errorMessage) {
    console.log('🔍 State - setError called with:', errorMessage)
    console.trace('🔍 State - setError stack trace')
    error.value = errorMessage
  }
  
  // Helper to get option by value
  function findOption(optionList, value) {
    if (!value) return null
    return optionList.find(opt => 
      opt?.slug === value || 
      opt?.id === value || 
      opt?.value === value
    ) || null
  }
  
  // Helper to convert selection to value object for events
  function toValueObject(optionList, selectedValue) {
    if (!selectedValue) return null
    const match = findOption(optionList, selectedValue)
    const slug = match?.slug ?? (typeof selectedValue === 'string' ? selectedValue : String(selectedValue))
    const title = match?.title ?? match?.name ?? match?.value ?? String(slug)
    return { slug, title }
  }
  
  return {
    // Configuration
    config,
    outputTemplate,
    
    // Global states
    loading,
    loadingResults,
    error,
    results,
    
    // Selection state
    selectedYear,
    selectedMake,
    selectedModel,
    selectedModification,
    selectedGeneration,
    
    // Options
    years,
    makes,
    models,
    modifications,
    generations,
    
    // Loading states
    loadingYears,
    loadingMakes,
    loadingModels,
    loadingGenerations,
    loadingModifications,
    
    // State loaded flags
    stateLoadedYears,
    stateLoadedMakes,
    stateLoadedModels,
    stateLoadedGenerations,
    stateLoadedModifications,
    
    // Request tracking
    activeSearchSignatureInFlight,
    
    // Computed properties
    flowType,
    apiVersion,
    widgetResources,
    hasYearSelected,
    hasMakeSelected,
    hasModelSelected,
    hasGenerationSelected,
    hasModificationSelected,
    isConfigured,
    hasResults,
    isSearching,
    hasError,
    canSearch,
    
    // Methods
    resetVehicleSearch,
    clearResults,
    clearError,
    resetAll,
    setConfig,
    setError,
    findOption,
    toValueObject
  }
}

/**
 * Factory function to create a finder state instance
 */
export function createState() {
  return createFinderState()
}