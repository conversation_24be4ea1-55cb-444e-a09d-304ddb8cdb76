/**
 * Vehicle Loader Module
 * Handles all vehicle data loading operations
 * Part of the finder.js store refactoring - Phase 2
 */

export class VehicleLoader {
  constructor(apiClient, state, filterBuilder, analytics) {
    this.api = apiClient
    this.state = state
    this.filterBuilder = filterBuilder
    this.analytics = analytics
  }

  /**
   * Load years with optional make/model filtering
   */
  async loadYears(make = null, model = null) {
    try {
      this.state.loadingYears.value = true
      this.state.stateLoadedYears.value = false

      const params = {}
      if (make) params.make = make
      if (model) params.model = model
      
      // Apply region filters
      Object.assign(params, this.filterBuilder.getFiltersForEndpoint('year'))

      const response = await this.api.call('year', params)
      console.log('🔍 loadYears - response:', response)
      
      // API response is wrapped: { data: { data: [...] } }
      this.state.years.value = response.data?.data || response.data || []
      console.log('🔍 loadYears - years set to:', this.state.years.value)

      this.state.stateLoadedYears.value = true
      
      // Track successful data load
      if (this.analytics) {
        this.analytics.trackEvent('data_load_complete', {
          data_type: 'years',
          data_count: this.state.years.value.length,
          make: make || '',
          model: model || ''
        })
      }
      
      return this.state.years.value
    } catch (err) {
      this.state.setError(err.message)
      
      // Track error
      if (this.analytics) {
        this.analytics.trackError('data_load_failed', {
          data_type: 'years',
          error_message: err.message,
          api_endpoint: 'year',
          make: make || '',
          model: model || ''
        })
      }
      
      throw err
    } finally {
      this.state.loadingYears.value = false
    }
  }

  /**
   * Load makes with optional year filtering
   */
  async loadMakes(year = null) {
    try {
      this.state.loadingMakes.value = true
      this.state.stateLoadedMakes.value = false

      const params = year ? { year } : {}
      
      // Apply filters (including brand filter)
      Object.assign(params, this.filterBuilder.getFiltersForEndpoint('make'))

      const response = await this.api.call('make', params)
      
      // API response is wrapped: { data: { data: [...] } }
      this.state.makes.value = response.data?.data || response.data || []

      this.state.stateLoadedMakes.value = true
      
      return this.state.makes.value
    } catch (err) {
      this.state.setError(err.message)
      throw err
    } finally {
      this.state.loadingMakes.value = false
    }
  }

  /**
   * Load models for a specific make
   */
  async loadModels(make, year = null) {
    if (!make) {
      throw new Error('Make is required to load models')
    }
    
    try {
      this.state.loadingModels.value = true
      this.state.stateLoadedModels.value = false

      const params = { make }
      if (year) params.year = year
      
      // Apply region filter only (NOT brand filter - user already selected a make)
      Object.assign(params, this.filterBuilder.getFiltersForEndpoint('model'))

      const response = await this.api.call('model', params)
      
      // API response is wrapped: { data: { data: [...] } }
      this.state.models.value = response.data?.data || response.data || []

      this.state.stateLoadedModels.value = true
      
      return this.state.models.value
    } catch (err) {
      this.state.setError(err.message)
      throw err
    } finally {
      this.state.loadingModels.value = false
    }
  }

  /**
   * Load modifications for a specific make/model
   */
  async loadModifications(make, model, yearOrGeneration = null) {
    if (!make || !model) {
      throw new Error('Make and model are required to load modifications')
    }
    
    try {
      this.state.loadingModifications.value = true
      this.state.stateLoadedModifications.value = false

      const params = { make, model }

      // Handle flow-specific parameter
      if (yearOrGeneration) {
        const flowType = this.state.flowType.value
        if (flowType === 'primary' || flowType === 'year_select') {
          params.year = yearOrGeneration
        } else if (flowType === 'alternative') {
          params.generation = yearOrGeneration
        }
      }
      
      // Apply region filter
      Object.assign(params, this.filterBuilder.getFiltersForEndpoint('modification'))

      const response = await this.api.call('modification', params)
      
      // API response is wrapped: { data: { data: [...] } }
      this.state.modifications.value = response.data?.data || response.data || []

      this.state.stateLoadedModifications.value = true
      
      return this.state.modifications.value
    } catch (err) {
      this.state.setError(err.message)
      throw err
    } finally {
      this.state.loadingModifications.value = false
    }
  }

  /**
   * Load generations for a specific make/model
   */
  async loadGenerations(make, model) {
    if (!make || !model) {
      throw new Error('Make and model are required to load generations')
    }
    
    try {
      this.state.loadingGenerations.value = true
      this.state.stateLoadedGenerations.value = false

      const params = { make, model }
      
      // Apply region filter
      Object.assign(params, this.filterBuilder.getFiltersForEndpoint('generation'))

      const response = await this.api.call('generation', params)
      
      // API response is wrapped: { data: { data: [...] } }
      this.state.generations.value = response.data?.data || response.data || []

      this.state.stateLoadedGenerations.value = true
      
      return this.state.generations.value
    } catch (err) {
      this.state.setError(err.message)
      throw err
    } finally {
      this.state.loadingGenerations.value = false
    }
  }

  /**
   * Load initial data based on flow type
   */
  async loadInitialData() {
    try {
      this.state.loading.value = true
      this.state.clearError()

      const flowType = this.state.flowType.value
      
      // Load years for primary flow or makes for alternative flow
      if (flowType === 'primary') {
        await this.loadYears()
      } else {
        await this.loadMakes()
      }
      
      return true
    } catch (err) {
      this.state.setError(err.message)
      return false
    } finally {
      this.state.loading.value = false
    }
  }

  /**
   * Check if any data is currently loading
   */
  isLoading() {
    return (
      this.state.loadingYears.value ||
      this.state.loadingMakes.value ||
      this.state.loadingModels.value ||
      this.state.loadingGenerations.value ||
      this.state.loadingModifications.value
    )
  }

  /**
   * Get loading status summary
   */
  getLoadingStatus() {
    return {
      years: this.state.loadingYears.value,
      makes: this.state.loadingMakes.value,
      models: this.state.loadingModels.value,
      generations: this.state.loadingGenerations.value,
      modifications: this.state.loadingModifications.value,
      anyLoading: this.isLoading()
    }
  }

  /**
   * Get loaded state summary
   */
  getLoadedStatus() {
    return {
      years: this.state.stateLoadedYears.value,
      makes: this.state.stateLoadedMakes.value,
      models: this.state.stateLoadedModels.value,
      generations: this.state.stateLoadedGenerations.value,
      modifications: this.state.stateLoadedModifications.value
    }
  }
}

/**
 * Factory function to create a vehicle loader instance
 */
export function createVehicleLoader(apiClient, state, filterBuilder, analytics) {
  return new VehicleLoader(apiClient, state, filterBuilder, analytics)
}