/**
 * History Manager Module
 * Handles search history operations and form population
 * Part of the finder.js store refactoring - Phase 4
 */

export class HistoryManager {
  constructor(state, vehicleLoader, searchExecutor, searchHistory) {
    this.state = state
    this.vehicleLoader = vehicleLoader
    this.searchExecutor = searchExecutor
    this.searchHistory = searchHistory
  }

  /**
   * Execute a search from history
   */
  async executeSearchFromHistory(searchId) {
    if (!this.searchHistory) {
      console.warn('Search history not initialized')
      return false
    }
    
    const searchItem = this.searchHistory.getSearch(searchId)
    if (!searchItem) {
      console.warn('Search not found:', searchId)
      return false
    }
    
    try {
      // Clear current results
      this.state.clearResults()
      
      // Populate form with saved search parameters
      await this.populateFormFromSearch(searchItem)
      
      // Update search timestamp to move it to top of history
      this.searchHistory.updateSearchTimestamp(searchId)
      
      // Execute the search
      const results = await this.searchExecutor.searchByVehicle()
      
      return results !== null
    } catch (error) {
      console.error('Failed to execute search from history:', error)
      this.state.setError('Failed to execute search from history')
      return false
    }
  }

  /**
   * Populate form fields from a saved search
   */
  async populateFormFromSearch(searchItem) {
    const params = searchItem.parameters
    const flow = searchItem.flowType || 'primary'
    
    // Reset current selections
    this.state.resetVehicleSearch()
    
    try {
      switch (flow) {
        case 'primary':
          await this.populatePrimaryFlow(params)
          break
          
        case 'alternative':
          await this.populateAlternativeFlow(params)
          break
          
        case 'year_select':
          await this.populateYearSelectFlow(params)
          break
          
        default:
          throw new Error(`Unknown flow type: ${flow}`)
      }
    } catch (error) {
      console.error('Failed to populate form from search:', error)
      throw error
    }
  }

  /**
   * Populate form for primary flow (Year → Make → Model → Modification)
   */
  async populatePrimaryFlow(params) {
    // Load and select year
    if (params.year) {
      // Years should already be loaded in primary flow
      if (!this.state.years.value.length) {
        await this.vehicleLoader.loadYears()
      }
      this.state.selectedYear.value = params.year
      
      // Load makes for the selected year
      await this.vehicleLoader.loadMakes(params.year)
    }
    
    // Load and select make
    if (params.make) {
      this.state.selectedMake.value = params.make
      
      // Load models for the selected make and year
      await this.vehicleLoader.loadModels(params.make, params.year)
    }
    
    // Load and select model
    if (params.model) {
      this.state.selectedModel.value = params.model
      
      // Load modifications for the selected combination
      await this.vehicleLoader.loadModifications(params.make, params.model, params.year)
    }
    
    // Select modification if available
    if (params.modification) {
      this.state.selectedModification.value = params.modification
    }
  }

  /**
   * Populate form for alternative flow (Make → Model → Generation → Modification)
   */
  async populateAlternativeFlow(params) {
    // Load and select make
    if (params.make) {
      // Makes should already be loaded in alternative flow
      if (!this.state.makes.value.length) {
        await this.vehicleLoader.loadMakes()
      }
      this.state.selectedMake.value = params.make
      
      // Load models for the selected make
      await this.vehicleLoader.loadModels(params.make)
    }
    
    // Load and select model
    if (params.model) {
      this.state.selectedModel.value = params.model
      
      // Load generations for the selected make and model
      await this.vehicleLoader.loadGenerations(params.make, params.model)
    }
    
    // Load and select generation
    if (params.generation) {
      this.state.selectedGeneration.value = params.generation
      
      // Load modifications for the selected combination
      await this.vehicleLoader.loadModifications(params.make, params.model, params.generation)
    }
    
    // Select modification if available
    if (params.modification) {
      this.state.selectedModification.value = params.modification
    }
  }

  /**
   * Populate form for year_select flow (Make → Model → Year → Modification)
   */
  async populateYearSelectFlow(params) {
    // Load and select make
    if (params.make) {
      // Makes should already be loaded
      if (!this.state.makes.value.length) {
        await this.vehicleLoader.loadMakes()
      }
      this.state.selectedMake.value = params.make
      
      // Load models for the selected make
      await this.vehicleLoader.loadModels(params.make)
    }
    
    // Load and select model
    if (params.model) {
      this.state.selectedModel.value = params.model
      
      // Load years for the selected make and model
      await this.vehicleLoader.loadYears(params.make, params.model)
    }
    
    // Load and select year
    if (params.year) {
      this.state.selectedYear.value = params.year
      
      // Load modifications for the selected combination
      await this.vehicleLoader.loadModifications(params.make, params.model, params.year)
    }
    
    // Select modification if available
    if (params.modification) {
      this.state.selectedModification.value = params.modification
    }
  }

  /**
   * Get search history instance
   */
  getSearchHistory() {
    return this.searchHistory
  }

  /**
   * Get all search history items
   */
  getHistoryItems() {
    if (!this.searchHistory) return []
    return this.searchHistory.getHistory()
  }

  /**
   * Get a specific search from history
   */
  getHistoryItem(searchId) {
    if (!this.searchHistory) return null
    return this.searchHistory.getSearch(searchId)
  }

  /**
   * Clear all search history
   */
  clearHistory() {
    if (!this.searchHistory) return
    this.searchHistory.clearHistory()
  }

  /**
   * Remove a specific search from history
   */
  removeHistoryItem(searchId) {
    if (!this.searchHistory) return
    this.searchHistory.removeSearch(searchId)
  }

  /**
   * Check if history is available
   */
  hasHistory() {
    return !!this.searchHistory && this.searchHistory.getHistory().length > 0
  }

  /**
   * Get the most recent search
   */
  getMostRecentSearch() {
    const history = this.getHistoryItems()
    return history.length > 0 ? history[0] : null
  }

  /**
   * Restore the most recent search
   */
  async restoreMostRecentSearch() {
    const mostRecent = this.getMostRecentSearch()
    if (!mostRecent) return false
    
    return await this.executeSearchFromHistory(mostRecent.id)
  }

  /**
   * Get search count
   */
  getSearchCount() {
    return this.getHistoryItems().length
  }

  /**
   * Check if a search matches current selections
   */
  isCurrentSearch(searchItem) {
    if (!searchItem) return false
    
    const params = searchItem.parameters
    const flowType = searchItem.flowType || 'primary'
    
    // Check flow type
    if (flowType !== this.state.flowType.value) return false
    
    // Check common fields
    if (params.make !== this.state.selectedMake.value) return false
    if (params.model !== this.state.selectedModel.value) return false
    
    // Check flow-specific fields
    if (flowType === 'primary' || flowType === 'year_select') {
      if (params.year !== this.state.selectedYear.value) return false
      if (params.modification !== this.state.selectedModification.value) return false
    } else if (flowType === 'alternative') {
      if (params.generation !== this.state.selectedGeneration.value) return false
      if (params.modification !== this.state.selectedModification.value) return false
    }
    
    return true
  }
}

/**
 * Factory function to create a history manager instance
 */
export function createHistoryManager(state, vehicleLoader, searchExecutor, searchHistory) {
  return new HistoryManager(state, vehicleLoader, searchExecutor, searchHistory)
}