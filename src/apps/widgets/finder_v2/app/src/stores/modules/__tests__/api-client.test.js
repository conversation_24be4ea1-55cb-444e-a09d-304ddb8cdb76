/**
 * Unit tests for API Client Module
 * Part of the finder.js store refactoring - Phase 1
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import axios from 'axios'
import { ApiClient, createApiClient } from '../api-client.js'

// Mock axios
vi.mock('axios')

describe('ApiClient', () => {
  let apiClient
  let mockConfig
  let mockWidgetResources

  beforeEach(() => {
    // Setup mock config and resources
    mockConfig = {
      baseUrl: 'https://api.example.com'
    }
    
    mockWidgetResources = {
      year: ['Year', '/api/year'],
      make: ['Make', '/api/make'],
      model: ['Model', '/api/model'],
      search_by_model: ['Search', '/api/search_by_model']
    }

    // Create API client instance
    apiClient = new ApiClient(mockConfig, mockWidgetResources)

    // Reset axios mock
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with config and widget resources', () => {
      expect(apiClient.config).toBe(mockConfig)
      expect(apiClient.widgetResources).toBe(mockWidgetResources)
      expect(apiClient.ongoingRequests).toBeInstanceOf(Map)
    })
  })

  describe('call method', () => {
    it('should make API call with correct URL', async () => {
      const mockResponse = { data: { data: ['2023', '2022'] } }
      axios.get.mockResolvedValue(mockResponse)

      const result = await apiClient.call('year', { make: 'toyota' })

      expect(axios.get).toHaveBeenCalledWith(
        'https://api.example.com/api/year',
        { params: { make: 'toyota' } }
      )
      expect(result).toBe(mockResponse)
    })

    it('should throw error for unconfigured endpoint', async () => {
      await expect(apiClient.call('invalid_endpoint')).rejects.toThrow(
        'API endpoint not configured: invalid_endpoint'
      )
    })

    it('should handle relative URLs correctly', async () => {
      const mockResponse = { data: { data: [] } }
      axios.get.mockResolvedValue(mockResponse)

      await apiClient.call('make')

      expect(axios.get).toHaveBeenCalledWith(
        'https://api.example.com/api/make',
        { params: {} }
      )
    })

    it('should handle absolute URLs correctly', async () => {
      mockWidgetResources.external = ['External', 'https://external.com/api']
      const mockResponse = { data: { data: [] } }
      axios.get.mockResolvedValue(mockResponse)

      await apiClient.call('external')

      expect(axios.get).toHaveBeenCalledWith(
        'https://external.com/api',
        { params: {} }
      )
    })
  })

  describe('request deduplication', () => {
    it('should deduplicate concurrent identical requests', async () => {
      const mockResponse = { data: { data: ['2023'] } }
      axios.get.mockResolvedValue(mockResponse)

      // Make two identical requests concurrently
      const promise1 = apiClient.call('year', { make: 'toyota' })
      const promise2 = apiClient.call('year', { make: 'toyota' })

      // Both should return the same promise
      expect(promise1).toBe(promise2)

      // axios.get should only be called once
      expect(axios.get).toHaveBeenCalledTimes(1)

      const [result1, result2] = await Promise.all([promise1, promise2])
      expect(result1).toBe(result2)
      expect(result1).toBe(mockResponse)
    })

    it('should not deduplicate requests with different parameters', async () => {
      const mockResponse1 = { data: { data: ['2023'] } }
      const mockResponse2 = { data: { data: ['2022'] } }
      
      axios.get
        .mockResolvedValueOnce(mockResponse1)
        .mockResolvedValueOnce(mockResponse2)

      // Make two different requests concurrently
      const promise1 = apiClient.call('year', { make: 'toyota' })
      const promise2 = apiClient.call('year', { make: 'honda' })

      // Should be different promises
      expect(promise1).not.toBe(promise2)

      // axios.get should be called twice
      expect(axios.get).toHaveBeenCalledTimes(2)

      const [result1, result2] = await Promise.all([promise1, promise2])
      expect(result1).toBe(mockResponse1)
      expect(result2).toBe(mockResponse2)
    })

    it('should allow new request after previous one completes', async () => {
      const mockResponse1 = { data: { data: ['2023'] } }
      const mockResponse2 = { data: { data: ['2022'] } }
      
      axios.get
        .mockResolvedValueOnce(mockResponse1)
        .mockResolvedValueOnce(mockResponse2)

      // Make first request
      const result1 = await apiClient.call('year', { make: 'toyota' })
      expect(result1).toBe(mockResponse1)
      expect(axios.get).toHaveBeenCalledTimes(1)

      // Make second identical request after first completes
      const result2 = await apiClient.call('year', { make: 'toyota' })
      expect(result2).toBe(mockResponse2)
      expect(axios.get).toHaveBeenCalledTimes(2)
    })
  })

  describe('error handling', () => {
    it('should handle server error responses', async () => {
      const errorResponse = {
        response: {
          status: 404,
          data: { message: 'Not found' }
        }
      }
      axios.get.mockRejectedValue(errorResponse)

      await expect(apiClient.call('year')).rejects.toThrow('Not found')
    })

    it('should handle network errors', async () => {
      const networkError = {
        request: {}
      }
      axios.get.mockRejectedValue(networkError)

      await expect(apiClient.call('year')).rejects.toThrow(
        'Network error: Unable to reach the server'
      )
    })

    it('should handle unexpected errors', async () => {
      const unexpectedError = new Error('Something went wrong')
      axios.get.mockRejectedValue(unexpectedError)

      await expect(apiClient.call('year')).rejects.toThrow('Something went wrong')
    })

    it('should clean up ongoing requests on error', async () => {
      const error = new Error('Test error')
      axios.get.mockRejectedValue(error)

      expect(apiClient.ongoingRequests.size).toBe(0)
      
      try {
        await apiClient.call('year')
      } catch (e) {
        // Expected to throw
      }

      expect(apiClient.ongoingRequests.size).toBe(0)
    })
  })

  describe('utility methods', () => {
    it('should check if request is in progress', async () => {
      const mockResponse = { data: { data: [] } }
      // Create a promise that we can control
      let resolvePromise
      const controlledPromise = new Promise(resolve => {
        resolvePromise = resolve
      })
      axios.get.mockReturnValue(controlledPromise)

      // Start a request but don't await it
      const requestPromise = apiClient.call('year', { make: 'toyota' })
      
      // Check if request is in progress
      expect(apiClient.isRequestInProgress('year', { make: 'toyota' })).toBe(true)
      expect(apiClient.isRequestInProgress('year', { make: 'honda' })).toBe(false)
      
      // Resolve the promise
      resolvePromise(mockResponse)
      await requestPromise

      // Check that request is no longer in progress
      expect(apiClient.isRequestInProgress('year', { make: 'toyota' })).toBe(false)
    })

    it('should get ongoing request count', async () => {
      const mockResponse = { data: { data: [] } }
      let resolvePromise1, resolvePromise2
      
      axios.get
        .mockReturnValueOnce(new Promise(resolve => { resolvePromise1 = resolve }))
        .mockReturnValueOnce(new Promise(resolve => { resolvePromise2 = resolve }))

      expect(apiClient.getOngoingRequestCount()).toBe(0)

      // Start two different requests
      const promise1 = apiClient.call('year', { make: 'toyota' })
      const promise2 = apiClient.call('make', { year: '2023' })

      expect(apiClient.getOngoingRequestCount()).toBe(2)

      // Complete first request
      resolvePromise1(mockResponse)
      await promise1

      expect(apiClient.getOngoingRequestCount()).toBe(1)

      // Complete second request
      resolvePromise2(mockResponse)
      await promise2

      expect(apiClient.getOngoingRequestCount()).toBe(0)
    })

    it('should cancel all requests', () => {
      // Add some fake ongoing requests
      apiClient.ongoingRequests.set('test1', Promise.resolve())
      apiClient.ongoingRequests.set('test2', Promise.resolve())
      
      expect(apiClient.ongoingRequests.size).toBe(2)

      apiClient.cancelAllRequests()

      expect(apiClient.ongoingRequests.size).toBe(0)
    })
  })

  describe('generateRequestKey', () => {
    it('should generate consistent keys for same parameters', () => {
      const key1 = apiClient.generateRequestKey('year', { make: 'toyota', year: '2023' })
      const key2 = apiClient.generateRequestKey('year', { year: '2023', make: 'toyota' })

      expect(key1).toBe(key2)
    })

    it('should generate different keys for different endpoints', () => {
      const key1 = apiClient.generateRequestKey('year', { make: 'toyota' })
      const key2 = apiClient.generateRequestKey('make', { make: 'toyota' })

      expect(key1).not.toBe(key2)
    })

    it('should generate different keys for different parameters', () => {
      const key1 = apiClient.generateRequestKey('year', { make: 'toyota' })
      const key2 = apiClient.generateRequestKey('year', { make: 'honda' })

      expect(key1).not.toBe(key2)
    })
  })

  describe('factory function', () => {
    it('should create API client instance', () => {
      const client = createApiClient(mockConfig, mockWidgetResources)
      expect(client).toBeInstanceOf(ApiClient)
      expect(client.config).toBe(mockConfig)
      expect(client.widgetResources).toBe(mockWidgetResources)
    })
  })
})