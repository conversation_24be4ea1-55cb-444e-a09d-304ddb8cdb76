/**
 * Unit tests for Filter Builder Module
 * Part of the finder.js store refactoring - Phase 1
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { FilterBuilder, createFilterBuilder } from '../filter-builder.js'

describe('FilterBuilder', () => {
  let filterBuilder
  let mockConfig

  beforeEach(() => {
    mockConfig = {
      content: {
        filter: {},
        regions: []
      }
    }
    filterBuilder = new FilterBuilder(mockConfig)
  })

  describe('constructor', () => {
    it('should initialize with config', () => {
      expect(filterBuilder.config).toBe(mockConfig)
    })
  })

  describe('buildBrandFilterParams', () => {
    it('should return empty object when no brand filter configured', () => {
      const params = filterBuilder.buildBrandFilterParams()
      expect(params).toEqual({})
    })

    it('should build include brand filter', () => {
      mockConfig.content.filter = {
        by: 'brands',
        brands: ['toyota', 'honda', 'mazda']
      }
      
      const params = filterBuilder.buildBrandFilterParams()
      expect(params).toEqual({
        brands: 'toyota,honda,mazda'
      })
    })

    it('should build exclude brand filter', () => {
      mockConfig.content.filter = {
        by: 'brands_exclude',
        brands_exclude: ['ford', 'chevrolet']
      }
      
      const params = filterBuilder.buildBrandFilterParams()
      expect(params).toEqual({
        brands_exclude: 'ford,chevrolet'
      })
    })

    it('should handle brand objects with slug property', () => {
      mockConfig.content.filter = {
        by: 'brands',
        brands: [
          { slug: 'toyota', name: 'Toyota' },
          { slug: 'honda', name: 'Honda' }
        ]
      }
      
      const params = filterBuilder.buildBrandFilterParams()
      expect(params).toEqual({
        brands: 'toyota,honda'
      })
    })

    it('should handle brand objects with value property', () => {
      mockConfig.content.filter = {
        by: 'brands',
        brands: [
          { value: 'toyota', label: 'Toyota' },
          { value: 'honda', label: 'Honda' }
        ]
      }
      
      const params = filterBuilder.buildBrandFilterParams()
      expect(params).toEqual({
        brands: 'toyota,honda'
      })
    })

    it('should filter out empty values', () => {
      mockConfig.content.filter = {
        by: 'brands',
        brands: ['toyota', '', null, undefined, 'honda']
      }
      
      const params = filterBuilder.buildBrandFilterParams()
      expect(params).toEqual({
        brands: 'toyota,honda'
      })
    })

    it('should handle filter config at root level', () => {
      mockConfig.filter = {
        by: 'brands',
        brands: ['toyota']
      }
      delete mockConfig.content.filter
      
      const params = filterBuilder.buildBrandFilterParams()
      expect(params).toEqual({
        brands: 'toyota'
      })
    })

    it('should handle by config at content level', () => {
      mockConfig.content.by = 'brands'
      mockConfig.content.filter = {
        brands: ['toyota']
      }
      
      const params = filterBuilder.buildBrandFilterParams()
      expect(params).toEqual({
        brands: 'toyota'
      })
    })
  })

  describe('buildRegionParams', () => {
    it('should return empty object when no regions configured', () => {
      const params = filterBuilder.buildRegionParams()
      expect(params).toEqual({})
    })

    it('should build region filter params', () => {
      mockConfig.content.regions = ['us', 'eu', 'jp']
      
      const params = filterBuilder.buildRegionParams()
      expect(params).toEqual({
        region: ['us', 'eu', 'jp']
      })
    })

    it('should handle empty regions array', () => {
      mockConfig.content.regions = []
      
      const params = filterBuilder.buildRegionParams()
      expect(params).toEqual({})
    })
  })

  describe('shouldApplyRegionFilter', () => {
    it('should return true for region-enabled endpoints', () => {
      expect(filterBuilder.shouldApplyRegionFilter('make')).toBe(true)
      expect(filterBuilder.shouldApplyRegionFilter('model')).toBe(true)
      expect(filterBuilder.shouldApplyRegionFilter('year')).toBe(true)
      expect(filterBuilder.shouldApplyRegionFilter('generation')).toBe(true)
      expect(filterBuilder.shouldApplyRegionFilter('modification')).toBe(true)
    })

    it('should return false for search endpoint', () => {
      expect(filterBuilder.shouldApplyRegionFilter('search_by_model')).toBe(false)
    })

    it('should return false for unknown endpoints', () => {
      expect(filterBuilder.shouldApplyRegionFilter('unknown')).toBe(false)
    })
  })

  describe('shouldApplyBrandFilter', () => {
    it('should return true only for make endpoint', () => {
      expect(filterBuilder.shouldApplyBrandFilter('make')).toBe(true)
    })

    it('should return false for all other endpoints', () => {
      expect(filterBuilder.shouldApplyBrandFilter('model')).toBe(false)
      expect(filterBuilder.shouldApplyBrandFilter('year')).toBe(false)
      expect(filterBuilder.shouldApplyBrandFilter('generation')).toBe(false)
      expect(filterBuilder.shouldApplyBrandFilter('modification')).toBe(false)
      expect(filterBuilder.shouldApplyBrandFilter('search_by_model')).toBe(false)
    })
  })

  describe('getFiltersForEndpoint', () => {
    beforeEach(() => {
      mockConfig.content.filter = {
        by: 'brands',
        brands: ['toyota', 'honda']
      }
      mockConfig.content.regions = ['us', 'eu']
    })

    it('should apply both filters for make endpoint', () => {
      const filters = filterBuilder.getFiltersForEndpoint('make')
      expect(filters).toEqual({
        brands: 'toyota,honda',
        region: ['us', 'eu']
      })
    })

    it('should apply only region filter for model endpoint', () => {
      const filters = filterBuilder.getFiltersForEndpoint('model')
      expect(filters).toEqual({
        region: ['us', 'eu']
      })
    })

    it('should apply only region filter for year endpoint', () => {
      const filters = filterBuilder.getFiltersForEndpoint('year')
      expect(filters).toEqual({
        region: ['us', 'eu']
      })
    })

    it('should apply no filters for search endpoint', () => {
      const filters = filterBuilder.getFiltersForEndpoint('search_by_model')
      expect(filters).toEqual({})
    })
  })

  describe('updateConfig', () => {
    it('should update configuration', () => {
      const newConfig = {
        content: {
          filter: { by: 'brands', brands: ['new'] },
          regions: ['new-region']
        }
      }
      
      filterBuilder.updateConfig(newConfig)
      expect(filterBuilder.config).toBe(newConfig)
      
      const params = filterBuilder.buildBrandFilterParams()
      expect(params).toEqual({ brands: 'new' })
    })
  })

  describe('getFilterSummary', () => {
    it('should return no filters when none configured', () => {
      const summary = filterBuilder.getFilterSummary()
      expect(summary).toEqual({
        hasFilters: false,
        brandFilter: null,
        regionFilter: null
      })
    })

    it('should return include brand filter summary', () => {
      mockConfig.content.filter = {
        by: 'brands',
        brands: ['toyota', 'honda']
      }
      
      const summary = filterBuilder.getFilterSummary()
      expect(summary).toEqual({
        hasFilters: true,
        brandFilter: {
          type: 'include',
          brands: ['toyota', 'honda']
        },
        regionFilter: null
      })
    })

    it('should return exclude brand filter summary', () => {
      mockConfig.content.filter = {
        by: 'brands_exclude',
        brands_exclude: ['ford', 'chevrolet']
      }
      
      const summary = filterBuilder.getFilterSummary()
      expect(summary).toEqual({
        hasFilters: true,
        brandFilter: {
          type: 'exclude',
          brands: ['ford', 'chevrolet']
        },
        regionFilter: null
      })
    })

    it('should return region filter summary', () => {
      mockConfig.content.regions = ['us', 'eu']
      
      const summary = filterBuilder.getFilterSummary()
      expect(summary).toEqual({
        hasFilters: true,
        brandFilter: null,
        regionFilter: {
          regions: ['us', 'eu']
        }
      })
    })

    it('should return both filters in summary', () => {
      mockConfig.content.filter = {
        by: 'brands',
        brands: ['toyota']
      }
      mockConfig.content.regions = ['us']
      
      const summary = filterBuilder.getFilterSummary()
      expect(summary).toEqual({
        hasFilters: true,
        brandFilter: {
          type: 'include',
          brands: ['toyota']
        },
        regionFilter: {
          regions: ['us']
        }
      })
    })
  })

  describe('hasActiveFilters', () => {
    it('should return false when no filters configured', () => {
      expect(filterBuilder.hasActiveFilters()).toBe(false)
    })

    it('should return true when brand include filter configured', () => {
      mockConfig.content.filter = {
        by: 'brands',
        brands: ['toyota']
      }
      expect(filterBuilder.hasActiveFilters()).toBe(true)
    })

    it('should return true when brand exclude filter configured', () => {
      mockConfig.content.filter = {
        by: 'brands_exclude',
        brands_exclude: ['ford']
      }
      expect(filterBuilder.hasActiveFilters()).toBe(true)
    })

    it('should return true when region filter configured', () => {
      mockConfig.content.regions = ['us']
      expect(filterBuilder.hasActiveFilters()).toBe(true)
    })

    it('should return false when filters are empty arrays', () => {
      mockConfig.content.filter = {
        by: 'brands',
        brands: []
      }
      mockConfig.content.regions = []
      expect(filterBuilder.hasActiveFilters()).toBe(false)
    })
  })

  describe('factory function', () => {
    it('should create filter builder instance', () => {
      const builder = createFilterBuilder(mockConfig)
      expect(builder).toBeInstanceOf(FilterBuilder)
      expect(builder.config).toBe(mockConfig)
    })
  })
})