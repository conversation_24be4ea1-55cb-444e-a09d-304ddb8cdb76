/**
 * Filter Builder Module
 * Handles building of region and brand filter parameters for API calls
 * Part of the finder.js store refactoring - Phase 1
 */

export class FilterBuilder {
  constructor(config) {
    this.config = config
  }

  /**
   * Build brand filter parameters based on widget configuration
   * @returns {Object} Filter parameters for brand filtering
   */
  buildBrandFilterParams() {
    const filter = this.config?.content?.filter || this.config?.filter || {}
    const by = filter.by || this.config?.content?.by || ''
    const mapToSlug = (item) => {
      if (typeof item === 'string') return item
      return item?.slug || item?.value || ''
    }
    
    const params = {}
    
    if (by === 'brands' && Array.isArray(filter.brands) && filter.brands.length) {
      const list = filter.brands.map(mapToSlug).filter(Boolean)
      if (list.length) {
        params.brands = list.join(',')
      }
    } else if (by === 'brands_exclude' && Array.isArray(filter.brands_exclude) && filter.brands_exclude.length) {
      const list = filter.brands_exclude.map(mapToSlug).filter(Boolean)
      if (list.length) {
        params.brands_exclude = list.join(',')
      }
    }
    
    return params
  }

  /**
   * Build region filter parameters based on widget configuration
   * @returns {Object} Filter parameters for region filtering
   */
  buildRegionParams() {
    const regions = this.config?.content?.regions || []
    return regions.length ? { region: regions } : {}
  }

  /**
   * Check if region filter should be applied to an endpoint
   * @param {string} endpoint - The API endpoint name
   * @returns {boolean} Whether region filter should be applied
   */
  shouldApplyRegionFilter(endpoint) {
    // Region filtering applies to specific endpoints only
    // NOT applied to search_by_model endpoint
    const regionEnabledEndpoints = [
      'make', 
      'model', 
      'year', 
      'generation', 
      'modification'
    ]
    return regionEnabledEndpoints.includes(endpoint)
  }

  /**
   * Check if brand filter should be applied to an endpoint
   * @param {string} endpoint - The API endpoint name
   * @returns {boolean} Whether brand filter should be applied
   */
  shouldApplyBrandFilter(endpoint) {
    // Brand filter only applies to 'make' endpoint
    // Once user selects a make, we don't filter anymore
    return endpoint === 'make'
  }

  /**
   * Get all applicable filters for a specific endpoint
   * @param {string} endpoint - The API endpoint name
   * @returns {Object} Combined filter parameters for the endpoint
   */
  getFiltersForEndpoint(endpoint) {
    const filters = {}
    
    // Apply region filter for appropriate endpoints
    if (this.shouldApplyRegionFilter(endpoint)) {
      Object.assign(filters, this.buildRegionParams())
    }
    
    // Apply brand filter for make endpoint
    if (this.shouldApplyBrandFilter(endpoint)) {
      Object.assign(filters, this.buildBrandFilterParams())
    }
    
    return filters
  }

  /**
   * Update configuration (useful when config changes)
   * @param {Object} newConfig - New configuration object
   */
  updateConfig(newConfig) {
    this.config = newConfig
  }

  /**
   * Get current filter configuration summary
   * @returns {Object} Summary of active filters
   */
  getFilterSummary() {
    const summary = {
      hasFilters: false,
      brandFilter: null,
      regionFilter: null
    }

    // Check brand filter
    const brandParams = this.buildBrandFilterParams()
    if (brandParams.brands) {
      summary.hasFilters = true
      summary.brandFilter = {
        type: 'include',
        brands: brandParams.brands.split(',')
      }
    } else if (brandParams.brands_exclude) {
      summary.hasFilters = true
      summary.brandFilter = {
        type: 'exclude',
        brands: brandParams.brands_exclude.split(',')
      }
    }

    // Check region filter
    const regionParams = this.buildRegionParams()
    if (regionParams.region) {
      summary.hasFilters = true
      summary.regionFilter = {
        regions: regionParams.region
      }
    }

    return summary
  }

  /**
   * Check if any filters are active
   * @returns {boolean} True if any filters are configured
   */
  hasActiveFilters() {
    const brandParams = this.buildBrandFilterParams()
    const regionParams = this.buildRegionParams()
    
    return !!(
      brandParams.brands || 
      brandParams.brands_exclude || 
      regionParams.region
    )
  }
}

/**
 * Factory function to create a filter builder instance
 */
export function createFilterBuilder(config) {
  return new FilterBuilder(config)
}