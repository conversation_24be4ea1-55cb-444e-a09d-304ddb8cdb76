/**
 * Search Executor Module
 * Handles search execution, bot protection, and result processing
 * Part of the finder.js store refactoring - Phase 3
 */

export class SearchExecutor {
  constructor(apiClient, state, widgetEvents, analytics, searchHistory) {
    this.api = apiClient
    this.state = state
    this.widgetEvents = widgetEvents
    this.analytics = analytics
    this.searchHistory = searchHistory
    this.botProtection = null
  }

  /**
   * Initialize bot protection if available
   */
  async initBotProtection() {
    if (this.botProtection) return this.botProtection
    
    try {
      const module = await import('../../composables/useBotProtection')
      this.botProtection = module.useBotProtection()
      return this.botProtection
    } catch (e) {
      // Bot protection not available
      return null
    }
  }

  /**
   * Execute vehicle search with all necessary checks and tracking
   */
  async searchByVehicle() {
    const searchStartTime = performance.now()
    
    // Validate search readiness
    if (!this.canSearch()) {
      this.state.setError('Please select all required fields before searching')
      return null
    }
    
    try {
      this.state.loadingResults.value = true
      this.state.clearError()
      
      // Handle bot protection
      const botProtectionPassed = await this.handleBotProtection()
      if (!botProtectionPassed) {
        return null
      }

      // Build search parameters
      const params = this.buildSearchParams()
      
      // Check for duplicate search
      if (this.isDuplicateSearch(params)) {
        return null
      }

      // Track search start
      this.emitSearchStart(params)
      this.trackSearchInitiation(params)

      // Execute API call
      const apiStartTime = performance.now()
      const response = await this.api.call('search_by_model', params)
      const apiEndTime = performance.now()
      
      // Process results
      const results = this.processSearchResults(response)
      
      // Calculate timing
      const searchEndTime = performance.now()
      const timing = {
        total: Math.round(searchEndTime - searchStartTime),
        api: Math.round(apiEndTime - apiStartTime)
      }
      
      // Track search completion
      this.emitSearchComplete(results, timing)
      this.trackSearchCompletion(params, results, timing)
      
      // Save to history if successful
      if (results.length > 0) {
        this.saveSearchToHistory(params)
      }
      
      // Schedule iframe resize
      this.scheduleIframeResize(results.length)
      
      return results
    } catch (err) {
      this.handleSearchError(err)
      return null
    } finally {
      this.state.loadingResults.value = false
      this.state.activeSearchSignatureInFlight.value = ''
    }
  }

  /**
   * Check if search can be executed
   */
  canSearch() {
    return this.state.canSearch.value
  }

  /**
   * Handle bot protection challenge
   */
  async handleBotProtection() {
    const botProtection = await this.initBotProtection()
    
    if (!botProtection) {
      return true // No bot protection, proceed
    }
    
    if (botProtection.hasValidChallengeToken()) {
      return true // Already have valid token
    }
    
    // Need to solve challenge
    this.state.setError('Verifying you are human... This may take a few seconds.')
    
    const challengeSolved = await botProtection.solveAndVerifyChallenge()
    
    if (!challengeSolved) {
      this.state.setError('Unable to verify. Please ensure JavaScript is enabled and try again.')
      this.state.loadingResults.value = false
      return false
    }
    
    this.state.clearError()
    return true
  }

  /**
   * Build search parameters based on flow type
   */
  buildSearchParams() {
    const params = {
      make: this.state.selectedMake.value,
      model: this.state.selectedModel.value
    }

    const flowType = this.state.flowType.value
    
    if (flowType === 'primary' || flowType === 'year_select') {
      params.year = this.state.selectedYear.value
      if (this.state.selectedModification.value) {
        params.modification = this.state.selectedModification.value
      }
    } else if (flowType === 'alternative') {
      if (this.state.selectedGeneration.value) {
        params.generation = this.state.selectedGeneration.value
      }
      if (this.state.selectedModification.value) {
        params.modification = this.state.selectedModification.value
      }
    }
    
    return params
  }

  /**
   * Check if this is a duplicate search
   */
  isDuplicateSearch(params) {
    const searchSignature = this.generateSearchSignature(params)
    
    if (this.state.activeSearchSignatureInFlight.value === searchSignature) {
      // Duplicate search already in progress
      return true
    }
    
    this.state.activeSearchSignatureInFlight.value = searchSignature
    return false
  }

  /**
   * Generate unique signature for search
   */
  generateSearchSignature(params) {
    return `search_by_model:${JSON.stringify(params)}`
  }

  /**
   * Process search response
   */
  processSearchResults(response) {
    const results = response.data?.data || response.data || []
    this.state.results.value = results
    return results
  }

  /**
   * Emit search start event
   */
  emitSearchStart(params) {
    this.widgetEvents.emit('search:start', {
      search_type: 'by_vehicle',
      parameters: {
        year: params.year || '',
        make: params.make || '',
        model: params.model || '',
        generation: params.generation || '',
        modification: params.modification || ''
      }
    })
  }

  /**
   * Emit search complete event
   */
  emitSearchComplete(results, timing) {
    this.widgetEvents.emit('search:complete', {
      search_type: 'by_vehicle',
      results_count: results.length,
      timing_ms: timing
    })
  }

  /**
   * Track search initiation with analytics
   */
  trackSearchInitiation(params) {
    if (!this.analytics) return
    
    this.analytics.trackSearch('search_initiate', {
      search_type: 'by_vehicle',
      selected_year: params.year || '',
      selected_make: params.make || '',
      selected_model: params.model || '',
      selected_modification: params.modification || '',
      selected_generation: params.generation || ''
    })
  }

  /**
   * Track search completion with analytics
   */
  trackSearchCompletion(params, results, timing) {
    if (!this.analytics) return
    
    this.analytics.trackSearch('search_complete', {
      search_type: 'by_vehicle',
      selected_year: params.year || '',
      selected_make: params.make || '',
      selected_model: params.model || '',
      selected_modification: params.modification || '',
      selected_generation: params.generation || '',
      results_count: results.length,
      search_completion_time: timing.total,
      api_response_time: timing.api
    })
    
    this.analytics.trackEvent('search_results_view', {
      results_count: results.length,
      search_type: 'by_vehicle'
    })
  }

  /**
   * Save successful search to history
   */
  saveSearchToHistory(params) {
    if (!this.searchHistory) return
    
    const searchData = {
      flowType: this.state.flowType.value,
      year: params.year || '',
      make: params.make || '',
      model: params.model || '',
      modification: params.modification || '',
      generation: params.generation || '',
      options: {
        year: this.state.findOption(this.state.years.value, params.year),
        make: this.state.findOption(this.state.makes.value, params.make),
        model: this.state.findOption(this.state.models.value, params.model),
        modification: this.state.findOption(this.state.modifications.value, params.modification),
        generation: this.state.findOption(this.state.generations.value, params.generation)
      }
    }
    
    this.searchHistory.addSearch(searchData)
  }

  /**
   * Schedule iframe resize after results
   */
  scheduleIframeResize(resultCount) {
    setTimeout(async () => {
      if (window.parentIFrame) {
        window.parentIFrame.size()
      }
      
      // Wait for next tick before emitting display event
      await new Promise(resolve => setTimeout(resolve, 0))
      
      this.widgetEvents.emit('results:display', {
        results_count: resultCount
      })
    }, 100)
  }

  /**
   * Handle search error
   */
  handleSearchError(err) {
    const errorMessage = err?.message || String(err)
    this.state.setError(errorMessage)
    
    // Emit error event
    this.widgetEvents.emit('search:error', {
      search_type: 'by_vehicle',
      error_message: errorMessage
    })

    // Track error with analytics
    if (this.analytics) {
      this.analytics.trackError('search_failed', {
        error_message: errorMessage,
        search_type: 'by_vehicle',
        api_endpoint: 'search_by_model',
        selected_year: this.state.selectedYear.value,
        selected_make: this.state.selectedMake.value,
        selected_model: this.state.selectedModel.value,
        widget_state: 'searching'
      })
    }
  }

  /**
   * Get current search parameters
   */
  getCurrentSearchParams() {
    return this.buildSearchParams()
  }

  /**
   * Check if a search is currently in progress
   */
  isSearching() {
    return this.state.loadingResults.value
  }

  /**
   * Cancel current search (clears signature)
   */
  cancelSearch() {
    this.state.activeSearchSignatureInFlight.value = ''
    this.state.loadingResults.value = false
  }
}

/**
 * Factory function to create a search executor instance
 */
export function createSearchExecutor(apiClient, state, widgetEvents, analytics, searchHistory) {
  return new SearchExecutor(apiClient, state, widgetEvents, analytics, searchHistory)
}