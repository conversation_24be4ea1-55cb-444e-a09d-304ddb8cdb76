/**
 * Refactored Finder Store - Phase 3 & 4 Complete Version
 * Final refactored version with all modules integrated
 * Part of the finder.js store refactoring - Phases 3 & 4
 * 
 * This version demonstrates complete modularization with:
 * - Centralized state management (state.js)
 * - Dedicated API client (api-client.js)
 * - Filter building logic (filter-builder.js)
 * - Vehicle loading logic (vehicle-loader.js)
 * - Search execution logic (search-executor.js)
 * - History management (history-manager.js)
 */

import { defineStore } from 'pinia'
import { watch, nextTick } from 'vue'
import { useApiClient } from '../composables/useApiClient.js'
import { createFilterBuilder } from './modules/filter-builder.js'
import { createState } from './modules/state.js'
import { createVehicleLoader } from './modules/vehicle-loader.js'
import { createSearchExecutor } from './modules/search-executor.js'
import { createHistoryManager } from './modules/history-manager.js'
import { useSearchHistory } from '../composables/useSearchHistory.js'
import { useAnalytics } from '../composables/useAnalytics.js'
import { useCrossDomainTracking } from '../composables/useCrossDomainTracking.js'
import { useWidgetEvents } from '../composables/useWidgetEvents.js'

export const useFinderStoreRefactoredV3 = defineStore('finder-refactored-v3', () => {
  // Create state management instance
  const state = createState()
  
  // External composables
  let searchHistory = null
  let analytics = null
  let crossDomainTracking = null
  const widgetEvents = useWidgetEvents()

  // Modular services
  let apiClient = null
  let filterBuilder = null
  let vehicleLoader = null
  let searchExecutor = null
  let historyManager = null

  // Initialize store
  function initialize(widgetConfig) {
    console.log('🚀 Initializing refactored store v3 with config:', widgetConfig)
    
    // Set configuration in state
    state.setConfig(widgetConfig)
    
    // Initialize API client and filter builder
    apiClient = useApiClient(widgetConfig, widgetConfig.widgetResources)
    filterBuilder = createFilterBuilder(widgetConfig)
    
    // Initialize analytics
    analytics = useAnalytics(widgetConfig)
    
    // Create vehicle loader with dependencies
    vehicleLoader = createVehicleLoader(apiClient, state, filterBuilder, analytics)
    
    // Initialize search history with widget ID
    const widgetId = widgetConfig.id || widgetConfig.uuid || widgetConfig.widgetUuid || 'default'
    searchHistory = useSearchHistory(widgetId)
    
    // Configure search history from widget config
    const historyConfig = widgetConfig.search_history || {}
    if (Object.keys(historyConfig).length > 0) {
      searchHistory.configure(historyConfig)
    }
    
    // Initialize cross-domain tracking
    crossDomainTracking = useCrossDomainTracking()

    // Create search executor with all dependencies
    searchExecutor = createSearchExecutor(
      apiClient,
      state,
      widgetEvents,
      analytics,
      searchHistory
    )

    // Create history manager with all dependencies
    historyManager = createHistoryManager(
      state,
      vehicleLoader,
      searchExecutor,
      searchHistory
    )

    // Configure widget events context and config
    widgetEvents.setConfig(widgetConfig)
    widgetEvents.setContextProvider(() => ({
      widgetUuid: widgetId,
      widgetType: 'finder-v2',
      flowType: state.flowType.value,
      selections: {
        year: state.selectedYear.value,
        make: state.selectedMake.value,
        model: state.selectedModel.value,
        generation: state.selectedGeneration.value,
        modification: state.selectedModification.value
      }
    }))
    
    // Load initial data with retry
    function attemptLoadInitialData(attempts = 0) {
      if (state.widgetResources.value && state.widgetResources.value.year) {
        loadInitialData()
      } else if (attempts < 5) {
        setTimeout(() => attemptLoadInitialData(attempts + 1), 100)
      } else {
        state.setError('Failed to initialize widget configuration')
      }
    }
    
    attemptLoadInitialData()
  }

  async function loadInitialData() {
    const success = await vehicleLoader.loadInitialData()
    if (success) {
      widgetEvents.markInitialDataLoaded()
    }
  }

  /**
   * Main search function - delegates to search executor
   */
  async function searchByVehicle() {
    const results = await searchExecutor.searchByVehicle()
    
    if (results && results.length > 0) {
      // Trigger iframe resize and notify parent
      setTimeout(async () => {
        if (window.parentIFrame) {
          window.parentIFrame.size()
        }
        await nextTick()
        widgetEvents.emit('results:display', {
          results_count: results.length
        })
      }, 100)
    }
    
    return results
  }

  /**
   * Execute search from history - delegates to history manager
   */
  async function executeSearchFromHistory(searchId) {
    return await historyManager.executeSearchFromHistory(searchId)
  }

  /**
   * Populate form from search - delegates to history manager
   */
  async function populateFormFromSearch(searchItem) {
    return await historyManager.populateFormFromSearch(searchItem)
  }

  /**
   * Get search history - delegates to history manager
   */
  function getSearchHistory() {
    return historyManager.getSearchHistory()
  }

  // Watchers for widget events
  watch(state.selectedYear, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:year', { 
      value: state.toValueObject(state.years.value, nv) 
    })
  })

  watch(state.selectedMake, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:make', { 
      value: state.toValueObject(state.makes.value, nv) 
    })
  })

  watch(state.selectedModel, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:model', { 
      value: state.toValueObject(state.models.value, nv) 
    })
  })

  watch(state.selectedGeneration, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:generation', { 
      value: state.toValueObject(state.generations.value, nv) 
    })
  })

  watch(state.selectedModification, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:modification', { 
      value: state.toValueObject(state.modifications.value, nv) 
    })
  })

  // Expose filter builder methods for backward compatibility
  function buildBrandFilterParams() {
    return filterBuilder.buildBrandFilterParams()
  }

  function buildRegionParams() {
    return filterBuilder.buildRegionParams()
  }

  return {
    // Spread all state properties and methods
    ...state,
    
    // Core actions
    initialize,
    searchByVehicle,
    
    // Vehicle loading (delegated to loader)
    loadYears: (make, model) => vehicleLoader.loadYears(make, model),
    loadMakes: (year) => vehicleLoader.loadMakes(year),
    loadModels: (make, year) => vehicleLoader.loadModels(make, year),
    loadGenerations: (make, model) => vehicleLoader.loadGenerations(make, model),
    loadModifications: (make, model, yearOrGen) => 
      vehicleLoader.loadModifications(make, model, yearOrGen),
    
    // Filter methods (backward compatibility)
    buildBrandFilterParams,
    buildRegionParams,
    
    // Search History Actions (delegated to history manager)
    executeSearchFromHistory,
    populateFormFromSearch,
    getSearchHistory,
    
    // History Manager convenience methods
    getHistoryItems: () => historyManager.getHistoryItems(),
    getHistoryItem: (searchId) => historyManager.getHistoryItem(searchId),
    clearHistory: () => historyManager.clearHistory(),
    removeHistoryItem: (searchId) => historyManager.removeHistoryItem(searchId),
    hasHistory: () => historyManager.hasHistory(),
    getMostRecentSearch: () => historyManager.getMostRecentSearch(),
    restoreMostRecentSearch: () => historyManager.restoreMostRecentSearch(),
    getSearchCount: () => historyManager.getSearchCount(),
    isCurrentSearch: (searchItem) => historyManager.isCurrentSearch(searchItem),
    
    // Search Executor convenience methods
    canSearch: () => searchExecutor.canSearch(),
    getCurrentSearchParams: () => searchExecutor.getCurrentSearchParams(),
    isSearching: () => searchExecutor.isSearching(),
    cancelSearch: () => searchExecutor.cancelSearch(),
    
    // Analytics and Cross-Domain Tracking
    getAnalytics: () => analytics,
    getCrossDomainTracking: () => crossDomainTracking
  }
})