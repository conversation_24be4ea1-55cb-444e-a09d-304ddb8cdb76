/**
 * Refactored Finder Store - Phase 2 Version
 * Uses State Management and Vehicle Loader modules
 * Part of the finder.js store refactoring - Phase 2
 * 
 * This version demonstrates further modularization with:
 * - Centralized state management
 * - Dedicated vehicle loading logic
 * - Cleaner separation of concerns
 */

import { defineStore } from 'pinia'
import { watch, nextTick } from 'vue'
import { useApiClient } from '../composables/useApiClient.js'
import { createFilterBuilder } from './modules/filter-builder.js'
import { createState } from './modules/state.js'
import { createVehicleLoader } from './modules/vehicle-loader.js'
import { useSearchHistory } from '../composables/useSearchHistory.js'
import { useAnalytics } from '../composables/useAnalytics.js'
import { useCrossDomainTracking } from '../composables/useCrossDomainTracking.js'
import { useWidgetEvents } from '../composables/useWidgetEvents.js'

export const useFinderStoreRefactoredV2 = defineStore('finder-refactored-v2', () => {
  // Create state management instance
  const state = createState()
  
  // Search History
  let searchHistory = null
  
  // Analytics and Cross-Domain Tracking
  let analytics = null
  let crossDomainTracking = null

  // Widget Events
  const widgetEvents = useWidgetEvents()

  // Modular services
  let apiClient = null
  let filterBuilder = null
  let vehicleLoader = null

  // Initialize store
  function initialize(widgetConfig) {
    // Set configuration in state
    state.setConfig(widgetConfig)
    
    // Initialize modular services
    apiClient = useApiClient(widgetConfig, widgetConfig.widgetResources)
    filterBuilder = createFilterBuilder(widgetConfig)
    
    // Initialize analytics
    analytics = useAnalytics(widgetConfig)
    
    // Create vehicle loader with all dependencies
    vehicleLoader = createVehicleLoader(apiClient, state, filterBuilder, analytics)
    
    // Initialize search history with widget ID
    const widgetId = widgetConfig.id || widgetConfig.uuid || widgetConfig.widgetUuid || 'default'
    searchHistory = useSearchHistory(widgetId)
    
    // Configure search history from widget config
    const historyConfig = widgetConfig.search_history || {}
    if (Object.keys(historyConfig).length > 0) {
      searchHistory.configure(historyConfig)
    }
    
    // Initialize cross-domain tracking
    crossDomainTracking = useCrossDomainTracking()

    // Configure widget events context and config
    widgetEvents.setConfig(widgetConfig)
    widgetEvents.setContextProvider(() => ({
      widgetUuid: widgetId,
      widgetType: 'finder-v2',
      flowType: state.flowType.value,
      selections: {
        year: state.selectedYear.value,
        make: state.selectedMake.value,
        model: state.selectedModel.value,
        generation: state.selectedGeneration.value,
        modification: state.selectedModification.value
      }
    }))
    
    // Load initial data with retry
    function attemptLoadInitialData(attempts = 0) {
      if (state.widgetResources.value && state.widgetResources.value.year) {
        loadInitialData()
      } else if (attempts < 5) {
        setTimeout(() => attemptLoadInitialData(attempts + 1), 100)
      } else {
        state.setError('Failed to initialize widget configuration')
      }
    }
    
    attemptLoadInitialData()
  }

  async function loadInitialData() {
    const success = await vehicleLoader.loadInitialData()
    if (success) {
      widgetEvents.markInitialDataLoaded()
    }
  }

  async function searchByVehicle() {
    const searchStartTime = performance.now()
    
    // Check if we can search
    if (!state.canSearch.value) {
      state.setError('Please select all required fields before searching')
      return
    }
    
    // Check if we need a challenge token for search/by_model endpoint
    let botProtection = null
    try {
      const module = await import('../composables/useBotProtection')
      botProtection = module.useBotProtection()
    } catch (e) {
      // Bot protection not available
    }
    
    try {
      state.loadingResults.value = true
      state.clearError()
      
      // Handle bot protection
      if (botProtection && !botProtection.hasValidChallengeToken()) {
        state.setError('Verifying you are human... This may take a few seconds.')
        
        const challengeSolved = await botProtection.solveAndVerifyChallenge()
        
        if (!challengeSolved) {
          state.setError('Unable to verify. Please ensure JavaScript is enabled and try again.')
          state.loadingResults.value = false
          return
        }
        
        state.clearError()
      }

      // Build search parameters
      const params = {
        make: state.selectedMake.value,
        model: state.selectedModel.value
      }

      if (state.flowType.value === 'primary' || state.flowType.value === 'year_select') {
        params.year = state.selectedYear.value
        params.modification = state.selectedModification.value
      } else if (state.flowType.value === 'alternative') {
        params.generation = state.selectedGeneration.value
        params.modification = state.selectedModification.value
      }

      // Prevent duplicate concurrent searches
      const searchSignature = `search_by_model:${JSON.stringify(params)}`
      if (state.activeSearchSignatureInFlight.value === searchSignature) {
        return
      }
      state.activeSearchSignatureInFlight.value = searchSignature

      // Dispatch events: search started
      widgetEvents.emit('search:start', {
        search_type: 'by_vehicle',
        parameters: {
          year: state.selectedYear.value,
          make: state.selectedMake.value,
          model: state.selectedModel.value,
          generation: state.selectedGeneration.value,
          modification: state.selectedModification.value
        }
      })

      // Track search initiation
      if (analytics) {
        analytics.trackSearch('search_initiate', {
          search_type: 'by_vehicle',
          selected_year: state.selectedYear.value,
          selected_make: state.selectedMake.value,
          selected_model: state.selectedModel.value,
          selected_modification: state.selectedModification.value,
          selected_generation: state.selectedGeneration.value
        })
      }

      const apiStartTime = performance.now()
      
      // Use API client for the search (no filters for search endpoint)
      const response = await apiClient.call('search_by_model', params)
      
      const apiEndTime = performance.now()
      
      state.results.value = response.data?.data || response.data || []
      
      const searchEndTime = performance.now()
      const searchDuration = searchEndTime - searchStartTime
      const apiDuration = apiEndTime - apiStartTime

      // Dispatch event: search complete
      widgetEvents.emit('search:complete', {
        search_type: 'by_vehicle',
        results_count: state.results.value.length,
        timing_ms: {
          search_total: Math.round(searchDuration),
          api: Math.round(apiDuration)
        }
      })

      // Track successful search completion
      if (analytics) {
        analytics.trackSearch('search_complete', {
          search_type: 'by_vehicle',
          selected_year: state.selectedYear.value,
          selected_make: state.selectedMake.value,
          selected_model: state.selectedModel.value,
          selected_modification: state.selectedModification.value,
          selected_generation: state.selectedGeneration.value,
          results_count: state.results.value.length,
          search_completion_time: Math.round(searchDuration),
          api_response_time: Math.round(apiDuration)
        })
        
        analytics.trackEvent('search_results_view', {
          results_count: state.results.value.length,
          search_type: 'by_vehicle'
        })
      }

      // Add successful search to history
      if (searchHistory && state.results.value.length > 0) {
        const searchParams = {
          flowType: state.flowType.value,
          year: state.selectedYear.value,
          make: state.selectedMake.value,
          model: state.selectedModel.value,
          modification: state.selectedModification.value,
          generation: state.selectedGeneration.value,
          options: {
            year: state.findOption(state.years.value, state.selectedYear.value),
            make: state.findOption(state.makes.value, state.selectedMake.value),
            model: state.findOption(state.models.value, state.selectedModel.value),
            modification: state.findOption(state.modifications.value, state.selectedModification.value),
            generation: state.findOption(state.generations.value, state.selectedGeneration.value)
          }
        }
        searchHistory.addSearch(searchParams)
      }

      // Trigger iframe resize and notify parent
      setTimeout(async () => {
        if (window.parentIFrame) {
          window.parentIFrame.size()
        }
        await nextTick()
        widgetEvents.emit('results:display', {
          results_count: state.results.value.length
        })
      }, 100)
    } catch (err) {
      state.setError(err.message)
      
      widgetEvents.emit('search:error', {
        search_type: 'by_vehicle',
        error_message: err?.message || String(err)
      })

      if (analytics) {
        analytics.trackError('search_failed', {
          error_message: err.message,
          search_type: 'by_vehicle',
          api_endpoint: 'search_by_model',
          selected_year: state.selectedYear.value,
          selected_make: state.selectedMake.value,
          selected_model: state.selectedModel.value,
          widget_state: 'searching'
        })
      }
    } finally {
      state.loadingResults.value = false
      state.activeSearchSignatureInFlight.value = ''
    }
  }

  // Search history methods
  async function executeSearchFromHistory(searchId) {
    if (!searchHistory) {
      return
    }
    
    const searchItem = searchHistory.getSearch(searchId)
    if (!searchItem) {
      return
    }
    
    try {
      state.clearResults()
      await populateFormFromSearch(searchItem)
      searchHistory.updateSearchTimestamp(searchId)
      await searchByVehicle()
    } catch (error) {
      state.setError('Failed to execute search from history')
    }
  }
  
  async function populateFormFromSearch(searchItem) {
    const params = searchItem.parameters
    const flow = searchItem.flowType || 'primary'
    
    state.resetVehicleSearch()
    
    try {
      if (flow === 'primary') {
        if (params.year) {
          state.selectedYear.value = params.year
          await vehicleLoader.loadMakes(params.year)
        }
        
        if (params.make) {
          state.selectedMake.value = params.make
          await vehicleLoader.loadModels(params.make, params.year)
        }
        
        if (params.model) {
          state.selectedModel.value = params.model
          await vehicleLoader.loadModifications(params.make, params.model, params.year)
        }
        
        if (params.modification) {
          state.selectedModification.value = params.modification
        }
      } else if (flow === 'alternative') {
        if (params.make) {
          state.selectedMake.value = params.make
          await vehicleLoader.loadModels(params.make)
        }
        
        if (params.model) {
          state.selectedModel.value = params.model
          await vehicleLoader.loadGenerations(params.make, params.model)
        }
        
        if (params.generation) {
          state.selectedGeneration.value = params.generation
          await vehicleLoader.loadModifications(params.make, params.model, params.generation)
        }
        
        if (params.modification) {
          state.selectedModification.value = params.modification
        }
      } else if (flow === 'year_select') {
        if (params.make) {
          state.selectedMake.value = params.make
          await vehicleLoader.loadModels(params.make)
        }
        
        if (params.model) {
          state.selectedModel.value = params.model
          await vehicleLoader.loadYears(params.make, params.model)
        }
        
        if (params.year) {
          state.selectedYear.value = params.year
          await vehicleLoader.loadModifications(params.make, params.model, params.year)
        }
        
        if (params.modification) {
          state.selectedModification.value = params.modification
        }
      }
    } catch (error) {
      throw error
    }
  }
  
  function getSearchHistory() {
    return searchHistory
  }

  // Watchers for widget events
  watch(state.selectedYear, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:year', { 
      value: state.toValueObject(state.years.value, nv) 
    })
  })

  watch(state.selectedMake, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:make', { 
      value: state.toValueObject(state.makes.value, nv) 
    })
  })

  watch(state.selectedModel, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:model', { 
      value: state.toValueObject(state.models.value, nv) 
    })
  })

  watch(state.selectedGeneration, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:generation', { 
      value: state.toValueObject(state.generations.value, nv) 
    })
  })

  watch(state.selectedModification, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:modification', { 
      value: state.toValueObject(state.modifications.value, nv) 
    })
  })

  // Expose filter builder methods for backward compatibility
  function buildBrandFilterParams() {
    return filterBuilder.buildBrandFilterParams()
  }

  function buildRegionParams() {
    return filterBuilder.buildRegionParams()
  }

  return {
    // Spread all state properties and methods
    ...state,
    
    // Actions
    initialize,
    searchByVehicle,
    
    // Vehicle loading (delegated to loader)
    loadYears: (make, model) => vehicleLoader.loadYears(make, model),
    loadMakes: (year) => vehicleLoader.loadMakes(year),
    loadModels: (make, year) => vehicleLoader.loadModels(make, year),
    loadGenerations: (make, model) => vehicleLoader.loadGenerations(make, model),
    loadModifications: (make, model, yearOrGen) => 
      vehicleLoader.loadModifications(make, model, yearOrGen),
    
    // Filter methods (backward compatibility)
    buildBrandFilterParams,
    buildRegionParams,
    
    // Search History Actions
    executeSearchFromHistory,
    getSearchHistory,
    
    // Analytics and Cross-Domain Tracking
    getAnalytics: () => analytics,
    getCrossDomainTracking: () => crossDomainTracking
  }
})