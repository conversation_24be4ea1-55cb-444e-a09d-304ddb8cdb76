/**
 * Refactored Finder Store using new modular architecture
 * This is a demonstration of Phase 1 refactoring with API Client and Filter Builder
 * Part of the finder.js store refactoring - Phase 1
 * 
 * To enable this refactored version, set:
 * window.FinderV2Config.useRefactoredStore = true
 */

import { defineStore } from 'pinia'
import { ref, computed, watch, nextTick } from 'vue'
import { useApiClient } from '../composables/useApiClient.js'
import { createFilterBuilder } from './modules/filter-builder.js'
import { useSearchHistory } from '../composables/useSearchHistory.js'
import { useAnalytics } from '../composables/useAnalytics.js'
import { useCrossDomainTracking } from '../composables/useCrossDomainTracking.js'
import { useWidgetEvents } from '../composables/useWidgetEvents.js'

export const useFinderStoreRefactored = defineStore('finder-refactored', () => {
  // State
  const config = ref({})
  const loading = ref(false)
  const loadingResults = ref(false)
  const error = ref(null)
  const results = ref([])
  const outputTemplate = ref('')
  
  // Search History
  let searchHistory = null
  
  // Analytics and Cross-Domain Tracking
  let analytics = null
  let crossDomainTracking = null

  // Widget Events
  const widgetEvents = useWidgetEvents()

  // NEW: Modular services
  let apiClient = null
  let filterBuilder = null

  // Individual loading states for each API call
  const loadingYears = ref(false)
  const loadingMakes = ref(false)
  const loadingModels = ref(false)
  const loadingGenerations = ref(false)
  const loadingModifications = ref(false)

  // State loaded flags for auto-expand behavior
  const stateLoadedYears = ref(false)
  const stateLoadedMakes = ref(false)
  const stateLoadedModels = ref(false)
  const stateLoadedGenerations = ref(false)
  const stateLoadedModifications = ref(false)

  // Track active search to prevent duplicates
  const activeSearchSignatureInFlight = ref('')

  // Vehicle search state
  const selectedYear = ref('')
  const selectedMake = ref('')
  const selectedModel = ref('')
  const selectedModification = ref('')
  const selectedGeneration = ref('')

  // Available options
  const years = ref([])
  const makes = ref([])
  const models = ref([])
  const modifications = ref([])
  const generations = ref([])

  // Getters
  const flowType = computed(() => config.value.flowType || 'primary')
  const apiVersion = computed(() => config.value.apiVersion || 'v2')
  const widgetResources = computed(() => config.value.widgetResources || {})

  // Actions
  function initialize(widgetConfig) {
    config.value = widgetConfig
    outputTemplate.value = widgetConfig.interface?.outputTemplate || ''
    
    // NEW: Initialize modular services
    apiClient = useApiClient(widgetConfig, widgetConfig.widgetResources)
    filterBuilder = createFilterBuilder(widgetConfig)
    
    // Initialize search history with widget ID
    const widgetId = widgetConfig.id || widgetConfig.uuid || widgetConfig.widgetUuid || 'default'
    searchHistory = useSearchHistory(widgetId)
    
    // Configure search history from widget config
    const historyConfig = widgetConfig.search_history || {}
    if (Object.keys(historyConfig).length > 0) {
      searchHistory.configure(historyConfig)
    }
    
    // Initialize analytics tracking
    analytics = useAnalytics(widgetConfig)
    
    // Initialize cross-domain tracking
    crossDomainTracking = useCrossDomainTracking()

    // Configure widget events context and config
    widgetEvents.setConfig(widgetConfig)
    widgetEvents.setContextProvider(() => ({
      widgetUuid: widgetId,
      widgetType: 'finder-v2',
      flowType: flowType.value,
      selections: {
        year: selectedYear.value,
        make: selectedMake.value,
        model: selectedModel.value,
        generation: selectedGeneration.value,
        modification: selectedModification.value
      }
    }))
    
    // Load initial data with retry
    function attemptLoadInitialData(attempts = 0) {
      if (widgetResources.value && widgetResources.value.year) {
        loadInitialData()
      } else if (attempts < 5) {
        setTimeout(() => attemptLoadInitialData(attempts + 1), 100)
      } else {
        error.value = 'Failed to initialize widget configuration'
      }
    }
    
    attemptLoadInitialData()
  }

  async function loadInitialData() {
    try {
      loading.value = true
      error.value = null

      // Load years for primary flow or makes for alternative flow
      if (flowType.value === 'primary') {
        await loadYears()
      } else {
        await loadMakes()
      }
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
      widgetEvents.markInitialDataLoaded()
    }
  }

  // NEW: Refactored load functions using modular services
  async function loadYears(make = null, model = null) {
    try {
      loadingYears.value = true
      stateLoadedYears.value = false

      const params = {}
      if (make) params.make = make
      if (model) params.model = model

      // NEW: Use filter builder for consistent filtering
      Object.assign(params, filterBuilder.getFiltersForEndpoint('year'))

      // NEW: Use API client for deduplication
      const response = await apiClient.call('year', params)
      years.value = response.data?.data || response.data || []

      stateLoadedYears.value = true
      
      // Track successful data load
      if (analytics) {
        analytics.trackEvent('data_load_complete', {
          data_type: 'years',
          data_count: years.value.length,
          make: make || '',
          model: model || ''
        })
      }
    } catch (err) {
      error.value = err.message
      
      if (analytics) {
        analytics.trackError('data_load_failed', {
          data_type: 'years',
          error_message: err.message,
          api_endpoint: 'year',
          make: make || '',
          model: model || ''
        })
      }
    } finally {
      loadingYears.value = false
    }
  }

  async function loadMakes(year = null) {
    try {
      loadingMakes.value = true
      stateLoadedMakes.value = false

      const params = year ? { year } : {}

      // NEW: Use filter builder for consistent filtering
      Object.assign(params, filterBuilder.getFiltersForEndpoint('make'))

      // NEW: Use API client for deduplication
      const response = await apiClient.call('make', params)
      makes.value = response.data?.data || response.data || []

      stateLoadedMakes.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingMakes.value = false
    }
  }

  async function loadModels(make, year = null) {
    try {
      loadingModels.value = true
      stateLoadedModels.value = false

      const params = { make }
      if (year) params.year = year
      
      // NEW: Use filter builder (no brand filter for models)
      Object.assign(params, filterBuilder.getFiltersForEndpoint('model'))
      
      // NEW: Use API client for deduplication
      const response = await apiClient.call('model', params)
      models.value = response.data?.data || response.data || []

      stateLoadedModels.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingModels.value = false
    }
  }

  async function loadModifications(make, model, yearOrGeneration = null) {
    try {
      loadingModifications.value = true
      stateLoadedModifications.value = false

      const params = { make, model }

      // Handle year-based flows vs generation-based flow
      if (yearOrGeneration) {
        if (flowType.value === 'primary' || flowType.value === 'year_select') {
          params.year = yearOrGeneration
        } else if (flowType.value === 'alternative') {
          params.generation = yearOrGeneration
        }
      }

      // NEW: Use filter builder
      Object.assign(params, filterBuilder.getFiltersForEndpoint('modification'))

      // NEW: Use API client for deduplication
      const response = await apiClient.call('modification', params)
      modifications.value = response.data?.data || response.data || []

      stateLoadedModifications.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingModifications.value = false
    }
  }

  async function loadGenerations(make, model) {
    try {
      loadingGenerations.value = true
      stateLoadedGenerations.value = false

      const params = { make, model }
      
      // NEW: Use filter builder
      Object.assign(params, filterBuilder.getFiltersForEndpoint('generation'))
      
      // NEW: Use API client for deduplication
      const response = await apiClient.call('generation', params)
      generations.value = response.data?.data || response.data || []

      stateLoadedGenerations.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingGenerations.value = false
    }
  }

  function clearError() {
    error.value = null
  }

  async function searchByVehicle() {
    const searchStartTime = performance.now()
    
    // Check if we need a challenge token for search/by_model endpoint
    let botProtection = null
    try {
      const module = await import('../composables/useBotProtection')
      botProtection = module.useBotProtection()
    } catch (e) {
      // Bot protection not available
    }
    
    try {
      loadingResults.value = true
      error.value = null
      
      // For search/by_model, ensure we have a valid challenge token
      if (botProtection) {
        if (!botProtection.hasValidChallengeToken()) {
          error.value = 'Verifying you are human... This may take a few seconds.'
          
          const challengeSolved = await botProtection.solveAndVerifyChallenge()
          
          if (!challengeSolved) {
            error.value = 'Unable to verify. Please ensure JavaScript is enabled and try again.'
            loadingResults.value = false
            return
          }
          
          error.value = null
        }
      }

      const params = {
        make: selectedMake.value,
        model: selectedModel.value
      }

      if (flowType.value === 'primary' || flowType.value === 'year_select') {
        params.year = selectedYear.value
        params.modification = selectedModification.value
      } else if (flowType.value === 'alternative') {
        params.generation = selectedGeneration.value
        params.modification = selectedModification.value
      }

      // Prevent duplicate concurrent searches
      const searchSignature = `search_by_model:${JSON.stringify(params)}`
      if (activeSearchSignatureInFlight.value === searchSignature) {
        return
      }
      activeSearchSignatureInFlight.value = searchSignature

      // Dispatch events: search started
      widgetEvents.emit('search:start', {
        search_type: 'by_vehicle',
        parameters: {
          year: selectedYear.value,
          make: selectedMake.value,
          model: selectedModel.value,
          generation: selectedGeneration.value,
          modification: selectedModification.value
        }
      })

      // Track search initiation
      if (analytics) {
        analytics.trackSearch('search_initiate', {
          search_type: 'by_vehicle',
          selected_year: selectedYear.value,
          selected_make: selectedMake.value,
          selected_model: selectedModel.value,
          selected_modification: selectedModification.value,
          selected_generation: selectedGeneration.value
        })
      }

      const apiStartTime = performance.now()
      
      // NEW: Use API client for the search (no filters for search endpoint)
      const response = await apiClient.call('search_by_model', params)
      
      const apiEndTime = performance.now()
      
      results.value = response.data?.data || response.data || []
      
      const searchEndTime = performance.now()
      const searchDuration = searchEndTime - searchStartTime
      const apiDuration = apiEndTime - apiStartTime

      // Dispatch event: search complete
      widgetEvents.emit('search:complete', {
        search_type: 'by_vehicle',
        results_count: results.value.length,
        timing_ms: {
          search_total: Math.round(searchDuration),
          api: Math.round(apiDuration)
        }
      })

      // Track successful search completion
      if (analytics) {
        analytics.trackSearch('search_complete', {
          search_type: 'by_vehicle',
          selected_year: selectedYear.value,
          selected_make: selectedMake.value,
          selected_model: selectedModel.value,
          selected_modification: selectedModification.value,
          selected_generation: selectedGeneration.value,
          results_count: results.value.length,
          search_completion_time: Math.round(searchDuration),
          api_response_time: Math.round(apiDuration)
        })
        
        analytics.trackEvent('search_results_view', {
          results_count: results.value.length,
          search_type: 'by_vehicle'
        })
      }

      // Add successful search to history
      if (searchHistory && results.value.length > 0) {
        const selectedYearOption = years.value.find(opt => opt.slug === selectedYear.value || opt.id === selectedYear.value)
        const selectedMakeOption = makes.value.find(opt => opt.slug === selectedMake.value || opt.id === selectedMake.value)
        const selectedModelOption = models.value.find(opt => opt.slug === selectedModel.value || opt.id === selectedModel.value)
        const selectedModificationOption = modifications.value.find(opt => opt.slug === selectedModification.value || opt.id === selectedModification.value)
        const selectedGenerationOption = generations.value.find(opt => opt.slug === selectedGeneration.value || opt.id === selectedGeneration.value)
        
        const searchParams = {
          flowType: flowType.value,
          year: selectedYear.value,
          make: selectedMake.value,
          model: selectedModel.value,
          modification: selectedModification.value,
          generation: selectedGeneration.value,
          options: {
            year: selectedYearOption,
            make: selectedMakeOption,
            model: selectedModelOption,
            modification: selectedModificationOption,
            generation: selectedGenerationOption
          }
        }
        searchHistory.addSearch(searchParams)
      }

      // Trigger iframe resize and notify parent that results are displayed
      setTimeout(async () => {
        if (window.parentIFrame) {
          window.parentIFrame.size()
        }
        await nextTick()
        widgetEvents.emit('results:display', {
          results_count: results.value.length
        })
      }, 100)
    } catch (err) {
      error.value = err.message
      
      widgetEvents.emit('search:error', {
        search_type: 'by_vehicle',
        error_message: err?.message || String(err)
      })

      if (analytics) {
        analytics.trackError('search_failed', {
          error_message: err.message,
          search_type: 'by_vehicle',
          api_endpoint: 'search_by_model',
          selected_year: selectedYear.value,
          selected_make: selectedMake.value,
          selected_model: selectedModel.value,
          widget_state: 'searching'
        })
      }
    } finally {
      loadingResults.value = false
      activeSearchSignatureInFlight.value = ''
    }
  }

  function resetVehicleSearch() {
    selectedYear.value = ''
    selectedMake.value = ''
    selectedModel.value = ''
    selectedModification.value = ''
    selectedGeneration.value = ''
    models.value = []
    modifications.value = []
    generations.value = []
  }

  function clearResults() {
    results.value = []

    setTimeout(() => {
      if (window.parentIFrame) {
        window.parentIFrame.size()
      }
    }, 50)
  }

  // Search history methods (same as original)
  async function executeSearchFromHistory(searchId) {
    if (!searchHistory) {
      return
    }
    
    const searchItem = searchHistory.getSearch(searchId)
    if (!searchItem) {
      return
    }
    
    try {
      clearResults()
      await populateFormFromSearch(searchItem)
      searchHistory.updateSearchTimestamp(searchId)
      await searchByVehicle()
    } catch (error) {
      error.value = 'Failed to execute search from history'
    }
  }
  
  async function populateFormFromSearch(searchItem) {
    const params = searchItem.parameters
    const flow = searchItem.flowType || 'primary'
    
    resetVehicleSearch()
    
    try {
      if (flow === 'primary') {
        if (params.year) {
          selectedYear.value = params.year
          await loadMakes(params.year)
        }
        
        if (params.make) {
          selectedMake.value = params.make
          await loadModels(params.make, params.year)
        }
        
        if (params.model) {
          selectedModel.value = params.model
          await loadModifications(params.make, params.model, params.year)
        }
        
        if (params.modification) {
          selectedModification.value = params.modification
        }
      } else if (flow === 'alternative') {
        if (params.make) {
          selectedMake.value = params.make
          await loadModels(params.make)
        }
        
        if (params.model) {
          selectedModel.value = params.model
          await loadGenerations(params.make, params.model)
        }
        
        if (params.generation) {
          selectedGeneration.value = params.generation
          await loadModifications(params.make, params.model, params.generation)
        }
        
        if (params.modification) {
          selectedModification.value = params.modification
        }
      } else if (flow === 'year_select') {
        if (params.make) {
          selectedMake.value = params.make
          await loadModels(params.make)
        }
        
        if (params.model) {
          selectedModel.value = params.model
          await loadYears(params.make, params.model)
        }
        
        if (params.year) {
          selectedYear.value = params.year
          await loadModifications(params.make, params.model, params.year)
        }
        
        if (params.modification) {
          selectedModification.value = params.modification
        }
      }
    } catch (error) {
      throw error
    }
  }
  
  function getSearchHistory() {
    return searchHistory
  }

  // Helper function for watchers
  function toValueObject(optionList, selectedValue) {
    if (!selectedValue) return null
    const match = optionList.value?.find?.(opt => opt?.slug === selectedValue || opt?.id === selectedValue) || null
    const slug = match?.slug ?? (typeof selectedValue === 'string' ? selectedValue : String(selectedValue))
    const title = match?.title ?? match?.name ?? match?.value ?? String(slug)
    return { slug, title }
  }

  // Watchers for widget events
  watch(selectedYear, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:year', { value: toValueObject(years, nv) })
  })

  watch(selectedMake, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:make', { value: toValueObject(makes, nv) })
  })

  watch(selectedModel, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:model', { value: toValueObject(models, nv) })
  })

  watch(selectedGeneration, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:generation', { value: toValueObject(generations, nv) })
  })

  watch(selectedModification, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:modification', { value: toValueObject(modifications, nv) })
  })

  // NEW: Expose filter builder methods for backward compatibility
  function buildBrandFilterParams() {
    return filterBuilder.buildBrandFilterParams()
  }

  function buildRegionParams() {
    return filterBuilder.buildRegionParams()
  }

  return {
    // State
    config,
    loading,
    loadingResults,
    error,
    results,
    outputTemplate,

    // Individual loading states
    loadingYears,
    loadingMakes,
    loadingModels,
    loadingGenerations,
    loadingModifications,

    // State loaded flags
    stateLoadedYears,
    stateLoadedMakes,
    stateLoadedModels,
    stateLoadedGenerations,
    stateLoadedModifications,

    // Vehicle search (only supported search type)
    selectedYear,
    selectedMake,
    selectedModel,
    selectedModification,
    selectedGeneration,
    years,
    makes,
    models,
    modifications,
    generations,

    // Getters
    flowType,
    apiVersion,
    widgetResources,

    // Actions
    initialize,
    loadYears,
    loadMakes,
    loadModels,
    loadModifications,
    loadGenerations,
    searchByVehicle,
    resetVehicleSearch,
    clearResults,
    clearError,
    buildBrandFilterParams,
    buildRegionParams,
    
    // Search History Actions
    executeSearchFromHistory,
    getSearchHistory,
    
    // Analytics and Cross-Domain Tracking
    getAnalytics: () => analytics,
    getCrossDomainTracking: () => crossDomainTracking
  }
})