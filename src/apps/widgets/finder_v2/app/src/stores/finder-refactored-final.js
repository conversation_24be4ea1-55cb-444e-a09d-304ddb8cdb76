/**
 * Fully Refactored Finder Store - Final Version
 * Uses all modular components for complete separation of concerns
 * Part of the finder.js store refactoring - Phase 3 & 4
 * 
 * This is the final refactored version with all modules:
 * - API Client (Phase 1)
 * - Filter Builder (Phase 1)
 * - State Management (Phase 2)
 * - Vehicle Loader (Phase 2)
 * - Search Executor (Phase 3)
 * - History Manager (Phase 4)
 */

import { defineStore } from 'pinia'
import { watch } from 'vue'
import { useApiClient } from '../composables/useApiClient.js'
import { createFilterBuilder } from './modules/filter-builder.js'
import { createState } from './modules/state.js'
import { createVehicleLoader } from './modules/vehicle-loader.js'
import { createSearchExecutor } from './modules/search-executor.js'
import { createHistoryManager } from './modules/history-manager.js'
import { useSearchHistory } from '../composables/useSearchHistory.js'
import { useAnalytics } from '../composables/useAnalytics.js'
import { useCrossDomainTracking } from '../composables/useCrossDomainTracking.js'
import { useWidgetEvents } from '../composables/useWidgetEvents.js'

export const useFinderStoreRefactoredFinal = defineStore('finder-refactored-final', () => {
  // Create state management instance
  const state = createState()
  
  // External services
  let searchHistory = null
  let analytics = null
  let crossDomainTracking = null
  const widgetEvents = useWidgetEvents()

  // Modular services
  let apiClient = null
  let filterBuilder = null
  let vehicleLoader = null
  let searchExecutor = null
  let historyManager = null
  
  // Track initialization status
  let isInitialized = false
  
  // Create a stub analytics object to prevent errors before initialization
  const stubAnalytics = {
    trackEvent: () => {},
    trackInteraction: () => {},
    trackError: () => {},
    track: () => {}
  }

  /**
   * Initialize the store with widget configuration
   */
  function initialize(widgetConfig) {
    console.log('🔍 FINAL Store Initialize - widgetConfig:', widgetConfig)
    console.log('🔍 FINAL Store Initialize - widgetResources:', widgetConfig.widgetResources)
    
    try {
      // Set configuration in state
      state.setConfig(widgetConfig)
      console.log('🔍 FINAL Store - state.setConfig completed')
      console.log('🔍 FINAL Store - widgetConfig.widgetResources:', widgetConfig.widgetResources)
      console.log('🔍 FINAL Store - state.widgetResources.value:', state.widgetResources.value)
    
      // Initialize API client and filter builder
      // Use widgetResources directly from config since computed might not be ready yet
      const resources = widgetConfig.widgetResources || state.widgetResources.value || {}
      console.log('🔍 FINAL Store - Using resources for API client:', resources)
      apiClient = useApiClient(widgetConfig, resources)
      console.log('🔍 FINAL Store - apiClient created with resources:', resources)
      
      filterBuilder = createFilterBuilder(widgetConfig)
      console.log('🔍 FINAL Store - filterBuilder created')
      
      // Initialize analytics
      analytics = useAnalytics(widgetConfig)
      console.log('🔍 FINAL Store - analytics created')
      
      // Create vehicle loader
      vehicleLoader = createVehicleLoader(apiClient, state, filterBuilder, analytics)
      console.log('🔍 FINAL Store - vehicleLoader created')
    
      // Initialize search history
      const widgetId = widgetConfig.id || widgetConfig.uuid || widgetConfig.widgetUuid || 'default'
      searchHistory = useSearchHistory(widgetId)
      console.log('🔍 FINAL Store - searchHistory created')
      
      // Configure search history
      const historyConfig = widgetConfig.search_history || {}
      if (Object.keys(historyConfig).length > 0) {
        searchHistory.configure(historyConfig)
      }
      
      // Initialize cross-domain tracking
      crossDomainTracking = useCrossDomainTracking()
      console.log('🔍 FINAL Store - crossDomainTracking created')
      
      // Create search executor with all dependencies
      searchExecutor = createSearchExecutor(
        apiClient,
        state,
        widgetEvents,
        analytics,
        searchHistory
      )
      console.log('🔍 FINAL Store - searchExecutor created')
      
      // Create history manager
      historyManager = createHistoryManager(
        state,
        vehicleLoader,
        searchExecutor,
        searchHistory
      )
      console.log('🔍 FINAL Store - historyManager created')
      
      // Configure widget events
      widgetEvents.setConfig(widgetConfig)
      widgetEvents.setContextProvider(() => ({
        widgetUuid: widgetId,
        widgetType: 'finder-v2',
        flowType: state.flowType.value,
        selections: {
          year: state.selectedYear.value,
          make: state.selectedMake.value,
          model: state.selectedModel.value,
          generation: state.selectedGeneration.value,
          modification: state.selectedModification.value
        }
      }))
      console.log('🔍 FINAL Store - widgetEvents configured')
      
      // Setup watchers for widget events
      setupWatchers()
      console.log('🔍 FINAL Store - watchers setup completed')
      
      // Load initial data with retry
      attemptLoadInitialData()
      console.log('🔍 FINAL Store - attemptLoadInitialData called')
      
      // Mark as initialized
      isInitialized = true
      
    } catch (error) {
      console.error('🔍 FINAL Store - Initialize error:', error, error.stack)
      state.setError(error.message || 'Failed to initialize store')
      isInitialized = false
    }
  }

  /**
   * Attempt to load initial data with retries
   */
  function attemptLoadInitialData(attempts = 0) {
    console.log('🔍 FINAL Store - attemptLoadInitialData, attempt:', attempts)
    console.log('🔍 FINAL Store - state.widgetResources.value:', state.widgetResources.value)
    
    // Check if widget resources are properly loaded
    const resources = state.widgetResources.value
    const hasResources = resources && Object.keys(resources).length > 0 && resources.year
    
    console.log('🔍 FINAL Store - hasResources:', hasResources)
    
    if (hasResources) {
      console.log('🔍 FINAL Store - Calling loadInitialData')
      loadInitialData()
    } else if (attempts < 5) {
      console.log('🔍 FINAL Store - Retrying in 100ms')
      setTimeout(() => attemptLoadInitialData(attempts + 1), 100)
    } else {
      console.error('🔍 FINAL Store - Failed to initialize after 5 attempts')
      state.setError('Failed to initialize widget configuration')
    }
  }

  /**
   * Load initial data based on flow type
   */
  async function loadInitialData() {
    console.log('🔍 FINAL Store - loadInitialData called')
    try {
      const success = await vehicleLoader.loadInitialData()
      console.log('🔍 FINAL Store - loadInitialData success:', success)
      if (success) {
        widgetEvents.markInitialDataLoaded()
        console.log('🔍 FINAL Store - markInitialDataLoaded called')
      }
    } catch (error) {
      console.error('🔍 FINAL Store - loadInitialData error:', error)
      state.setError(error.message || 'Failed to load initial data')
    }
  }

  /**
   * Setup watchers for widget events
   */
  function setupWatchers() {
    try {
      watch(state.selectedYear, (nv, ov) => {
        if (nv === ov) return
        widgetEvents.emit('change:year', { 
          value: state.toValueObject(state.years.value, nv) 
        })
      })

      watch(state.selectedMake, (nv, ov) => {
        if (nv === ov) return
        widgetEvents.emit('change:make', { 
          value: state.toValueObject(state.makes.value, nv) 
        })
      })

      watch(state.selectedModel, (nv, ov) => {
        if (nv === ov) return
        widgetEvents.emit('change:model', { 
          value: state.toValueObject(state.models.value, nv) 
        })
      })

      watch(state.selectedGeneration, (nv, ov) => {
        if (nv === ov) return
        widgetEvents.emit('change:generation', { 
          value: state.toValueObject(state.generations.value, nv) 
        })
      })

      watch(state.selectedModification, (nv, ov) => {
        if (nv === ov) return
        widgetEvents.emit('change:modification', { 
          value: state.toValueObject(state.modifications.value, nv) 
        })
      })
    } catch (error) {
      console.error('🔍 FINAL Store - setupWatchers error:', error)
    }
  }

  /**
   * Execute vehicle search (delegated to search executor)
   */
  async function searchByVehicle() {
    if (!searchExecutor) {
      console.warn('searchByVehicle called before initialization')
      return null
    }
    return await searchExecutor.searchByVehicle()
  }

  /**
   * Execute search from history (delegated to history manager)
   */
  async function executeSearchFromHistory(searchId) {
    if (!historyManager) {
      console.warn('executeSearchFromHistory called before initialization')
      return false
    }
    return await historyManager.executeSearchFromHistory(searchId)
  }

  /**
   * Get search history (delegated to history manager)
   */
  function getSearchHistory() {
    if (!historyManager) {
      console.warn('getSearchHistory called before initialization')
      return null
    }
    return historyManager.getSearchHistory()
  }

  /**
   * Backward compatibility methods
   */
  function buildBrandFilterParams() {
    if (!filterBuilder) {
      console.warn('buildBrandFilterParams called before initialization')
      return {}
    }
    return filterBuilder.buildBrandFilterParams()
  }

  function buildRegionParams() {
    if (!filterBuilder) {
      console.warn('buildRegionParams called before initialization')
      return {}
    }
    return filterBuilder.buildRegionParams()
  }

  // Return public API
  return {
    // ===== State Properties =====
    // Spread all state properties and computed values
    ...state,
    
    // ===== Core Actions =====
    initialize,
    loadInitialData,
    searchByVehicle,
    clearError: () => state.clearError(),
    resetAll: () => state.resetAll(),
    
    // ===== Vehicle Loading =====
    // Delegate to vehicle loader with safety checks
    loadYears: (make, model) => {
      if (!vehicleLoader) {
        console.warn('loadYears called before initialization')
        return Promise.resolve()
      }
      return vehicleLoader.loadYears(make, model)
    },
    loadMakes: (year) => {
      if (!vehicleLoader) {
        console.warn('loadMakes called before initialization')
        return Promise.resolve()
      }
      return vehicleLoader.loadMakes(year)
    },
    loadModels: (make, year) => {
      if (!vehicleLoader) {
        console.warn('loadModels called before initialization')
        return Promise.resolve()
      }
      return vehicleLoader.loadModels(make, year)
    },
    loadGenerations: (make, model) => {
      if (!vehicleLoader) {
        console.warn('loadGenerations called before initialization')
        return Promise.resolve()
      }
      return vehicleLoader.loadGenerations(make, model)
    },
    loadModifications: (make, model, yearOrGen) => {
      if (!vehicleLoader) {
        console.warn('loadModifications called before initialization')
        return Promise.resolve()
      }
      return vehicleLoader.loadModifications(make, model, yearOrGen)
    },
    
    // ===== Search History =====
    // Delegate to history manager with safety checks
    executeSearchFromHistory,
    getSearchHistory,
    getHistoryItems: () => historyManager ? historyManager.getHistoryItems() : [],
    clearHistory: () => historyManager ? historyManager.clearHistory() : null,
    hasHistory: () => historyManager ? historyManager.hasHistory() : false,
    getMostRecentSearch: () => historyManager ? historyManager.getMostRecentSearch() : null,
    restoreMostRecentSearch: () => historyManager ? historyManager.restoreMostRecentSearch() : null,
    
    // ===== Filter Methods =====
    // Backward compatibility with safety checks
    buildBrandFilterParams,
    buildRegionParams,
    getFilterSummary: () => filterBuilder ? filterBuilder.getFilterSummary() : '',
    hasActiveFilters: () => filterBuilder ? filterBuilder.hasActiveFilters() : false,
    
    // ===== Search Status =====
    // Delegate to search executor
    isSearching: () => searchExecutor ? searchExecutor.isSearching() : false,
    getCurrentSearchParams: () => searchExecutor ? searchExecutor.getCurrentSearchParams() : null,
    getLastSearchParams: () => searchExecutor ? searchExecutor.getCurrentSearchParams() : null, // Alias for compatibility
    cancelSearch: () => searchExecutor ? searchExecutor.cancelSearch() : null,
    search: (params) => {
      if (!searchExecutor) {
        console.warn('search called before initialization')
        return Promise.resolve(null)
      }
      return searchExecutor.searchByVehicle()
    },
    
    // ===== Vehicle Loading Status =====
    // Delegate to vehicle loader with safety checks
    isLoadingData: () => vehicleLoader ? vehicleLoader.isLoading() : false,
    getLoadingStatus: () => vehicleLoader ? vehicleLoader.getLoadingStatus() : {},
    getLoadedStatus: () => vehicleLoader ? vehicleLoader.getLoadedStatus() : {},
    
    // ===== External Services =====
    getAnalytics: () => analytics || stubAnalytics,
    getCrossDomainTracking: () => crossDomainTracking,
    getWidgetEvents: () => widgetEvents,
    
    // ===== Module Access (for debugging) =====
    _modules: {
      apiClient: () => apiClient,
      filterBuilder: () => filterBuilder,
      vehicleLoader: () => vehicleLoader,
      searchExecutor: () => searchExecutor,
      historyManager: () => historyManager,
      state: () => state
    }
  }
})