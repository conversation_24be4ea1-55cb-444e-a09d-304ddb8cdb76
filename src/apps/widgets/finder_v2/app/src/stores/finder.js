import { defineStore } from 'pinia'
import { ref, computed, watch, nextTick } from 'vue'
import axios from 'axios'
import { useSearchHistory } from '../composables/useSearchHistory.js'
import { useAnalytics } from '../composables/useAnalytics.js'
import { useCrossDomainTracking } from '../composables/useCrossDomainTracking.js'
import { useWidgetEvents } from '../composables/useWidgetEvents.js'

// Ensure arrays (e.g. region) are serialised as repeated keys, not region[]=
axios.defaults.paramsSerializer = (params) => {
  const search = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((v) => {
        if (v !== undefined && v !== null && v !== '') {
          search.append(key, v)
        }
      })
    } else if (value !== undefined && value !== null && value !== '') {
      search.append(key, value)
    }
  })
  return search.toString()
}

export const useFinderStore = defineStore('finder', () => {
  // State
  const config = ref({})
  const loading = ref(false)
  const loadingResults = ref(false) // Separate loading state for search results
  const error = ref(null)
  const results = ref([])
  const outputTemplate = ref('')
  
  // Search History
  let searchHistory = null
  
  // Analytics and Cross-Domain Tracking
  let analytics = null
  let crossDomainTracking = null

  // Widget Events
  const widgetEvents = useWidgetEvents()

  // Individual loading states for each API call
  const loadingYears = ref(false)
  const loadingMakes = ref(false)
  const loadingModels = ref(false)
  const loadingGenerations = ref(false)
  const loadingModifications = ref(false)

  // State loaded flags for auto-expand behavior
  const stateLoadedYears = ref(false)
  const stateLoadedMakes = ref(false)
  const stateLoadedModels = ref(false)
  const stateLoadedGenerations = ref(false)
  const stateLoadedModifications = ref(false)

  // Request deduplication - track ongoing requests
  const ongoingRequests = ref(new Map())
  // Deduplicate concurrent identical searches to prevent double events
  const activeSearchSignatureInFlight = ref('')

  // Vehicle search state
  const selectedYear = ref('')
  const selectedMake = ref('')
  const selectedModel = ref('')
  const selectedModification = ref('')
  const selectedGeneration = ref('')

  // Available options
  const years = ref([])
  const makes = ref([])
  const models = ref([])
  const modifications = ref([])
  const generations = ref([])

  // Note: Tire and rim search functionality removed
  // Finder-v2 only supports vehicle search (by_vehicle tab)

  // Getters
  const flowType = computed(() => config.value.flowType || 'primary')
  const apiVersion = computed(() => config.value.apiVersion || 'v2')
  const widgetResources = computed(() => config.value.widgetResources || {})

  // Actions
  function initialize(widgetConfig) {
    config.value = widgetConfig
    outputTemplate.value = widgetConfig.interface?.outputTemplate || ''
    
    // Initialize search history with widget ID
    // console.log('Widget config debug:', widgetConfig)
    const widgetId = widgetConfig.id || widgetConfig.uuid || widgetConfig.widgetUuid || 'default'
    // console.log('Using widget ID for search history:', widgetId)
    searchHistory = useSearchHistory(widgetId)
    
    // Configure search history from widget config
    const historyConfig = widgetConfig.search_history || {}
    if (Object.keys(historyConfig).length > 0) {
      searchHistory.configure(historyConfig)
    }
    
    // Initialize analytics tracking
    analytics = useAnalytics(widgetConfig)
    // console.log('Analytics initialized for widget:', widgetId)
    
    // Initialize cross-domain tracking
    crossDomainTracking = useCrossDomainTracking()
    // console.log('Cross-domain tracking initialized')

    // Configure widget events context and config
    widgetEvents.setConfig(widgetConfig)
    widgetEvents.setContextProvider(() => ({
      widgetUuid: widgetId,
      widgetType: 'finder-v2',
      flowType: flowType.value,
      selections: {
        year: selectedYear.value,
        make: selectedMake.value,
        model: selectedModel.value,
        generation: selectedGeneration.value,
        modification: selectedModification.value
      }
    }))
    
    // Add configuration validation with retry
    function attemptLoadInitialData(attempts = 0) {
      // console.log(`Attempt ${attempts + 1}: Checking widget resources...`)
      // console.log('config.value:', config.value)
      // console.log('widgetResources.value:', widgetResources.value)
      
      if (widgetResources.value && widgetResources.value.year) {
        // console.log('Widget resources loaded successfully, calling loadInitialData()')
        loadInitialData()
      } else if (attempts < 5) {
        // console.warn(`Widget resources not ready (attempt ${attempts + 1}), retrying in 100ms`)
        // console.warn('Missing widgetResources or widgetResources.year')
        setTimeout(() => attemptLoadInitialData(attempts + 1), 100)
      } else {
        // console.error('Widget resources failed to load after retries')
        // console.error('Final config.value:', config.value)
        // console.error('Final widgetResources.value:', widgetResources.value)
        error.value = 'Failed to initialize widget configuration'
      }
    }
    
    attemptLoadInitialData()
  }

  async function loadInitialData() {
    try {
      loading.value = true
      error.value = null

      // Load years for primary flow or makes for alternative flow
      if (flowType.value === 'primary') {
        await loadYears()
      } else {
        await loadMakes()
      }

      // Note: Finder-v2 only supports vehicle search (by_vehicle tab)
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
      // Inform parent that initial data is ready for interaction
      widgetEvents.markInitialDataLoaded()
    }
  }

  async function loadYears(make = null, model = null) {
    try {
      loadingYears.value = true
      stateLoadedYears.value = false

      const params = {}
      if (make) params.make = make
      if (model) params.model = model

      const response = await apiCallWithDeduplication('year', params)
      // API response is wrapped in data property: { data: [...] }
      years.value = response.data?.data || response.data || []

      stateLoadedYears.value = true
      
      // Track successful data load
      if (analytics) {
        analytics.trackEvent('data_load_complete', {
          data_type: 'years',
          data_count: years.value.length,
          make: make || '',
          model: model || ''
        })
      }
    } catch (err) {
      error.value = err.message
      
      // Track data load error
      if (analytics) {
        analytics.trackError('data_load_failed', {
          data_type: 'years',
          error_message: err.message,
          api_endpoint: 'year',
          make: make || '',
          model: model || ''
        })
      }
    } finally {
      loadingYears.value = false
    }
  }

  async function loadMakes(year = null) {
    try {
      loadingMakes.value = true
      stateLoadedMakes.value = false

      const params = year ? { year } : {}

      // NEW: Apply brand filter from widget configuration
      Object.assign(params, buildBrandFilterParams())

      const response = await apiCallWithDeduplication('make', params)
      // API response is wrapped in data property: { data: [...] }
      makes.value = response.data?.data || response.data || []

      stateLoadedMakes.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingMakes.value = false
    }
  }

  async function loadModels(make, year = null) {
    try {
      loadingModels.value = true
      stateLoadedModels.value = false

      const params = { make }
      if (year) params.year = year
      // NOTE: Do not include brand filter here - user already selected a specific make
      // Object.assign(params, buildBrandFilterParams())
      const response = await apiCallWithDeduplication('model', params)
      // API response is wrapped in data property: { data: [...] }
      models.value = response.data?.data || response.data || []

      stateLoadedModels.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingModels.value = false
    }
  }

  async function loadModifications(make, model, yearOrGeneration = null) {
    try {
      loadingModifications.value = true
      stateLoadedModifications.value = false

      const params = { make, model }

      // Handle year-based flows (primary, year_select) vs generation-based flow (alternative)
      if (yearOrGeneration) {
        if (flowType.value === 'primary' || flowType.value === 'year_select') {
          params.year = yearOrGeneration
        } else if (flowType.value === 'alternative') {
          params.generation = yearOrGeneration
        }
      }

      const response = await apiCallWithDeduplication('modification', params)
      // API response is wrapped in data property: { data: [...] }
      modifications.value = response.data?.data || response.data || []

      stateLoadedModifications.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingModifications.value = false
    }
  }

  async function loadGenerations(make, model) {
    try {
      loadingGenerations.value = true
      stateLoadedGenerations.value = false

      const params = { make, model }
      const response = await apiCallWithDeduplication('generation', params)
      // API response is wrapped in data property: { data: [...] }
      generations.value = response.data?.data || response.data || []

      stateLoadedGenerations.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingGenerations.value = false
    }
  }

  // Tire and rim search functions removed - not used in finder-v2

  function clearError() {
    error.value = null
  }

  async function searchByVehicle() {
    const searchStartTime = performance.now()
    
    // Check if we need a challenge token for search/by_model endpoint
    // Import bot protection if available
    let botProtection = null
    try {
      const module = await import('../composables/useBotProtection')
      botProtection = module.useBotProtection()
    } catch (e) {
      // console.debug('Bot protection not available')
    }
    
    try {
      loadingResults.value = true
      error.value = null
      
      // For search/by_model, ensure we have a valid challenge token
      if (botProtection) {
        if (!botProtection.hasValidChallengeToken()) {
          // console.log('Challenge token required for search. Solving challenge...')
          
          // Show user feedback that we're solving a challenge
          error.value = 'Verifying you are human... This may take a few seconds.'
          
          const challengeSolved = await botProtection.solveAndVerifyChallenge()
          
          if (!challengeSolved) {
            error.value = 'Unable to verify. Please ensure JavaScript is enabled and try again.'
            loadingResults.value = false
            return
          }
          
          // Clear the temporary message
          error.value = null
        }
      }

      const params = {
        make: selectedMake.value,
        model: selectedModel.value
      }

      if (flowType.value === 'primary' || flowType.value === 'year_select') {
        params.year = selectedYear.value
        params.modification = selectedModification.value
      } else if (flowType.value === 'alternative') {
        params.generation = selectedGeneration.value
        params.modification = selectedModification.value
      }

      // Compute a signature to prevent concurrent duplicate execution
      const signatureParts = { ...params }
      if (flowType.value === 'primary' || flowType.value === 'year_select') {
        signatureParts.year = selectedYear.value
        signatureParts.modification = selectedModification.value
      } else if (flowType.value === 'alternative') {
        signatureParts.generation = selectedGeneration.value
        signatureParts.modification = selectedModification.value
      }
      const searchSignature = `search_by_model:${JSON.stringify(signatureParts)}`

      if (activeSearchSignatureInFlight.value === searchSignature) {
        // An identical search is already in-flight; skip to avoid double events
        return
      }
      activeSearchSignatureInFlight.value = searchSignature

      // Dispatch events: search started
      widgetEvents.emit('search:start', {
        search_type: 'by_vehicle',
        parameters: {
          year: selectedYear.value,
          make: selectedMake.value,
          model: selectedModel.value,
          generation: selectedGeneration.value,
          modification: selectedModification.value
        }
      })

      // Track search initiation
      if (analytics) {
        analytics.trackSearch('search_initiate', {
          search_type: 'by_vehicle',
          selected_year: selectedYear.value,
          selected_make: selectedMake.value,
          selected_model: selectedModel.value,
          selected_modification: selectedModification.value,
          selected_generation: selectedGeneration.value
        })
      }

      const apiStartTime = performance.now()
      const response = await apiCallWithDeduplication('search_by_model', params)
      const apiEndTime = performance.now()
      
      // API response is wrapped in data property: { data: [...] }
      results.value = response.data?.data || response.data || []
      
      const searchEndTime = performance.now()
      const searchDuration = searchEndTime - searchStartTime
      const apiDuration = apiEndTime - apiStartTime

      // Dispatch event: search complete
      widgetEvents.emit('search:complete', {
        search_type: 'by_vehicle',
        results_count: results.value.length,
        timing_ms: {
          search_total: Math.round(searchDuration),
          api: Math.round(apiDuration)
        }
      })

      // Track successful search completion
      if (analytics) {
        analytics.trackSearch('search_complete', {
          search_type: 'by_vehicle',
          selected_year: selectedYear.value,
          selected_make: selectedMake.value,
          selected_model: selectedModel.value,
          selected_modification: selectedModification.value,
          selected_generation: selectedGeneration.value,
          results_count: results.value.length,
          search_completion_time: Math.round(searchDuration),
          api_response_time: Math.round(apiDuration)
        })
        
        // Track results view
        analytics.trackEvent('search_results_view', {
          results_count: results.value.length,
          search_type: 'by_vehicle'
        })
      }

      // Add successful search to history
      if (searchHistory && results.value.length > 0) {
        // Find the selected option objects for display text
        const selectedYearOption = years.value.find(opt => opt.slug === selectedYear.value || opt.id === selectedYear.value)
        const selectedMakeOption = makes.value.find(opt => opt.slug === selectedMake.value || opt.id === selectedMake.value)
        const selectedModelOption = models.value.find(opt => opt.slug === selectedModel.value || opt.id === selectedModel.value)
        const selectedModificationOption = modifications.value.find(opt => opt.slug === selectedModification.value || opt.id === selectedModification.value)
        const selectedGenerationOption = generations.value.find(opt => opt.slug === selectedGeneration.value || opt.id === selectedGeneration.value)
        
        const searchParams = {
          flowType: flowType.value,
          year: selectedYear.value,
          make: selectedMake.value,
          model: selectedModel.value,
          modification: selectedModification.value,
          generation: selectedGeneration.value,
          // Include option objects for display text generation
          options: {
            year: selectedYearOption,
            make: selectedMakeOption,
            model: selectedModelOption,
            modification: selectedModificationOption,
            generation: selectedGenerationOption
          }
        }
        searchHistory.addSearch(searchParams)
      }

      // Trigger iframe resize and notify parent that results are displayed
      setTimeout(async () => {
        if (window.parentIFrame) {
          window.parentIFrame.size()
        }
        await nextTick()
        widgetEvents.emit('results:display', {
          results_count: results.value.length
        })
      }, 100)
    } catch (err) {
      error.value = err.message
      
      // Dispatch event: search error
      widgetEvents.emit('search:error', {
        search_type: 'by_vehicle',
        error_message: err?.message || String(err)
      })

      // Track search error
      if (analytics) {
        analytics.trackError('search_failed', {
          error_message: err.message,
          search_type: 'by_vehicle',
          api_endpoint: 'search_by_model',
          selected_year: selectedYear.value,
          selected_make: selectedMake.value,
          selected_model: selectedModel.value,
          widget_state: 'searching'
        })
      }
    } finally {
      loadingResults.value = false
      // Release lock
      activeSearchSignatureInFlight.value = ''
    }
  }

  // searchByTire and searchByRim functions removed - not used in finder-v2

  async function apiCall(endpoint, params = {}) {
    const resource = widgetResources.value[endpoint]
    if (!resource || !resource[1]) {
      throw new Error(`API endpoint not configured: ${endpoint}`)
    }

    let url = resource[1]

    // If URL is relative and we have a baseUrl, make it absolute
    if (url.startsWith('/') && config.value.baseUrl) {
      url = config.value.baseUrl + url
    }

    // Merge region parameters for appropriate endpoints
    // Region filtering applies to make, model, year, generation, modification endpoints
    // but NOT to search_by_model endpoint
    const regionEnabledEndpoints = ['make', 'model', 'year', 'generation', 'modification']
    if (regionEnabledEndpoints.includes(endpoint)) {
      Object.assign(params, buildRegionParams())
    }

    return await axios.get(url, { params })
  }

  function resetVehicleSearch() {
    selectedYear.value = ''
    selectedMake.value = ''
    selectedModel.value = ''
    selectedModification.value = ''
    selectedGeneration.value = ''
    models.value = []
    modifications.value = []
    generations.value = []
  }

  // resetTireSearch and resetRimSearch functions removed - not used in finder-v2

  function clearResults() {
    results.value = []

    // Trigger iframe resize after results are cleared
    setTimeout(() => {
      if (window.parentIFrame) {
        window.parentIFrame.size()
      }
    }, 50)
  }

  // NEW HELPER: returns filter params for API based on configuration
  function buildBrandFilterParams() {
    const filter = config.value?.content?.filter || config.value?.filter || {}
    const by = filter.by || config.value?.content?.by || ''
    const mapToSlug = (item) => (typeof item === 'string' ? item : (item?.slug || item?.value || ''))
    const params = {}
    if (by === 'brands' && Array.isArray(filter.brands) && filter.brands.length) {
      const list = filter.brands.map(mapToSlug).filter(Boolean)
      if (list.length) params.brands = list.join(',')
    } else if (by === 'brands_exclude' && Array.isArray(filter.brands_exclude) && filter.brands_exclude.length) {
      const list = filter.brands_exclude.map(mapToSlug).filter(Boolean)
      if (list.length) params.brands_exclude = list.join(',')
    }
    return params
  }

  // NEW HELPER: returns region params for API based on configuration
  function buildRegionParams() {
    const regions = config.value?.content?.regions || []
    return regions.length ? { region: regions } : {}
  }

  // NEW HELPER: generate unique request key for deduplication
  function generateRequestKey(endpoint, params) {
    const sortedParams = Object.keys(params).sort().reduce((result, key) => {
      result[key] = params[key]
      return result
    }, {})
    return `${endpoint}:${JSON.stringify(sortedParams)}`
  }

  // NEW HELPER: deduplicated API call wrapper
  async function apiCallWithDeduplication(endpoint, params = {}) {
    const requestKey = generateRequestKey(endpoint, params)
    
    // If identical request is already in progress, return the existing promise
    if (ongoingRequests.value.has(requestKey)) {
      return await ongoingRequests.value.get(requestKey)
    }

    // Create new request and store promise for deduplication
    const requestPromise = apiCall(endpoint, params)
    ongoingRequests.value.set(requestKey, requestPromise)

    try {
      const result = await requestPromise
      return result
    } finally {
      // Clean up completed request
      ongoingRequests.value.delete(requestKey)
    }
  }

  // SEARCH HISTORY METHODS
  
  async function executeSearchFromHistory(searchId) {
    if (!searchHistory) {
      // console.warn('Search history not initialized')
      return
    }
    
    const searchItem = searchHistory.getSearch(searchId)
    if (!searchItem) {
      // console.warn('Search not found:', searchId)
      return
    }
    
    try {
      // Clear current results
      clearResults()
      
      // Set form values from history
      await populateFormFromSearch(searchItem)
      
      // Update search timestamp (move to top)
      searchHistory.updateSearchTimestamp(searchId)
      
      // Execute the search
      await searchByVehicle()
      
    } catch (error) {
      // console.error('Failed to execute search from history:', error)
      error.value = 'Failed to execute search from history'
    }
  }
  
  async function populateFormFromSearch(searchItem) {
    const params = searchItem.parameters
    const flow = searchItem.flowType || 'primary'
    
    // Reset current selections
    resetVehicleSearch()
    
    try {
      if (flow === 'primary') {
        // Primary flow: Year → Make → Model → Modification
        if (params.year) {
          selectedYear.value = params.year
          await loadMakes(params.year)
        }
        
        if (params.make) {
          selectedMake.value = params.make
          await loadModels(params.make, params.year)
        }
        
        if (params.model) {
          selectedModel.value = params.model
          await loadModifications(params.make, params.model, params.year)
        }
        
        if (params.modification) {
          selectedModification.value = params.modification
        }
        
      } else if (flow === 'alternative') {
        // Alternative flow: Make → Model → Generation → Modification
        if (params.make) {
          selectedMake.value = params.make
          await loadModels(params.make)
        }
        
        if (params.model) {
          selectedModel.value = params.model
          await loadGenerations(params.make, params.model)
        }
        
        if (params.generation) {
          selectedGeneration.value = params.generation
          await loadModifications(params.make, params.model, params.generation)
        }
        
        if (params.modification) {
          selectedModification.value = params.modification
        }
        
      } else if (flow === 'year_select') {
        // Year selection flow: Make → Model → Year → Modification
        if (params.make) {
          selectedMake.value = params.make
          await loadModels(params.make)
        }
        
        if (params.model) {
          selectedModel.value = params.model
          await loadYears(params.make, params.model)
        }
        
        if (params.year) {
          selectedYear.value = params.year
          await loadModifications(params.make, params.model, params.year)
        }
        
        if (params.modification) {
          selectedModification.value = params.modification
        }
      }
      
    } catch (error) {
      // console.error('Failed to populate form from search:', error)
      throw error
    }
  }
  
  function getSearchHistory() {
    return searchHistory
  }

  // EVENT HELPERS AND WATCHERS
  function toValueObject(optionList, selectedValue) {
    if (!selectedValue) return null
    const match = optionList.value?.find?.(opt => opt?.slug === selectedValue || opt?.id === selectedValue) || null
    const slug = match?.slug ?? (typeof selectedValue === 'string' ? selectedValue : String(selectedValue))
    const title = match?.title ?? match?.name ?? match?.value ?? String(slug)
    return { slug, title }
  }

  watch(selectedYear, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:year', { value: toValueObject(years, nv) })
  })

  watch(selectedMake, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:make', { value: toValueObject(makes, nv) })
  })

  watch(selectedModel, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:model', { value: toValueObject(models, nv) })
  })

  watch(selectedGeneration, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:generation', { value: toValueObject(generations, nv) })
  })

  watch(selectedModification, (nv, ov) => {
    if (nv === ov) return
    widgetEvents.emit('change:modification', { value: toValueObject(modifications, nv) })
  })

  return {
    // State
    config,
    loading,
    loadingResults,
    error,
    results,
    outputTemplate,

    // Individual loading states
    loadingYears,
    loadingMakes,
    loadingModels,
    loadingGenerations,
    loadingModifications,

    // State loaded flags
    stateLoadedYears,
    stateLoadedMakes,
    stateLoadedModels,
    stateLoadedGenerations,
    stateLoadedModifications,

    // Vehicle search (only supported search type)
    selectedYear,
    selectedMake,
    selectedModel,
    selectedModification,
    selectedGeneration,
    years,
    makes,
    models,
    modifications,
    generations,

    // Getters
    flowType,
    apiVersion,
    widgetResources,

    // Actions
    initialize,
    loadYears,
    loadMakes,
    loadModels,
    loadModifications,
    loadGenerations,
    searchByVehicle,
    resetVehicleSearch,
    clearResults,
    clearError,
    buildBrandFilterParams,
    buildRegionParams,
    
    // Search History Actions
    executeSearchFromHistory,
    getSearchHistory,
    
    // Analytics and Cross-Domain Tracking
    getAnalytics: () => analytics,
    getCrossDomainTracking: () => crossDomainTracking
  }
})
