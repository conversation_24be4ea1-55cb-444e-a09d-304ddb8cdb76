import { ref } from 'vue'

// Singleton instance storage
let widgetEventsInstance = null

/**
 * Widget Events Composable
 *
 * Provides a postMessage-based event system compatible with legacy WheelSizeWidgets API.
 * Ensures messages use the expected envelope: { src, type, data }.
 *
 * Event types (phase 1 support):
 * - ready:document
 * - ready:window (emitted after window load AND initial data load)
 *
 * Additional helpers for phase 2/3 integrations:
 * - emit(type, data)
 * - setConfig(config)
 * - setContextProvider(fn)
 * - markInitialDataLoaded()
 * - onBeforeSend(fn) / onAfterSend(fn)
 */
export function useWidgetEvents() {
  if (widgetEventsInstance) return widgetEventsInstance

  /** @type {import('vue').Ref<Record<string, any>>} */
  const currentConfig = ref(window.FinderV2Config || {})

  /**
   * Optional context provider supplied by the store/component to enrich event payloads.
   * Should return a plain object with context data (e.g., current selections, widget id, etc.).
   * @type {null | (() => Record<string, any>)}
   */
  let contextProvider = null

  /** @type {Array<(evt: any) => void>} */
  const beforeSendHandlers = []
  /** @type {Array<(evt: any) => void>} */
  const afterSendHandlers = []

  // Ready gating
  const domReady = ref(document.readyState !== 'loading')
  const windowLoaded = ref(false)
  const initialDataLoaded = ref(false)
  const readyWindowEmitted = ref(false)

  /**
   * Build event envelope compatible with WheelSizeWidgets
   * @param {string} type
   * @param {Record<string, any>=} data
   */
  function buildEvent(type, data = {}) {
    function getConfigMeta() {
      try {
        const cfg = currentConfig.value || {}
        const flow = cfg.flowType || cfg.widgetConfig?.flowType || undefined
        return {
          widgetUuid: cfg.widgetUuid || cfg.uuid || cfg.id || undefined,
          type: cfg.type || 'finder-v2',
          flowType: flow
        }
      } catch (_) {
        return {}
      }
    }

    let context = {}
    try {
      context = typeof contextProvider === 'function' ? contextProvider() || {} : {}
    } catch (_) {
      // ignore context provider failures
    }

    return {
      src: window.location.href,
      type,
      data: {
        ...data,
        context: {
          // Only include a serializable subset of config to avoid DataCloneError
          config: getConfigMeta(),
          ...context
        }
      }
    }
  }

  /**
   * Post event to parent window with robust cross-origin handling
   * @param {string} type
   * @param {Record<string, any>=} data
   */
  function emit(type, data = {}) {
    const eventPayload = buildEvent(type, data)

    // before hooks
    for (const handler of beforeSendHandlers) {
      try { handler(eventPayload) } catch (_) { /* ignore */ }
    }

    try {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage(eventPayload, '*')
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      // console.warn('WidgetEvents: postMessage failed', error)
    }

    // after hooks
    for (const handler of afterSendHandlers) {
      try { handler(eventPayload) } catch (_) { /* ignore */ }
    }

    return eventPayload
  }

  function tryEmitReadyWindow() {
    if (!readyWindowEmitted.value && windowLoaded.value && initialDataLoaded.value) {
      emit('ready:window')
      readyWindowEmitted.value = true
    }
  }

  // DOM ready handling → ready:document
  if (!domReady.value) {
    document.addEventListener('DOMContentLoaded', () => {
      domReady.value = true
      emit('ready:document')
    })
  } else {
    // If the script initialized after DOMContentLoaded
    emit('ready:document')
  }

  // Window load handling (wait for full load; gate with initialDataLoaded)
  if (document.readyState === 'complete') {
    windowLoaded.value = true
    // ready:window is gated by initialDataLoaded
    tryEmitReadyWindow()
  } else {
    window.addEventListener('load', () => {
      windowLoaded.value = true
      tryEmitReadyWindow()
    })
  }

  /**
   * Set/replace widget configuration used in event context
   * @param {Record<string, any>} config
   */
  function setConfig(config) {
    currentConfig.value = config || {}
  }

  /**
   * Provide a function that returns additional context to include with every event
   * @param {() => Record<string, any>} provider
   */
  function setContextProvider(provider) {
    contextProvider = provider
  }

  /**
   * Mark initial data as loaded; will emit ready:window after the window load event
   */
  function markInitialDataLoaded() {
    initialDataLoaded.value = true
    tryEmitReadyWindow()
  }

  /**
   * Register a callback invoked before each event send
   * @param {(evt: any) => void} handler
   */
  function onBeforeSend(handler) {
    if (typeof handler === 'function') beforeSendHandlers.push(handler)
  }

  /**
   * Register a callback invoked after each event send
   * @param {(evt: any) => void} handler
   */
  function onAfterSend(handler) {
    if (typeof handler === 'function') afterSendHandlers.push(handler)
  }

  widgetEventsInstance = {
    emit,
    setConfig,
    setContextProvider,
    markInitialDataLoaded,
    onBeforeSend,
    onAfterSend
  }

  return widgetEventsInstance
}

export default useWidgetEvents


