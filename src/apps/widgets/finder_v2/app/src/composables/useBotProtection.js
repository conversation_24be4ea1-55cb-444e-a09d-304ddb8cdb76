/**
 * Bot Protection Composable
 * 
 * Provides browser fingerprinting and bot protection capabilities
 * for the finder-v2 widget.
 */
import { ref, onMounted } from 'vue'
import axios from 'axios'

export function useBotProtection() {
  const challengeSolved = ref(false)
  const challengeToken = ref('')
  const browserFeatures = ref({})
  const fingerprintCollected = ref(false)
  
  /**
   * Collect browser features for fingerprinting
   */
  function collectBrowserFeatures() {
    const features = {
      // Screen information
      screen_width: window.screen?.width || 0,
      screen_height: window.screen?.height || 0,
      screen_available_width: window.screen?.availWidth || 0,
      screen_available_height: window.screen?.availHeight || 0,
      color_depth: window.screen?.colorDepth || 0,
      pixel_depth: window.screen?.pixelDepth || 0,
      
      // Browser information
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timezone_offset: new Date().getTimezoneOffset(),
      language: navigator.language || '',
      languages: navigator.languages?.join(',') || '',
      platform: navigator.platform || '',
      user_agent: navigator.userAgent || '',
      
      // Feature detection
      cookies_enabled: navigator.cookieEnabled,
      online: navigator.onLine,
      do_not_track: navigator.doNotTrack || 'unspecified',
      hardware_concurrency: navigator.hardwareConcurrency || 0,
      max_touch_points: navigator.maxTouchPoints || 0,
      
      // Plugin information (limited in modern browsers)
      plugins_count: navigator.plugins?.length || 0,
      
      // Canvas fingerprint
      canvas_hash: generateCanvasHash(),
      
      // WebGL information
      webgl_vendor: getWebGLVendor(),
      webgl_renderer: getWebGLRenderer(),
      
      // Feature support
      touch_support: ('ontouchstart' in window) || (navigator.maxTouchPoints > 0),
      media_devices: !!navigator.mediaDevices,
      webrtc_enabled: !!window.RTCPeerConnection,
      websocket_enabled: !!window.WebSocket,
      session_storage: !!window.sessionStorage,
      local_storage: !!window.localStorage,
      indexed_db: !!window.indexedDB,
      
      // Performance
      connection_type: navigator.connection?.effectiveType || 'unknown',
      
      // Window information
      window_outer_width: window.outerWidth,
      window_outer_height: window.outerHeight,
      window_inner_width: window.innerWidth,
      window_inner_height: window.innerHeight,
      
      // Timestamp for validation
      timestamp: Date.now()
    }
    
    // Store collected features
    browserFeatures.value = features
    fingerprintCollected.value = true
    
    return features
  }
  
  /**
   * Generate canvas fingerprint
   */
  function generateCanvasHash() {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) {
        return 'canvas_not_available'
      }
      
      // Set canvas size
      canvas.width = 200
      canvas.height = 50
      
      // Draw test content
      ctx.textBaseline = 'alphabetic'
      ctx.fillStyle = '#f60'
      ctx.fillRect(125, 1, 62, 20)
      
      ctx.fillStyle = '#069'
      ctx.font = '11pt Arial'
      ctx.fillText('Canvas fingerprint 🛡️', 2, 15)
      
      ctx.fillStyle = 'rgba(102, 204, 0, 0.7)'
      ctx.font = '18pt Arial'
      ctx.fillText('Widget Protection', 4, 45)
      
      // Generate hash from canvas data
      const dataURL = canvas.toDataURL()
      return hashString(dataURL).substring(0, 32)
    } catch (e) {
      console.debug('Canvas fingerprinting blocked:', e.message)
      return 'canvas_blocked'
    }
  }
  
  /**
   * Get WebGL vendor information
   */
  function getWebGLVendor() {
    try {
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      
      if (!gl) {
        return 'webgl_not_supported'
      }
      
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
      if (debugInfo) {
        return gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) || 'unknown'
      }
      
      return 'debug_info_not_available'
    } catch (e) {
      console.debug('WebGL vendor detection failed:', e.message)
      return 'webgl_blocked'
    }
  }
  
  /**
   * Get WebGL renderer information
   */
  function getWebGLRenderer() {
    try {
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      
      if (!gl) {
        return 'webgl_not_supported'
      }
      
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
      if (debugInfo) {
        return gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || 'unknown'
      }
      
      return 'debug_info_not_available'
    } catch (e) {
      console.debug('WebGL renderer detection failed:', e.message)
      return 'webgl_blocked'
    }
  }
  
  /**
   * Simple hash function for strings
   */
  function hashString(str) {
    let hash = 0
    if (str.length === 0) return '0'
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32bit integer
    }
    
    return Math.abs(hash).toString(16)
  }
  
  /**
   * Solve proof-of-work challenge
   */
  async function solveChallenge(difficulty = 4) {
    // Check if we have proper crypto support BEFORE starting
    const hasCrypto = window.crypto && window.crypto.subtle
    
    if (!hasCrypto) {
      console.warn('crypto.subtle not available - cannot solve real challenge')
      // For browsers without crypto.subtle, we can't solve real challenges
      // Return a marker that indicates this
      throw new Error('JavaScript cryptography (crypto.subtle) is required but not available. Please use a modern browser.')
    }
    
    const challenge = Math.random().toString(36).substring(2, 15)
    let nonce = 0
    const startTime = Date.now()
    const targetPrefix = '0'.repeat(difficulty)
    
    console.log(`Solving challenge with difficulty ${difficulty}...`)
    
    // Find nonce that produces hash with required leading zeros
    while (true) {
      const attempt = `${challenge}:${nonce}`
      
      // Use ONLY the real SHA-256 for challenges
      let hash
      try {
        const msgBuffer = new TextEncoder().encode(attempt)
        const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer)
        const hashArray = Array.from(new Uint8Array(hashBuffer))
        hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
      } catch (e) {
        throw new Error('Failed to compute hash: ' + e.message)
      }
      
      if (hash.startsWith(targetPrefix)) {
        const duration = Date.now() - startTime
        console.log(`Challenge solved in ${duration}ms with nonce ${nonce}`)
        
        return {
          challenge,
          nonce,
          hash,
          duration,
          difficulty
        }
      }
      
      nonce++
      
      // Yield to browser every 100 iterations to prevent blocking
      if (nonce % 100 === 0) {
        await new Promise(resolve => setTimeout(resolve, 0))
      }
      
      // Timeout after 10 seconds
      if (Date.now() - startTime > 10000) {
        throw new Error('Challenge timeout - took too long to solve')
      }
    }
  }
  
  /**
   * SHA-256 hash function with robust fallback
   */
  async function sha256(message) {
    // Try native crypto API first
    if (window.crypto && window.crypto.subtle) {
      try {
        const msgBuffer = new TextEncoder().encode(message)
        const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer)
        const hashArray = Array.from(new Uint8Array(hashBuffer))
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
      } catch (e) {
        // Don't log on every call - this creates spam
        // console.warn('crypto.subtle failed, using fallback:', e.message)
      }
    }
    
    // Fallback to simple hash for older browsers or when crypto.subtle is blocked
    // This is less secure but allows the widget to function
    // Don't log on every call - this creates spam
    return simpleSHA256(message)
  }
  
  /**
   * Simple SHA-256 implementation for fallback
   * Note: This is a simplified version, not cryptographically secure
   */
  function simpleSHA256(message) {
    // Simple hash for fallback when crypto.subtle is not available
    // This provides compatibility but less security
    let hash = 0
    for (let i = 0; i < message.length; i++) {
      const char = message.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32bit integer
    }
    
    // Convert to hex string and pad with zeros to look like SHA-256
    const hexHash = Math.abs(hash).toString(16)
    return hexHash.padEnd(64, '0').substring(0, 64)
  }
  
  /**
   * Send browser features to server
   */
  async function sendFingerprintToServer() {
    try {
      const features = browserFeatures.value
      
      // Send as custom header (will be read by BrowserFingerprint class)
      const headers = {
        'X-Client-Features': JSON.stringify(features)
      }
      
      // Also update default headers for all future requests
      axios.defaults.headers.common['X-Client-Features'] = JSON.stringify(features)
      
      console.log('Browser fingerprint sent to server')
      return true
    } catch (error) {
      console.error('Failed to send fingerprint:', error)
      return false
    }
  }
  
  /**
   * Initialize bot protection
   */
  async function initialize() {
    try {
      console.log('Initializing bot protection...')
      
      // Step 1: Collect browser features
      const features = collectBrowserFeatures()
      console.log('Browser features collected:', Object.keys(features).length, 'features')
      
      // Step 2: Send fingerprint to server
      await sendFingerprintToServer()
      
      // Step 3: Solve challenge (if required by server)
      // This would typically be triggered by server response
      // For now, we'll prepare the capability
      
      // Step 4: Mark as initialized
      console.log('Bot protection initialized successfully')
      
      return true
    } catch (error) {
      console.error('Bot protection initialization failed:', error)
      // Fallback gracefully for legitimate users with issues
      // Don't block widget functionality
      return false
    }
  }
  
  /**
   * Request a challenge from the server
   */
  async function requestChallenge() {
    try {
      const response = await axios.post('/widget/api/request-challenge/', {
        widget_uuid: window.FinderV2Config?.uuid || window.FinderV2Config?.id
      })
      
      return response.data
    } catch (error) {
      console.error('Failed to request challenge:', error)
      throw error
    }
  }
  
  /**
   * Solve and verify challenge for search/by_model endpoint
   */
  async function solveAndVerifyChallenge() {
    try {
      // Check crypto support first - be more lenient for HTTP contexts
      const hasCrypto = window.crypto && window.crypto.subtle
      
      // In development (HTTP), crypto.subtle may not be available
      // Log warning but try to proceed with degraded security
      if (!hasCrypto) {
        console.warn('crypto.subtle not available - likely due to insecure context (HTTP)')
        
        // For development/HTTP contexts, skip challenge verification
        // In production with HTTPS, crypto.subtle should be available
        if (window.location.protocol === 'http:') {
          console.warn('Running in HTTP mode - skipping challenge verification for development')
          // Return success but without a real token
          challengeSolved.value = true
          challengeToken.value = 'development-bypass'
          
          // Add development bypass token to axios headers
          axios.defaults.headers.common['X-Challenge-Token'] = 'development-bypass'
          
          return true
        }
        
        // For HTTPS, this is a real problem
        throw new Error('Security features are not available. This may be due to browser settings or an outdated browser. Please ensure JavaScript is enabled and try using Chrome, Firefox, Safari, or Edge.')
      }
      
      // Step 1: Request a challenge
      console.log('Requesting challenge for search/by_model endpoint...')
      const challenge = await requestChallenge()
      
      // Step 2: Solve the challenge
      console.log(`Solving challenge with difficulty ${challenge.difficulty}...`)
      const solution = await solveChallenge(challenge.difficulty)
      
      // Override the challenge string with the one from server
      solution.challenge = challenge.challenge
      
      // Step 3: Verify the solution with server
      const verifyResponse = await axios.post('/widget/api/verify-challenge/', {
        solution,
        widget_uuid: window.FinderV2Config?.uuid || window.FinderV2Config?.id
      })
      
      if (verifyResponse.data.success) {
        challengeToken.value = verifyResponse.data.token
        challengeSolved.value = true
        
        // Add challenge token to axios headers
        axios.defaults.headers.common['X-Challenge-Token'] = challengeToken.value
        
        console.log('Challenge solved and verified successfully')
        console.log(`Token valid for ${verifyResponse.data.max_uses} uses`)
        
        // Store in session storage for recovery
        sessionStorage.setItem('challenge_token', challengeToken.value)
        sessionStorage.setItem('challenge_token_time', Date.now())
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('Challenge verification failed:', error.message)
      
      // Re-throw with user-friendly message
      if (error.message.includes('crypto')) {
        throw new Error('Your browser does not support required security features. Please use Chrome, Firefox, Safari, or Edge.')
      }
      
      throw error
    }
  }
  
  /**
   * Check if we have a valid challenge token
   */
  function hasValidChallengeToken() {
    // Check in memory first
    if (challengeToken.value && challengeSolved.value) {
      return true
    }
    
    // Check session storage (for page refresh)
    const storedToken = sessionStorage.getItem('challenge_token')
    const storedTime = sessionStorage.getItem('challenge_token_time')
    
    if (storedToken && storedTime) {
      const age = Date.now() - parseInt(storedTime)
      // Token valid for 1 hour
      if (age < 3600000) {
        challengeToken.value = storedToken
        challengeSolved.value = true
        axios.defaults.headers.common['X-Challenge-Token'] = storedToken
        return true
      }
    }
    
    return false
  }
  
  /**
   * Verify human with server
   */
  async function verifyHuman() {
    try {
      // Collect fresh features
      const features = collectBrowserFeatures()
      
      // Solve challenge
      const solution = await solveChallenge(4)
      
      // Send proof to server
      const response = await axios.post('/widget/api/verify-human/', {
        features,
        solution,
        widget_uuid: window.FinderV2Config?.uuid || window.FinderV2Config?.id
      })
      
      if (response.data.token) {
        challengeToken.value = response.data.token
        challengeSolved.value = true
        
        // Add challenge token to all future requests
        axios.defaults.headers.common['X-Challenge-Token'] = challengeToken.value
        
        console.log('Human verification successful')
        return true
      }
      
      return false
    } catch (error) {
      console.error('Human verification failed:', error)
      return false
    }
  }
  
  // Auto-initialize on mount
  onMounted(() => {
    // Initialize bot protection automatically
    initialize().catch(error => {
      console.warn('Bot protection auto-init failed:', error)
    })
  })
  
  return {
    // State
    challengeSolved,
    challengeToken,
    browserFeatures,
    fingerprintCollected,
    
    // Methods
    initialize,
    collectBrowserFeatures,
    sendFingerprintToServer,
    solveChallenge,
    verifyHuman,
    requestChallenge,
    solveAndVerifyChallenge,
    hasValidChallengeToken,
    
    // Utilities
    generateCanvasHash,
    getWebGLVendor,
    getWebGLRenderer
  }
}