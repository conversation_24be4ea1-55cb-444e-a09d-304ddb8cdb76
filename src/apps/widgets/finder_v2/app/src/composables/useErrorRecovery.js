/**
 * Error Recovery Composable
 * Provides retry logic, fallback values, and circuit breaker functionality
 */

import { ref } from 'vue'

export function useErrorRecovery() {
  const maxRetries = 3
  const retryDelay = 1000
  const circuitBreakerThreshold = 5
  const circuitBreakerTimeout = 60000 // 1 minute

  // Circuit breaker state
  const failures = new Map()

  /**
   * Execute a function with automatic retry logic
   */
  const withRetry = async (fn, options = {}) => {
    const { 
      retries = maxRetries, 
      delay = retryDelay,
      backoff = true,
      onRetry = null
    } = options

    let lastError = null
    let attempt = 0

    while (attempt < retries) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        attempt++
        
        if (attempt < retries) {
          // Call retry callback if provided
          if (onRetry) {
            onRetry(attempt, error)
          }

          // Calculate wait time with exponential backoff
          const waitTime = backoff ? delay * Math.pow(2, attempt - 1) : delay
          
          // Log retry attempt in development
          if (import.meta.env.DEV) {
            console.warn(`Retry attempt ${attempt}/${retries} after ${waitTime}ms`, error)
          }

          await new Promise(resolve => setTimeout(resolve, waitTime))
        }
      }
    }

    throw lastError
  }

  /**
   * Execute a function with a fallback value on error
   */
  const withFallback = async (fn, fallbackValue) => {
    try {
      return await fn()
    } catch (error) {
      if (import.meta.env.DEV) {
        console.warn('Using fallback value due to error:', error)
      }
      return typeof fallbackValue === 'function' ? fallbackValue(error) : fallbackValue
    }
  }

  /**
   * Execute a function with circuit breaker protection
   */
  const withCircuitBreaker = async (key, fn) => {
    const now = Date.now()
    const failureData = failures.get(key) || { count: 0, lastFailure: 0 }
    
    // Check if circuit is open
    if (failureData.count >= circuitBreakerThreshold) {
      if (now - failureData.lastFailure < circuitBreakerTimeout) {
        throw new Error(`Circuit breaker open for ${key}. Too many failures.`)
      }
      // Reset after timeout
      failures.delete(key)
    }

    try {
      const result = await fn()
      // Reset on success
      failures.delete(key)
      return result
    } catch (error) {
      // Update failure count
      failureData.count++
      failureData.lastFailure = now
      failures.set(key, failureData)
      
      if (import.meta.env.DEV) {
        console.error(`Circuit breaker: ${key} failed ${failureData.count} times`, error)
      }
      
      throw error
    }
  }

  /**
   * Create a debounced error handler
   */
  const createDebouncedErrorHandler = (handler, delay = 1000) => {
    let timeoutId = null
    let errorQueue = []

    return (error) => {
      errorQueue.push(error)
      
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      timeoutId = setTimeout(() => {
        handler(errorQueue)
        errorQueue = []
      }, delay)
    }
  }

  /**
   * Create an error aggregator
   */
  const createErrorAggregator = () => {
    const errors = ref([])
    const errorCounts = ref(new Map())

    const addError = (error, context = {}) => {
      const errorKey = `${error.name}:${error.message}`
      const count = errorCounts.value.get(errorKey) || 0
      errorCounts.value.set(errorKey, count + 1)

      errors.value.push({
        error,
        context,
        timestamp: Date.now(),
        count: count + 1
      })

      // Keep only last 50 errors
      if (errors.value.length > 50) {
        errors.value.shift()
      }
    }

    const getTopErrors = (limit = 5) => {
      return Array.from(errorCounts.value.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, limit)
        .map(([key, count]) => ({ error: key, count }))
    }

    const clear = () => {
      errors.value = []
      errorCounts.value.clear()
    }

    return {
      errors,
      errorCounts,
      addError,
      getTopErrors,
      clear
    }
  }

  /**
   * Execute with timeout
   */
  const withTimeout = async (fn, timeout = 5000) => {
    return Promise.race([
      fn(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error(`Operation timed out after ${timeout}ms`)), timeout)
      )
    ])
  }

  /**
   * Batch error handler for multiple operations
   */
  const batchWithErrorHandling = async (operations, options = {}) => {
    const { stopOnError = false, parallel = false } = options
    const results = []
    const errors = []

    if (parallel) {
      // Execute all operations in parallel
      const promises = operations.map(async (op, index) => {
        try {
          const result = await op()
          return { success: true, result, index }
        } catch (error) {
          return { success: false, error, index }
        }
      })

      const outcomes = await Promise.all(promises)
      
      outcomes.forEach(outcome => {
        if (outcome.success) {
          results[outcome.index] = outcome.result
        } else {
          errors[outcome.index] = outcome.error
        }
      })
    } else {
      // Execute operations sequentially
      for (let i = 0; i < operations.length; i++) {
        try {
          results[i] = await operations[i]()
        } catch (error) {
          errors[i] = error
          if (stopOnError) {
            throw error
          }
        }
      }
    }

    return { results, errors, hasErrors: errors.length > 0 }
  }

  /**
   * Reset circuit breaker for a specific key
   */
  const resetCircuitBreaker = (key) => {
    if (key) {
      failures.delete(key)
    } else {
      failures.clear()
    }
  }

  /**
   * Get circuit breaker status
   */
  const getCircuitBreakerStatus = (key) => {
    const failureData = failures.get(key)
    if (!failureData) {
      return { status: 'closed', failures: 0 }
    }

    const now = Date.now()
    const isOpen = failureData.count >= circuitBreakerThreshold && 
                   (now - failureData.lastFailure) < circuitBreakerTimeout

    return {
      status: isOpen ? 'open' : 'closed',
      failures: failureData.count,
      lastFailure: new Date(failureData.lastFailure).toISOString()
    }
  }

  return {
    withRetry,
    withFallback,
    withCircuitBreaker,
    withTimeout,
    batchWithErrorHandling,
    createDebouncedErrorHandler,
    createErrorAggregator,
    resetCircuitBreaker,
    getCircuitBreakerStatus
  }
}