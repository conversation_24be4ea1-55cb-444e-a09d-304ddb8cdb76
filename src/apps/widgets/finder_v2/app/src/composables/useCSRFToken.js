/**
 * Enhanced CSRF Token Management Composable
 * 
 * Provides automatic CSRF token management with session binding,
 * token rotation, and fallback mechanisms for widget security.
 */

import { ref, onMounted, onUnmounted, computed } from 'vue'
import axios from 'axios'

export function useCSRFToken() {
  // Token state
  const csrfToken = ref('')
  const tokenAge = ref(0)
  const tokenStatus = ref('initializing') // 'initializing', 'active', 'refreshing', 'error'
  const lastRefresh = ref(null)
  
  // Configuration
  const TOKEN_REFRESH_INTERVAL = 4 * 60 * 1000 // 4 minutes
  const TOKEN_MAX_AGE = 55 * 60 * 1000 // 55 minutes
  const RETRY_DELAY = 5000 // 5 seconds retry on failure
  const MAX_RETRIES = 3
  
  // Internal state
  let refreshInterval = null
  let retryCount = 0
  let isRefreshing = false
  
  /**
   * Check if enhanced CSRF is enabled
   */
  const isEnhancedCSRFEnabled = computed(() => {
    return window.FinderV2Config?.enhancedCSRF === true
  })
  
  /**
   * Get base URL for API calls
   */
  function getBaseUrl() {
    const config = window.FinderV2Config || {}
    const baseUrl = config.baseUrl || ''
    
    // Ensure baseUrl doesn't end with slash
    return baseUrl.replace(/\/$/, '')
  }
  
  /**
   * Refresh CSRF token from server
   */
  async function refreshToken() {
    if (isRefreshing) {
      console.warn('Token refresh already in progress')
      return
    }
    
    // Skip refresh if enhanced CSRF is not enabled
    if (!isEnhancedCSRFEnabled.value) {
      // console.log('Enhanced CSRF not enabled, using legacy token')
      return
    }
    
    isRefreshing = true
    tokenStatus.value = 'refreshing'
    
    try {
      const baseUrl = getBaseUrl()
      const widgetUuid = window.FinderV2Config?.uuid || window.FinderV2Config?.id || ''
      
      // console.log('Attempting to refresh token from:', `${baseUrl}/widget/api/refresh-token/`)
      
      const response = await axios.post(
        `${baseUrl}/widget/api/refresh-token/`,
        {
          widget_uuid: widgetUuid,
          old_token: csrfToken.value || undefined
        },
        {
          headers: {
            'Content-Type': 'application/json',
            // Include current token for rotation
            'X-CSRF-TOKEN': csrfToken.value || ''
          },
          withCredentials: true // Important for session cookies
        }
      )
      
      if (response.data && response.data.token) {
        // Update token
        const newToken = response.data.token
        csrfToken.value = newToken
        tokenAge.value = 0
        lastRefresh.value = Date.now()
        tokenStatus.value = 'active'
        retryCount = 0
        
        // Update axios defaults
        axios.defaults.headers.common['X-CSRF-TOKEN'] = newToken
        
        // Store in session storage for recovery
        sessionStorage.setItem('widget_csrf_token', newToken)
        sessionStorage.setItem('widget_csrf_timestamp', Date.now().toString())
        
        // console.log('CSRF token refreshed successfully')
      } else {
        throw new Error('Invalid token response')
      }
      
    } catch (error) {
      console.error('Failed to refresh CSRF token:', error)
      console.error('Error details:', error.response?.data || error.message)
      tokenStatus.value = 'error'
      
      // Try to recover from session storage
      const storedToken = sessionStorage.getItem('widget_csrf_token')
      const storedTimestamp = sessionStorage.getItem('widget_csrf_timestamp')
      
      if (storedToken && storedTimestamp) {
        const age = Date.now() - parseInt(storedTimestamp)
        
        // Use stored token if it's less than 1 hour old
        if (age < 60 * 60 * 1000) {
          csrfToken.value = storedToken
          tokenAge.value = age
          axios.defaults.headers.common['X-CSRF-TOKEN'] = storedToken
          tokenStatus.value = 'active'
          // console.log('Recovered CSRF token from session storage')
        }
      }
      
      // Retry if we haven't exceeded max retries
      if (retryCount < MAX_RETRIES) {
        retryCount++
        // console.log(`Retrying token refresh in ${RETRY_DELAY}ms (attempt ${retryCount}/${MAX_RETRIES})`)
        setTimeout(() => refreshToken(), RETRY_DELAY)
      } else {
        console.error('Max token refresh retries exceeded')
        // Fall back to legacy token if available
        useLegacyToken()
      }
      
    } finally {
      isRefreshing = false
    }
  }
  
  /**
   * Use legacy User-Agent based token as fallback
   */
  function useLegacyToken() {
    const legacyToken = window.FinderV2Config?.csrfToken
    
    if (legacyToken) {
      // console.log('Falling back to legacy CSRF token')
      csrfToken.value = legacyToken
      axios.defaults.headers.common['X-CSRF-TOKEN'] = legacyToken
      tokenStatus.value = 'active'
    } else {
      console.error('No fallback CSRF token available')
      tokenStatus.value = 'error'
    }
  }
  
  /**
   * Start automatic token refresh cycle
   */
  function startTokenRefresh() {
    if (!isEnhancedCSRFEnabled.value) {
      // console.log('Enhanced CSRF not enabled, skipping auto-refresh')
      return
    }
    
    refreshInterval = setInterval(() => {
      tokenAge.value += TOKEN_REFRESH_INTERVAL
      
      // Refresh token before it expires
      if (tokenAge.value >= TOKEN_MAX_AGE) {
        // console.log('Token age limit reached, refreshing...')
        refreshToken()
      }
    }, TOKEN_REFRESH_INTERVAL)
    
    // console.log('CSRF token auto-refresh started')
  }
  
  /**
   * Stop automatic token refresh
   */
  function stopTokenRefresh() {
    if (refreshInterval) {
      clearInterval(refreshInterval)
      refreshInterval = null
      // console.log('CSRF token auto-refresh stopped')
    }
  }
  
  /**
   * Initialize CSRF token management
   */
  async function initialize() {
    try {
      // Check if enhanced CSRF is enabled
      if (isEnhancedCSRFEnabled.value) {
        // console.log('Enhanced CSRF protection enabled')
        // console.log('Widget UUID:', window.FinderV2Config?.uuid || 'NOT SET')
        // console.log('Base URL:', getBaseUrl() || 'NOT SET')
        
        // Try to get a fresh token
        await refreshToken()
        
        // Start auto-refresh
        startTokenRefresh()
      } else {
        // console.log('Using legacy CSRF protection')
        
        // Use legacy token from config
        const legacyToken = window.FinderV2Config?.csrfToken || ''
        csrfToken.value = legacyToken
        axios.defaults.headers.common['X-CSRF-TOKEN'] = legacyToken
        tokenStatus.value = legacyToken ? 'active' : 'error'
        // console.log('Legacy token configured:', legacyToken ? 'YES' : 'NO')
      }
    } catch (error) {
      console.error('CSRF token initialization failed:', error)
      
      // Fall back to legacy token
      useLegacyToken()
    }
  }
  
  /**
   * Manual token refresh (for user-triggered refresh)
   */
  async function manualRefresh() {
    retryCount = 0 // Reset retry count for manual refresh
    await refreshToken()
  }
  
  /**
   * Get current token status information
   */
  function getTokenInfo() {
    return {
      token: csrfToken.value,
      status: tokenStatus.value,
      age: tokenAge.value,
      lastRefresh: lastRefresh.value,
      enhanced: isEnhancedCSRFEnabled.value
    }
  }
  
  // Lifecycle hooks
  onMounted(() => {
    initialize()
  })
  
  onUnmounted(() => {
    stopTokenRefresh()
  })
  
  // Expose public API
  return {
    csrfToken,
    tokenStatus,
    tokenAge,
    isEnhancedCSRFEnabled,
    refreshToken: manualRefresh,
    getTokenInfo
  }
}