import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useAnalytics } from '../useAnalytics.js'

// Mock navigator
Object.defineProperty(global.navigator, 'doNotTrack', {
  value: '0',
  configurable: true
})

// Setup window mock 
Object.assign(window, {
  gtag: vi.fn(),
  dataLayer: [],
  location: {
    hostname: 'localhost'
  },
  parent: {
    location: {
      hostname: 'example.com',  
      href: 'https://example.com/test-page'
    }
  }
})

// Setup document mock
Object.defineProperty(document, 'createElement', {
  value: vi.fn(() => ({
    async: false,
    src: '',
    onload: null,
    onerror: null
  })),
  writable: true
})

Object.defineProperty(document, 'referrer', {
  value: 'https://example.com/test-page',
  writable: true
})

// Mock document.head.appendChild
if (!document.head.appendChild) {
  document.head.appendChild = vi.fn()
}

describe('useAnalytics', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Reset window.gtag
    window.gtag = vi.fn()
    window.dataLayer = []
    
    // Reset navigator.doNotTrack
    Object.defineProperty(global.navigator, 'doNotTrack', {
      value: '0',
      configurable: true
    })
  })

  it('should initialize analytics with widget config', async () => {
    const widgetConfig = {
      uuid: 'test-widget-123',
      apiVersion: 'v2',
      flowType: 'primary',
      theme: { name: 'default' }
    }

    const analytics = useAnalytics(widgetConfig)

    expect(analytics.isEnabled.value).toBe(true)
    expect(analytics.isInitialized.value).toBe(false)
  })

  it('should track events with widget context', () => {
    const widgetConfig = {
      uuid: 'test-widget-123',
      apiVersion: 'v2',
      flowType: 'primary'
    }

    const analytics = useAnalytics(widgetConfig)
    // Directly set initialized state and enabled state
    analytics.isInitialized.value = true
    analytics.isEnabled.value = true
    
    // Ensure gtag is available as a function
    expect(typeof window.gtag).toBe('function')

    analytics.trackEvent('test_event', {
      custom_param: 'test_value'
    })

    expect(window.gtag).toHaveBeenCalledWith('event', 'test_event', 
      expect.objectContaining({
        widget_uuid: 'test-widget-123',
        widget_type: 'finder-v2',
        flow_type: 'primary',
        api_version: 'v2',
        custom_param: 'test_value'
      })
    )
  })

  it('should track search events', () => {
    const widgetConfig = {
      uuid: 'test-widget-123',
      flowType: 'primary'
    }

    const analytics = useAnalytics(widgetConfig)
    analytics.isInitialized.value = true
    analytics.isEnabled.value = true

    analytics.trackSearch('search_complete', {
      selected_year: '2020',
      selected_make: 'BMW',
      results_count: 15
    })

    expect(window.gtag).toHaveBeenCalledWith('event', 'widget_search',
      expect.objectContaining({
        search_type: 'search_complete',
        search_flow_type: 'primary',
        selected_year: '2020',
        selected_make: 'BMW',
        results_count: 15
      })
    )
  })

  it('should track interaction events', () => {
    const widgetConfig = {
      uuid: 'test-widget-123'
    }

    const analytics = useAnalytics(widgetConfig)
    analytics.isInitialized.value = true
    analytics.isEnabled.value = true

    analytics.trackInteraction('year_selected', {
      selected_year: '2020',
      flow_step: 1
    })

    expect(window.gtag).toHaveBeenCalledWith('event', 'widget_interaction',
      expect.objectContaining({
        interaction_type: 'year_selected',
        selected_year: '2020',
        flow_step: 1
      })
    )
  })

  it('should track error events', () => {
    const widgetConfig = {
      uuid: 'test-widget-123'
    }

    const analytics = useAnalytics(widgetConfig)
    analytics.isInitialized.value = true
    analytics.isEnabled.value = true

    analytics.trackError('api_error', {
      error_message: 'Network timeout',
      api_endpoint: '/years'
    })

    expect(window.gtag).toHaveBeenCalledWith('event', 'widget_error',
      expect.objectContaining({
        error_type: 'api_error',
        error_message: 'Network timeout',
        api_endpoint: '/years'
      })
    )
  })

  it('should respect Do Not Track setting', async () => {
    Object.defineProperty(global.navigator, 'doNotTrack', {
      value: '1',
      configurable: true
    })

    const widgetConfig = { uuid: 'test-widget-123' }
    const analytics = useAnalytics(widgetConfig)

    // Initialize to trigger privacy check
    await analytics.initialize()

    // Should be disabled due to DNT
    expect(analytics.isEnabled.value).toBe(false)
  })

  it('should get client hostname from parent window', () => {
    const widgetConfig = { uuid: 'test-widget-123' }
    const analytics = useAnalytics(widgetConfig)

    const hostname = analytics.getClientHostname()
    expect(hostname).toBe('example.com')
  })

  it('should provide analytics status', () => {
    const widgetConfig = {
      uuid: 'test-widget-123',
      apiVersion: 'v2',
      flowType: 'primary'
    }

    const analytics = useAnalytics(widgetConfig)
    const status = analytics.getStatus()

    expect(status).toEqual({
      isEnabled: true,
      isInitialized: false,
      measurementId: 'G-XXXXXXXXXX',
      isDebugMode: false,
      widgetContext: expect.objectContaining({
        widget_uuid: 'test-widget-123',
        widget_type: 'finder-v2',
        api_version: 'v2',
        flow_type: 'primary'
      })
    })
  })
})