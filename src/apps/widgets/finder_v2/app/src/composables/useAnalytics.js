import { ref, computed, onMounted, onUnmounted } from 'vue'

/**
 * Google Analytics 4 integration composable for finder-v2 widget
 * 
 * This composable provides GA4 tracking functionality with:
 * - Non-blocking analytics loading
 * - Cross-domain iframe tracking
 * - Privacy compliance features
 * - Error handling and graceful degradation
 * - Widget-specific event tracking
 * 
 * @param {Object} widgetConfig - Widget configuration object
 * @returns {Object} Analytics tracking methods and state
 */
export function useAnalytics(widgetConfig = {}) {
  // Analytics state
  const isEnabled = ref(true)
  const isInitialized = ref(false)
  const measurementId = ref('G-7HHLF4RGTD') // To be configured with actual GA4 property ID
  const isDebugMode = ref(false)
  const sessionStartTime = ref(Date.now())
  
  // Widget context data
  const widgetContext = computed(() => ({
    widget_uuid: widgetConfig.uuid || widgetConfig.id || widgetConfig.widgetUuid || 'unknown',
    widget_type: 'finder-v2',
    client_hostname: getClientHostname(),
    api_version: widgetConfig.apiVersion || 'v2',
    flow_type: widgetConfig.flowType || 'primary',
    theme_name: widgetConfig.theme?.name || 'default',
    widget_width: widgetConfig.width || 'auto',
    widget_height: widgetConfig.height || 'auto',
    subscription_paid: widgetConfig.subscriptionPaid || false,
    user_profile_uuid: widgetConfig.userProfileUuid || ''
  }))

  /**
   * Initialize GA4 analytics
   * Loads gtag script and configures GA4 with widget-specific settings
   */
  const initialize = async () => {
    if (isInitialized.value || !isEnabled.value) {
      // console.log('GA4 Analytics already initialized or disabled')
      return
    }

    try {
      // Check for privacy settings
      if (!checkPrivacyConsent()) {
        // console.log('GA4 Analytics disabled due to privacy settings')
        isEnabled.value = false
        return
      }

      // Determine measurement ID from config or use default
      const configMeasurementId = widgetConfig.analytics?.measurementId || 
                                   widgetConfig.ga4MeasurementId ||
                                   window.FinderV2Config?.analytics?.measurementId
      
      if (configMeasurementId && configMeasurementId !== 'G-XXXXXXXXXX') {
        measurementId.value = configMeasurementId
      }

      // Set debug mode for development
      isDebugMode.value = window.location.hostname === 'development.local' || 
                         window.location.hostname === 'localhost' ||
                         widgetConfig.analytics?.debugMode === true

      // console.log('Initializing GA4 Analytics for finder-v2 widget:', {
      //   measurementId: measurementId.value,
      //   debugMode: isDebugMode.value,
      //   widgetUuid: widgetContext.value.widget_uuid
      // })

      // Load gtag script asynchronously
      await loadGtagScript()

      // Initialize gtag
      initializeGtag()

      isInitialized.value = true
      // console.log('GA4 Analytics initialized successfully')

      // Track widget load event
      trackEvent('widget_load', {
        ...widgetContext.value,
        page_url: getClientPageUrl(),
        referrer: document.referrer || '',
        timestamp: Date.now()
      })

    } catch (error) {
      // console.warn('Failed to initialize GA4 analytics:', error)
      isEnabled.value = false
    }
  }

  /**
   * Load Google Analytics gtag script
   */
  const loadGtagScript = () => {
    return new Promise((resolve, reject) => {
      // Check if gtag is already loaded
      if (window.gtag) {
        resolve()
        return
      }

      const script = document.createElement('script')
      script.async = true
      script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId.value}`
      
      script.onload = () => resolve()
      script.onerror = () => reject(new Error('Failed to load gtag script'))
      
      document.head.appendChild(script)
    })
  }

  /**
   * Initialize gtag with GA4 configuration
   */
  const initializeGtag = () => {
    // Initialize dataLayer
    window.dataLayer = window.dataLayer || []
    window.gtag = window.gtag || function() { window.dataLayer.push(arguments) }

    // Configure gtag
    gtag('js', new Date())
    
    // GA4 configuration
    const config = {
      // Cross-domain tracking for iframe embedding
      linker: {
        domains: ['wheel-size.com', 'services.wheel-size.com', 'development.local']
      },
      // Privacy settings
      anonymize_ip: true,
      allow_google_signals: false,
      allow_ad_personalization_signals: false,
      // Custom parameters mapping
      custom_map: {
        'custom_parameter_1': 'widget_uuid',
        'custom_parameter_2': 'user_profile_uuid', 
        'custom_parameter_3': 'client_hostname',
        'custom_parameter_4': 'widget_type',
        'custom_parameter_5': 'flow_type'
      },
      // Debug mode for development
      debug_mode: isDebugMode.value,
      // Disable automatic page view for widget context
      send_page_view: false
    }

    gtag('config', measurementId.value, config)

    if (isDebugMode.value) {
      // console.log('GA4 configured with settings:', config)
    }
  }

  /**
   * Track custom event with widget context
   * @param {string} eventName - GA4 event name
   * @param {Object} parameters - Event parameters
   */
  const trackEvent = (eventName, parameters = {}) => {
    if (!isInitialized.value || !isEnabled.value || typeof window.gtag !== 'function') {
      if (isDebugMode.value) {
        // console.log('GA4 tracking skipped (not initialized):', eventName, parameters)
      }
      return
    }

    try {
      // Merge widget context with event parameters
      const eventData = {
        ...parameters,
        // Core widget context
        widget_uuid: widgetContext.value.widget_uuid,
        widget_type: widgetContext.value.widget_type,
        client_hostname: widgetContext.value.client_hostname,
        flow_type: widgetContext.value.flow_type,
        api_version: widgetContext.value.api_version,
        // Session context
        session_duration: Date.now() - sessionStartTime.value,
        timestamp: Date.now()
      }

      // Remove undefined/null values
      Object.keys(eventData).forEach(key => {
        if (eventData[key] === undefined || eventData[key] === null || eventData[key] === '') {
          delete eventData[key]
        }
      })

      // Track the event
      gtag('event', eventName, eventData)

      if (isDebugMode.value) {
        // console.log('GA4 Event tracked:', eventName, eventData)
      }

    } catch (error) {
      // console.warn('Failed to track GA4 event:', error)
    }
  }

  /**
   * Track widget interaction events
   * @param {string} interactionType - Type of interaction
   * @param {Object} data - Additional interaction data
   */
  const trackInteraction = (interactionType, data = {}) => {
    trackEvent('widget_interaction', {
      interaction_type: interactionType,
      ...data
    })
  }

  /**
   * Track search events
   * @param {string} searchType - Type of search performed
   * @param {Object} searchData - Search parameters and results
   */
  const trackSearch = (searchType, searchData = {}) => {
    trackEvent('widget_search', {
      search_type: searchType,
      search_flow_type: widgetContext.value.flow_type,
      ...searchData
    })
  }

  /**
   * Track error events
   * @param {string} errorType - Category of error
   * @param {Object} errorData - Error details
   */
  const trackError = (errorType, errorData = {}) => {
    trackEvent('widget_error', {
      error_type: errorType,
      widget_state: errorData.widgetState || 'unknown',
      ...errorData
    })
  }

  /**
   * Track performance metrics
   * @param {string} metricType - Type of performance metric
   * @param {Object} performanceData - Performance measurements
   */
  const trackPerformance = (metricType, performanceData = {}) => {
    trackEvent('widget_performance', {
      metric_type: metricType,
      ...performanceData
    })
  }

  /**
   * Get client hostname from parent window or referrer
   * Handles cross-origin restrictions gracefully
   */
  const getClientHostname = () => {
    try {
      // Try to get hostname from parent window (same-origin)
      if (window.parent && window.parent !== window && window.parent.location) {
        return window.parent.location.hostname
      }
      return window.location.hostname
    } catch (error) {
      // Cross-origin restriction, use referrer
      try {
        const referrer = document.referrer
        if (referrer) {
          const url = new URL(referrer)
          return url.hostname
        }
      } catch (e) {
        // console.warn('Unable to determine client hostname', e)
      }
      return 'unknown'
    }
  }

  /**
   * Get client page URL
   */
  const getClientPageUrl = () => {
    try {
      if (window.parent && window.parent !== window && window.parent.location) {
        return window.parent.location.href
      }
      return document.referrer || window.location.href
    } catch (error) {
      return document.referrer || 'unknown'
    }
  }

  /**
   * Check privacy consent and Do Not Track settings
   */
  const checkPrivacyConsent = () => {
    // Check Do Not Track header
    if (navigator.doNotTrack === '1' || navigator.doNotTrack === 'yes') {
      // console.log('Analytics disabled due to Do Not Track setting')
      return false
    }

    // Check widget-specific opt-out
    try {
      const optOut = localStorage.getItem('ws_widget_analytics_opt_out')
      if (optOut === 'true') {
        // console.log('Analytics disabled due to widget opt-out setting')
        return false
      }
    } catch (error) {
      // localStorage might not be available in some contexts
      // console.warn('Could not check localStorage for analytics opt-out')
    }

    // Check if analytics is explicitly disabled in config
    if (widgetConfig.analytics?.enabled === false) {
      // console.log('Analytics disabled via widget configuration')
      return false
    }

    return true
  }

  /**
   * Disable analytics tracking
   */
  const disable = () => {
    isEnabled.value = false
    // console.log('GA4 Analytics disabled')
  }

  /**
   * Enable analytics tracking
   */
  const enable = () => {
    if (!isEnabled.value) {
      isEnabled.value = true
      if (!isInitialized.value) {
        initialize()
      }
    }
  }

  /**
   * Get current analytics status
   */
  const getStatus = () => ({
    isEnabled: isEnabled.value,
    isInitialized: isInitialized.value,
    measurementId: measurementId.value,
    isDebugMode: isDebugMode.value,
    widgetContext: widgetContext.value
  })

  // Initialize analytics on mount
  onMounted(() => {
    // Delay initialization slightly to allow widget to fully mount
    setTimeout(() => {
      initialize()
    }, 100)
  })

  // Track widget ready event when component is fully mounted
  onMounted(() => {
    setTimeout(() => {
      if (isInitialized.value) {
        trackEvent('widget_ready', {
          ...widgetContext.value,
          load_time: Date.now() - sessionStartTime.value
        })
      }
    }, 500)
  })

  // Cleanup on unmount
  onUnmounted(() => {
    if (isInitialized.value) {
      trackEvent('widget_unload', {
        session_duration: Date.now() - sessionStartTime.value
      })
    }
  })

  return {
    // State
    isEnabled,
    isInitialized,
    isDebugMode,
    
    // Methods
    initialize,
    trackEvent,
    trackInteraction,
    trackSearch,
    trackError,
    trackPerformance,
    disable,
    enable,
    getStatus,
    
    // Utility methods
    getClientHostname,
    getClientPageUrl
  }
}