import { ref, onMounted, onUnmounted } from 'vue'

/**
 * Cross-domain tracking composable for finder-v2 widget
 * 
 * Handles communication with parent window to gather analytics context
 * when widget is embedded in iframe across different domains
 * 
 * @returns {Object} Cross-domain tracking utilities and parent window info
 */
export function useCrossDomainTracking() {
  // Parent window information
  const parentHostname = ref('unknown')
  const parentUrl = ref('')
  const parentTitle = ref('')
  const isInIframe = ref(false)
  const communicationEstablished = ref(false)
  
  // Message listener reference for cleanup
  let messageListener = null
  
  /**
   * Initialize cross-domain tracking
   */
  const initialize = () => {
    // Detect if widget is running in iframe
    isInIframe.value = window.parent && window.parent !== window
    
    if (isInIframe.value) {
      // console.log('Widget detected in iframe, setting up cross-domain tracking')
      setupParentCommunication()
    } else {
      // Widget is running directly, use current window info
      parentHostname.value = window.location.hostname
      parentUrl.value = window.location.href
      parentTitle.value = document.title
      communicationEstablished.value = true
    }
  }
  
  /**
   * Set up communication with parent window
   */
  const setupParentCommunication = () => {
    // First try direct access (same-origin)
    tryDirectParentAccess()
    
    // Set up postMessage communication for cross-origin scenarios
    setupPostMessageCommunication()
    
    // Request parent information
    requestParentInfo()
  }
  
  /**
   * Try to access parent window directly (same-origin)
   */
  const tryDirectParentAccess = () => {
    try {
      if (window.parent && window.parent.location) {
        parentHostname.value = window.parent.location.hostname
        parentUrl.value = window.parent.location.href
        parentTitle.value = window.parent.document.title
        communicationEstablished.value = true
        // console.log('Direct parent access successful:', {
        //   hostname: parentHostname.value,
        //   url: parentUrl.value
        // })
        return true
      }
    } catch (error) {
      // Expected for cross-origin scenarios
      // console.log('Direct parent access blocked (cross-origin):', error.message)
    }
    return false
  }
  
  /**
   * Set up postMessage communication with parent window
   */
  const setupPostMessageCommunication = () => {
    messageListener = (event) => {
      handleParentMessage(event)
    }
    
    window.addEventListener('message', messageListener)
  }
  
  /**
   * Request parent window information via postMessage
   */
  const requestParentInfo = () => {
    if (!isInIframe.value) return
    
    const widgetId = getWidgetId()
    const requestData = {
      type: 'widget_request_parent_info',
      widgetId: widgetId,
      timestamp: Date.now(),
      source: 'finder-v2-widget'
    }
    
    // Send request to parent window
    try {
      window.parent.postMessage(requestData, '*')
      // console.log('Requested parent info via postMessage:', requestData)
      
      // Fallback: if no response within timeout, use referrer
      setTimeout(() => {
        if (!communicationEstablished.value) {
          useReferrerFallback()
        }
      }, 2000) // 2 second timeout
      
    } catch (error) {
      // console.warn('Failed to send parent info request:', error)
      useReferrerFallback()
    }
  }
  
  /**
   * Handle messages from parent window
   */
  const handleParentMessage = (event) => {
    try {
      const data = event.data
      
      if (data.type === 'widget_parent_info') {
        // Validate the response is for this widget
        const widgetId = getWidgetId()
        if (data.widgetId && data.widgetId !== widgetId) {
          return // Response is for different widget
        }
        
        parentHostname.value = data.hostname || 'unknown'
        parentUrl.value = data.url || ''
        parentTitle.value = data.title || ''
        communicationEstablished.value = true
        
        // console.log('Received parent info via postMessage:', {
        //   hostname: parentHostname.value,
        //   url: parentUrl.value,
        //   title: parentTitle.value
        // })
      }
      
      // Handle other message types if needed
      else if (data.type === 'widget_analytics_config') {
        // Parent can send analytics configuration updates
        handleAnalyticsConfigUpdate(data)
      }
      
    } catch (error) {
      // console.warn('Error handling parent message:', error)
    }
  }
  
  /**
   * Use document referrer as fallback for parent info
   */
  const useReferrerFallback = () => {
    try {
      const referrer = document.referrer
      if (referrer) {
        const url = new URL(referrer)
        parentHostname.value = url.hostname
        parentUrl.value = referrer
        communicationEstablished.value = true
        
        // console.log('Using referrer as parent info fallback:', {
        //   hostname: parentHostname.value,
        //   url: parentUrl.value
        // })
      } else {
        // console.warn('No referrer available for parent info')
      }
    } catch (error) {
      // console.warn('Failed to parse referrer for parent info:', error)
    }
  }
  
  /**
   * Get widget ID from configuration
   */
  const getWidgetId = () => {
    const config = window.FinderV2Config || {}
    return config.uuid || config.id || config.widgetUuid || 'unknown'
  }
  
  /**
   * Handle analytics configuration updates from parent
   */
  const handleAnalyticsConfigUpdate = (data) => {
    // console.log('Received analytics config update from parent:', data)
    
    // Emit custom event for analytics composable to handle
    window.dispatchEvent(new CustomEvent('parentAnalyticsConfig', {
      detail: data.config
    }))
  }
  
  /**
   * Send analytics event to parent window
   * Useful for parent-level tracking integration
   */
  const notifyParentAnalyticsEvent = (eventName, eventData) => {
    if (!isInIframe.value) return
    
    const message = {
      type: 'widget_analytics_event',
      widgetId: getWidgetId(),
      eventName: eventName,
      eventData: eventData,
      timestamp: Date.now(),
      source: 'finder-v2-widget'
    }
    
    try {
      window.parent.postMessage(message, '*')
      // console.log('Notified parent of analytics event:', message)
    } catch (error) {
      // console.warn('Failed to notify parent of analytics event:', error)
    }
  }
  
  /**
   * Request analytics configuration from parent
   */
  const requestAnalyticsConfig = () => {
    if (!isInIframe.value) return
    
    const request = {
      type: 'widget_request_analytics_config',
      widgetId: getWidgetId(),
      timestamp: Date.now(),
      source: 'finder-v2-widget'
    }
    
    try {
      window.parent.postMessage(request, '*')
      // console.log('Requested analytics config from parent:', request)
    } catch (error) {
      // console.warn('Failed to request analytics config from parent:', error)
    }
  }
  
  /**
   * Get parent context data for analytics
   */
  const getParentContextData = () => {
    return {
      parent_hostname: parentHostname.value,
      parent_url: parentUrl.value,
      parent_title: parentTitle.value,
      is_in_iframe: isInIframe.value,
      communication_established: communicationEstablished.value
    }
  }
  
  /**
   * Cleanup event listeners
   */
  const cleanup = () => {
    if (messageListener) {
      window.removeEventListener('message', messageListener)
      messageListener = null
    }
  }
  
  // Initialize on mount
  onMounted(() => {
    initialize()
  })
  
  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })
  
  return {
    // State
    parentHostname,
    parentUrl,
    parentTitle,
    isInIframe,
    communicationEstablished,
    
    // Methods
    initialize,
    requestParentInfo,
    requestAnalyticsConfig,
    notifyParentAnalyticsEvent,
    getParentContextData,
    cleanup
  }
}