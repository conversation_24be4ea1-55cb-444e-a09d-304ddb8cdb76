/**
 * useApiClient Composable
 * Provides a singleton API client instance for the application
 * Part of the finder.js store refactoring - Phase 1
 * Enhanced with selective caching for year, make, model, generation endpoints
 */

import { createApiClient } from '@/stores/modules/api-client.js'
import { createCachedApiClient } from '@/cache/cached-api-client.js'
import { ConfigChangeHandler } from '@/cache/config-change-handler.js'
import { localStorageManager } from '@/cache/localstorage-manager.js'

let apiClientInstance = null
let configChangeHandler = null

/**
 * Get or create an API client instance
 * @param {Object} config - Widget configuration
 * @param {Object} widgetResources - Widget resource URLs
 * @returns {ApiClient} The API client instance (cached or regular based on config)
 */
export function useApiClient(config, widgetResources) {
  // Return existing instance if already created
  if (apiClientInstance) {
    return apiClientInstance
  }

  // Create new instance
  if (config) {
    // Check if caching is enabled (default: true)
    const cacheEnabled = config.cacheEnabled !== false
    const widgetId = config.widgetId || 'default'
    
    console.log(`🎯 API Client initialization - Cache ${cacheEnabled ? 'ENABLED' : 'DISABLED'}`)
    
    if (cacheEnabled) {
      // Create cached API client with configuration
      const cacheConfig = {
        enabled: true,
        memoryMaxSize: config.cacheMemorySize || 50,
        storageMaxSize: config.cacheStorageSize || (500 * 1024), // 500KB default
        defaultTTL: config.cacheTTL || (60 * 60 * 1000), // 1 hour default
        ttls: config.cacheTTLs || {}
      }
      
      // widgetResources can be undefined initially, will be set to {} in CachedApiClient constructor
      apiClientInstance = createCachedApiClient(config, widgetResources, cacheConfig)
      
      // Set up configuration change detection
      configChangeHandler = new ConfigChangeHandler(apiClientInstance, widgetId)
      
      // Check localStorage health
      const health = localStorageManager.checkStorageHealth()
      console.log(`📊 localStorage health: ${health}`)
      
      // Preload common selector data if enabled
      if (config.preloadSelectors !== false) {
        setTimeout(() => {
          apiClientInstance.preloadSelectorData().catch(err => {
            console.warn('Failed to preload selector data:', err)
          })
        }, 100)
      }
      
      // Run initial maintenance
      setTimeout(() => {
        apiClientInstance.runMaintenance()
      }, 5000)
      
      // Set up periodic maintenance (every 5 minutes)
      setInterval(() => {
        apiClientInstance.runMaintenance()
      }, 5 * 60 * 1000)
      
    } else {
      // Use regular API client without caching
      apiClientInstance = createApiClient(config, widgetResources)
    }
    
    return apiClientInstance
  }

  throw new Error('useApiClient requires config parameter on first call')
}

/**
 * Reset the API client instance (useful for testing)
 */
export function resetApiClient() {
  if (apiClientInstance) {
    apiClientInstance.cancelAllRequests()
    
    // Clean up cache if it's a cached client
    if (apiClientInstance.clearCache) {
      apiClientInstance.clearCache()
    }
    
    apiClientInstance = null
  }
  
  // Clean up config change handler
  if (configChangeHandler) {
    configChangeHandler.destroy()
    configChangeHandler = null
  }
}

/**
 * Get the current API client instance without creating a new one
 * @returns {ApiClient|null} The current API client instance or null
 */
export function getApiClient() {
  return apiClientInstance
}

/**
 * Get cache statistics if caching is enabled
 * @returns {Object|null} Cache statistics or null if caching is disabled
 */
export function getCacheStats() {
  if (apiClientInstance && apiClientInstance.getCacheStats) {
    return apiClientInstance.getCacheStats()
  }
  return null
}

/**
 * Clear cache if caching is enabled
 */
export function clearCache() {
  if (apiClientInstance && apiClientInstance.clearCache) {
    apiClientInstance.clearCache()
    console.log('🗑️ Cache cleared via useApiClient')
  }
}

/**
 * Get localStorage usage statistics
 * @returns {Object} Storage statistics
 */
export function getStorageStats() {
  return localStorageManager.getDetailedStats()
}