import { createApp } from 'vue'
import { createPinia } from 'pinia'
import FinderV2Widget from './components/FinderV2Widget.vue'
import './styles/main.css'
import axios from 'axios'

// Debug: Add visible debug info to page
window.FinderV2Debug = {
  mainLoaded: true,
  timestamp: new Date().toISOString()
}

// Create Vue app instance
const app = createApp(FinderV2Widget)

// Add Pinia store
app.use(createPinia())

// Iframe height management for widget embedding
// Note: iframeResizer.contentWindow.js handles the actual resizing
// This function provides additional resize triggers for Vue content changes
function notifyIframeResize() {
  if (window.parent && window.parent !== window && window.parentIFrame) {
    // Use iframeResizer's built-in resize method
    window.parentIFrame.size()
  }
}

// Mount the app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Debug: Update page to show initialization status
  window.FinderV2Debug.domReady = true
  window.FinderV2Debug.config = !!window.FinderV2Config
  
  // Legacy CSRF token configuration (will be replaced by enhanced CSRF if enabled)
  // This provides initial backward compatibility until useCSRFToken composable initializes
  const legacyToken = window.FinderV2Config?.csrfToken || ''
  if (legacyToken && !window.FinderV2Config?.enhancedCSRF) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = legacyToken
    console.log('Legacy CSRF token configured')
  }

  // Global properties for widget configuration
  app.config.globalProperties.$config = window.FinderV2Config || {}

  const container = document.getElementById('finder-v2-app')
  window.FinderV2Debug.containerFound = !!container
  
  if (container) {
    try {
      console.log('🎯 About to mount Vue app on container:', container)
      const vueApp = app.mount(container)
      window.FinderV2Debug.mounted = true
      window.FinderV2Debug.mountError = null
      console.log('✅ Vue app mounted successfully!')
    } catch (error) {
      window.FinderV2Debug.mounted = false
      window.FinderV2Debug.mountError = error.message
      console.error('❌ Mount error:', error)
      
      // Show persistent error in DOM
      const errorDiv = document.createElement('div')
      errorDiv.style.cssText = 'position:fixed;top:0;left:0;background:red;color:white;padding:10px;z-index:9999'
      errorDiv.textContent = `Mount error: ${error.message}`
      document.body.appendChild(errorDiv)
    }

    // Set up iframe height management with iframeResizer
    if (window.FinderV2Config?.iframeResize) {
      // Wait for iframeResizer to initialize
      setTimeout(() => {
        if (window.parentIFrame) {
          // Initial height notification
          notifyIframeResize()

          // Watch for content changes and notify parent
          const observer = new MutationObserver(() => {
            setTimeout(notifyIframeResize, 50)
          })

          observer.observe(container, {
            childList: true,
            subtree: true,
            attributes: true
          })

          // Also notify on window resize
          window.addEventListener('resize', notifyIframeResize)
        }
      }, 100)
    }

    // Phase 1: Widget events will be initialized inside the component setup
    // Don't call useWidgetEvents() here as it needs to be in a Vue setup context
  } else {
    window.FinderV2Debug.containerNotFound = true
    
    // Show error in DOM
    const errorDiv = document.createElement('div')
    errorDiv.style.cssText = 'position:fixed;top:0;left:0;background:orange;color:white;padding:10px;z-index:9999'
    errorDiv.textContent = 'Container #finder-v2-app not found!'
    document.body.appendChild(errorDiv)
  }
})

// Export for potential external access
window.FinderV2App = app
