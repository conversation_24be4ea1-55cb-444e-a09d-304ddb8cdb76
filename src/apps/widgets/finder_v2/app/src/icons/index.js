/**
 * Centralized icon imports for Finder-v2 widget
 * This file optimizes bundle size by importing only the icons we actually use
 * instead of importing the entire @heroicons/vue library
 */

// Import specific icons from the ESM versions
import CheckIcon from '@heroicons/vue/20/solid/esm/CheckIcon.js'
import ChevronUpDownIcon from '@heroicons/vue/20/solid/esm/ChevronUpDownIcon.js'

// Re-export the icons
export { CheckIcon, ChevronUpDownIcon }

// If we need additional icons in the future, add them here:
// import XMarkIcon from '@heroicons/vue/24/outline/esm/XMarkIcon.js'
// import MagnifyingGlassIcon from '@heroicons/vue/24/outline/esm/MagnifyingGlassIcon.js'
// export { XMarkIcon, MagnifyingGlassIcon }