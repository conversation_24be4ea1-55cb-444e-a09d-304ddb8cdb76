<template>
  <div class="results-display">
    <div v-if="loadingResults" class="loading">
      <svg class="spinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-2">{{ translation.loading_results || 'Loading results...' }}</span>
    </div>

    <div v-else-if="results.length === 0" class="no-results">
      <p class="no-results-text">{{ translation.no_results || 'No results found. Please try different search criteria.' }}</p>
    </div>

    <div v-else class="results-content">
      <!-- Custom template rendering -->
      <div v-if="hasCustomTemplate" class="custom-results">
        <div v-for="(result, index) in results" :key="index" 
             v-html="renderCustomTemplate(result)">
        </div>
      </div>
      
      <!-- Fallback to default template design -->
      <div v-else class="default-results">
        <div class="space-y-4" v-for="(result, index) in results" :key="index">
          <h3 class="text-lg font-semibold mb-2">
            {{ result.make.name }} {{ result.model.name }} ({{ result.start_year }}-{{ result.end_year }})
          </h3>
          <div 
            v-for="(wheel, wheelIndex) in result.wheels" 
            :key="wheelIndex"
            class="p-3 theme-rounded-md border"
            :class="wheel.is_stock ? 'border-indigo-400 bg-indigo-50' : 'border-gray-300'"
          >
            <div class="font-medium uppercase tracking-wide text-sm mb-1">
              {{ wheel.is_stock ? 'OE option1' : 'After-market option1' }}
            </div>
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span class="text-gray-500">Front:</span>
                {{ wheel.front.tire }} – {{ wheel.front.rim }}
              </div>
              <div v-if="!wheel.showing_fp_only && wheel.rear.tire">
                <span class="text-gray-500">Rear:</span>
                {{ wheel.rear.tire }} – {{ wheel.rear.rim }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Show "See on Wheel-Size.com" button if configured -->
      <div v-if="showWheelSizeButton" class="wheel-size-button-container">
        <a
          :href="wheelSizeUrl"
          target="_blank"
          rel="noopener noreferrer"
          class="inline-block rounded-md bg-white px-3.5 py-2 text-sm font-semibold text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50"
        >
          {{ translation.search_button || 'Unlock More Insights at Wheel-Size.com' }}
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/finder'
import { renderTemplate } from '../utils/templateEngine'

export default {
  name: 'ResultsDisplay',
  props: {
    translation: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const finderStore = useFinderStore()

    const { loadingResults, results, config, outputTemplate } = storeToRefs(finderStore)

    const hasCustomTemplate = computed(() => {
      return outputTemplate.value && outputTemplate.value.trim().length > 0
    })

    const renderCustomTemplate = (result) => {
      if (!hasCustomTemplate.value) return ''
      return renderTemplate(outputTemplate.value, result)
    }

    const subscriptionPaid = computed(() => {
      return !!(config.value.subscriptionPaid || config.value.widgetConfig?.subscriptionPaid)
    })

    const buttonHideSetting = computed(() => {
      // Prefer interface.blocks if provided; fallback to widgetConfig.blocks for dev playground
      const interfaceHide = config.value.interface?.blocks?.button_to_ws?.hide
      const widgetConfigHide = config.value.widgetConfig?.blocks?.button_to_ws?.hide
      return interfaceHide !== undefined ? interfaceHide : !!widgetConfigHide
    })

    const showWheelSizeButton = computed(() => {
      // Business rule: never show for paid widgets; otherwise respect hide setting
      if (subscriptionPaid.value) return false
      return !buttonHideSetting.value
    })

    const wheelSizeUrl = computed(() => {
      const utm = config.value.utm || config.value.widgetConfig?.utm || ''
      return `https://www.wheel-size.com${utm}`
    })

    return {
      loadingResults,
      results,
      hasCustomTemplate,
      renderCustomTemplate,
      showWheelSizeButton,
      wheelSizeUrl,
      translation: props.translation
    }
  }
}
</script>

<style scoped>
.results-display {
  width: 100%;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  color: #6b7280;
}

.spinner {
  animation: spin 1s linear infinite;
  height: 1.25rem;
  width: 1.25rem;
  color: var(--color-ws-primary-600);
}

.no-results {
  padding: 2rem 0;
}

.no-results-text {
  color: #6b7280;
  text-align: center;
}

.results-content {
  width: 100%;
}

.wheel-size-button-container {
  margin-top: 1.5rem;
  text-align: center;
}
</style>
