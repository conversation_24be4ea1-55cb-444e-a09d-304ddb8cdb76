<template>
  <div v-if="!ListboxComponent" class="loading-placeholder">
    <div class="selector-skeleton">
      <div class="skeleton-animation"></div>
    </div>
  </div>
  <component 
    v-else 
    :is="CustomSelectorComponent" 
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script>
import { defineAsyncComponent, ref, onMounted } from 'vue'

export default {
  name: 'CustomSelectorLazy',
  setup() {
    const ListboxComponent = ref(null)
    const CustomSelectorComponent = ref(null)

    onMounted(async () => {
      // Lazy load the CustomSelector component
      CustomSelectorComponent.value = defineAsyncComponent(() => 
        import('./CustomSelector.vue')
      )
      ListboxComponent.value = true
    })

    return {
      ListboxComponent,
      CustomSelectorComponent
    }
  }
}
</script>

<style scoped>
.loading-placeholder {
  width: 100%;
}

.selector-skeleton {
  height: 42px;
  background: #f3f4f6;
  border-radius: 0.375rem;
  position: relative;
  overflow: hidden;
}

.skeleton-animation {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% { left: -100%; }
  100% { left: 100%; }
}
</style>