<template>
  <div class="vehicle-search">
    <!-- Search History Icon (floating button) -->
    <SearchHistoryIcon />

    <form @submit.prevent="handleSearch" class="search-form">
      <!-- Primary Flow: Year → Make → Model → Modifications -->
      <div v-if="flowType === 'primary'" class="form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Year Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.year_label || 'Year' }}</label>
          <CustomSelector
            v-model="selectedYear"
            :options="years"
            :loading="loading && !years.length"
            :preloader="loadingYears"
            :state-loaded="stateLoadedYears"
            :auto-expand="false"
            :disabled="loadingYears && years.length === 0"
            :placeholder="translation.select_year || 'Select Year'"
            @change="onYearChange"
          />
        </div>

        <!-- Make Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.make_label || 'Make' }}</label>
          <CustomSelector
            v-model="selectedMake"
            :options="makes"
            :loading="loading && selectedYear && !makes.length"
            :preloader="loadingMakes"
            :state-loaded="stateLoadedMakes"
            :disabled="!selectedYear || loadingMakes || !stateLoadedMakes"
            :placeholder="translation.select_make || 'Select Make'"
            selector-type="make"
            @change="onMakeChange"
          />
        </div>

        <!-- Model Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.model_label || 'Model' }}</label>
          <CustomSelector
            v-model="selectedModel"
            :options="models"
            :loading="loading && selectedMake && !models.length"
            :preloader="loadingModels"
            :state-loaded="stateLoadedModels"
            :disabled="!selectedMake || loadingModels || !stateLoadedModels"
            :placeholder="translation.select_model || 'Select Model'"
            @change="onModelChange"
          />
        </div>

        <!-- Modification Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.modification_label || 'Modification' }}</label>
          <CustomSelector
            v-model="selectedModification"
            :options="modifications"
            :loading="loading && selectedModel && !modifications.length"
            :preloader="loadingModifications"
            :state-loaded="stateLoadedModifications"
            :disabled="!selectedModel || loadingModifications || !stateLoadedModifications"
            :placeholder="translation.select_modification || 'Select Modification'"
          />
        </div>
      </div>

      <!-- Alternative Flow: Make → Model → Generation → Modifications -->
      <div v-else-if="flowType === 'alternative'" class="form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Make Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.make_label || 'Make' }}</label>
          <CustomSelector
            v-model="selectedMake"
            :options="makes"
            :loading="loading && !makes.length"
            :preloader="loadingMakes"
            :state-loaded="stateLoadedMakes"
            :auto-expand="false"
            :disabled="loadingMakes && makes.length === 0"
            :placeholder="translation.select_make || 'Select Make'"
            selector-type="make"
            @change="onMakeChange"
          />
        </div>

        <!-- Model Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.model_label || 'Model' }}</label>
          <CustomSelector
            v-model="selectedModel"
            :options="models"
            :loading="loading && selectedMake && !models.length"
            :preloader="loadingModels"
            :state-loaded="stateLoadedModels"
            :disabled="!selectedMake || loadingModels || !stateLoadedModels"
            :placeholder="translation.select_model || 'Select Model'"
            @change="onModelChange"
          />
        </div>

        <!-- Generation Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.generation_label || 'Generation' }}</label>
          <CustomSelector
            v-model="selectedGeneration"
            :options="generations"
            :loading="loading && selectedModel && !generations.length"
            :preloader="loadingGenerations"
            :state-loaded="stateLoadedGenerations"
            :disabled="!selectedModel || loadingGenerations || !stateLoadedGenerations"
            :placeholder="translation.select_generation || 'Select Generation'"
            @change="onGenerationChange"
          />
        </div>

        <!-- Modification Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.modification_label || 'Modification' }}</label>
          <CustomSelector
            v-model="selectedModification"
            :options="modifications"
            :loading="loading && selectedGeneration && !modifications.length"
            :preloader="loadingModifications"
            :state-loaded="stateLoadedModifications"
            :disabled="!selectedGeneration || loadingModifications || !stateLoadedModifications"
            :placeholder="translation.select_modification || 'Select Modification'"
          />
        </div>
      </div>

      <!-- Year Selection Flow: Make → Model → Years → Modifications -->
      <div v-else-if="flowType === 'year_select'" class="form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Make Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.make_label || 'Make' }}</label>
          <CustomSelector
            v-model="selectedMake"
            :options="makes"
            :loading="loading && !makes.length"
            :preloader="loadingMakes"
            :state-loaded="stateLoadedMakes"
            :auto-expand="false"
            :disabled="loadingMakes && makes.length === 0"
            :placeholder="translation.select_make || 'Select Make'"
            selector-type="make"
            @change="onMakeChange"
          />
        </div>

        <!-- Model Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.model_label || 'Model' }}</label>
          <CustomSelector
            v-model="selectedModel"
            :options="models"
            :loading="loading && selectedMake && !models.length"
            :preloader="loadingModels"
            :state-loaded="stateLoadedModels"
            :disabled="!selectedMake || loadingModels || !stateLoadedModels"
            :placeholder="translation.select_model || 'Select Model'"
            @change="onModelChange"
          />
        </div>

        <!-- Year Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.year_label || 'Year' }}</label>
          <CustomSelector
            v-model="selectedYear"
            :options="years"
            :loading="loading && selectedModel && !years.length"
            :preloader="loadingYears"
            :state-loaded="stateLoadedYears"
            :disabled="!selectedModel || loadingYears || !stateLoadedYears"
            :placeholder="translation.select_year || 'Select Year'"
            @change="onYearChange"
          />
        </div>

        <!-- Modification Selection -->
        <div class="form-group">
          <label class="form-label">{{ translation.modification_label || 'Modification' }}</label>
          <CustomSelector
            v-model="selectedModification"
            :options="modifications"
            :loading="loading && selectedYear && !modifications.length"
            :preloader="loadingModifications"
            :state-loaded="stateLoadedModifications"
            :disabled="!selectedYear || loadingModifications || !stateLoadedModifications"
            :placeholder="translation.select_modification || 'Select Modification'"
          />
        </div>
      </div>

      <!-- Search Button removed: automatic search enabled -->
    </form>

    <!-- Error Display with User-Friendly Messages -->
    <div v-if="error" class="error-container">
      <div class="error-boundary-style">
        <div class="error-icon">
          <svg style="width: 32px; height: 32px; color: #ef4444;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
        <p class="error-title">{{ getErrorTitle(error) }}</p>
        <p class="error-message">{{ getErrorMessage(error) }}</p>
        <button 
          v-if="isRecoverableError(error)"
          @click="retryLastAction"
          class="retry-button"
        >
          Try Again
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, watch, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/index'
import CustomSelector from './CustomSelector.vue'
import SearchHistoryIcon from './SearchHistoryIcon.vue'

export default {
  name: 'VehicleSearch',
  components: {
    CustomSelector,
    SearchHistoryIcon
  },
  props: {
    translation: {
      type: Object,
      default: () => ({})
    }
  },
  setup() {
    console.log('🔍 VehicleSearch setup - starting')
    
    let finderStore, analytics, loading, error, selectedYear, selectedMake, selectedModel,
        selectedModification, selectedGeneration, years, makes, models, modifications,
        generations, flowType, loadingYears, loadingMakes, loadingModels, loadingGenerations,
        loadingModifications, stateLoadedYears, stateLoadedMakes, stateLoadedModels,
        stateLoadedGenerations, stateLoadedModifications
    
    try {
      finderStore = useFinderStore()
      console.log('🔍 VehicleSearch setup - finderStore:', finderStore)
      
      // Get analytics instance from store
      analytics = finderStore.getAnalytics()
      console.log('🔍 VehicleSearch setup - analytics:', analytics)

      const refs = storeToRefs(finderStore)
      console.log('🔍 VehicleSearch setup - storeToRefs result:', refs)
      
      // Destructure refs
      loading = refs.loading
      error = refs.error
      selectedYear = refs.selectedYear
      selectedMake = refs.selectedMake
      selectedModel = refs.selectedModel
      selectedModification = refs.selectedModification
      selectedGeneration = refs.selectedGeneration
      years = refs.years
      makes = refs.makes
      models = refs.models
      modifications = refs.modifications
      generations = refs.generations
      flowType = refs.flowType
      loadingYears = refs.loadingYears
      loadingMakes = refs.loadingMakes
      loadingModels = refs.loadingModels
      loadingGenerations = refs.loadingGenerations
      loadingModifications = refs.loadingModifications
      stateLoadedYears = refs.stateLoadedYears
      stateLoadedMakes = refs.stateLoadedMakes
      stateLoadedModels = refs.stateLoadedModels
      stateLoadedGenerations = refs.stateLoadedGenerations
      stateLoadedModifications = refs.stateLoadedModifications
      
      console.log('🔍 VehicleSearch setup - refs assigned')
      console.log('🔍 VehicleSearch setup - error ref:', error)
      console.log('🔍 VehicleSearch setup - error value:', error?.value)
    } catch (setupError) {
      console.error('🔍 VehicleSearch setup - ERROR:', setupError, setupError.stack)
      // Return minimal setup to prevent complete failure
      return {
        loading: ref(false),
        error: ref('Failed to initialize component'),
        selectedYear: ref(''),
        selectedMake: ref(''),
        selectedModel: ref(''),
        selectedModification: ref(''),
        selectedGeneration: ref(''),
        years: ref([]),
        makes: ref([]),
        models: ref([]),
        modifications: ref([]),
        generations: ref([]),
        flowType: ref('primary'),
        loadingYears: ref(false),
        loadingMakes: ref(false),
        loadingModels: ref(false),
        loadingGenerations: ref(false),
        loadingModifications: ref(false),
        stateLoadedYears: ref(false),
        stateLoadedMakes: ref(false),
        stateLoadedModels: ref(false),
        stateLoadedGenerations: ref(false),
        stateLoadedModifications: ref(false),
        canSearch: ref(false),
        onYearChange: () => {},
        onMakeChange: () => {},
        onModelChange: () => {},
        onGenerationChange: () => {},
        handleSearch: () => {},
        getErrorTitle: () => 'Component Error',
        getErrorMessage: () => 'Failed to initialize component',
        isRecoverableError: () => false,
        retryLastAction: () => {}
      }
    }

    const canSearch = computed(() => {
      if (flowType.value === 'primary') {
        return selectedYear.value && selectedMake.value && selectedModel.value && selectedModification.value
      } else if (flowType.value === 'alternative') {
        return selectedMake.value && selectedModel.value && selectedGeneration.value && selectedModification.value
      } else if (flowType.value === 'year_select') {
        return selectedMake.value && selectedModel.value && selectedYear.value && selectedModification.value
      }
      return false
    })

    // Track if search was just triggered by canSearch watcher to prevent duplicate calls
    const searchTriggeredByCanSearch = ref(false)

    // Automatically trigger search when all selections are ready
    watch(canSearch, async (ready, wasReady) => {
      if (ready && !wasReady) {
        searchTriggeredByCanSearch.value = true
        await handleSearch()
        // Reset flag after a short delay to allow modification watcher to see it
        setTimeout(() => {
          searchTriggeredByCanSearch.value = false
        }, 100)
      }
    })

    // Trigger a new search whenever the user changes the Modification selector.
    // This handles the scenario where canSearch was already true (second/third change).
    // Skip if search was just triggered by canSearch watcher to prevent duplicates.
    watch(selectedModification, async (newVal, oldVal) => {
      // Track modification selection
      if (analytics && newVal && newVal !== oldVal) {
        analytics.trackInteraction('modification_selected', {
          selected_modification: newVal,
          selected_make: selectedMake.value,
          selected_model: selectedModel.value,
          selected_year: selectedYear.value,
          selected_generation: selectedGeneration.value,
          flow_step: 4,
          flow_type: flowType.value
        })
      }
      
      if (newVal && newVal !== oldVal && !loading.value && !searchTriggeredByCanSearch.value) {
        await handleSearch()
      }
    })

    async function onYearChange() {
      // Track user interaction
      if (analytics && selectedYear.value) {
        analytics.trackInteraction('year_selected', {
          selected_year: selectedYear.value,
          flow_step: flowType.value === 'primary' ? 1 : 3,
          flow_type: flowType.value
        })
      }
      
      if (flowType.value === 'primary') {
        // Primary flow: Year is first selector, clear everything and load makes
        finderStore.selectedMake = ''
        finderStore.selectedModel = ''
        finderStore.selectedModification = ''
        finderStore.models = []
        finderStore.modifications = []
        
        // Reset stateLoaded flags for dependent selectors
        finderStore.stateLoadedMakes = false
        finderStore.stateLoadedModels = false
        finderStore.stateLoadedModifications = false

        if (selectedYear.value) {
          await finderStore.loadMakes(selectedYear.value)
        }
      } else if (flowType.value === 'year_select') {
        // Year selection flow: Year is third selector, only clear modifications and load them
        finderStore.selectedModification = ''
        finderStore.modifications = []
        
        // Reset stateLoaded flag for dependent selector
        finderStore.stateLoadedModifications = false

        if (selectedYear.value) {
          await finderStore.loadModifications(selectedMake.value, selectedModel.value, selectedYear.value)
        }
      }
    }

    async function onMakeChange() {
      // Track user interaction
      if (analytics && selectedMake.value) {
        analytics.trackInteraction('make_selected', {
          selected_make: selectedMake.value,
          flow_step: flowType.value === 'primary' ? 2 : 1,
          flow_type: flowType.value
        })
      }
      
      finderStore.selectedModel = ''
      finderStore.selectedModification = ''
      finderStore.selectedGeneration = ''
      finderStore.models = []
      finderStore.modifications = []
      finderStore.generations = []
      
      // Reset stateLoaded flags for dependent selectors
      finderStore.stateLoadedModels = false
      finderStore.stateLoadedModifications = false
      finderStore.stateLoadedGenerations = false

      if (selectedMake.value) {
        if (flowType.value === 'primary') {
          await finderStore.loadModels(selectedMake.value, selectedYear.value)
        } else if (flowType.value === 'alternative' || flowType.value === 'year_select') {
          await finderStore.loadModels(selectedMake.value)
        }
      }
    }

    async function onModelChange() {
      // Track user interaction
      if (analytics && selectedModel.value) {
        analytics.trackInteraction('model_selected', {
          selected_model: selectedModel.value,
          selected_make: selectedMake.value,
          flow_step: flowType.value === 'primary' ? 3 : 2,
          flow_type: flowType.value
        })
      }
      
      finderStore.selectedModification = ''
      finderStore.selectedGeneration = ''
      finderStore.modifications = []
      finderStore.generations = []
      
      // Reset stateLoaded flags for dependent selectors
      finderStore.stateLoadedModifications = false
      finderStore.stateLoadedGenerations = false

      // Only clear year and years array for flows where year comes after model selection
      if (flowType.value === 'year_select') {
        finderStore.selectedYear = ''
        finderStore.years = []
        finderStore.stateLoadedYears = false
      }

      if (selectedModel.value) {
        if (flowType.value === 'primary') {
          // In primary flow, year is already selected, so use it for modifications
          await finderStore.loadModifications(selectedMake.value, selectedModel.value, selectedYear.value)
        } else if (flowType.value === 'alternative') {
          await finderStore.loadGenerations(selectedMake.value, selectedModel.value)
        } else if (flowType.value === 'year_select') {
          // In year_select flow, load years for the selected make/model combination
          await finderStore.loadYears(selectedMake.value, selectedModel.value)
        }
      }
    }

    async function onGenerationChange() {
      // Track user interaction
      if (analytics && selectedGeneration.value) {
        analytics.trackInteraction('generation_selected', {
          selected_generation: selectedGeneration.value,
          selected_make: selectedMake.value,
          selected_model: selectedModel.value,
          flow_step: 3,
          flow_type: flowType.value
        })
      }
      
      finderStore.selectedModification = ''
      finderStore.modifications = []
      
      // Reset stateLoaded flag for dependent selector
      finderStore.stateLoadedModifications = false

      if (selectedGeneration.value) {
        await finderStore.loadModifications(selectedMake.value, selectedModel.value, selectedGeneration.value)
      }
    }

    async function handleSearch() {
      if (canSearch.value) {
        await finderStore.searchByVehicle()
      }
    }

    // Error handling helper methods
    const getErrorTitle = (errorMessage) => {
      if (!errorMessage) return ''
      
      const message = errorMessage.toLowerCase()
      
      if (message.includes('network') || message.includes('failed to fetch')) {
        return 'Connection Problem'
      }
      if (message.includes('timeout')) {
        return 'Request Timeout'
      }
      if (message.includes('404') || message.includes('not found')) {
        return 'Not Found'
      }
      if (message.includes('500') || message.includes('server')) {
        return 'Server Error'
      }
      
      return 'Something Went Wrong'
    }
    
    const getErrorMessage = (errorMessage) => {
      if (!errorMessage) return ''
      
      const message = errorMessage.toLowerCase()
      
      if (message.includes('network') || message.includes('failed to fetch') || message.includes('err_internet_disconnected')) {
        return 'Unable to connect. Please check your internet connection and try again.'
      }
      if (message.includes('timeout')) {
        return 'The request took too long. Please try again.'
      }
      if (message.includes('404') || message.includes('not found')) {
        return 'The requested resource was not found.'
      }
      if (message.includes('500') || message.includes('server')) {
        return 'Service temporarily unavailable. Please try again later.'
      }
      
      return 'We encountered an error. Please try again.'
    }
    
    const isRecoverableError = (errorMessage) => {
      if (!errorMessage) return false
      
      const message = errorMessage.toLowerCase()
      const nonRecoverable = ['permission', 'forbidden', 'unauthorized', 'invalid']
      
      return !nonRecoverable.some(term => message.includes(term))
    }
    
    const retryLastAction = async () => {
      // Clear the error
      finderStore.clearError()
      
      // Determine what to retry based on the current flow and selections
      if (flowType.value === 'primary') {
        // Primary flow: Year → Make → Model → Modifications
        if (!selectedYear.value || years.value.length === 0) {
          // Load years if not loaded
          await finderStore.loadYears()
        } else if (selectedYear.value && !selectedMake.value) {
          // Load makes for selected year
          await finderStore.loadMakes(selectedYear.value)
        } else if (selectedMake.value && !selectedModel.value) {
          // Load models for selected make and year
          await finderStore.loadModels(selectedMake.value, selectedYear.value)
        } else if (selectedModel.value && !selectedModification.value) {
          // Load modifications
          await finderStore.loadModifications(selectedMake.value, selectedModel.value, selectedYear.value)
        } else if (selectedModification.value) {
          // All selected, retry search
          await finderStore.searchByVehicle()
        }
      } else if (flowType.value === 'alternative') {
        // Alternative flow: Make → Model → Generation → Modifications
        if (!selectedMake.value || makes.value.length === 0) {
          // Load makes if not loaded
          await finderStore.loadMakes()
        } else if (selectedMake.value && !selectedModel.value) {
          // Load models for selected make
          await finderStore.loadModels(selectedMake.value)
        } else if (selectedModel.value && !selectedGeneration.value) {
          // Load generations
          await finderStore.loadGenerations(selectedMake.value, selectedModel.value)
        } else if (selectedGeneration.value && !selectedModification.value) {
          // Load modifications
          await finderStore.loadModifications(selectedMake.value, selectedModel.value, selectedGeneration.value)
        } else if (selectedModification.value) {
          // All selected, retry search
          await finderStore.searchByVehicle()
        }
      } else if (flowType.value === 'year_select') {
        // Year selection flow: Make → Model → Year → Modifications
        if (!selectedMake.value || makes.value.length === 0) {
          // Load makes if not loaded
          await finderStore.loadMakes()
        } else if (selectedMake.value && !selectedModel.value) {
          // Load models for selected make
          await finderStore.loadModels(selectedMake.value)
        } else if (selectedModel.value && !selectedYear.value) {
          // Load years for make/model
          await finderStore.loadYears(selectedMake.value, selectedModel.value)
        } else if (selectedYear.value && !selectedModification.value) {
          // Load modifications
          await finderStore.loadModifications(selectedMake.value, selectedModel.value, selectedYear.value)
        } else if (selectedModification.value) {
          // All selected, retry search
          await finderStore.searchByVehicle()
        }
      }
    }

    return {
      loading,
      error,
      selectedYear,
      selectedMake,
      selectedModel,
      selectedModification,
      selectedGeneration,
      years,
      makes,
      models,
      modifications,
      generations,
      flowType,
      // Individual loading states
      loadingYears,
      loadingMakes,
      loadingModels,
      loadingGenerations,
      loadingModifications,
      // State loaded flags
      stateLoadedYears,
      stateLoadedMakes,
      stateLoadedModels,
      stateLoadedGenerations,
      stateLoadedModifications,
      canSearch,
      onYearChange,
      onMakeChange,
      onModelChange,
      onGenerationChange,
      handleSearch,
      // Error handling methods
      getErrorTitle,
      getErrorMessage,
      isRecoverableError,
      retryLastAction
    }
  }
}
</script>

<style scoped>
.vehicle-search {
  position: relative;
  width: 100%;
}

.search-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 1rem;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .form-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.error-container {
  margin-top: 1rem;
  padding: 1.5rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
}

.error-boundary-style {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
}

.error-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #991b1b;
  margin: 0 0 0.5rem 0;
}

.error-message {
  color: #7f1d1d;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.retry-button {
  background-color: #3b82f6;
  color: white;
  padding: 0.5rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #2563eb;
}

.retry-button:active {
  background-color: #1d4ed8;
}

/* Search button removed; spinner classes obsolete */
</style>
