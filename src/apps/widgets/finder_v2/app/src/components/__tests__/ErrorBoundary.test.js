import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import ErrorBoundary from '../ErrorBoundary.vue'

describe('ErrorBoundary', () => {
  beforeEach(() => {
    // Clear any console mocks
    vi.clearAllMocks()
  })

  it('renders slot content when no error', () => {
    const wrapper = mount(ErrorBoundary, {
      slots: {
        default: '<div>Normal Content</div>'
      }
    })

    expect(wrapper.text()).toContain('Normal Content')
    expect(wrapper.find('.error-boundary').exists()).toBe(false)
  })

  it('catches and displays child component errors', async () => {
    const ThrowError = {
      setup() {
        throw new Error('Test error from component')
      },
      template: '<div>Should not render</div>'
    }

    const wrapper = mount(ErrorBoundary, {
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(wrapper.find('.error-boundary').exists()).toBe(true)
    expect(wrapper.text()).toContain('Oops! Something went wrong')
    expect(wrapper.text()).toContain('Something went wrong. Please try again.')
  })

  it('shows custom error title and message', async () => {
    const ThrowError = {
      setup() {
        throw new Error('Generic error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ErrorBoundary, {
      props: {
        errorTitle: 'Custom Error Title',
        fallback: 'Custom fallback message'
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(wrapper.text()).toContain('Custom Error Title')
    expect(wrapper.text()).toContain('Custom fallback message')
  })

  it('shows retry button when canRetry is true', async () => {
    const ThrowError = {
      setup() {
        throw new Error('Test error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ErrorBoundary, {
      props: {
        canRetry: true
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    const retryButton = wrapper.find('.retry-button')
    expect(retryButton.exists()).toBe(true)
    expect(retryButton.text()).toBe('Try Again')
  })

  it('hides retry button when canRetry is false', async () => {
    const ThrowError = {
      setup() {
        throw new Error('Test error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ErrorBoundary, {
      props: {
        canRetry: false
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(wrapper.find('.retry-button').exists()).toBe(false)
  })

  it('emits retry event when retry button is clicked', async () => {
    const ThrowError = {
      setup() {
        throw new Error('Test error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ErrorBoundary, {
      props: {
        canRetry: true
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    await wrapper.find('.retry-button').trigger('click')

    expect(wrapper.emitted('retry')).toBeTruthy()
    expect(wrapper.emitted('retry')).toHaveLength(1)
  })

  it('resets error state on retry', async () => {
    const wrapper = mount(ErrorBoundary, {
      props: {
        canRetry: true
      }
    })

    // Manually set error state
    wrapper.vm.hasError = true
    wrapper.vm.error = new Error('Test error')
    await nextTick()

    expect(wrapper.find('.error-boundary').exists()).toBe(true)

    // Click retry
    await wrapper.find('.retry-button').trigger('click')
    await nextTick()

    expect(wrapper.vm.hasError).toBe(false)
    expect(wrapper.vm.error).toBe(null)
    expect(wrapper.find('.error-boundary').exists()).toBe(false)
  })

  it('calls onError prop when error is caught', async () => {
    const onError = vi.fn()
    const testError = new Error('Test error')

    const ThrowError = {
      setup() {
        throw testError
      },
      template: '<div>Content</div>'
    }

    mount(ErrorBoundary, {
      props: {
        onError
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(onError).toHaveBeenCalledWith(
      testError,
      expect.anything(),
      expect.anything()
    )
  })

  it('emits error event with details', async () => {
    const testError = new Error('Test error')

    const ThrowError = {
      setup() {
        throw testError
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ErrorBoundary, {
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(wrapper.emitted('error')).toBeTruthy()
    expect(wrapper.emitted('error')[0][0]).toMatchObject({
      error: testError,
      instance: expect.anything(),
      info: expect.anything()
    })
  })

  it('shows user-friendly messages for known error types', async () => {
    const testCases = [
      { error: 'Network error', expected: 'Unable to connect. Please check your internet connection.' },
      { error: 'Failed to fetch', expected: 'Unable to connect to the server. Please try again later.' },
      { error: 'Timeout', expected: 'Request timed out. Please try again.' },
      { error: '404', expected: 'The requested resource was not found.' },
      { error: '500', expected: 'Server error occurred. Please try again later.' }
    ]

    for (const testCase of testCases) {
      const ThrowError = {
        setup() {
          throw new Error(testCase.error)
        },
        template: '<div>Content</div>'
      }

      const wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowError
        }
      })

      await nextTick()

      expect(wrapper.text()).toContain(testCase.expected)
      wrapper.unmount()
    }
  })

  it('shows error details in development mode', async () => {
    // Since we can't mock import.meta.env.DEV directly in tests,
    // and the component checks it at setup time, we'll test the component's
    // isDevelopment property directly
    const testError = new Error('Test error with stack')
    testError.stack = 'Error: Test error with stack\n  at TestComponent.setup'

    const ThrowError = {
      setup() {
        throw testError
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ErrorBoundary, {
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    // The component sets isDevelopment based on import.meta.env.DEV
    // In test environment, it's typically true
    if (wrapper.vm.isDevelopment) {
      const details = wrapper.find('.error-details')
      expect(details.exists()).toBe(true)
      expect(details.find('summary').text()).toBe('Error Details')
    } else {
      // In production mode, error details should not be shown
      const details = wrapper.find('.error-details')
      expect(details.exists()).toBe(false)
    }
  })

  it('prevents error propagation to parent components', async () => {
    const parentErrorHandler = vi.fn()

    const ParentComponent = {
      template: '<ErrorBoundary><ChildComponent /></ErrorBoundary>',
      components: {
        ErrorBoundary,
        ChildComponent: {
          setup() {
            throw new Error('Child error')
          },
          template: '<div>Child</div>'
        }
      },
      errorCaptured: parentErrorHandler
    }

    mount(ParentComponent)
    await nextTick()

    // Parent error handler should not be called because ErrorBoundary returns false
    expect(parentErrorHandler).not.toHaveBeenCalled()
  })
})