import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import ApiErrorBoundary from '../ApiErrorBoundary.vue'
import ErrorBoundary from '../ErrorBoundary.vue'
import { useFinderStore } from '../../stores/finder'

describe('ApiErrorBoundary', () => {
  let originalNavigatorOnline

  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia())
    
    // Save original navigator.onLine
    originalNavigatorOnline = Object.getOwnPropertyDescriptor(window.navigator, 'onLine')
    
    // Clear any console mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Restore original navigator.onLine
    if (originalNavigatorOnline) {
      Object.defineProperty(window.navigator, 'onLine', originalNavigatorOnline)
    }
  })

  it('renders slot content when no error', () => {
    const wrapper = mount(ApiErrorBoundary, {
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: '<div>API Content</div>'
      }
    })

    expect(wrapper.text()).toContain('API Content')
  })

  it('shows offline message when navigator.onLine is false', async () => {
    // Mock navigator.onLine to false
    Object.defineProperty(window.navigator, 'onLine', {
      writable: true,
      value: false
    })

    const ThrowError = {
      setup() {
        throw new Error('Network error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ApiErrorBoundary, {
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(wrapper.text()).toContain('No Internet Connection')
    expect(wrapper.text()).toContain('Please check your internet connection and try again.')
  })

  it('shows connection problem message when online', async () => {
    // Mock navigator.onLine to true
    Object.defineProperty(window.navigator, 'onLine', {
      writable: true,
      value: true
    })

    const ThrowError = {
      setup() {
        throw new Error('API error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ApiErrorBoundary, {
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(wrapper.text()).toContain('Connection Problem')
    expect(wrapper.text()).toContain('We\'re having trouble loading the data. Please try again.')
  })

  it('tracks retry attempts', async () => {
    const ThrowError = {
      setup() {
        throw new Error('API error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ApiErrorBoundary, {
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    // Initial state
    expect(wrapper.text()).toContain('Try Again')

    // Simulate retry
    const retryButton = wrapper.find('.retry-button')
    await retryButton.trigger('click')
    await nextTick()

    // Should show retry count
    expect(wrapper.vm.retryCount).toBe(1)
  })

  it('shows max retries message after 3 attempts', async () => {
    const wrapper = mount(ApiErrorBoundary, {
      global: {
        components: { ErrorBoundary }
      }
    })

    // Simulate max retries
    wrapper.vm.retryCount = 3
    await nextTick()

    expect(wrapper.vm.errorTitle).toBe('Unable to Load Data')
    expect(wrapper.vm.fallbackMessage).toContain('Please try again later or contact support')
  })

  it('calls custom retry action when provided', async () => {
    const customRetry = vi.fn().mockResolvedValue()

    const ThrowError = {
      setup() {
        throw new Error('API error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ApiErrorBoundary, {
      props: {
        retryAction: customRetry,
        componentName: 'TestComponent'
      },
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    const retryButton = wrapper.find('.retry-button')
    await retryButton.trigger('click')

    expect(customRetry).toHaveBeenCalled()
  })

  it('calls store loadInitialData when no custom retry action', async () => {
    const store = useFinderStore()
    store.loadInitialData = vi.fn().mockResolvedValue()

    const ThrowError = {
      setup() {
        throw new Error('API error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ApiErrorBoundary, {
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    const retryButton = wrapper.find('.retry-button')
    await retryButton.trigger('click')

    expect(store.loadInitialData).toHaveBeenCalled()
  })

  it('prevents retry when already retrying', async () => {
    const customRetry = vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    )

    const ThrowError = {
      setup() {
        throw new Error('API error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ApiErrorBoundary, {
      props: {
        retryAction: customRetry
      },
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    const retryButton = wrapper.find('.retry-button')
    
    // Click twice quickly
    await retryButton.trigger('click')
    await retryButton.trigger('click')

    // Should only call once due to isRetrying flag
    expect(customRetry).toHaveBeenCalledTimes(1)
  })

  it('shows retrying state in button text', async () => {
    const customRetry = vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    )

    const ThrowError = {
      setup() {
        throw new Error('API error')
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ApiErrorBoundary, {
      props: {
        retryAction: customRetry
      },
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    // Set retrying state
    wrapper.vm.isRetrying = true
    await nextTick()

    expect(wrapper.vm.retryText).toBe('Retrying...')
  })

  it('logs API errors with context in development', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const apiError = new Error('API failed')
    apiError.config = {
      url: '/api/test',
      method: 'GET'
    }
    apiError.response = {
      status: 500,
      statusText: 'Internal Server Error'
    }

    const ThrowError = {
      setup() {
        throw apiError
      },
      template: '<div>Content</div>'
    }

    const wrapper = mount(ApiErrorBoundary, {
      props: {
        componentName: 'TestComponent'
      },
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(consoleSpy).toHaveBeenCalled()

    consoleSpy.mockRestore()
  })

  it('dispatches custom event for error monitoring', async () => {
    const eventListener = vi.fn()
    window.addEventListener('finderv2:api-error', eventListener)

    const apiError = new Error('API failed')
    apiError.config = {
      url: '/api/test',
      method: 'GET'
    }

    const ThrowError = {
      setup() {
        throw apiError
      },
      template: '<div>Content</div>'
    }

    mount(ApiErrorBoundary, {
      props: {
        componentName: 'TestComponent'
      },
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(eventListener).toHaveBeenCalled()
    const eventDetail = eventListener.mock.calls[0][0].detail
    expect(eventDetail.component).toBe('TestComponent')
    expect(eventDetail.endpoint).toBe('/api/test')

    window.removeEventListener('finderv2:api-error', eventListener)
  })

  it('tracks errors with analytics when available', async () => {
    const trackFn = vi.fn()
    window.FinderV2Config = {
      analytics: {
        track: trackFn
      }
    }

    const apiError = new Error('API failed')

    const ThrowError = {
      setup() {
        throw apiError
      },
      template: '<div>Content</div>'
    }

    mount(ApiErrorBoundary, {
      props: {
        componentName: 'TestComponent'
      },
      global: {
        components: { ErrorBoundary }
      },
      slots: {
        default: ThrowError
      }
    })

    await nextTick()

    expect(trackFn).toHaveBeenCalledWith('api_error', expect.objectContaining({
      component: 'TestComponent',
      message: 'API failed'
    }))

    delete window.FinderV2Config
  })

  it('resets retry count on successful retry', async () => {
    const customRetry = vi.fn()
      .mockRejectedValueOnce(new Error('First fail'))
      .mockResolvedValueOnce()

    const wrapper = mount(ApiErrorBoundary, {
      props: {
        retryAction: customRetry
      },
      global: {
        components: { ErrorBoundary }
      }
    })

    // First retry fails
    wrapper.vm.retryCount = 0
    await wrapper.vm.handleRetry()
    expect(wrapper.vm.retryCount).toBe(1)

    // Second retry succeeds
    await wrapper.vm.handleRetry()
    expect(wrapper.vm.retryCount).toBe(0)
  })
})