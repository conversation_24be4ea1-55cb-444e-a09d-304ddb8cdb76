<template>
  <div v-if="showMonitor" class="cache-monitor">
    <div class="cache-monitor-header">
      <h3>🎯 Cache Statistics</h3>
      <button @click="toggleDetails" class="details-toggle">
        {{ showDetails ? '−' : '+' }}
      </button>
    </div>
    
    <div class="cache-stats">
      <div class="stat-item">
        <span class="stat-label">Hit Rate:</span>
        <span class="stat-value" :class="hitRateClass">{{ stats.hitRate || '0%' }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Hits/Misses:</span>
        <span class="stat-value">{{ stats.hits || 0 }} / {{ stats.misses || 0 }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Bandwidth Saved:</span>
        <span class="stat-value success">{{ stats.bandwidthSaved || '0 B' }}</span>
      </div>
    </div>
    
    <div v-if="showDetails" class="cache-details">
      <div class="cache-section">
        <h4>📊 Storage Usage</h4>
        <div class="storage-stats">
          <div class="stat-item">
            <span class="stat-label">API Cache:</span>
            <span class="stat-value">{{ storageStats.apiCache.size }} / {{ storageStats.apiCache.limit }}</span>
            <span class="stat-percent">{{ storageStats.apiCache.utilization }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Search History:</span>
            <span class="stat-value">{{ storageStats.searchHistory.size }} / {{ storageStats.searchHistory.limit }}</span>
            <span class="stat-percent">{{ storageStats.searchHistory.utilization }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Total Used:</span>
            <span class="stat-value">{{ storageStats.summary.totalUsed }}</span>
            <span class="stat-percent">{{ storageStats.summary.utilizationPercent }}</span>
          </div>
        </div>
      </div>
      
      <div class="cache-section">
        <h4>💾 Cache Layers</h4>
        <div class="layer-stats">
          <div class="stat-item">
            <span class="stat-label">Memory Cache:</span>
            <span class="stat-value">{{ stats.memoryCache?.size || 0 }} / {{ stats.memoryCache?.maxSize || 0 }} items</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Storage Cache:</span>
            <span class="stat-value">{{ stats.storageCache?.entries || 0 }} entries</span>
            <span class="stat-percent">{{ stats.storageCache?.utilization || '0%' }}</span>
          </div>
        </div>
      </div>
      
      <div class="cache-actions">
        <button @click="refreshStats" class="action-btn refresh">
          🔄 Refresh
        </button>
        <button @click="clearCache" class="action-btn clear">
          🗑️ Clear Cache
        </button>
        <button @click="runMaintenance" class="action-btn maintenance">
          🔧 Run Maintenance
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { getCacheStats, clearCache as clearApiCache, getStorageStats, getApiClient } from '@/composables/useApiClient.js'

export default {
  name: 'CacheMonitor',
  props: {
    enabled: {
      type: Boolean,
      default: true
    },
    autoRefresh: {
      type: Boolean,
      default: true
    },
    refreshInterval: {
      type: Number,
      default: 5000 // 5 seconds
    }
  },
  setup(props) {
    const showMonitor = ref(props.enabled)
    const showDetails = ref(false)
    const stats = ref({})
    const storageStats = ref({
      summary: {},
      apiCache: {},
      searchHistory: {}
    })
    let refreshTimer = null
    
    // Computed properties
    const hitRateClass = computed(() => {
      const rate = parseFloat(stats.value.hitRate) || 0
      if (rate >= 75) return 'success'
      if (rate >= 50) return 'warning'
      return 'danger'
    })
    
    // Methods
    function toggleDetails() {
      showDetails.value = !showDetails.value
    }
    
    function refreshStats() {
      const cacheStats = getCacheStats()
      if (cacheStats) {
        stats.value = cacheStats
      }
      
      const storage = getStorageStats()
      if (storage) {
        storageStats.value = storage
      }
      
      console.log('📊 Cache stats refreshed:', {
        cache: stats.value,
        storage: storageStats.value
      })
    }
    
    function clearCache() {
      if (confirm('Are you sure you want to clear the API cache?')) {
        clearApiCache()
        setTimeout(refreshStats, 100)
      }
    }
    
    function runMaintenance() {
      const client = getApiClient()
      if (client && client.runMaintenance) {
        client.runMaintenance()
        console.log('🔧 Cache maintenance completed')
        setTimeout(refreshStats, 100)
      }
    }
    
    // Auto-refresh setup
    function startAutoRefresh() {
      if (props.autoRefresh && !refreshTimer) {
        refreshTimer = setInterval(refreshStats, props.refreshInterval)
      }
    }
    
    function stopAutoRefresh() {
      if (refreshTimer) {
        clearInterval(refreshTimer)
        refreshTimer = null
      }
    }
    
    // Lifecycle
    onMounted(() => {
      refreshStats()
      if (props.autoRefresh) {
        startAutoRefresh()
      }
    })
    
    onUnmounted(() => {
      stopAutoRefresh()
    })
    
    return {
      showMonitor,
      showDetails,
      stats,
      storageStats,
      hitRateClass,
      toggleDetails,
      refreshStats,
      clearCache,
      runMaintenance
    }
  }
}
</script>

<style scoped>
.cache-monitor {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 12px;
  max-width: 400px;
  z-index: 9999;
  font-size: 12px;
}

.cache-monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.cache-monitor-header h3 {
  margin: 0;
  font-size: 14px;
  color: #1f2937;
}

.details-toggle {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  padding: 0;
}

.cache-stats {
  display: grid;
  gap: 6px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.stat-label {
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  color: #1f2937;
  font-weight: 600;
}

.stat-value.success {
  color: #10b981;
}

.stat-value.warning {
  color: #f59e0b;
}

.stat-value.danger {
  color: #ef4444;
}

.stat-percent {
  color: #9ca3af;
  font-size: 11px;
  margin-left: 4px;
}

.cache-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.cache-section {
  margin-bottom: 12px;
}

.cache-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #4b5563;
}

.storage-stats,
.layer-stats {
  display: grid;
  gap: 4px;
}

.cache-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.action-btn {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  color: #4b5563;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.action-btn.refresh {
  color: #3b82f6;
}

.action-btn.clear {
  color: #ef4444;
}

.action-btn.maintenance {
  color: #f59e0b;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .cache-monitor {
    background: #1f2937;
    border-color: #374151;
  }
  
  .cache-monitor-header h3 {
    color: #f3f4f6;
  }
  
  .details-toggle {
    background: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
  }
  
  .stat-label {
    color: #9ca3af;
  }
  
  .stat-value {
    color: #f3f4f6;
  }
  
  .cache-section h4 {
    color: #d1d5db;
  }
  
  .cache-details {
    border-top-color: #374151;
  }
  
  .action-btn {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }
  
  .action-btn:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
}
</style>