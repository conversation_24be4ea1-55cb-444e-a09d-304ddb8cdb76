<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-container max-w-md mx-auto text-center p-8">
      <!-- Error Icon -->
      <div class="error-icon mb-4 flex justify-center">
        <svg class="w-12 h-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
      </div>

      <!-- Error Message -->
      <div class="error-content space-y-4">
        <h3 class="error-title text-xl font-semibold text-gray-900">{{ errorTitle }}</h3>
        <p class="error-message text-gray-600">{{ errorMessage }}</p>
        
        <!-- Retry Button -->
        <button 
          v-if="showRetry"
          @click="retry"
          class="retry-button px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          {{ retryText }}
        </button>

        <!-- Details (Development Only) -->
        <details v-if="isDevelopment && error" class="error-details mt-4 text-left">
          <summary class="cursor-pointer text-sm text-gray-500 hover:text-gray-700">Error Details</summary>
          <pre class="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto whitespace-pre-wrap break-words">{{ error.stack || error.message }}</pre>
        </details>
      </div>
    </div>
  </div>
  
  <!-- Normal slot content -->
  <slot v-else />
</template>

<script>
import { ref, onErrorCaptured, computed } from 'vue'

export default {
  name: 'ErrorBoundary',
  props: {
    fallback: {
      type: String,
      default: 'Something went wrong. Please try again.'
    },
    canRetry: {
      type: Boolean,
      default: true
    },
    onError: {
      type: Function,
      default: null
    },
    errorTitle: {
      type: String,
      default: 'Oops! Something went wrong'
    },
    retryText: {
      type: String,
      default: 'Try Again'
    },
    customErrorMessage: {
      type: String,
      default: null
    }
  },
  emits: ['error', 'retry'],
  setup(props, { emit }) {
    const hasError = ref(false)
    const error = ref(null)
    const isDevelopment = import.meta.env.DEV

    const errorMessage = computed(() => {
      // Use custom error message if provided
      if (props.customErrorMessage) {
        return props.customErrorMessage
      }

      if (!error.value) return props.fallback
      
      // User-friendly error messages based on error type
      const errorMap = {
        'Network': 'Unable to connect. Please check your internet connection.',
        'Failed to fetch': 'Unable to connect to the server. Please try again later.',
        'API': 'Service temporarily unavailable. Please try again later.',
        'Timeout': 'Request timed out. Please try again.',
        'TimeoutError': 'The request took too long. Please try again.',
        'Permission': 'You don\'t have permission to access this resource.',
        'NotFound': 'The requested resource was not found.',
        '404': 'The requested resource was not found.',
        '500': 'Server error occurred. Please try again later.',
        '503': 'Service temporarily unavailable. Please try again later.',
        'TypeError': 'An unexpected error occurred. Please refresh the page.',
        'SyntaxError': 'An unexpected error occurred. Please refresh the page.'
      }

      const errorMsg = error.value.message || ''
      
      for (const [key, message] of Object.entries(errorMap)) {
        if (errorMsg.includes(key)) {
          return message
        }
      }

      // Default fallback message
      return props.fallback
    })

    const showRetry = computed(() => {
      return props.canRetry && hasError.value
    })

    // Capture errors from child components
    onErrorCaptured((err, instance, info) => {
      if (isDevelopment) {
        console.group('🔴 Error caught by ErrorBoundary')
        console.error('Error:', err)
        console.log('Component:', instance?.$options?.name || 'Unknown')
        console.log('Info:', info)
        console.groupEnd()
      }
      
      hasError.value = true
      error.value = err

      // Call custom error handler if provided
      if (props.onError) {
        props.onError(err, instance, info)
      }

      // Emit error event for parent components
      emit('error', {
        error: err,
        instance,
        info
      })

      // Prevent error propagation to parent components
      return false
    })

    const retry = () => {
      hasError.value = false
      error.value = null
      emit('retry')
    }

    // Expose for testing
    return {
      hasError,
      error,
      errorMessage,
      showRetry,
      retry,
      isDevelopment
    }
  }
}
</script>

<style scoped>
.error-boundary {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>