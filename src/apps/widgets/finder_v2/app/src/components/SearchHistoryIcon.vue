<template>
  <div v-if="shouldShow" class="search-history-container">
    <!-- Search History Header with <PERSON><PERSON> Button -->
    <div class="search-history-header">
      <button
        @click="toggleAccordion"
        :aria-label="`Search History (${searchCount} searches)`"
        :aria-expanded="isAccordionOpen"
        :aria-controls="accordionId"
        class="history-toggle-button"
        :class="{ 'has-history': searchCount > 0, 'expanded': isAccordionOpen }"
      >
        <!-- Clock/History Icon -->
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="history-icon">
          <path d="M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3Z" />
        </svg>
        
        <!-- Search count badge -->
        <span v-if="searchCount > 0" class="search-count-badge">
          {{ searchCount }}
        </span>
      </button>
    </div>

    <!-- Accordion Panel -->
    <transition name="accordion">
      <div
        v-if="isAccordionOpen"
        :id="accordionId"
        class="search-history-accordion"
        role="region"
        :aria-labelledby="buttonId"
      >
        <!-- Accordion Content -->
        <div class="accordion-content">
          <!-- Search History List -->
          <div v-if="allSearches.length > 0" class="search-list">
            <div
              v-for="search in visibleSearches"
              :key="search.id"
              class="search-item"
              @click="executeSearch(search.id)"
              @keydown.enter="executeSearch(search.id)"
              @keydown.space.prevent="executeSearch(search.id)"
              tabindex="0"
              role="button"
              :aria-label="`Execute search for ${search.description}`"
            >
              <div class="search-content">
                <div class="search-description">{{ search.description }}</div>
                <div class="search-time">{{ getRelativeTime(search.timestamp) }}</div>
              </div>
              <button
                class="remove-button"
                @click.stop="removeSearch(search.id)"
                @keydown.enter.stop="removeSearch(search.id)"
                @keydown.space.stop.prevent="removeSearch(search.id)"
                :aria-label="`Remove search for ${search.description}`"
                title="Remove this search"
              >
                ✕
              </button>
            </div>
          </div>

          <!-- Show More/Less -->
          <div v-if="hasMoreSearches && !showAll" class="show-more-container">
            <button
              class="show-more-button"
              @click="showAll = true"
              @keydown.enter="showAll = true"
              @keydown.space.prevent="showAll = true"
              aria-label="Show more search history"
            >
              Show More ({{ remainingCount }} more)
            </button>
          </div>

          <div v-if="showAll && hasMoreSearches" class="show-less-container">
            <button
              class="show-less-button"
              @click="showAll = false"
              @keydown.enter="showAll = false"
              @keydown.space.prevent="showAll = false"
              aria-label="Show less search history"
            >
              Show Less
            </button>
          </div>

          <!-- Empty State -->
          <div v-if="allSearches.length === 0" class="empty-state">
            <div class="empty-icon">🔍</div>
            <p class="empty-message">No search history yet</p>
            <p class="empty-hint">Your recent searches will appear here</p>
          </div>

          <!-- Clear All Button -->
          <div v-if="allSearches.length > 0" class="clear-all-container">
            <button
              class="clear-all-button"
              @click="confirmClearAll"
              @keydown.enter="confirmClearAll"
              @keydown.space.prevent="confirmClearAll"
              aria-label="Clear all search history"
            >
              Clear All History
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Clear Confirmation Modal -->
    <transition name="modal">
      <div v-if="showClearConfirmation" class="modal-overlay" @click="cancelClearAll">
        <div class="confirmation-modal" @click.stop>
          <h4 class="confirmation-title">Clear Search History</h4>
          <p class="confirmation-message">
            Are you sure you want to clear all your search history? This action cannot be undone.
          </p>
          <div class="confirmation-buttons">
            <button
              class="confirm-button"
              @click="clearAllHistory"
              @keydown.enter="clearAllHistory"
              @keydown.space.prevent="clearAllHistory"
            >
              Clear All
            </button>
            <button
              class="cancel-button"
              @click="cancelClearAll"
              @keydown.enter="cancelClearAll"
              @keydown.space.prevent="cancelClearAll"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount, isRef } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/finder'

export default {
  name: 'SearchHistoryIcon',
  setup() {
    const finderStore = useFinderStore()
    const { config } = storeToRefs(finderStore)
    
    // Component state
    const isAccordionOpen = ref(false)
    const showAll = ref(false)
    const showClearConfirmation = ref(false)
    const searchHistory = ref(null)
    
    // Accessibility IDs
    const accordionId = `search-history-accordion-${Math.random().toString(36).substr(2, 9)}`
    const buttonId = `search-history-button-${Math.random().toString(36).substr(2, 9)}`
    
    // Initialize search history - try immediately and watch for changes
    function updateSearchHistory() {
      const newSearchHistory = finderStore.getSearchHistory()
      // console.log('SearchHistoryIcon updateSearchHistory:', newSearchHistory)
      searchHistory.value = newSearchHistory
    }
    
    onMounted(() => {
      updateSearchHistory()
    })
    
    // Watch for config changes to reinitialize search history
    watch(() => config.value, () => {
      updateSearchHistory()
    }, { deep: true })
    
    // Also watch for finder store initialization
    watch(() => finderStore.getSearchHistory(), (newSearchHistory) => {
      // console.log('SearchHistoryIcon watch getSearchHistory changed:', newSearchHistory)
      if (newSearchHistory && newSearchHistory !== searchHistory.value) {
        searchHistory.value = newSearchHistory
      }
    })
    
    // Computed properties
    const getVal = (maybeRef) => (isRef(maybeRef) ? maybeRef.value : maybeRef)

    const isEnabled = computed(() => {
      const enabledSource = searchHistory.value?.isEnabled
      return getVal(enabledSource) ?? false
    })
    
    const allSearches = computed(() => {
      // console.log('ALLSEARCHES COMPUTED START EVALUATION')
      const history = searchHistory.value
      // console.log('  - searchHistory.value:', history)
      if (!history) return []
      const searchesSource = history.searches
      const searchesVal = getVal(searchesSource) || []
      // console.log('  - searches value:', searchesVal, 'length:', searchesVal.length)
      // console.log('SearchHistoryIcon allSearches debug:', {
      //   searchHistory: searchHistory.value,
      //   searches: searchesVal,
      //   length: searchesVal.length,
      //   isEnabled: getVal(history.isEnabled)
      // })
      return searchesVal
    })
    watch(allSearches, (newVal, oldVal) => {
      // console.log('ALLSEARCHES WATCHER TRIGGERED:', {
      //   newLength: newVal.length,
      //   oldLength: oldVal.length,
      //   newVal: newVal,
      //   oldVal: oldVal
      // })
    }, { deep: true })
    
    const displaySearches = computed(() => {
      const src = searchHistory.value?.displaySearches
      return getVal(src) ?? []
    })
    
    const hasMoreSearches = computed(() => {
      const src = searchHistory.value?.hasMoreSearches
      return getVal(src) ?? false
    })
    
    const searchCount = computed(() => {
      return allSearches.value.length
    })
    
    const shouldShow = computed(() => {
      // Show if search history is enabled in config OR if we have a searchHistory instance
      const configEnabled = config.value?.search_history?.enabled !== false
      return configEnabled || isEnabled.value
    })
    
    const visibleSearches = computed(() => {
      if (showAll.value) {
        return allSearches.value
      }
      return displaySearches.value
    })
    
    const remainingCount = computed(() => {
      return Math.max(0, allSearches.value.length - displaySearches.value.length)
    })
    
    // Methods
    function toggleAccordion() {
      isAccordionOpen.value = !isAccordionOpen.value
      if (isAccordionOpen.value) {
        showAll.value = false
      }
      // Trigger iframe resize when accordion opens/closes
      triggerResize()
    }
    
    /** Trigger iframeResizer size recalculation */
    function triggerResize() {
      if (window.parentIFrame && window.parentIFrame.size) {
        window.parentIFrame.size()
        // second call after animation completes (~300ms)
        setTimeout(() => {
          window.parentIFrame && window.parentIFrame.size()
        }, 350)
      }
    }
    
    function closeAccordion() {
      isAccordionOpen.value = false
      showAll.value = false
      // Trigger iframe resize when accordion closes
      triggerResize()
    }
    
    async function executeSearch(searchId) {
      try {
        closeAccordion()
        await finderStore.executeSearchFromHistory(searchId)
        
        // Update search history reference after execution
        searchHistory.value = finderStore.getSearchHistory()
        
        // Scroll to results if available
        setTimeout(() => {
          const resultsElement = document.querySelector('.results-container')
          if (resultsElement) {
            resultsElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
          }
        }, 100)
        
      } catch (error) {
        console.error('Failed to execute search from history:', error)
      }
    }
    
    function removeSearch(searchId) {
      if (searchHistory.value) {
        searchHistory.value.removeSearch(searchId)
        
        // If no searches left, close accordion
        if (allSearches.value.length === 0) {
          closeAccordion()
        }
      }
    }
    
    function confirmClearAll() {
      showClearConfirmation.value = true
    }
    
    function clearAllHistory() {
      if (searchHistory.value) {
        searchHistory.value.clearHistory()
        showClearConfirmation.value = false
        closeAccordion()
      }
    }
    
    function cancelClearAll() {
      showClearConfirmation.value = false
    }
    
    function getRelativeTime(timestamp) {
      return searchHistory.value?.getRelativeTime(timestamp) || ''
    }
    
    // Keyboard navigation
    function handleKeydown(event) {
      if (event.key === 'Escape') {
        if (showClearConfirmation.value) {
          cancelClearAll()
        } else if (isAccordionOpen.value) {
          closeAccordion()
        }
        event.preventDefault()
      }
    }
    
    // Add global keyboard listener
    onMounted(() => {
      document.addEventListener('keydown', handleKeydown)
    })
    
    // Clean up listener
    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleKeydown)
    })
    
    return {
      isAccordionOpen,
      showAll,
      showClearConfirmation,
      shouldShow,
      allSearches,
      displaySearches,
      hasMoreSearches,
      visibleSearches,
      remainingCount,
      searchCount,
      accordionId,
      buttonId,
      toggleAccordion,
      closeAccordion,
      executeSearch,
      removeSearch,
      confirmClearAll,
      clearAllHistory,
      cancelClearAll,
      getRelativeTime
    }
  }
}
</script>

<style scoped>
/* Main container - relative positioning for iframe compatibility */
.search-history-container {
  position: relative;
  width: 100%;
  margin-bottom: 1rem;
  /* Add padding to prevent icon overlap */
  padding-top: 0;
}

/* Header section with toggle button - positioned as small icon in top right */
.search-history-header {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}

.history-toggle-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  background-color: #f9fafb;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.history-toggle-button:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.history-toggle-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.history-toggle-button.has-history {
  background-color: rgba(var(--theme-accent-rgb), 0.05);
  border-color: var(--theme-accent);
  color: var(--theme-accent);
}

.history-toggle-button.has-history:hover {
  background-color: rgba(var(--theme-accent-rgb), 0.1);
}

.history-toggle-button.expanded {
  background-color: rgba(var(--theme-primary-rgb), 0.05);
  border-color: var(--theme-primary);
  color: var(--theme-primary);
}

.history-icon {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.search-count-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 14px;
  height: 14px;
  padding: 0 4px;
  background-color: #dc2626;
  color: white;
  font-size: 0.5rem;
  font-weight: 400;
  border-radius: 9px;
  border: 1px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Accordion Panel - relative positioning */
.search-history-accordion {
  position: relative;
  width: 100%;
  margin-top: 0.5rem;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.accordion-content {
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

/* Search items using flex layout for inline display */
.search-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.search-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 0;
  flex: 1 1 calc(50% - 0.375rem);
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.15s ease;
  outline: none;
}

/* Single column on small screens */
@media (max-width: 640px) {
  .search-item {
    flex: 1 1 100%;
  }
}

.search-item:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.search-content {
  flex: 1;
  min-width: 0;
}

.search-description {
  font-size: 0.8125rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.remove-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin-left: 12px;
  border: none;
  border-radius: 6px;
  background-color: transparent;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.15s ease;
  outline: none;
}

.remove-button:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

.show-more-container,
.show-less-container {
  display: flex;
  justify-content: center;
  margin-top: 12px;
}

.show-more-button,
.show-less-button {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: #ffffff;
  color: #374151;
  font-size: 0.8125rem;
  cursor: pointer;
  transition: all 0.15s ease;
  outline: none;
}

.show-more-button:hover,
.show-less-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.show-more-button:focus,
.show-less-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.empty-state {
  text-align: center;
  padding: 24px 16px;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.empty-message {
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
}

.empty-hint {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.clear-all-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.clear-all-button {
  padding: 8px 16px;
  border: 1px solid #dc2626;
  border-radius: 4px;
  background-color: #ffffff;
  color: #dc2626;
  font-size: 0.8125rem;
  cursor: pointer;
  transition: all 0.15s ease;
  outline: none;
}

.clear-all-button:hover {
  background-color: #dc2626;
  color: #ffffff;
}

.clear-all-button:focus {
  outline: 2px solid #dc2626;
  outline-offset: 2px;
}

/* Confirmation Modal */
.confirmation-modal {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.confirmation-title {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.confirmation-message {
  margin: 0 0 24px 0;
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
}

.confirmation-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirm-button,
.cancel-button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.confirm-button {
  border: 1px solid #dc2626;
  background-color: #dc2626;
  color: #ffffff;
}

.confirm-button:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.cancel-button {
  border: 1px solid #d1d5db;
  background-color: #ffffff;
  color: #374151;
}

.cancel-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

/* Transitions */
.accordion-enter-active,
.accordion-leave-active {
  transition: all 0.2s ease;
}

.accordion-enter-from,
.accordion-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Modal overlay - fixed positioning for overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Responsive Design */
@media (max-width: 640px) {
  .accordion-content {
    padding: 0.75rem;
  }
  
  .search-item {
    padding: 0.625rem;
  }
  
  .search-description {
    font-size: 0.75rem;
  }
  
  .search-time {
    font-size: 0.6875rem;
  }
  
  .confirmation-modal {
    padding: 1.25rem;
  }
  
  .confirmation-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .confirm-button,
  .cancel-button {
    width: 100%;
    justify-content: center;
  }
}
</style>