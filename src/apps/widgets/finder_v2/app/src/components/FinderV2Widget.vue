<template>
  <div class="finder-v2-widget p-1" data-iframe-height :class="themeClasses" :style="themeStyles">
    <!-- Debug info -->
    <div v-if="showDebug" style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; border: 2px solid #333;">
      <strong>🔍 Widget Debug Info:</strong><br>
      Config exists: {{ !!configData }}<br>
      Config UUID: {{ configData?.uuid || configData?.id || 'undefined' }}<br>
      Window.FinderV2Config exists: {{ !!window.FinderV2Config }}<br>
      Store initialized: {{ storeInitialized }}<br>
      Loading: {{ loading }}<br>
      Error: {{ error || 'None' }}<br>
      Theme name: {{ configData?.theme?.name || 'default' }}<br>
      Translation loaded: {{ !!translation }}
    </div>
    
    <!-- Vehicle Search (finder-v2 only supports by_vehicle search type) -->
    <div class="search-content">
      <VehicleSearch :translation="translation" />
    </div>

    <!-- Results Display -->
    <div v-if="hasResults || loadingResults" class="results-section">
      <ResultsDisplay :translation="translation" />
    </div>
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/finder'
import { useCSRFToken } from '../composables/useCSRFToken'
import { useBotProtection } from '../composables/useBotProtection'
import VehicleSearch from './VehicleSearch.vue'
import ResultsDisplay from './ResultsDisplay.vue'

export default {
  name: 'FinderV2Widget',
  components: {
    VehicleSearch,
    ResultsDisplay
  },
  setup() {
    const finderStore = useFinderStore()
    const { loadingResults, results, loading, error } = storeToRefs(finderStore)
    const storeInitialized = computed(() => !!finderStore.config)
    
    // Initialize enhanced CSRF token management
    const { csrfToken, tokenStatus, isEnhancedCSRFEnabled } = useCSRFToken()
    
    // Initialize bot protection
    const { initialize: initBotProtection, fingerprintCollected } = useBotProtection()

    // Widget configuration from global config  
    const configData = computed(() => {
      const cfg = window.FinderV2Config || {}
      console.log('Config data computed:', cfg)
      return cfg
    })
    const theme = computed(() => configData.value.theme || {})

    // Check if there are search results
    const hasResults = computed(() => {
      return results.value.length > 0
    })

    // Generate theme classes based on configuration
    const themeClasses = computed(() => {
      const classes = []
      if (theme.value.name) {
        classes.push(`theme-${theme.value.name.toLowerCase().replace(/\s+/g, '-')}`)
      }
      if (theme.value.effects?.hoverEffect) {
        classes.push(`hover-${theme.value.effects.hoverEffect}`)
      }
      return classes.join(' ')
    })

    // Generate theme styles based on configuration
    const themeStyles = computed(() => {
      const styles = {}
      
      // Apply theme colors
      if (theme.value.colors) {
        styles['--theme-primary'] = theme.value.colors.primary
        styles['--theme-secondary'] = theme.value.colors.secondary
        styles['--theme-accent'] = theme.value.colors.accent
        styles['--theme-background'] = theme.value.colors.background
        styles['--theme-text'] = theme.value.colors.text
        
        // Convert hex colors to RGB for rgba() usage
        styles['--theme-primary-rgb'] = hexToRgb(theme.value.colors.primary)
        styles['--theme-secondary-rgb'] = hexToRgb(theme.value.colors.secondary)
        styles['--theme-accent-rgb'] = hexToRgb(theme.value.colors.accent)
      }
      
      // Apply typography
      if (theme.value.typography) {
        styles['--theme-font-family'] = theme.value.typography.fontFamily
        styles['--theme-font-size'] = theme.value.typography.fontSize
        styles['--theme-font-weight'] = theme.value.typography.fontWeight
        styles['--theme-line-height'] = theme.value.typography.lineHeight
        styles['--theme-letter-spacing'] = theme.value.typography.letterSpacing
      }
      
      // Apply spacing
      if (theme.value.spacing) {
        styles['--theme-padding'] = theme.value.spacing.padding
        styles['--theme-margin'] = theme.value.spacing.margin
      }
      
      // Apply effects
      if (theme.value.effects) {
        styles['--theme-border-radius'] = theme.value.effects.borderRadius
        styles['--theme-border-width'] = theme.value.effects.borderWidth
        styles['--theme-animation-speed'] = theme.value.effects.animationSpeed
        
        // Apply shadow based on intensity
        const shadowIntensity = theme.value.effects.shadowIntensity
        const shadows = {
          'none': 'none',
          'light': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
          'medium': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          'heavy': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        }
        styles['--theme-shadow'] = shadows[shadowIntensity] || shadows['medium']
      }
      
      return styles
    })

    // Helper function to convert hex to RGB
    function hexToRgb(hex) {
      if (!hex) return '0, 0, 0'
      
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result 
        ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
        : '0, 0, 0'
    }

    const translation = computed(() => {
      return configData.value.translation || {
        year_label: 'Year',
        make_label: 'Make',
        model_label: 'Model',
        generation_label: 'Generation',
        modification_label: 'Modification',
        select_year: 'Select Year',
        select_make: 'Select Make',
        select_model: 'Select Model',
        select_generation: 'Select Generation',
        select_modification: 'Select Modification',
        loading: 'Loading...',
        loading_results: 'Loading results...',
        no_results: 'No results found. Please try different search criteria.',
        search_button: 'Unlock More Insights at Wheel-Size.com'
      };
    });

    onMounted(async () => {
      // Component mounted - initialize everything
      window.FinderV2Debug = window.FinderV2Debug || {}
      window.FinderV2Debug.componentMounted = true
      window.FinderV2Debug.configAtMount = JSON.parse(JSON.stringify(window.FinderV2Config || {}))
      
      // Initialize bot protection (non-blocking)
      initBotProtection().then(() => {
        console.log('Bot protection initialized, fingerprint collected:', fingerprintCollected.value)
      }).catch(error => {
        console.warn('Bot protection init failed (non-blocking):', error)
      })
      
      // Wait for CSRF token to be ready if enhanced CSRF is enabled
      if (isEnhancedCSRFEnabled.value) {
        console.log('Waiting for enhanced CSRF token...')
        
        // Wait for token status to become active (with timeout)
        let attempts = 0
        while (tokenStatus.value !== 'active' && attempts < 20) {
          await new Promise(resolve => setTimeout(resolve, 100))
          attempts++
        }
        
        if (tokenStatus.value === 'active') {
          console.log('Enhanced CSRF token ready:', csrfToken.value.substring(0, 10))
        } else {
          console.warn('Enhanced CSRF token not ready after timeout, proceeding with legacy')
        }
      } else {
        console.log('Using legacy CSRF, no wait needed')
      }
      
      // Initialize finder store with configuration
      console.log('Initializing store with config:', configData.value)
      finderStore.initialize(configData.value)
      
      // Log CSRF token status for debugging
      console.log('CSRF token status:', tokenStatus.value)
      
      // Apply theme styles to document root for global access
      if (theme.value.colors || theme.value.typography || theme.value.spacing || theme.value.effects) {
        const root = document.documentElement
        Object.entries(themeStyles.value).forEach(([property, value]) => {
          root.style.setProperty(property, value)
        })
      }
    })

    // Show debug panel in development or when URL has ?debug
    const showDebug = computed(() => {
      return import.meta.env.DEV || window.location.search.includes('debug')
    })

    return {
      loadingResults,
      hasResults,
      themeClasses,
      themeStyles,
      translation,
      configData,
      storeInitialized,
      loading,
      error,
      showDebug
    }
  }
}
</script>

<style scoped>
.finder-v2-widget {
  width: 100%;
}

.search-content {
  padding: 1rem 0;
}

.results-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}
</style>
