<template>
  <ErrorBoundary 
    :error-title="'Widget Error'"
    :fallback="'The widget encountered an error. Please refresh the page to try again.'"
    @error="handleWidgetError"
    @retry="handleWidgetRetry"
  >
    <div class="finder-v2-widget p-1" data-iframe-height :class="themeClasses" :style="themeStyles">
      <!-- Vehicle Search (finder-v2 only supports by_vehicle search type) -->
      <div class="search-content">
        <ApiErrorBoundary 
          :component-name="'VehicleSearch'"
          :retry-action="retryVehicleSearch"
        >
          <VehicleSearch :translation="translation" />
        </ApiErrorBoundary>
      </div>

      <!-- Results Display -->
      <div v-if="hasResults || loadingResults" class="results-section">
        <ApiErrorBoundary 
          :component-name="'ResultsDisplay'"
          :retry-action="retryResultsLoad"
        >
          <ResultsDisplay :translation="translation" />
        </ApiErrorBoundary>
      </div>
    </div>
  </ErrorBoundary>
</template>

<script>
import { computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/index'
import { useCSRFToken } from '../composables/useCSRFToken'
import { useBotProtection } from '../composables/useBotProtection'
import { useErrorRecovery } from '../composables/useErrorRecovery'
import VehicleSearch from './VehicleSearch.vue'
import ResultsDisplay from './ResultsDisplay.vue'
import ErrorBoundary from './ErrorBoundary.vue'
import ApiErrorBoundary from './ApiErrorBoundary.vue'

export default {
  name: 'FinderV2Widget',
  components: {
    VehicleSearch,
    ResultsDisplay,
    ErrorBoundary,
    ApiErrorBoundary
  },
  setup() {
    const finderStore = useFinderStore()
    
    // Widget configuration from global config
    const config = window.FinderV2Config || {}
    const theme = config.theme || {}
    
    const { loadingResults, results } = storeToRefs(finderStore)
    
    // Initialize enhanced CSRF token management
    const { csrfToken, tokenStatus, isEnhancedCSRFEnabled } = useCSRFToken()
    
    // Initialize bot protection
    const { initialize: initBotProtection, fingerprintCollected } = useBotProtection()

    // Check if there are search results
    const hasResults = computed(() => {
      return results.value.length > 0
    })

    // Generate theme classes based on configuration
    const themeClasses = computed(() => {
      const classes = []
      if (theme.name) {
        classes.push(`theme-${theme.name.toLowerCase().replace(/\s+/g, '-')}`)
      }
      if (theme.effects?.hoverEffect) {
        classes.push(`hover-${theme.effects.hoverEffect}`)
      }
      return classes.join(' ')
    })

    // Generate theme styles based on configuration
    const themeStyles = computed(() => {
      const styles = {}
      
      // Apply theme colors
      if (theme.colors) {
        styles['--theme-primary'] = theme.colors.primary
        styles['--theme-secondary'] = theme.colors.secondary
        styles['--theme-accent'] = theme.colors.accent
        styles['--theme-background'] = theme.colors.background
        styles['--theme-text'] = theme.colors.text
        
        // Convert hex colors to RGB for rgba() usage
        styles['--theme-primary-rgb'] = hexToRgb(theme.colors.primary)
        styles['--theme-secondary-rgb'] = hexToRgb(theme.colors.secondary)
        styles['--theme-accent-rgb'] = hexToRgb(theme.colors.accent)
      }
      
      // Apply typography
      if (theme.typography) {
        styles['--theme-font-family'] = theme.typography.fontFamily
        styles['--theme-font-size'] = theme.typography.fontSize
        styles['--theme-font-weight'] = theme.typography.fontWeight
        styles['--theme-line-height'] = theme.typography.lineHeight
        styles['--theme-letter-spacing'] = theme.typography.letterSpacing
      }
      
      // Apply spacing
      if (theme.spacing) {
        styles['--theme-padding'] = theme.spacing.padding
        styles['--theme-margin'] = theme.spacing.margin
      }
      
      // Apply effects
      if (theme.effects) {
        styles['--theme-border-radius'] = theme.effects.borderRadius
        styles['--theme-border-width'] = theme.effects.borderWidth
        styles['--theme-animation-speed'] = theme.effects.animationSpeed
        
        // Apply shadow based on intensity
        const shadowIntensity = theme.effects.shadowIntensity
        const shadows = {
          'none': 'none',
          'light': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
          'medium': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          'heavy': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        }
        styles['--theme-shadow'] = shadows[shadowIntensity] || shadows['medium']
      }
      
      return styles
    })

    // Helper function to convert hex to RGB
    function hexToRgb(hex) {
      if (!hex) return '0, 0, 0'
      
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result 
        ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
        : '0, 0, 0'
    }

    const translation = computed(() => {
      return config.translation || {
        year_label: 'Year',
        make_label: 'Make',
        model_label: 'Model',
        generation_label: 'Generation',
        modification_label: 'Modification',
        select_year: 'Select Year',
        select_make: 'Select Make',
        select_model: 'Select Model',
        select_generation: 'Select Generation',
        select_modification: 'Select Modification',
        loading: 'Loading...',
        loading_results: 'Loading results...',
        no_results: 'No results found. Please try different search criteria.',
        search_button: 'Unlock More Insights at Wheel-Size.com'
      };
    });

    onMounted(async () => {
      // Initialize bot protection (non-blocking)
      initBotProtection().then(() => {
        // console.log('Bot protection initialized, fingerprint collected:', fingerprintCollected.value)
      }).catch(error => {
        console.warn('Bot protection init failed (non-blocking):', error)
      })
      
      // Wait for CSRF token to be ready if enhanced CSRF is enabled
      if (isEnhancedCSRFEnabled.value) {
        // console.log('Waiting for enhanced CSRF token...')
        
        // Wait for token status to become active (with timeout)
        let attempts = 0
        while (tokenStatus.value !== 'active' && attempts < 20) {
          await new Promise(resolve => setTimeout(resolve, 100))
          attempts++
        }
        
        if (tokenStatus.value === 'active') {
          // console.log('Enhanced CSRF token ready:', csrfToken.value.substring(0, 10))
        } else {
          console.warn('Enhanced CSRF token not ready after timeout, proceeding with legacy')
        }
      } else {
        // console.log('Using legacy CSRF, no wait needed')
      }
      
      // Initialize the store with configuration
      finderStore.initialize(config)
      
      // Log CSRF token status for debugging
      // console.log('CSRF token status:', tokenStatus.value)
      
      // Apply theme styles to document root for global access
      if (theme.colors || theme.typography || theme.spacing || theme.effects) {
        const root = document.documentElement
        Object.entries(themeStyles.value).forEach(([property, value]) => {
          root.style.setProperty(property, value)
        })
      }
    })

    // Error recovery composable
    const { withRetry, withFallback } = useErrorRecovery()

    // Error handling methods
    const handleWidgetError = ({ error, instance, info }) => {
      console.error('Widget error caught:', error)
      
      // Track widget-level errors
      if (window.FinderV2Config?.analytics?.track) {
        window.FinderV2Config.analytics.track('widget_error', {
          error: error.message,
          component: instance?.$options?.name || 'Unknown',
          info: info
        })
      }
    }

    const handleWidgetRetry = async () => {
      // Reset the entire widget state and reload
      finderStore.resetAll()
      await finderStore.initialize(config)
    }

    const retryVehicleSearch = async () => {
      // Retry loading initial vehicle search data
      await withRetry(
        () => finderStore.loadInitialData(),
        { retries: 3, delay: 1000, backoff: true }
      )
    }

    const retryResultsLoad = async () => {
      // Retry loading search results
      const lastSearch = finderStore.getLastSearchParams()
      if (lastSearch) {
        await withRetry(
          () => finderStore.search(lastSearch),
          { retries: 3, delay: 1000, backoff: true }
        )
      }
    }

    return {
      loadingResults,
      hasResults,
      themeClasses,
      themeStyles,
      translation,
      handleWidgetError,
      handleWidgetRetry,
      retryVehicleSearch,
      retryResultsLoad
    }
  }
}
</script>

<style scoped>
.finder-v2-widget {
  width: 100%;
}

.search-content {
  padding: 1rem 0;
}

.results-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}
</style>
