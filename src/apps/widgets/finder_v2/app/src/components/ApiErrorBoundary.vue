<template>
  <ErrorBoundary
    :can-retry="true"
    :error-title="errorTitle"
    :custom-error-message="fallbackMessage"
    :retry-text="retryText"
    @retry="handleRetry"
    @error="handleApiError"
  >
    <slot />
  </ErrorBoundary>
</template>

<script>
import { computed, ref } from 'vue'
import ErrorBoundary from './ErrorBoundary.vue'
import { useFinderStore } from '@/stores/index'

export default {
  name: 'ApiErrorBoundary',
  components: { ErrorBoundary },
  props: {
    retryAction: {
      type: Function,
      default: null
    },
    componentName: {
      type: String,
      default: 'Component'
    }
  },
  setup(props) {
    const store = useFinderStore()
    const isRetrying = ref(false)
    const retryCount = ref(0)
    const maxRetries = 3

    const errorTitle = computed(() => {
      // Check for offline state
      if (!navigator.onLine) {
        return 'No Internet Connection'
      }
      
      // Check retry count
      if (retryCount.value >= maxRetries) {
        return 'Unable to Load Data'
      }
      
      return 'Connection Problem'
    })

    const fallbackMessage = computed(() => {
      if (!navigator.onLine) {
        return 'Please check your internet connection and try again.'
      }
      
      if (retryCount.value >= maxRetries) {
        return 'We\'re having trouble connecting to our servers. Please try again later or contact support if the problem persists.'
      }
      
      return 'We\'re having trouble loading the data. Please try again.'
    })

    const retryText = computed(() => {
      if (isRetrying.value) {
        return 'Retrying...'
      }
      if (retryCount.value > 0) {
        return `Try Again (${retryCount.value}/${maxRetries})`
      }
      return 'Try Again'
    })

    const handleRetry = async () => {
      if (isRetrying.value || retryCount.value >= maxRetries) {
        return
      }

      isRetrying.value = true
      retryCount.value++

      try {
        // If a custom retry action is provided, use it
        if (props.retryAction) {
          await props.retryAction()
        } else {
          // Default retry: reload initial data
          await store.loadInitialData()
        }
        
        // Reset retry count on success
        retryCount.value = 0
      } catch (error) {
        console.error('Retry failed:', error)
        
        // If we've hit max retries, show a different message
        if (retryCount.value >= maxRetries) {
          console.error(`Max retries (${maxRetries}) reached for ${props.componentName}`)
        }
      } finally {
        isRetrying.value = false
      }
    }

    const handleApiError = ({ error }) => {
      // Log API errors with context
      const errorContext = {
        component: props.componentName,
        endpoint: error?.config?.url,
        method: error?.config?.method,
        status: error?.response?.status,
        statusText: error?.response?.statusText,
        message: error?.message || 'Unknown error',
        timestamp: new Date().toISOString(),
        online: navigator.onLine,
        retryCount: retryCount.value
      }

      if (import.meta.env.DEV) {
        console.group(`🔴 API Error in ${props.componentName}`)
        console.error('Error:', error)
        console.table(errorContext)
        console.groupEnd()
      } else {
        console.error('API Error:', errorContext)
      }
      
      // Track API failures for analytics
      if (window.FinderV2Config?.analytics?.track) {
        window.FinderV2Config.analytics.track('api_error', errorContext)
      }

      // Emit custom event for error monitoring
      window.dispatchEvent(new CustomEvent('finderv2:api-error', {
        detail: errorContext
      }))
    }

    return {
      errorTitle,
      fallbackMessage,
      retryText,
      handleRetry,
      handleApiError,
      isRetrying
    }
  }
}
</script>