// Third-party library imports for finder-v2 widget
// This file is bundled separately to optimize caching

// Vue 3 runtime (if needed for external access)
import { createApp } from 'vue'

// Pinia for state management
import { createPinia } from 'pinia'

// HeadlessUI components
import * as HeadlessUI from '@headlessui/vue'

// Axios for HTTP requests
import axios from 'axios'

// Export libraries for potential external use
window.Vue = { createApp, createPinia }
window.HeadlessUI = HeadlessUI
window.axios = axios

// Configure axios defaults for CSRF protection
axios.defaults.xsrfCookieName = 'csrftoken'
axios.defaults.xsrfHeaderName = 'X-CSRFToken'

// Add request interceptor for CSRF token
axios.interceptors.request.use((config) => {
  const token = window.FinderV2Config?.csrfToken
  if (token) {
    config.headers['X-CSRFToken'] = token
  }
  return config
})

// Add response interceptor to handle challenge requirements
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config
    
    // Check if this is a challenge requirement error
    if (error.response?.status === 403 && 
        error.response?.data?.error === 'challenge_required' &&
        !originalRequest._challengeRetry) {
      
      // console.log('Challenge required for API endpoint. Solving challenge...')
      originalRequest._challengeRetry = true
      
      // Import bot protection composable dynamically
      const { useBotProtection } = await import('./composables/useBotProtection.js')
      const botProtection = useBotProtection()
      
      // Initialize bot protection if not already done
      if (!botProtection.fingerprintCollected.value) {
        await botProtection.initialize()
      }
      
      // Request and solve challenge
      const challengeSolved = await botProtection.solveAndVerifyChallenge()
      
      if (challengeSolved) {
        // Retry the original request with the challenge token
        return axios.request(originalRequest)
      }
    }
    
    // For other errors, reject as normal
    return Promise.reject(error)
  }
)

// console.log('Finder-v2 widget libraries loaded')
