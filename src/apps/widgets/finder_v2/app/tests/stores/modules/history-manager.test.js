/**
 * Unit Tests for History Manager Module
 * Tests search history operations and form population
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createHistoryManager } from '../../../src/stores/modules/history-manager.js'

describe('HistoryManager', () => {
  let historyManager
  let mockState
  let mockVehicleLoader
  let mockSearchExecutor
  let mockSearchHistory

  beforeEach(() => {
    // Mock state
    mockState = {
      flowType: { value: 'primary' },
      selectedYear: { value: '' },
      selectedMake: { value: '' },
      selectedModel: { value: '' },
      selectedModification: { value: '' },
      selectedGeneration: { value: '' },
      years: { value: [{ value: '2023', label: '2023' }] },
      makes: { value: [{ value: 'toyota', label: 'Toyota' }] },
      models: { value: [{ value: 'camry', label: 'Camry' }] },
      modifications: { value: [] },
      generations: { value: [] },
      resetVehicleSearch: vi.fn(),
      clearResults: vi.fn(),
      setError: vi.fn()
    }

    // Mock vehicle loader
    mockVehicleLoader = {
      loadYears: vi.fn().mockResolvedValue(true),
      loadMakes: vi.fn().mockResolvedValue(true),
      loadModels: vi.fn().mockResolvedValue(true),
      loadGenerations: vi.fn().mockResolvedValue(true),
      loadModifications: vi.fn().mockResolvedValue(true)
    }

    // Mock search executor
    mockSearchExecutor = {
      searchByVehicle: vi.fn().mockResolvedValue([
        { id: 1, make: 'Toyota', model: 'Camry' }
      ])
    }

    // Mock search history
    mockSearchHistory = {
      getSearch: vi.fn(),
      getHistory: vi.fn().mockReturnValue([]),
      updateSearchTimestamp: vi.fn(),
      clearHistory: vi.fn(),
      removeSearch: vi.fn()
    }

    // Create history manager instance
    historyManager = createHistoryManager(
      mockState,
      mockVehicleLoader,
      mockSearchExecutor,
      mockSearchHistory
    )
  })

  describe('executeSearchFromHistory', () => {
    const mockSearch = {
      id: 'search-123',
      parameters: {
        year: '2023',
        make: 'toyota',
        model: 'camry',
        modification: ''
      },
      flowType: 'primary'
    }

    it('should execute search from history successfully', async () => {
      mockSearchHistory.getSearch.mockReturnValue(mockSearch)

      const result = await historyManager.executeSearchFromHistory('search-123')

      expect(result).toBe(true)
      expect(mockState.clearResults).toHaveBeenCalled()
      expect(mockSearchHistory.updateSearchTimestamp).toHaveBeenCalledWith('search-123')
      expect(mockSearchExecutor.searchByVehicle).toHaveBeenCalled()
    })

    it('should handle search not found', async () => {
      mockSearchHistory.getSearch.mockReturnValue(null)

      const result = await historyManager.executeSearchFromHistory('invalid-id')

      expect(result).toBe(false)
      expect(mockSearchExecutor.searchByVehicle).not.toHaveBeenCalled()
    })

    it('should handle search history not initialized', async () => {
      historyManager.searchHistory = null

      const result = await historyManager.executeSearchFromHistory('search-123')

      expect(result).toBe(false)
    })

    it('should handle search execution failure', async () => {
      mockSearchHistory.getSearch.mockReturnValue(mockSearch)
      mockSearchExecutor.searchByVehicle.mockRejectedValue(new Error('Search failed'))

      const result = await historyManager.executeSearchFromHistory('search-123')

      expect(result).toBe(false)
      expect(mockState.setError).toHaveBeenCalledWith('Failed to execute search from history')
    })
  })

  describe('populateFormFromSearch', () => {
    describe('primary flow', () => {
      const searchItem = {
        parameters: {
          year: '2023',
          make: 'toyota',
          model: 'camry',
          modification: 'mod-123'
        },
        flowType: 'primary'
      }

      it('should populate form for primary flow', async () => {
        await historyManager.populateFormFromSearch(searchItem)

        expect(mockState.resetVehicleSearch).toHaveBeenCalled()
        expect(mockState.selectedYear.value).toBe('2023')
        expect(mockState.selectedMake.value).toBe('toyota')
        expect(mockState.selectedModel.value).toBe('camry')
        expect(mockState.selectedModification.value).toBe('mod-123')
      })

      it('should load data in correct sequence', async () => {
        await historyManager.populateFormFromSearch(searchItem)

        expect(mockVehicleLoader.loadMakes).toHaveBeenCalledWith('2023')
        expect(mockVehicleLoader.loadModels).toHaveBeenCalledWith('toyota', '2023')
        expect(mockVehicleLoader.loadModifications).toHaveBeenCalledWith('toyota', 'camry', '2023')
      })

      it('should load years if not already loaded', async () => {
        mockState.years.value = []

        await historyManager.populateFormFromSearch(searchItem)

        expect(mockVehicleLoader.loadYears).toHaveBeenCalled()
      })
    })

    describe('alternative flow', () => {
      const searchItem = {
        parameters: {
          make: 'honda',
          model: 'civic',
          generation: 'gen-456',
          modification: 'mod-789'
        },
        flowType: 'alternative'
      }

      it('should populate form for alternative flow', async () => {
        await historyManager.populateFormFromSearch(searchItem)

        expect(mockState.selectedMake.value).toBe('honda')
        expect(mockState.selectedModel.value).toBe('civic')
        expect(mockState.selectedGeneration.value).toBe('gen-456')
        expect(mockState.selectedModification.value).toBe('mod-789')
      })

      it('should load data in correct sequence', async () => {
        await historyManager.populateFormFromSearch(searchItem)

        expect(mockVehicleLoader.loadModels).toHaveBeenCalledWith('honda')
        expect(mockVehicleLoader.loadGenerations).toHaveBeenCalledWith('honda', 'civic')
        expect(mockVehicleLoader.loadModifications).toHaveBeenCalledWith('honda', 'civic', 'gen-456')
      })

      it('should load makes if not already loaded', async () => {
        mockState.makes.value = []

        await historyManager.populateFormFromSearch(searchItem)

        expect(mockVehicleLoader.loadMakes).toHaveBeenCalled()
      })
    })

    describe('year_select flow', () => {
      const searchItem = {
        parameters: {
          make: 'ford',
          model: 'f-150',
          year: '2022',
          modification: 'mod-321'
        },
        flowType: 'year_select'
      }

      it('should populate form for year_select flow', async () => {
        await historyManager.populateFormFromSearch(searchItem)

        expect(mockState.selectedMake.value).toBe('ford')
        expect(mockState.selectedModel.value).toBe('f-150')
        expect(mockState.selectedYear.value).toBe('2022')
        expect(mockState.selectedModification.value).toBe('mod-321')
      })

      it('should load data in correct sequence', async () => {
        await historyManager.populateFormFromSearch(searchItem)

        expect(mockVehicleLoader.loadModels).toHaveBeenCalledWith('ford')
        expect(mockVehicleLoader.loadYears).toHaveBeenCalledWith('ford', 'f-150')
        expect(mockVehicleLoader.loadModifications).toHaveBeenCalledWith('ford', 'f-150', '2022')
      })
    })

    it('should handle unknown flow type', async () => {
      const searchItem = {
        parameters: {},
        flowType: 'unknown'
      }

      await expect(historyManager.populateFormFromSearch(searchItem))
        .rejects.toThrow('Unknown flow type: unknown')
    })
  })

  describe('history operations', () => {
    const mockHistoryItems = [
      { id: '1', parameters: { make: 'toyota' } },
      { id: '2', parameters: { make: 'honda' } }
    ]

    beforeEach(() => {
      mockSearchHistory.getHistory.mockReturnValue(mockHistoryItems)
    })

    it('should get all history items', () => {
      const items = historyManager.getHistoryItems()
      expect(items).toEqual(mockHistoryItems)
    })

    it('should get specific history item', () => {
      mockSearchHistory.getSearch.mockReturnValue(mockHistoryItems[0])

      const item = historyManager.getHistoryItem('1')
      expect(item).toEqual(mockHistoryItems[0])
    })

    it('should clear history', () => {
      historyManager.clearHistory()
      expect(mockSearchHistory.clearHistory).toHaveBeenCalled()
    })

    it('should remove history item', () => {
      historyManager.removeHistoryItem('1')
      expect(mockSearchHistory.removeSearch).toHaveBeenCalledWith('1')
    })

    it('should check if history exists', () => {
      expect(historyManager.hasHistory()).toBe(true)

      mockSearchHistory.getHistory.mockReturnValue([])
      expect(historyManager.hasHistory()).toBe(false)
    })

    it('should get most recent search', () => {
      const recent = historyManager.getMostRecentSearch()
      expect(recent).toEqual(mockHistoryItems[0])
    })

    it('should restore most recent search', async () => {
      mockSearchHistory.getSearch.mockReturnValue({
        ...mockHistoryItems[0],
        parameters: {
          year: '2023',
          make: 'toyota',
          model: 'camry'
        },
        flowType: 'primary'
      })

      const result = await historyManager.restoreMostRecentSearch()
      expect(result).toBe(true)
      expect(mockSearchExecutor.searchByVehicle).toHaveBeenCalled()
    })

    it('should get search count', () => {
      const count = historyManager.getSearchCount()
      expect(count).toBe(2)
    })
  })

  describe('isCurrentSearch', () => {
    it('should identify current search for primary flow', () => {
      mockState.flowType.value = 'primary'
      mockState.selectedYear.value = '2023'
      mockState.selectedMake.value = 'toyota'
      mockState.selectedModel.value = 'camry'
      mockState.selectedModification.value = 'mod-123'

      const searchItem = {
        parameters: {
          year: '2023',
          make: 'toyota',
          model: 'camry',
          modification: 'mod-123'
        },
        flowType: 'primary'
      }

      expect(historyManager.isCurrentSearch(searchItem)).toBe(true)
    })

    it('should identify current search for alternative flow', () => {
      mockState.flowType.value = 'alternative'
      mockState.selectedMake.value = 'honda'
      mockState.selectedModel.value = 'civic'
      mockState.selectedGeneration.value = 'gen-456'
      mockState.selectedModification.value = 'mod-789'

      const searchItem = {
        parameters: {
          make: 'honda',
          model: 'civic',
          generation: 'gen-456',
          modification: 'mod-789'
        },
        flowType: 'alternative'
      }

      expect(historyManager.isCurrentSearch(searchItem)).toBe(true)
    })

    it('should detect different search', () => {
      mockState.flowType.value = 'primary'
      mockState.selectedMake.value = 'toyota'

      const searchItem = {
        parameters: {
          make: 'honda'
        },
        flowType: 'primary'
      }

      expect(historyManager.isCurrentSearch(searchItem)).toBe(false)
    })

    it('should detect different flow type', () => {
      mockState.flowType.value = 'primary'

      const searchItem = {
        parameters: {},
        flowType: 'alternative'
      }

      expect(historyManager.isCurrentSearch(searchItem)).toBe(false)
    })

    it('should handle null search item', () => {
      expect(historyManager.isCurrentSearch(null)).toBe(false)
    })
  })

  describe('edge cases', () => {
    it('should handle missing search history service', () => {
      historyManager.searchHistory = null

      expect(historyManager.getHistoryItems()).toEqual([])
      expect(historyManager.getHistoryItem('1')).toBeNull()
      expect(historyManager.hasHistory()).toBe(false)
      expect(historyManager.getMostRecentSearch()).toBeNull()
      expect(historyManager.getSearchCount()).toBe(0)

      // These should not throw
      historyManager.clearHistory()
      historyManager.removeHistoryItem('1')
    })

    it('should handle empty history', () => {
      mockSearchHistory.getHistory.mockReturnValue([])

      expect(historyManager.getMostRecentSearch()).toBeNull()
      expect(historyManager.hasHistory()).toBe(false)
    })

    it('should handle partial search parameters', async () => {
      const searchItem = {
        parameters: {
          make: 'toyota'
          // missing model, year, etc.
        },
        flowType: 'primary'
      }

      await historyManager.populateFormFromSearch(searchItem)

      expect(mockState.selectedMake.value).toBe('toyota')
      expect(mockVehicleLoader.loadModels).toHaveBeenCalledWith('toyota', undefined)
    })
  })
})