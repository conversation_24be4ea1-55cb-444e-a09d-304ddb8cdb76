/**
 * Unit Tests for Search Executor Module
 * Tests search execution, bot protection, analytics, and error handling
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createSearchExecutor } from '../../../src/stores/modules/search-executor.js'

describe('SearchExecutor', () => {
  let searchExecutor
  let mockApiClient
  let mockState
  let mockWidgetEvents
  let mockAnalytics
  let mockSearchHistory

  beforeEach(() => {
    // Mock API client
    mockApiClient = {
      call: vi.fn().mockResolvedValue({
        data: {
          data: [
            { id: 1, make: 'Toyota', model: 'Camry', year: 2023 },
            { id: 2, make: 'Toyota', model: 'Camry', year: 2022 }
          ]
        }
      })
    }

    // Mock state
    mockState = {
      canSearch: { value: true },
      loadingResults: { value: false },
      activeSearchSignatureInFlight: { value: '' },
      results: { value: [] },
      flowType: { value: 'primary' },
      selectedYear: { value: '2023' },
      selectedMake: { value: 'toyota' },
      selectedModel: { value: 'camry' },
      selectedModification: { value: '' },
      selectedGeneration: { value: '' },
      years: { value: [] },
      makes: { value: [] },
      models: { value: [] },
      modifications: { value: [] },
      generations: { value: [] },
      setError: vi.fn(),
      clearError: vi.fn(),
      findOption: vi.fn().mockReturnValue({ value: 'test', label: 'Test' })
    }

    // Mock widget events
    mockWidgetEvents = {
      emit: vi.fn()
    }

    // Mock analytics
    mockAnalytics = {
      trackSearch: vi.fn(),
      trackEvent: vi.fn(),
      trackError: vi.fn()
    }

    // Mock search history
    mockSearchHistory = {
      addSearch: vi.fn()
    }

    // Create search executor instance
    searchExecutor = createSearchExecutor(
      mockApiClient,
      mockState,
      mockWidgetEvents,
      mockAnalytics,
      mockSearchHistory
    )
  })

  describe('searchByVehicle', () => {
    it('should execute successful search', async () => {
      const results = await searchExecutor.searchByVehicle()

      expect(results).toHaveLength(2)
      expect(results[0].make).toBe('Toyota')
      expect(mockApiClient.call).toHaveBeenCalledWith('search_by_model', {
        make: 'toyota',
        model: 'camry',
        year: '2023'
      })
    })

    it('should prevent duplicate searches', async () => {
      // Set active search signature
      mockState.activeSearchSignatureInFlight.value = 
        'search_by_model:{"make":"toyota","model":"camry","year":"2023"}'

      const results = await searchExecutor.searchByVehicle()

      expect(results).toBeNull()
      expect(mockApiClient.call).not.toHaveBeenCalled()
    })

    it('should validate search readiness', async () => {
      mockState.canSearch.value = false

      const results = await searchExecutor.searchByVehicle()

      expect(results).toBeNull()
      expect(mockState.setError).toHaveBeenCalledWith(
        'Please select all required fields before searching'
      )
      expect(mockApiClient.call).not.toHaveBeenCalled()
    })

    it('should track search with analytics', async () => {
      await searchExecutor.searchByVehicle()

      expect(mockAnalytics.trackSearch).toHaveBeenCalledWith('search_initiate', {
        search_type: 'by_vehicle',
        selected_year: '2023',
        selected_make: 'toyota',
        selected_model: 'camry',
        selected_modification: '',
        selected_generation: ''
      })

      expect(mockAnalytics.trackSearch).toHaveBeenCalledWith('search_complete', 
        expect.objectContaining({
          search_type: 'by_vehicle',
          results_count: 2
        })
      )
    })

    it('should emit widget events', async () => {
      await searchExecutor.searchByVehicle()

      expect(mockWidgetEvents.emit).toHaveBeenCalledWith('search:start', {
        search_type: 'by_vehicle',
        parameters: {
          year: '2023',
          make: 'toyota',
          model: 'camry',
          generation: '',
          modification: ''
        }
      })

      expect(mockWidgetEvents.emit).toHaveBeenCalledWith('search:complete', 
        expect.objectContaining({
          search_type: 'by_vehicle',
          results_count: 2
        })
      )
    })

    it('should save successful search to history', async () => {
      await searchExecutor.searchByVehicle()

      expect(mockSearchHistory.addSearch).toHaveBeenCalledWith({
        flowType: 'primary',
        year: '2023',
        make: 'toyota',
        model: 'camry',
        modification: '',
        generation: '',
        options: expect.any(Object)
      })
    })

    it('should handle API errors', async () => {
      const error = new Error('API Error')
      mockApiClient.call.mockRejectedValue(error)

      const results = await searchExecutor.searchByVehicle()

      expect(results).toBeNull()
      expect(mockState.setError).toHaveBeenCalledWith('API Error')
      expect(mockWidgetEvents.emit).toHaveBeenCalledWith('search:error', {
        search_type: 'by_vehicle',
        error_message: 'API Error'
      })
    })

    it('should clear loading state on error', async () => {
      mockApiClient.call.mockRejectedValue(new Error('API Error'))
      mockState.loadingResults.value = true

      await searchExecutor.searchByVehicle()

      expect(mockState.loadingResults.value).toBe(false)
    })
  })

  describe('buildSearchParams', () => {
    it('should build params for primary flow', () => {
      mockState.flowType.value = 'primary'
      mockState.selectedModification.value = 'mod-123'

      const params = searchExecutor.buildSearchParams()

      expect(params).toEqual({
        make: 'toyota',
        model: 'camry',
        year: '2023',
        modification: 'mod-123'
      })
    })

    it('should build params for alternative flow', () => {
      mockState.flowType.value = 'alternative'
      mockState.selectedGeneration.value = 'gen-456'
      mockState.selectedModification.value = 'mod-789'

      const params = searchExecutor.buildSearchParams()

      expect(params).toEqual({
        make: 'toyota',
        model: 'camry',
        generation: 'gen-456',
        modification: 'mod-789'
      })
    })

    it('should build params for year_select flow', () => {
      mockState.flowType.value = 'year_select'

      const params = searchExecutor.buildSearchParams()

      expect(params).toEqual({
        make: 'toyota',
        model: 'camry',
        year: '2023'
      })
    })

    it('should omit empty modification', () => {
      mockState.selectedModification.value = ''

      const params = searchExecutor.buildSearchParams()

      expect(params).not.toHaveProperty('modification')
    })
  })

  describe('isDuplicateSearch', () => {
    it('should detect duplicate search', () => {
      const params = { make: 'toyota', model: 'camry', year: '2023' }
      const signature = 'search_by_model:{"make":"toyota","model":"camry","year":"2023"}'
      mockState.activeSearchSignatureInFlight.value = signature

      const isDuplicate = searchExecutor.isDuplicateSearch(params)

      expect(isDuplicate).toBe(true)
    })

    it('should allow new search', () => {
      const params = { make: 'toyota', model: 'camry', year: '2023' }

      const isDuplicate = searchExecutor.isDuplicateSearch(params)

      expect(isDuplicate).toBe(false)
      expect(mockState.activeSearchSignatureInFlight.value).toBeTruthy()
    })
  })

  describe('processSearchResults', () => {
    it('should process API response with nested data', () => {
      const response = {
        data: {
          data: [{ id: 1 }, { id: 2 }]
        }
      }

      const results = searchExecutor.processSearchResults(response)

      expect(results).toEqual([{ id: 1 }, { id: 2 }])
      expect(mockState.results.value).toEqual([{ id: 1 }, { id: 2 }])
    })

    it('should handle direct data array', () => {
      const response = {
        data: [{ id: 1 }, { id: 2 }]
      }

      const results = searchExecutor.processSearchResults(response)

      expect(results).toEqual([{ id: 1 }, { id: 2 }])
    })

    it('should handle empty response', () => {
      const response = {}

      const results = searchExecutor.processSearchResults(response)

      expect(results).toEqual([])
    })
  })

  describe('cancelSearch', () => {
    it('should cancel active search', () => {
      mockState.activeSearchSignatureInFlight.value = 'active-search'
      mockState.loadingResults.value = true

      searchExecutor.cancelSearch()

      expect(mockState.activeSearchSignatureInFlight.value).toBe('')
      expect(mockState.loadingResults.value).toBe(false)
    })
  })

  describe('isSearching', () => {
    it('should return loading state', () => {
      mockState.loadingResults.value = false
      expect(searchExecutor.isSearching()).toBe(false)

      mockState.loadingResults.value = true
      expect(searchExecutor.isSearching()).toBe(true)
    })
  })

  describe('getCurrentSearchParams', () => {
    it('should return current search parameters', () => {
      const params = searchExecutor.getCurrentSearchParams()

      expect(params).toEqual({
        make: 'toyota',
        model: 'camry',
        year: '2023'
      })
    })
  })

  describe('handleBotProtection', () => {
    it('should proceed without bot protection', async () => {
      const result = await searchExecutor.handleBotProtection()
      expect(result).toBe(true)
    })

    it('should handle bot protection module not available', async () => {
      // Mock dynamic import failure
      searchExecutor.initBotProtection = vi.fn().mockResolvedValue(null)

      const result = await searchExecutor.handleBotProtection()
      expect(result).toBe(true)
    })
  })

  describe('scheduleIframeResize', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('should emit results display event after delay', async () => {
      searchExecutor.scheduleIframeResize(5)

      await vi.runAllTimersAsync()

      expect(mockWidgetEvents.emit).toHaveBeenCalledWith('results:display', {
        results_count: 5
      })
    })

    it('should call parentIFrame.size if available', async () => {
      const mockSize = vi.fn()
      window.parentIFrame = { size: mockSize }

      searchExecutor.scheduleIframeResize(3)

      await vi.runAllTimersAsync()

      expect(mockSize).toHaveBeenCalled()

      delete window.parentIFrame
    })
  })
})