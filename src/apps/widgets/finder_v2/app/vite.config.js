import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    tailwindcss()
  ],
  // Set base URL for static assets
  base: '/static/finder_v2/',
  build: {
    // Output to Django static directory
    outDir: 'dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        // Main app entry point
        'finder-v2-app': resolve(__dirname, 'src/main.js'),
        // Separate libs bundle for third-party dependencies
        'finder-v2-app-libs': resolve(__dirname, 'src/libs.js')
      },
      output: {
        // Manual chunks for better code splitting
        manualChunks: {
          'vue-core': ['vue', 'pinia'],
          'ui-components': ['@headlessui/vue'],
          'http-client': ['axios']
        },
        // Organize output files
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name.includes('libs')) {
            return 'js/[name].js'
          }
          return 'js/[name].js'
        },
        chunkFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.name.endsWith('.css')) {
            if (assetInfo.name.includes('libs')) {
              return 'css/[name][extname]'
            }
            return 'css/[name][extname]'
          }
          return 'assets/[name]-[hash][extname]'
        }
      },
      // Enhanced tree shaking
      treeshake: {
        preset: 'recommended',
        moduleSideEffects: false
      }
    },
    // Bundle size optimization
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false,  // Keep console.log for debugging
        drop_debugger: true,
        pure_funcs: [],  // Don't remove console functions
        passes: 2,  // Multiple passes for better compression
        dead_code: true,
        unused: true
      },
      mangle: {
        properties: {
          regex: /^_/  // Mangle properties starting with underscore
        }
      },
      format: {
        comments: false  // Remove all comments
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  // Development server configuration
  server: {
    port: 3000,
    host: true,
    cors: true
  },
  // CSS configuration
  css: {
    devSourcemap: true
  }
})
