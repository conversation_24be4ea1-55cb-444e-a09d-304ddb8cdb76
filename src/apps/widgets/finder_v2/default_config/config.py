from src.apps.widgets.finder_v2.default_config.themes import FinderV2Themes


FINDER_V2_DEFAULT_CONFIG = {
    "interface": {
        "dimensions": {
            "width": "100%",
            "height": ""
        },
        "display": {
            "output_template": ""  # Empty string = use default table layout
        },
        "flow_type": "primary",  # New: 'primary' (Year→Make→Model) or 'alternative' (Make→Model→Generation)
        "api_version": "v2",     # New: API version for this widget type
        "tabs": {
            "visible": [
                "by_vehicle"
            ],
            "primary": "by_vehicle"
        },
        "blocks": {
            "button_to_ws": {
                # Will be hidden only if subscription is paid
                "hide": True,
            }
        },
        "translation": {
            "year_label": "Year",
            "make_label": "Make", 
            "model_label": "Model",
            "generation_label": "Generation",
            "modification_label": "Modification",
            "select_year": "Select Year",
            "select_make": "Select Make",
            "select_model": "Select Model", 
            "select_generation": "Select Generation",
            "select_modification": "Select Modification",
            "loading": "Loading...",
            "loading_results": "Loading results...",
            "no_results": "No results found. Please try different search criteria.",
            "search_button": "Unlock More Insights at Wheel-Size.com"
        },
    },
    "permissions": {
        "domains": []
    },
    "content": {
        "regions": [],
        "filter": {
            "brands": [],
            "countries": [],
            "brands_exclude": [],
            "countries_exclude": [],
            "by": None
        },
        "only_oem": False
    },
    "theme": {
        "active": FinderV2Themes.get_default(),
        "inactive": None,
    },
    "search_history": {
        "enabled": True,
        "maxItems": 10,
        "displayItems": 5,
        "autoExpand": False,
        "showTimestamps": True
    }
}
