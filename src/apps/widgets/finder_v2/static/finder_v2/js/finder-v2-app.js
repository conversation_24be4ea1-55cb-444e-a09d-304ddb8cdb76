const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/useBotProtection-DKPGrmyF.js","js/listbox-Css7ETHV.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=Object.defineProperties,a=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,r=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,n=Reflect.get,s=(t,a,o)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[a]=o,c=(e,t)=>{for(var a in t||(t={}))l.call(t,a)&&s(e,a,t[a]);if(o)for(var a of o(t))i.call(t,a)&&s(e,a,t[a]);return e},d=(e,o)=>t(e,a(o)),u=(e,t,a)=>n(r(e),a,t),h=(e,t,a)=>new Promise((o,r)=>{var l=e=>{try{n(a.next(e))}catch(t){r(t)}},i=e=>{try{n(a.throw(e))}catch(t){r(t)}},n=e=>e.done?o(e.value):Promise.resolve(e.value).then(l,i);n((a=a.apply(e,t)).next())});import{r as g,c as m,o as v,a as f,b as y,d as p,w,_ as k,n as S,e as b,f as _,g as M,h as C,i as x,j as E,t as F,F as I,A as L,k as T,I as R,l as A,m as P,p as z,q as O,s as V,T as B,u as D,v as $,x as H,y as U,z as N,B as j,C as Y,D as G,E as J,G as q,H as W}from"./listbox-Css7ETHV.js";import{useBotProtection as K}from"./useBotProtection-DKPGrmyF.js";function X(e){const t=g([]),a=g(!0),o=g(10),r=g(5),l=`finder_v2_search_history_${e}`,i=m(()=>{try{const e="__localStorage_test__";return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}});function n(){if(t.value=[],i.value)try{localStorage.removeItem(l)}catch(e){}}function s(e){a.value=e,e||n(),y()}const d=m(()=>t.value.slice(0,r.value)),u=m(()=>t.value.length>r.value);function h(e){const t=[],a=e.options||{};function o(t,a){if(!t)return null;if("year"===a)return t.name||t.year||t.slug||t.id;if("make"===a||"model"===a){const e=t.name||t.slug||t.id;return"string"==typeof e?e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):e}if("modification"===a){if(t.engine||t.trim||t.generation){const a=[];let o=t.name||"";if(("primary"===e.flowType||"year_select"===e.flowType)&&t.generation){const e=t.generation;if(e.name&&""!==e.name.trim()){const t=`${e.start}-${e.end}`;a.push(`${e.name} (${t})`)}else a.push(`${e.start}-${e.end}`)}if(t.engine){const e=[];t.engine.capacity&&e.push(t.engine.capacity),t.engine.type&&e.push(t.engine.type),t.engine.fuel&&e.push(t.engine.fuel),t.engine.power&&t.engine.power.hp&&e.push(`${t.engine.power.hp}HP`),e.length>0&&a.push(e.join(" "))}return a.length>0?`${o} ${a.join(" • ")}`.trim():o}return t.name||t.slug||t.id}if("generation"===a){if(t.year_ranges&&Array.isArray(t.year_ranges)){const e=t.year_ranges.join(", ");return t.name&&""!==t.name.trim()?`${t.name} (${e})`:e}return t.name||t.slug||t.id}return t.name||t.slug||t.id}if(e.year){const r=o(a.year,"year")||e.year;t.push(r)}if(e.make){const r=o(a.make,"make")||e.make;t.push(r)}if(e.model){const r=o(a.model,"model")||e.model;t.push(r)}if(e.modification){const r=o(a.modification,"modification")||e.modification;t.push(r)}if(e.generation&&"alternative"===e.flowType){const r=o(a.generation,"generation")||e.generation;t.push(`(${r})`)}return t.join(" ")||"Unknown Search"}function v(e){if(!e||"object"!=typeof e)return{};const t=c({},e);return t.parameters||(t.parameters={year:t.year||"",make:t.make||"",model:t.model||"",modification:t.modification||"",generation:t.generation||""}),t.timestamp&&"number"!=typeof t.timestamp?t.timestamp=Number(t.timestamp)||Date.now():t.timestamp||(t.timestamp=Date.now()),t.id||(t.id=`${t.timestamp}_${Math.random().toString(36).substr(2,9)}`),t}function f(e){return!(!e||"object"!=typeof e)&&(!!(e.id&&e.timestamp&&e.parameters)&&(!("number"!=typeof e.timestamp||e.timestamp<=0)&&!(!e.parameters||"object"!=typeof e.parameters)))}function y(){if(i.value)try{const e={version:"1.0",enabled:a.value,searches:t.value};localStorage.setItem(l,JSON.stringify(e))}catch(e){"QuotaExceededError"===e.name&&function(){try{const e=t.value.slice(0,Math.floor(t.value.length/2));t.value=e,y()}catch(e){n()}}()}}return function(){if(i.value)try{const e=localStorage.getItem(l);if(e){const o=JSON.parse(e);if(function(e){if(!e||"object"!=typeof e)return!1;if(!Array.isArray(e.searches))return!1;return!0}(o)){a.value=!1!==o.enabled;const e=(o.searches||[]).map(v).filter(f);e.sort((e,t)=>t.timestamp-e.timestamp),t.value=e,y()}else n()}}catch(e){n()}else a.value=!1}(),{searches:t,isEnabled:a,displaySearches:d,hasMoreSearches:u,maxItems:o,displayItems:r,isLocalStorageAvailable:i,addSearch:function(e){if(a.value&&i.value)try{const a=function(e){const t=Date.now();return{id:`${t}_${Math.random().toString(36).substr(2,9)}`,timestamp:t,description:h(e),flowType:e.flowType||"primary",parameters:{year:e.year||"",make:e.make||"",model:e.model||"",modification:e.modification||"",generation:e.generation||""},options:e.options||{}}}(e),r=t.value.findIndex(e=>{return t=e.parameters,o=a.parameters,["year","make","model","modification","generation"].every(e=>(t[e]||"")===(o[e]||""));var t,o});-1!==r&&t.value.splice(r,1),t.value.unshift(a),t.value.length>o.value&&(t.value=t.value.slice(0,o.value)),y()}catch(r){}},removeSearch:function(e){if(a.value)try{const a=t.value.findIndex(t=>t.id===e);-1!==a&&(t.value.splice(a,1),y())}catch(o){}},getSearch:function(e){return t.value.find(t=>t.id===e)||null},updateSearchTimestamp:function(e){if(a.value)try{const a=t.value.findIndex(t=>t.id===e);if(-1!==a){const e=t.value.splice(a,1)[0];e.timestamp=Date.now(),t.value.unshift(e),y()}}catch(o){}},clearHistory:n,setEnabled:s,configure:function(e){void 0!==e.maxItems&&(o.value=Math.max(1,Math.min(50,e.maxItems))),void 0!==e.displayItems&&(r.value=Math.max(1,Math.min(o.value,e.displayItems))),void 0!==e.enabled&&s(e.enabled),t.value.length>o.value&&(t.value=t.value.slice(0,o.value),y())},getRelativeTime:function(e){const t=Date.now()-e,a=Math.floor(t/1e3),o=Math.floor(a/60),r=Math.floor(o/60),l=Math.floor(r/24);return l>0?`${l} day${l>1?"s":""} ago`:r>0?`${r} hour${r>1?"s":""} ago`:o>0?`${o} minute${o>1?"s":""} ago`:"Just now"}}}function Z(e={}){const t=g(!0),a=g(!1),o=g("G-7HHLF4RGTD"),r=g(!1),l=g(Date.now()),i=m(()=>{var t;return{widget_uuid:e.uuid||e.id||e.widgetUuid||"unknown",widget_type:"finder-v2",client_hostname:p(),api_version:e.apiVersion||"v2",flow_type:e.flowType||"primary",theme_name:(null==(t=e.theme)?void 0:t.name)||"default",widget_width:e.width||"auto",widget_height:e.height||"auto",subscription_paid:e.subscriptionPaid||!1,user_profile_uuid:e.userProfileUuid||""}}),n=()=>h(this,null,function*(){var l,n,h,g;if(!a.value&&t.value)try{if(!k())return void(t.value=!1);const m=(null==(l=e.analytics)?void 0:l.measurementId)||e.ga4MeasurementId||(null==(h=null==(n=window.FinderV2Config)?void 0:n.analytics)?void 0:h.measurementId);m&&"G-XXXXXXXXXX"!==m&&(o.value=m),r.value="development.local"===window.location.hostname||"localhost"===window.location.hostname||!0===(null==(g=e.analytics)?void 0:g.debugMode),yield s(),u(),a.value=!0,y("widget_load",d(c({},i.value),{page_url:w(),referrer:document.referrer||"",timestamp:Date.now()}))}catch(m){t.value=!1}}),s=()=>new Promise((e,t)=>{if(window.gtag)return void e();const a=document.createElement("script");a.async=!0,a.src=`https://www.googletagmanager.com/gtag/js?id=${o.value}`,a.onload=()=>e(),a.onerror=()=>t(new Error("Failed to load gtag script")),document.head.appendChild(a)}),u=()=>{window.dataLayer=window.dataLayer||[],window.gtag=window.gtag||function(){window.dataLayer.push(arguments)},gtag("js",new Date);const e={linker:{domains:["wheel-size.com","services.wheel-size.com","development.local"]},anonymize_ip:!0,allow_google_signals:!1,allow_ad_personalization_signals:!1,custom_map:{custom_parameter_1:"widget_uuid",custom_parameter_2:"user_profile_uuid",custom_parameter_3:"client_hostname",custom_parameter_4:"widget_type",custom_parameter_5:"flow_type"},debug_mode:r.value,send_page_view:!1};gtag("config",o.value,e),r.value},y=(e,o={})=>{if(a.value&&t.value&&"function"==typeof window.gtag)try{const t=d(c({},o),{widget_uuid:i.value.widget_uuid,widget_type:i.value.widget_type,client_hostname:i.value.client_hostname,flow_type:i.value.flow_type,api_version:i.value.api_version,session_duration:Date.now()-l.value,timestamp:Date.now()});Object.keys(t).forEach(e=>{void 0!==t[e]&&null!==t[e]&&""!==t[e]||delete t[e]}),gtag("event",e,t),r.value}catch(n){}else r.value},p=()=>{try{return window.parent&&window.parent!==window&&window.parent.location?window.parent.location.hostname:window.location.hostname}catch(e){try{const e=document.referrer;if(e){return new URL(e).hostname}}catch(t){}return"unknown"}},w=()=>{try{return window.parent&&window.parent!==window&&window.parent.location?window.parent.location.href:document.referrer||window.location.href}catch(e){return document.referrer||"unknown"}},k=()=>{var t;if("1"===navigator.doNotTrack||"yes"===navigator.doNotTrack)return!1;try{if("true"===localStorage.getItem("ws_widget_analytics_opt_out"))return!1}catch(a){}return!1!==(null==(t=e.analytics)?void 0:t.enabled)};return v(()=>{setTimeout(()=>{n()},100)}),v(()=>{setTimeout(()=>{a.value&&y("widget_ready",d(c({},i.value),{load_time:Date.now()-l.value}))},500)}),f(()=>{a.value&&y("widget_unload",{session_duration:Date.now()-l.value})}),{isEnabled:t,isInitialized:a,isDebugMode:r,initialize:n,trackEvent:y,trackInteraction:(e,t={})=>{y("widget_interaction",c({interaction_type:e},t))},trackSearch:(e,t={})=>{y("widget_search",c({search_type:e,search_flow_type:i.value.flow_type},t))},trackError:(e,t={})=>{y("widget_error",c({error_type:e,widget_state:t.widgetState||"unknown"},t))},trackPerformance:(e,t={})=>{y("widget_performance",c({metric_type:e},t))},disable:()=>{t.value=!1},enable:()=>{t.value||(t.value=!0,a.value||n())},getStatus:()=>({isEnabled:t.value,isInitialized:a.value,measurementId:o.value,isDebugMode:r.value,widgetContext:i.value}),getClientHostname:p,getClientPageUrl:w}}function Q(){const e=g("unknown"),t=g(""),a=g(""),o=g(!1),r=g(!1);let l=null;const i=()=>{o.value=window.parent&&window.parent!==window,o.value?n():(e.value=window.location.hostname,t.value=window.location.href,a.value=document.title,r.value=!0)},n=()=>{s(),c(),d()},s=()=>{try{if(window.parent&&window.parent.location)return e.value=window.parent.location.hostname,t.value=window.parent.location.href,a.value=window.parent.document.title,r.value=!0,!0}catch(o){}return!1},c=()=>{l=e=>{u(e)},window.addEventListener("message",l)},d=()=>{if(!o.value)return;const e={type:"widget_request_parent_info",widgetId:m(),timestamp:Date.now(),source:"finder-v2-widget"};try{window.parent.postMessage(e,"*"),setTimeout(()=>{r.value||h()},2e3)}catch(t){h()}},u=o=>{try{const l=o.data;if("widget_parent_info"===l.type){const o=m();if(l.widgetId&&l.widgetId!==o)return;e.value=l.hostname||"unknown",t.value=l.url||"",a.value=l.title||"",r.value=!0}else"widget_analytics_config"===l.type&&y(l)}catch(l){}},h=()=>{try{const a=document.referrer;if(a){const o=new URL(a);e.value=o.hostname,t.value=a,r.value=!0}}catch(a){}},m=()=>{const e=window.FinderV2Config||{};return e.uuid||e.id||e.widgetUuid||"unknown"},y=e=>{window.dispatchEvent(new CustomEvent("parentAnalyticsConfig",{detail:e.config}))},p=()=>{l&&(window.removeEventListener("message",l),l=null)};return v(()=>{i()}),f(()=>{p()}),{parentHostname:e,parentUrl:t,parentTitle:a,isInIframe:o,communicationEstablished:r,initialize:i,requestParentInfo:d,requestAnalyticsConfig:()=>{if(!o.value)return;const e={type:"widget_request_analytics_config",widgetId:m(),timestamp:Date.now(),source:"finder-v2-widget"};try{window.parent.postMessage(e,"*")}catch(t){}},notifyParentAnalyticsEvent:(e,t)=>{if(!o.value)return;const a={type:"widget_analytics_event",widgetId:m(),eventName:e,eventData:t,timestamp:Date.now(),source:"finder-v2-widget"};try{window.parent.postMessage(a,"*")}catch(r){}},getParentContextData:()=>({parent_hostname:e.value,parent_url:t.value,parent_title:a.value,is_in_iframe:o.value,communication_established:r.value}),cleanup:p}}let ee=null;function te(){if(ee)return ee;const e=g(window.FinderV2Config||{});let t=null;const a=[],o=[],r=g("loading"!==document.readyState),l=g(!1),i=g(!1),n=g(!1);function s(r,l={}){const i=function(a,o={}){let r={};try{r="function"==typeof t&&t()||{}}catch(l){}return{src:window.location.href,type:a,data:d(c({},o),{context:c({config:function(){var t;try{const a=e.value||{},o=a.flowType||(null==(t=a.widgetConfig)?void 0:t.flowType)||void 0;return{widgetUuid:a.widgetUuid||a.uuid||a.id||void 0,type:a.type||"finder-v2",flowType:o}}catch(l){return{}}}()},r)})}}(r,l);for(const e of a)try{e(i)}catch(n){}try{window.parent&&window.parent!==window&&window.parent.postMessage(i,"*")}catch(s){}for(const e of o)try{e(i)}catch(n){}return i}function u(){!n.value&&l.value&&i.value&&(s("ready:window"),n.value=!0)}return r.value?s("ready:document"):document.addEventListener("DOMContentLoaded",()=>{r.value=!0,s("ready:document")}),"complete"===document.readyState?(l.value=!0,u()):window.addEventListener("load",()=>{l.value=!0,u()}),ee={emit:s,setConfig:function(t){e.value=t||{}},setContextProvider:function(e){t=e},markInitialDataLoaded:function(){i.value=!0,u()},onBeforeSend:function(e){"function"==typeof e&&a.push(e)},onAfterSend:function(e){"function"==typeof e&&o.push(e)}},ee}y.defaults.paramsSerializer=e=>{const t=new URLSearchParams;return Object.entries(e).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>{null!=a&&""!==a&&t.append(e,a)}):null!=a&&""!==a&&t.append(e,a)}),t.toString()};const ae=p("finder",()=>{const e=g({}),t=g(!1),a=g(!1),o=g(null),r=g([]),l=g("");let i=null,n=null,s=null;const d=te(),u=g(!1),v=g(!1),f=g(!1),p=g(!1),b=g(!1),_=g(!1),M=g(!1),C=g(!1),x=g(!1),E=g(!1),F=g(new Map),I=g(""),L=g(""),T=g(""),R=g(""),A=g(""),P=g(""),z=g([]),O=g([]),V=g([]),B=g([]),D=g([]),$=m(()=>e.value.flowType||"primary"),H=m(()=>e.value.apiVersion||"v2"),U=m(()=>e.value.widgetResources||{});function N(e=null,t=null){return h(this,null,function*(){var a;try{u.value=!0,_.value=!1;const o={};e&&(o.make=e),t&&(o.model=t);const r=yield oe("year",o);z.value=(null==(a=r.data)?void 0:a.data)||r.data||[],_.value=!0,n&&n.trackEvent("data_load_complete",{data_type:"years",data_count:z.value.length,make:e||"",model:t||""})}catch(r){o.value=r.message,n&&n.trackError("data_load_failed",{data_type:"years",error_message:r.message,api_endpoint:"year",make:e||"",model:t||""})}finally{u.value=!1}})}function j(e=null){return h(this,null,function*(){var t;try{v.value=!0,M.value=!1;const a=e?{year:e}:{};Object.assign(a,ee());const o=yield oe("make",a);O.value=(null==(t=o.data)?void 0:t.data)||o.data||[],M.value=!0}catch(a){o.value=a.message}finally{v.value=!1}})}function Y(e,t=null){return h(this,null,function*(){var a;try{f.value=!0,C.value=!1;const o={make:e};t&&(o.year=t);const r=yield oe("model",o);V.value=(null==(a=r.data)?void 0:a.data)||r.data||[],C.value=!0}catch(r){o.value=r.message}finally{f.value=!1}})}function G(e,t,a=null){return h(this,null,function*(){var r;try{b.value=!0,E.value=!1;const o={make:e,model:t};a&&("primary"===$.value||"year_select"===$.value?o.year=a:"alternative"===$.value&&(o.generation=a));const l=yield oe("modification",o);B.value=(null==(r=l.data)?void 0:r.data)||l.data||[],E.value=!0}catch(l){o.value=l.message}finally{b.value=!1}})}function J(e,t){return h(this,null,function*(){var a;try{p.value=!0,x.value=!1;const o={make:e,model:t},r=yield oe("generation",o);D.value=(null==(a=r.data)?void 0:a.data)||r.data||[],x.value=!0}catch(r){o.value=r.message}finally{p.value=!1}})}function q(){return h(this,null,function*(){var e;const t=performance.now();let l=null;try{l=(yield k(()=>import("./useBotProtection-DKPGrmyF.js"),__vite__mapDeps([0,1]))).useBotProtection()}catch(s){}try{if(a.value=!0,o.value=null,l&&!l.hasValidChallengeToken()){o.value="Verifying you are human... This may take a few seconds.";if(!(yield l.solveAndVerifyChallenge()))return o.value="Unable to verify. Please ensure JavaScript is enabled and try again.",void(a.value=!1);o.value=null}const s={make:T.value,model:R.value};"primary"===$.value||"year_select"===$.value?(s.year=L.value,s.modification=A.value):"alternative"===$.value&&(s.generation=P.value,s.modification=A.value);const u=c({},s);"primary"===$.value||"year_select"===$.value?(u.year=L.value,u.modification=A.value):"alternative"===$.value&&(u.generation=P.value,u.modification=A.value);const g=`search_by_model:${JSON.stringify(u)}`;if(I.value===g)return;I.value=g,d.emit("search:start",{search_type:"by_vehicle",parameters:{year:L.value,make:T.value,model:R.value,generation:P.value,modification:A.value}}),n&&n.trackSearch("search_initiate",{search_type:"by_vehicle",selected_year:L.value,selected_make:T.value,selected_model:R.value,selected_modification:A.value,selected_generation:P.value});const m=performance.now(),v=yield oe("search_by_model",s),f=performance.now();r.value=(null==(e=v.data)?void 0:e.data)||v.data||[];const y=performance.now()-t,p=f-m;if(d.emit("search:complete",{search_type:"by_vehicle",results_count:r.value.length,timing_ms:{search_total:Math.round(y),api:Math.round(p)}}),n&&(n.trackSearch("search_complete",{search_type:"by_vehicle",selected_year:L.value,selected_make:T.value,selected_model:R.value,selected_modification:A.value,selected_generation:P.value,results_count:r.value.length,search_completion_time:Math.round(y),api_response_time:Math.round(p)}),n.trackEvent("search_results_view",{results_count:r.value.length,search_type:"by_vehicle"})),i&&r.value.length>0){const e=z.value.find(e=>e.slug===L.value||e.id===L.value),t=O.value.find(e=>e.slug===T.value||e.id===T.value),a=V.value.find(e=>e.slug===R.value||e.id===R.value),o=B.value.find(e=>e.slug===A.value||e.id===A.value),r=D.value.find(e=>e.slug===P.value||e.id===P.value),l={flowType:$.value,year:L.value,make:T.value,model:R.value,modification:A.value,generation:P.value,options:{year:e,make:t,model:a,modification:o,generation:r}};i.addSearch(l)}setTimeout(()=>h(this,null,function*(){window.parentIFrame&&window.parentIFrame.size(),yield S(),d.emit("results:display",{results_count:r.value.length})}),100)}catch(u){o.value=u.message,d.emit("search:error",{search_type:"by_vehicle",error_message:(null==u?void 0:u.message)||String(u)}),n&&n.trackError("search_failed",{error_message:u.message,search_type:"by_vehicle",api_endpoint:"search_by_model",selected_year:L.value,selected_make:T.value,selected_model:R.value,widget_state:"searching"})}finally{a.value=!1,I.value=""}})}function W(){L.value="",T.value="",R.value="",A.value="",P.value="",V.value=[],B.value=[],D.value=[]}function K(){r.value=[],setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},50)}function ee(){var t,a,o,r,l;const i=(null==(a=null==(t=e.value)?void 0:t.content)?void 0:a.filter)||(null==(o=e.value)?void 0:o.filter)||{},n=i.by||(null==(l=null==(r=e.value)?void 0:r.content)?void 0:l.by)||"",s=e=>"string"==typeof e?e:(null==e?void 0:e.slug)||(null==e?void 0:e.value)||"",c={};if("brands"===n&&Array.isArray(i.brands)&&i.brands.length){const e=i.brands.map(s).filter(Boolean);e.length&&(c.brands=e.join(","))}else if("brands_exclude"===n&&Array.isArray(i.brands_exclude)&&i.brands_exclude.length){const e=i.brands_exclude.map(s).filter(Boolean);e.length&&(c.brands_exclude=e.join(","))}return c}function ae(){var t,a;const o=(null==(a=null==(t=e.value)?void 0:t.content)?void 0:a.regions)||[];return o.length?{region:o}:{}}function oe(t){return h(this,arguments,function*(t,a={}){const o=function(e,t){const a=Object.keys(t).sort().reduce((e,a)=>(e[a]=t[a],e),{});return`${e}:${JSON.stringify(a)}`}(t,a);if(F.value.has(o))return yield F.value.get(o);const r=function(t){return h(this,arguments,function*(t,a={}){const o=U.value[t];if(!o||!o[1])throw new Error(`API endpoint not configured: ${t}`);let r=o[1];return r.startsWith("/")&&e.value.baseUrl&&(r=e.value.baseUrl+r),["make","model","year","generation","modification"].includes(t)&&Object.assign(a,ae()),yield y.get(r,{params:a})})}(t,a);F.value.set(o,r);try{return yield r}finally{F.value.delete(o)}})}function re(e,t){var a,o,r,l,i,n;if(!t)return null;const s=(null==(o=null==(a=e.value)?void 0:a.find)?void 0:o.call(a,e=>(null==e?void 0:e.slug)===t||(null==e?void 0:e.id)===t))||null,c=null!=(r=null==s?void 0:s.slug)?r:"string"==typeof t?t:String(t);return{slug:c,title:null!=(n=null!=(i=null!=(l=null==s?void 0:s.title)?l:null==s?void 0:s.name)?i:null==s?void 0:s.value)?n:String(c)}}return w(L,(e,t)=>{e!==t&&d.emit("change:year",{value:re(z,e)})}),w(T,(e,t)=>{e!==t&&d.emit("change:make",{value:re(O,e)})}),w(R,(e,t)=>{e!==t&&d.emit("change:model",{value:re(V,e)})}),w(P,(e,t)=>{e!==t&&d.emit("change:generation",{value:re(D,e)})}),w(A,(e,t)=>{e!==t&&d.emit("change:modification",{value:re(B,e)})}),{config:e,loading:t,loadingResults:a,error:o,results:r,outputTemplate:l,loadingYears:u,loadingMakes:v,loadingModels:f,loadingGenerations:p,loadingModifications:b,stateLoadedYears:_,stateLoadedMakes:M,stateLoadedModels:C,stateLoadedGenerations:x,stateLoadedModifications:E,selectedYear:L,selectedMake:T,selectedModel:R,selectedModification:A,selectedGeneration:P,years:z,makes:O,models:V,modifications:B,generations:D,flowType:$,apiVersion:H,widgetResources:U,initialize:function(a){var r;e.value=a,l.value=(null==(r=a.interface)?void 0:r.outputTemplate)||"";const c=a.id||a.uuid||a.widgetUuid||"default";i=X(c);const u=a.search_history||{};Object.keys(u).length>0&&i.configure(u),n=Z(a),s=Q(),d.setConfig(a),d.setContextProvider(()=>({widgetUuid:c,widgetType:"finder-v2",flowType:$.value,selections:{year:L.value,make:T.value,model:R.value,generation:P.value,modification:A.value}})),function e(a=0){U.value&&U.value.year?function(){h(this,null,function*(){try{t.value=!0,o.value=null,"primary"===$.value?yield N():yield j()}catch(e){o.value=e.message}finally{t.value=!1,d.markInitialDataLoaded()}})}():a<5?setTimeout(()=>e(a+1),100):o.value="Failed to initialize widget configuration"}()},loadYears:N,loadMakes:j,loadModels:Y,loadModifications:G,loadGenerations:J,searchByVehicle:q,resetVehicleSearch:W,clearResults:K,clearError:function(){o.value=null},buildBrandFilterParams:ee,buildRegionParams:ae,executeSearchFromHistory:function(e){return h(this,null,function*(){if(!i)return;const t=i.getSearch(e);if(t)try{K(),yield function(e){return h(this,null,function*(){const t=e.parameters,a=e.flowType||"primary";W();try{"primary"===a?(t.year&&(L.value=t.year,yield j(t.year)),t.make&&(T.value=t.make,yield Y(t.make,t.year)),t.model&&(R.value=t.model,yield G(t.make,t.model,t.year)),t.modification&&(A.value=t.modification)):"alternative"===a?(t.make&&(T.value=t.make,yield Y(t.make)),t.model&&(R.value=t.model,yield J(t.make,t.model)),t.generation&&(P.value=t.generation,yield G(t.make,t.model,t.generation)),t.modification&&(A.value=t.modification)):"year_select"===a&&(t.make&&(T.value=t.make,yield Y(t.make)),t.model&&(R.value=t.model,yield N(t.make,t.model)),t.year&&(L.value=t.year,yield G(t.make,t.model,t.year)),t.modification&&(A.value=t.modification))}catch(o){throw o}})}(t),i.updateSearchTimestamp(e),yield q()}catch(a){a.value="Failed to execute search from history"}})},getSearchHistory:function(){return i},getAnalytics:()=>n,getCrossDomainTracking:()=>s}});y.defaults.paramsSerializer=e=>{const t=new URLSearchParams;return Object.entries(e).forEach(([e,a])=>{Array.isArray(a)?a.forEach(a=>{null!=a&&""!==a&&t.append(e,a)}):null!=a&&""!==a&&t.append(e,a)}),t.toString()};class oe{constructor(e,t){this.config=e,this.widgetResources=t||{},this.ongoingRequests=new Map}call(e){return h(this,arguments,function*(e,t={}){console.log("🔍 API call - endpoint:",e),console.log("🔍 API call - widgetResources:",this.widgetResources);const a=this.widgetResources[e];if(console.log("🔍 API call - resource found:",a),!a||!a[1])throw console.error("🔍 API endpoint not configured:",e),console.error("🔍 Available endpoints:",Object.keys(this.widgetResources)),new Error(`API endpoint not configured: ${e}`);let o=a[1];o.startsWith("/")&&this.config.baseUrl&&(o=this.config.baseUrl+o),console.log("🔍 API call - URL before request:",o),console.log("🔍 API call - config.baseUrl:",this.config.baseUrl);const r=this.generateRequestKey(e,t);if(this.ongoingRequests.has(r))return yield this.ongoingRequests.get(r);const l=this.executeRequest(o,t);this.ongoingRequests.set(r,l);try{return yield l}finally{this.ongoingRequests.delete(r)}})}executeRequest(e,t){return h(this,null,function*(){var a,o;console.log("🔍 executeRequest - URL:",e),console.log("🔍 executeRequest - params:",t);try{const a=yield y.get(e,{params:t});return console.log("🔍 executeRequest - response:",a),a}catch(r){if(console.error("🔍 executeRequest - error:",r),r.response){const e=(null==(a=r.response.data)?void 0:a.message)||(null==(o=r.response.data)?void 0:o.error)||`API Error: ${r.response.status}`;throw new Error(e)}throw r.request?new Error("Network error: Unable to reach the server"):new Error(r.message||"An unexpected error occurred")}})}generateRequestKey(e,t){const a=Object.keys(t).sort().reduce((e,a)=>(e[a]=t[a],e),{});return`${e}:${JSON.stringify(a)}`}isRequestInProgress(e,t={}){const a=this.generateRequestKey(e,t);return this.ongoingRequests.has(a)}getOngoingRequestCount(){return this.ongoingRequests.size}cancelAllRequests(){this.ongoingRequests.clear()}}class re{constructor(e=50){this.cache=new Map,this.maxSize=e,this.accessOrder=[]}get(e){if(this.cache.has(e)){this.updateAccessOrder(e);const t=this.cache.get(e);if(this.isValidEntry(t))return console.log("🎯 Memory cache hit:",e),t.data;this.delete(e)}return null}set(e,t,a=36e5){this.cache.size>=this.maxSize&&this.evictLRU();const o={data:t,timestamp:Date.now(),ttl:a};this.cache.set(e,o),this.updateAccessOrder(e),console.log("💾 Memory cache set:",e)}delete(e){this.cache.delete(e);const t=this.accessOrder.indexOf(e);t>-1&&this.accessOrder.splice(t,1)}clear(){this.cache.clear(),this.accessOrder=[],console.log("🗑️ Memory cache cleared")}updateAccessOrder(e){const t=this.accessOrder.indexOf(e);t>-1&&this.accessOrder.splice(t,1),this.accessOrder.push(e)}evictLRU(){if(this.accessOrder.length>0){const e=this.accessOrder[0];this.delete(e),console.log("🔄 Memory cache LRU evicted:",e)}}isValidEntry(e){return!!(e&&e.timestamp&&e.ttl)&&Date.now()-e.timestamp<=e.ttl}getStats(){return{size:this.cache.size,maxSize:this.maxSize,keys:Array.from(this.cache.keys())}}}class le{constructor(e=512e3){this.maxSize=e,this.prefix="ws_finder_cache_v1_",this.metadataKey="ws_finder_cache_meta"}get(e){try{const t=localStorage.getItem(this.prefix+e);if(!t)return null;const a=JSON.parse(t);return Date.now()-a.timestamp>a.ttl?(this.delete(e),null):this.validateEntry(a)?(this.updateLastAccess(e),console.log("✅ Storage cache hit:",e),a.data):(this.delete(e),null)}catch(t){return console.error("Cache retrieval error:",t),null}}set(e,t,a=36e5){try{const o={data:t,timestamp:Date.now(),ttl:a,hash:this.generateHash(t)},r=JSON.stringify(o);return r.length>this.maxSize/10?(console.warn("Cache entry too large, skipping:",e),!1):this.ensureSpace(r.length)?(localStorage.setItem(this.prefix+e,r),this.updateMetadata(e,r.length),console.log("💾 Storage cache set:",e),!0):(console.warn("Cannot make space for cache entry:",e),!1)}catch(o){if("QuotaExceededError"===o.name){this.evictOldest();try{return localStorage.setItem(this.prefix+e,serialized),this.updateMetadata(e,serialized.length),!0}catch(r){return console.error("Cache storage failed after eviction:",r),!1}}return console.error("Cache storage error:",o),!1}}delete(e){localStorage.removeItem(this.prefix+e),this.removeFromMetadata(e)}clear(){const e=[];for(let t=0;t<localStorage.length;t++){const a=localStorage.key(t);a&&a.startsWith(this.prefix)&&e.push(a)}e.forEach(e=>localStorage.removeItem(e)),localStorage.removeItem(this.metadataKey),console.log("🗑️ Storage cache cleared")}validateEntry(e){if(!e.data||!e.timestamp||!e.ttl)return!1;if(e.hash){if(this.generateHash(e.data)!==e.hash)return console.warn("Cache entry failed integrity check"),!1}return!0}generateHash(e){const t=JSON.stringify(e);let a=0;for(let o=0;o<t.length;o++){a=(a<<5)-a+t.charCodeAt(o),a&=a}return a.toString(36)}getMetadata(){try{const e=localStorage.getItem(this.metadataKey);return e?JSON.parse(e):{version:"v1",entries:[],totalSize:0}}catch(e){return{version:"v1",entries:[],totalSize:0}}}updateMetadata(e,t){const a=this.getMetadata(),o=a.entries.findIndex(t=>t.key===e);o>-1&&(a.totalSize-=a.entries[o].size,a.entries.splice(o,1)),a.entries.push({key:e,size:t,timestamp:Date.now(),lastAccess:Date.now()}),a.totalSize+=t;try{localStorage.setItem(this.metadataKey,JSON.stringify(a))}catch(r){console.error("Failed to update metadata:",r)}}removeFromMetadata(e){const t=this.getMetadata(),a=t.entries.findIndex(t=>t.key===e);if(a>-1){t.totalSize-=t.entries[a].size,t.entries.splice(a,1);try{localStorage.setItem(this.metadataKey,JSON.stringify(t))}catch(o){console.error("Failed to update metadata:",o)}}}updateLastAccess(e){const t=this.getMetadata(),a=t.entries.find(t=>t.key===e);if(a){a.lastAccess=Date.now();try{localStorage.setItem(this.metadataKey,JSON.stringify(t))}catch(o){}}}ensureSpace(e){const t=this.getMetadata();if(t.totalSize+e<=this.maxSize)return!0;const a=t.totalSize+e-this.maxSize;return this.evictBySize(a)}evictOldest(){const e=this.getMetadata();if(0===e.entries.length)return;e.entries.sort((e,t)=>e.timestamp-t.timestamp);const t=Math.max(1,Math.floor(.2*e.entries.length));for(let o=0;o<t&&o<e.entries.length;o++){const t=e.entries[o];localStorage.removeItem(this.prefix+t.key),console.log("🔄 Storage cache evicted:",t.key)}e.entries=e.entries.slice(t),e.totalSize=e.entries.reduce((e,t)=>e+t.size,0);try{localStorage.setItem(this.metadataKey,JSON.stringify(e))}catch(a){console.error("Failed to update metadata after eviction:",a)}}evictBySize(e){const t=this.getMetadata();t.entries.sort((e,t)=>e.lastAccess-t.lastAccess);let a=0;const o=[];for(const l of t.entries){if(a>=e)break;o.push(l.key),a+=l.size}o.forEach(e=>{localStorage.removeItem(this.prefix+e),console.log("🔄 Storage cache evicted for space:",e)}),t.entries=t.entries.filter(e=>!o.includes(e.key)),t.totalSize=t.entries.reduce((e,t)=>e+t.size,0);try{localStorage.setItem(this.metadataKey,JSON.stringify(t))}catch(r){return console.error("Failed to update metadata after eviction:",r),!1}return a>=e}getStats(){const e=this.getMetadata();return{entries:e.entries.length,totalSize:e.totalSize,maxSize:this.maxSize,utilization:`${(e.totalSize/this.maxSize*100).toFixed(1)}%`}}invalidateEndpoint(e){const t=[];for(let a=0;a<localStorage.length;a++){const o=localStorage.key(a);o&&o.startsWith(this.prefix+e)&&t.push(o)}t.forEach(e=>{const t=e.replace(this.prefix,"");this.delete(t)}),console.log(`🗑️ Invalidated ${t.length} cache entries for ${e}`)}}class ie{static generate(e,t={}){const a=Object.keys(t).sort().reduce((e,a)=>(null!==t[a]&&void 0!==t[a]&&""!==t[a]&&(Array.isArray(t[a])?e[a]=t[a].sort().join(","):e[a]=String(t[a])),e),{});if(0===Object.keys(a).length)return e;return`${e}_${Object.entries(a).map(([e,t])=>`${e}_${t}`).join("_")}`}static parse(e){const t=e.split("_"),a=t[0],o={};for(let r=1;r<t.length;r+=2)if(r+1<t.length){const e=t[r],a=t[r+1];a.includes(",")?o[e]=a.split(","):o[e]=a}return{endpoint:a,params:o}}static generateSearchKey(e){const t=Object.keys(e).sort().reduce((t,a)=>(null!==e[a]&&void 0!==e[a]&&(t[a]=e[a]),t),{});return`search_${this.hashObject(t)}`}static hashObject(e){const t=JSON.stringify(e);let a=0;for(let o=0;o<t.length;o++){a=(a<<5)-a+t.charCodeAt(o),a&=a}return Math.abs(a).toString(36)}static generateWithConfig(e,t,a){const o=c({},t);return a&&(a.region&&(o.region=a.region),a.brands&&a.brands.length>0&&(o.brands=a.brands),a.excludeBrands&&a.excludeBrands.length>0&&(o.exclude_brands=a.excludeBrands)),this.generate(e,o)}static areKeysEquivalent(e,t){if(e===t)return!0;const a=this.parse(e),o=this.parse(t);if(a.endpoint!==o.endpoint)return!1;return JSON.stringify(a.params)===JSON.stringify(o.params)}}class ne{constructor(e){this.cache=e}evictLRU(e){const t=this.cache.getMetadata(),a=[...t.entries].sort((e,t)=>e.lastAccess-t.lastAccess);let o=t.totalSize;const r=[];for(const l of a){if(o<=e)break;r.push(l.key),o-=l.size}return r.forEach(e=>{this.cache.delete(e),console.log("🔄 LRU eviction:",e)}),r.length}evictExpired(){const e=Date.now();let t=0;const a=this.cache.prefix;for(let r=localStorage.length-1;r>=0;r--){const l=localStorage.key(r);if(l&&l.startsWith(a))try{const o=JSON.parse(localStorage.getItem(l));if(o&&o.timestamp&&o.ttl&&e-o.timestamp>o.ttl){const e=l.replace(a,"");this.cache.delete(e),t++,console.log("⏰ TTL eviction:",e)}}catch(o){localStorage.removeItem(l),t++}}return t}evictBySize(e){const t=this.cache.getMetadata();if(t.totalSize<=e)return 0;const a=[...t.entries].sort((e,t)=>{const a=t.size-e.size;return 0!==a?a:e.timestamp-t.timestamp});let o=t.totalSize;const r=[],l=.8*e;for(const i of a){if(o<=l)break;r.push(i.key),o-=i.size}return r.forEach(e=>{this.cache.delete(e),console.log("📏 Size eviction:",e)}),r.length}evictEndpoint(e){const t=this.cache.prefix,a=[];for(let o=0;o<localStorage.length;o++){const r=localStorage.key(o);r&&r.startsWith(t+e)&&a.push(r)}return a.forEach(e=>{const a=e.replace(t,"");this.cache.delete(a)}),console.log(`🎯 Endpoint eviction: ${a.length} entries for ${e}`),a.length}smartEvict(e){const t=this.cache.getMetadata().totalSize;this.evictExpired();let a=this.cache.getMetadata();if(a.totalSize+e<=this.cache.maxSize)return!0;const o=this.cache.maxSize-e;this.evictLRU(o),a=this.cache.getMetadata();const r=t-a.totalSize;return console.log(`🧹 Smart eviction freed ${this.formatBytes(r)}`),a.totalSize+e<=this.cache.maxSize}evictOlderThan(e){const t=Date.now(),a=this.cache.getMetadata(),o=[];for(const r of a.entries)t-r.timestamp>e&&o.push(r.key);return o.forEach(e=>{this.cache.delete(e),console.log("🕰️ Age eviction:",e)}),o.length}formatBytes(e){return e<1024?`${e} B`:e<1048576?`${(e/1024).toFixed(1)} KB`:`${(e/1048576).toFixed(1)} MB`}getStats(){const e=this.cache.getMetadata(),t=Date.now(),a=e.entries.filter(e=>{try{const a=localStorage.getItem(this.cache.prefix+e.key);if(!a)return!0;const o=JSON.parse(a);return t-o.timestamp>o.ttl}catch(a){return!0}}).length,o=e.entries.filter(e=>t-e.timestamp>864e5).length,r=e.entries.length>0?Math.round(e.totalSize/e.entries.length):0;return{totalEntries:e.entries.length,expiredEntries:a,oldEntries:o,totalSize:this.formatBytes(e.totalSize),averageSize:this.formatBytes(r),utilizationPercent:(e.totalSize/this.cache.maxSize*100).toFixed(1)}}}class se extends oe{constructor(e,t,a={}){super(e,t),this.cacheConfig=c({enabled:!0,memoryMaxSize:50,storageMaxSize:512e3,defaultTTL:36e5},a),this.cacheableEndpoints=["year","make","model","generation"],this.endpointTTLs=c({year:864e5,make:432e5,model:216e5,generation:216e5},a.ttls),this.memoryCache=new re(this.cacheConfig.memoryMaxSize),this.storageCache=new le(this.cacheConfig.storageMaxSize),this.evictionManager=new ne(this.storageCache),this.stats={hits:0,misses:0,errors:0,bytesServed:0,bytesFetched:0},this.widgetConfig=e.widgetConfig||{},this.initializeCache()}initializeCache(){return h(this,null,function*(){const e=this.evictionManager.evictExpired();e>0&&console.log(`🧹 Cleaned up ${e} expired cache entries on startup`)})}call(e){return h(this,arguments,function*(e,t={}){var a;const o=this.applyConfigFilters(t,this.widgetConfig);if(!(this.cacheConfig.enabled&&this.cacheableEndpoints.includes(e)))return console.log(`⚡ Direct API call (not cached): ${e}`),u(se.prototype,this,"call").call(this,e,o);const r=ie.generate(e,o);let l=this.memoryCache.get(r);if(l)return this.stats.hits++,this.stats.bytesServed+=JSON.stringify(l).length,console.log(`✅ Cache hit (memory): ${e}`,r),{data:l};if(l=this.storageCache.get(r),l)return this.stats.hits++,this.stats.bytesServed+=JSON.stringify(l).length,this.memoryCache.set(r,l),console.log(`✅ Cache hit (storage): ${e}`,r),{data:l};this.stats.misses++,console.log(`❌ Cache miss: ${e}`,r);try{const t=yield u(se.prototype,this,"call").call(this,e,o),l=this.endpointTTLs[e]||this.cacheConfig.defaultTTL,i=(null==(a=t.data)?void 0:a.data)||t.data;return this.memoryCache.set(r,i,l),this.storageCache.set(r,i,l),this.stats.bytesFetched+=JSON.stringify(i).length,console.log(`💾 Cached response for: ${e}`,r),t}catch(i){throw this.stats.errors++,i}})}applyConfigFilters(e,t){const a=c({},e);return t.region&&(a.region=t.region),t.brands&&t.brands.length>0&&(a.brands=t.brands),t.excludeBrands&&t.excludeBrands.length>0&&(a.exclude_brands=t.excludeBrands),a}clearCache(){this.memoryCache.clear(),this.storageCache.clear(),console.log("🗑️ All cache cleared")}invalidateEndpoint(e){this.memoryCache.getStats().keys.filter(t=>t.startsWith(e)).forEach(e=>this.memoryCache.delete(e)),this.storageCache.invalidateEndpoint(e),console.log(`🗑️ Invalidated cache for endpoint: ${e}`)}invalidateOnConfigChange(e){const t=this.widgetConfig;return!(t.region===e.region&&JSON.stringify(t.brands)===JSON.stringify(e.brands)&&JSON.stringify(t.excludeBrands)===JSON.stringify(e.excludeBrands))&&(console.log("🔄 Widget configuration changed - clearing affected cache"),this.cacheableEndpoints.forEach(e=>{this.invalidateEndpoint(e)}),this.widgetConfig=e,!0)}getCacheStats(){const e=this.stats.hits/(this.stats.hits+this.stats.misses)||0;return d(c({},this.stats),{hitRate:`${(100*e).toFixed(1)}%`,bandwidthSaved:this.formatBytes(this.stats.bytesServed),bandwidthUsed:this.formatBytes(this.stats.bytesFetched),memoryCache:this.memoryCache.getStats(),storageCache:this.storageCache.getStats(),evictionStats:this.evictionManager.getStats()})}formatBytes(e){return e<1024?`${e} B`:e<1048576?`${(e/1024).toFixed(1)} KB`:`${(e/1048576).toFixed(1)} MB`}preloadSelectorData(){return h(this,null,function*(){const e=[{endpoint:"year",params:{}},{endpoint:"make",params:{}}];this.widgetConfig.brands&&this.widgetConfig.brands.length>0&&e.pop(),console.log("📦 Preloading selector data...");const t=e.map(({endpoint:e,params:t})=>this.call(e,t).catch(t=>{console.warn(`Preload failed for ${e}:`,t)}));yield Promise.all(t),console.log("✅ Selector preload complete")})}isCacheable(e){return this.cacheableEndpoints.includes(e)}setCacheEnabled(e){this.cacheConfig.enabled=e,console.log("🎯 Cache "+(e?"enabled":"disabled"))}runMaintenance(){console.log("🔧 Running cache maintenance...");const e=this.evictionManager.evictExpired(),t=this.storageCache.getStats();if(parseFloat(t.utilization)>80){const e=.6*this.cacheConfig.storageMaxSize,t=this.evictionManager.evictLRU(e);console.log(`🧹 Evicted ${t} LRU entries to reduce storage usage`)}console.log(`✅ Maintenance complete: ${e} expired entries removed`)}exportStats(){const e=this.getCacheStats(),t=(new Date).toISOString();return d(c({timestamp:t},e),{configuration:{enabled:this.cacheConfig.enabled,cacheableEndpoints:this.cacheableEndpoints,ttls:this.endpointTTLs,memoryMaxSize:this.cacheConfig.memoryMaxSize,storageMaxSize:this.formatBytes(this.cacheConfig.storageMaxSize)}})}}class ce{constructor(e,t){this.cache=e,this.widgetId=t,this.configKey=`ws_finder_config_${t}`,this.lastConfig=this.loadConfig(),this.checkInterval=null,this.startConfigMonitoring()}loadConfig(){try{const e=localStorage.getItem(this.configKey);if(e)return JSON.parse(e)}catch(e){console.error("Failed to load stored config:",e)}return this.cache.widgetConfig||{}}saveConfig(e){try{localStorage.setItem(this.configKey,JSON.stringify(e))}catch(t){console.error("Failed to save config:",t)}}checkConfigChange(e){const t=e||this.cache.widgetConfig;return!!this.hasFiltersChanged(this.lastConfig,t)&&(console.log("🔄 Widget config changed - clearing selector cache"),console.log("Old config:",this.lastConfig),console.log("New config:",t),this.cache.invalidateOnConfigChange(t),this.lastConfig=t,this.saveConfig(t),this.emitConfigChangeEvent(t),!0)}hasFiltersChanged(e,t){if(e.region!==t.region)return!0;if(JSON.stringify(e.brands||[])!==JSON.stringify(t.brands||[]))return!0;return JSON.stringify(e.excludeBrands||[])!==JSON.stringify(t.excludeBrands||[])||e.year!==t.year}startConfigMonitoring(){this.checkInterval=setInterval(()=>{this.checkForRemoteConfigChange()},3e4),window.addEventListener("storage",this.handleStorageEvent.bind(this)),console.log("📡 Config monitoring started")}stopConfigMonitoring(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null),window.removeEventListener("storage",this.handleStorageEvent.bind(this)),console.log("📡 Config monitoring stopped")}checkForRemoteConfigChange(){return h(this,null,function*(){try{const e=this.loadConfig();JSON.stringify(e)!==JSON.stringify(this.lastConfig)&&this.checkConfigChange(e)}catch(e){console.error("Failed to check remote config:",e)}})}handleStorageEvent(e){if(e.key===this.configKey&&e.newValue)try{const t=JSON.parse(e.newValue);console.log("📨 Config change detected from another tab"),this.checkConfigChange(t)}catch(t){console.error("Failed to handle storage event:",t)}}emitConfigChangeEvent(e){const t=new CustomEvent("finderConfigChanged",{detail:{widgetId:this.widgetId,config:e,timestamp:Date.now()}});window.dispatchEvent(t)}updateConfig(e){const t=this.checkConfigChange(e);return t?console.log("✅ Configuration updated and cache invalidated"):console.log("ℹ️ Configuration unchanged, cache preserved"),t}getCurrentConfig(){return this.lastConfig}forceInvalidation(){console.log("⚠️ Forcing cache invalidation"),this.cache.cacheableEndpoints.forEach(e=>{this.cache.invalidateEndpoint(e)});const e=new CustomEvent("finderCacheInvalidated",{detail:{widgetId:this.widgetId,reason:"forced",timestamp:Date.now()}});window.dispatchEvent(e)}destroy(){this.stopConfigMonitoring(),console.log("🛑 Config change handler destroyed")}}const de=new class{constructor(){this.searchHistoryPrefix="finder_v2_search_history_",this.apiCachePrefix="ws_finder_cache_v1_",this.cacheMetaKey="ws_finder_cache_meta",this.maxTotalSize=8388608,this.limits={searchHistory:51200,apiCache:512e3,reserved:7811891.2}}getStorageUsage(){let e=0,t=0,a=0;for(let r=0;r<localStorage.length;r++){const o=localStorage.key(r);if(!o)continue;const l=localStorage.getItem(o),i=2*(o.length+(l?l.length:0));o.startsWith(this.searchHistoryPrefix)?e+=i:o.startsWith(this.apiCachePrefix)||o===this.cacheMetaKey?t+=i:a+=i}const o=e+t+a;return{searchHistory:e,apiCache:t,other:a,total:o,available:this.maxTotalSize-o,utilization:{searchHistory:this.formatPercent(e,this.limits.searchHistory),apiCache:this.formatPercent(t,this.limits.apiCache),total:this.formatPercent(o,this.maxTotalSize)}}}hasSpace(e,t="cache"){const a=this.getStorageUsage();return a.available>=e||("cache"===t&&a.apiCache+e<=this.limits.apiCache||"history"===t&&a.searchHistory+e<=this.limits.searchHistory)}cleanupStorage(e){const t=this.getStorageUsage();if(t.available>=e)return!0;console.log("🧹 Starting intelligent storage cleanup..."),console.log("Current usage:",this.formatStorageUsage(t));let a=0;return a+=this.cleanupExpiredCache(),t.available+a>=e?(console.log(`✅ Freed ${this.formatBytes(a)} by removing expired entries`),!0):(a+=this.cleanupOldCache(e-a),t.available+a>=e?(console.log(`✅ Freed ${this.formatBytes(a)} total`),!0):(console.warn("⚠️ Could not free enough space"),!1))}cleanupExpiredCache(){const e=Date.now();let t=0;const a=[];for(let r=0;r<localStorage.length;r++){const l=localStorage.key(r);if(l&&l.startsWith(this.apiCachePrefix))try{const o=localStorage.getItem(l),r=JSON.parse(o);r.timestamp&&r.ttl&&e-r.timestamp>r.ttl&&(a.push(l),t+=2*(l.length+o.length))}catch(o){a.push(l)}}return a.forEach(e=>localStorage.removeItem(e)),a.length>0&&console.log(`🗑️ Removed ${a.length} expired cache entries`),t}cleanupOldCache(e){const t=[];for(let l=0;l<localStorage.length;l++){const e=localStorage.key(l);if(e&&e.startsWith(this.apiCachePrefix))try{const a=localStorage.getItem(e),o=JSON.parse(a);t.push({key:e,timestamp:o.timestamp||0,size:2*(e.length+a.length)})}catch(r){}}t.sort((e,t)=>e.timestamp-t.timestamp);let a=0;const o=[];for(const l of t){if(a>=e)break;o.push(l.key),a+=l.size}return o.forEach(e=>localStorage.removeItem(e)),o.length>0&&console.log(`🗑️ Removed ${o.length} old cache entries`),a}getDetailedStats(){const e=this.getStorageUsage(),t=this.countEntriesByPrefix(this.apiCachePrefix),a=this.countEntriesByPrefix(this.searchHistoryPrefix);return{summary:{totalUsed:this.formatBytes(e.total),totalAvailable:this.formatBytes(e.available),utilizationPercent:this.formatPercent(e.total,this.maxTotalSize)},searchHistory:{size:this.formatBytes(e.searchHistory),limit:this.formatBytes(this.limits.searchHistory),utilization:e.utilization.searchHistory,entryCount:a},apiCache:{size:this.formatBytes(e.apiCache),limit:this.formatBytes(this.limits.apiCache),utilization:e.utilization.apiCache,entryCount:t},other:{size:this.formatBytes(e.other),description:"Other localStorage usage"}}}countEntriesByPrefix(e){let t=0;for(let a=0;a<localStorage.length;a++){const o=localStorage.key(a);o&&o.startsWith(e)&&t++}return t}formatBytes(e){return e<1024?`${e} B`:e<1048576?`${(e/1024).toFixed(1)} KB`:`${(e/1048576).toFixed(2)} MB`}formatPercent(e,t){return 0===t?"0%":`${(e/t*100).toFixed(1)}%`}formatStorageUsage(e){return{searchHistory:this.formatBytes(e.searchHistory),apiCache:this.formatBytes(e.apiCache),other:this.formatBytes(e.other),total:this.formatBytes(e.total),available:this.formatBytes(e.available)}}checkStorageHealth(){const e=this.getStorageUsage(),t=e.total/this.maxTotalSize*100;return t>90?(console.error("🚨 localStorage critically full:",this.formatPercent(e.total,this.maxTotalSize)),"critical"):t>75?(console.warn("⚠️ localStorage getting full:",this.formatPercent(e.total,this.maxTotalSize)),"warning"):t>50?(console.log("ℹ️ localStorage usage:",this.formatPercent(e.total,this.maxTotalSize)),"normal"):"healthy"}clearApiCache(){const e=[];for(let t=0;t<localStorage.length;t++){const a=localStorage.key(t);a&&(a.startsWith(this.apiCachePrefix)||a===this.cacheMetaKey)&&e.push(a)}return e.forEach(e=>localStorage.removeItem(e)),console.log(`🗑️ Cleared ${e.length} API cache entries`),e.length}clearSearchHistory(){const e=[];for(let t=0;t<localStorage.length;t++){const a=localStorage.key(t);a&&a.startsWith(this.searchHistoryPrefix)&&e.push(a)}return e.forEach(e=>localStorage.removeItem(e)),console.log(`🗑️ Cleared ${e.length} search history entries`),e.length}exportStorageState(){const e={timestamp:(new Date).toISOString(),usage:this.getStorageUsage(),stats:this.getDetailedStats(),health:this.checkStorageHealth(),entries:{}};for(let t=0;t<localStorage.length;t++){const a=localStorage.key(t);if(!a)continue;const o=localStorage.getItem(a);let r="other";a.startsWith(this.searchHistoryPrefix)?r="searchHistory":(a.startsWith(this.apiCachePrefix)||a===this.cacheMetaKey)&&(r="apiCache"),e.entries[r]||(e.entries[r]=[]),e.entries[r].push({key:a.substring(0,50),size:2*(a.length+(o?o.length:0))})}return e}};let ue=null;function he(e,t){if(ue)return ue;if(e){const a=!1!==e.cacheEnabled,o=e.widgetId||"default";if(console.log("🎯 API Client initialization - Cache "+(a?"ENABLED":"DISABLED")),a){const a={enabled:!0,memoryMaxSize:e.cacheMemorySize||50,storageMaxSize:e.cacheStorageSize||512e3,defaultTTL:e.cacheTTL||36e5,ttls:e.cacheTTLs||{}};ue=function(e,t,a){return new se(e,t,a)}(e,t,a),new ce(ue,o);const r=de.checkStorageHealth();console.log(`📊 localStorage health: ${r}`),!1!==e.preloadSelectors&&setTimeout(()=>{ue.preloadSelectorData().catch(e=>{console.warn("Failed to preload selector data:",e)})},100),setTimeout(()=>{ue.runMaintenance()},5e3),setInterval(()=>{ue.runMaintenance()},3e5)}else ue=function(e,t){return new oe(e,t)}(e,t);return ue}throw new Error("useApiClient requires config parameter on first call")}class ge{constructor(e){this.config=e}buildBrandFilterParams(){var e,t,a,o,r;const l=(null==(t=null==(e=this.config)?void 0:e.content)?void 0:t.filter)||(null==(a=this.config)?void 0:a.filter)||{},i=l.by||(null==(r=null==(o=this.config)?void 0:o.content)?void 0:r.by)||"",n=e=>"string"==typeof e?e:(null==e?void 0:e.slug)||(null==e?void 0:e.value)||"",s={};if("brands"===i&&Array.isArray(l.brands)&&l.brands.length){const e=l.brands.map(n).filter(Boolean);e.length&&(s.brands=e.join(","))}else if("brands_exclude"===i&&Array.isArray(l.brands_exclude)&&l.brands_exclude.length){const e=l.brands_exclude.map(n).filter(Boolean);e.length&&(s.brands_exclude=e.join(","))}return s}buildRegionParams(){var e,t;const a=(null==(t=null==(e=this.config)?void 0:e.content)?void 0:t.regions)||[];return a.length?{region:a}:{}}shouldApplyRegionFilter(e){return["make","model","year","generation","modification"].includes(e)}shouldApplyBrandFilter(e){return"make"===e}getFiltersForEndpoint(e){const t={};return this.shouldApplyRegionFilter(e)&&Object.assign(t,this.buildRegionParams()),this.shouldApplyBrandFilter(e)&&Object.assign(t,this.buildBrandFilterParams()),t}updateConfig(e){this.config=e}getFilterSummary(){const e={hasFilters:!1,brandFilter:null,regionFilter:null},t=this.buildBrandFilterParams();t.brands?(e.hasFilters=!0,e.brandFilter={type:"include",brands:t.brands.split(",")}):t.brands_exclude&&(e.hasFilters=!0,e.brandFilter={type:"exclude",brands:t.brands_exclude.split(",")});const a=this.buildRegionParams();return a.region&&(e.hasFilters=!0,e.regionFilter={regions:a.region}),e}hasActiveFilters(){const e=this.buildBrandFilterParams(),t=this.buildRegionParams();return!!(e.brands||e.brands_exclude||t.region)}}function me(e){return new ge(e)}const ve=p("finder-refactored",()=>{const e=g({}),t=g(!1),a=g(!1),o=g(null),r=g([]),l=g("");let i=null,n=null,s=null;const c=te();let d=null,u=null;const v=g(!1),f=g(!1),y=g(!1),p=g(!1),b=g(!1),_=g(!1),M=g(!1),C=g(!1),x=g(!1),E=g(!1),F=g(""),I=g(""),L=g(""),T=g(""),R=g(""),A=g(""),P=g([]),z=g([]),O=g([]),V=g([]),B=g([]),D=m(()=>e.value.flowType||"primary"),$=m(()=>e.value.apiVersion||"v2"),H=m(()=>e.value.widgetResources||{});function U(e=null,t=null){return h(this,null,function*(){var a;try{v.value=!0,_.value=!1;const o={};e&&(o.make=e),t&&(o.model=t),Object.assign(o,u.getFiltersForEndpoint("year"));const r=yield d.call("year",o);P.value=(null==(a=r.data)?void 0:a.data)||r.data||[],_.value=!0,n&&n.trackEvent("data_load_complete",{data_type:"years",data_count:P.value.length,make:e||"",model:t||""})}catch(r){o.value=r.message,n&&n.trackError("data_load_failed",{data_type:"years",error_message:r.message,api_endpoint:"year",make:e||"",model:t||""})}finally{v.value=!1}})}function N(e=null){return h(this,null,function*(){var t;try{f.value=!0,M.value=!1;const a=e?{year:e}:{};Object.assign(a,u.getFiltersForEndpoint("make"));const o=yield d.call("make",a);z.value=(null==(t=o.data)?void 0:t.data)||o.data||[],M.value=!0}catch(a){o.value=a.message}finally{f.value=!1}})}function j(e,t=null){return h(this,null,function*(){var a;try{y.value=!0,C.value=!1;const o={make:e};t&&(o.year=t),Object.assign(o,u.getFiltersForEndpoint("model"));const r=yield d.call("model",o);O.value=(null==(a=r.data)?void 0:a.data)||r.data||[],C.value=!0}catch(r){o.value=r.message}finally{y.value=!1}})}function Y(e,t,a=null){return h(this,null,function*(){var r;try{b.value=!0,E.value=!1;const o={make:e,model:t};a&&("primary"===D.value||"year_select"===D.value?o.year=a:"alternative"===D.value&&(o.generation=a)),Object.assign(o,u.getFiltersForEndpoint("modification"));const l=yield d.call("modification",o);V.value=(null==(r=l.data)?void 0:r.data)||l.data||[],E.value=!0}catch(l){o.value=l.message}finally{b.value=!1}})}function G(e,t){return h(this,null,function*(){var a;try{p.value=!0,x.value=!1;const o={make:e,model:t};Object.assign(o,u.getFiltersForEndpoint("generation"));const r=yield d.call("generation",o);B.value=(null==(a=r.data)?void 0:a.data)||r.data||[],x.value=!0}catch(r){o.value=r.message}finally{p.value=!1}})}function J(){return h(this,null,function*(){var e;const t=performance.now();let l=null;try{l=(yield k(()=>import("./useBotProtection-DKPGrmyF.js"),__vite__mapDeps([0,1]))).useBotProtection()}catch(s){}try{if(a.value=!0,o.value=null,l&&!l.hasValidChallengeToken()){o.value="Verifying you are human... This may take a few seconds.";if(!(yield l.solveAndVerifyChallenge()))return o.value="Unable to verify. Please ensure JavaScript is enabled and try again.",void(a.value=!1);o.value=null}const s={make:L.value,model:T.value};"primary"===D.value||"year_select"===D.value?(s.year=I.value,s.modification=R.value):"alternative"===D.value&&(s.generation=A.value,s.modification=R.value);const u=`search_by_model:${JSON.stringify(s)}`;if(F.value===u)return;F.value=u,c.emit("search:start",{search_type:"by_vehicle",parameters:{year:I.value,make:L.value,model:T.value,generation:A.value,modification:R.value}}),n&&n.trackSearch("search_initiate",{search_type:"by_vehicle",selected_year:I.value,selected_make:L.value,selected_model:T.value,selected_modification:R.value,selected_generation:A.value});const g=performance.now(),m=yield d.call("search_by_model",s),v=performance.now();r.value=(null==(e=m.data)?void 0:e.data)||m.data||[];const f=performance.now()-t,y=v-g;if(c.emit("search:complete",{search_type:"by_vehicle",results_count:r.value.length,timing_ms:{search_total:Math.round(f),api:Math.round(y)}}),n&&(n.trackSearch("search_complete",{search_type:"by_vehicle",selected_year:I.value,selected_make:L.value,selected_model:T.value,selected_modification:R.value,selected_generation:A.value,results_count:r.value.length,search_completion_time:Math.round(f),api_response_time:Math.round(y)}),n.trackEvent("search_results_view",{results_count:r.value.length,search_type:"by_vehicle"})),i&&r.value.length>0){const e=P.value.find(e=>e.slug===I.value||e.id===I.value),t=z.value.find(e=>e.slug===L.value||e.id===L.value),a=O.value.find(e=>e.slug===T.value||e.id===T.value),o=V.value.find(e=>e.slug===R.value||e.id===R.value),r=B.value.find(e=>e.slug===A.value||e.id===A.value),l={flowType:D.value,year:I.value,make:L.value,model:T.value,modification:R.value,generation:A.value,options:{year:e,make:t,model:a,modification:o,generation:r}};i.addSearch(l)}setTimeout(()=>h(this,null,function*(){window.parentIFrame&&window.parentIFrame.size(),yield S(),c.emit("results:display",{results_count:r.value.length})}),100)}catch(u){o.value=u.message,c.emit("search:error",{search_type:"by_vehicle",error_message:(null==u?void 0:u.message)||String(u)}),n&&n.trackError("search_failed",{error_message:u.message,search_type:"by_vehicle",api_endpoint:"search_by_model",selected_year:I.value,selected_make:L.value,selected_model:T.value,widget_state:"searching"})}finally{a.value=!1,F.value=""}})}function q(){I.value="",L.value="",T.value="",R.value="",A.value="",O.value=[],V.value=[],B.value=[]}function W(){r.value=[],setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},50)}function K(e,t){var a,o,r,l,i,n;if(!t)return null;const s=(null==(o=null==(a=e.value)?void 0:a.find)?void 0:o.call(a,e=>(null==e?void 0:e.slug)===t||(null==e?void 0:e.id)===t))||null,c=null!=(r=null==s?void 0:s.slug)?r:"string"==typeof t?t:String(t);return{slug:c,title:null!=(n=null!=(i=null!=(l=null==s?void 0:s.title)?l:null==s?void 0:s.name)?i:null==s?void 0:s.value)?n:String(c)}}return w(I,(e,t)=>{e!==t&&c.emit("change:year",{value:K(P,e)})}),w(L,(e,t)=>{e!==t&&c.emit("change:make",{value:K(z,e)})}),w(T,(e,t)=>{e!==t&&c.emit("change:model",{value:K(O,e)})}),w(A,(e,t)=>{e!==t&&c.emit("change:generation",{value:K(B,e)})}),w(R,(e,t)=>{e!==t&&c.emit("change:modification",{value:K(V,e)})}),{config:e,loading:t,loadingResults:a,error:o,results:r,outputTemplate:l,loadingYears:v,loadingMakes:f,loadingModels:y,loadingGenerations:p,loadingModifications:b,stateLoadedYears:_,stateLoadedMakes:M,stateLoadedModels:C,stateLoadedGenerations:x,stateLoadedModifications:E,selectedYear:I,selectedMake:L,selectedModel:T,selectedModification:R,selectedGeneration:A,years:P,makes:z,models:O,modifications:V,generations:B,flowType:D,apiVersion:$,widgetResources:H,initialize:function(a){var r;e.value=a,l.value=(null==(r=a.interface)?void 0:r.outputTemplate)||"",d=he(a,a.widgetResources),u=me(a);const g=a.id||a.uuid||a.widgetUuid||"default";i=X(g);const m=a.search_history||{};Object.keys(m).length>0&&i.configure(m),n=Z(a),s=Q(),c.setConfig(a),c.setContextProvider(()=>({widgetUuid:g,widgetType:"finder-v2",flowType:D.value,selections:{year:I.value,make:L.value,model:T.value,generation:A.value,modification:R.value}})),function e(a=0){H.value&&H.value.year?function(){h(this,null,function*(){try{t.value=!0,o.value=null,"primary"===D.value?yield U():yield N()}catch(e){o.value=e.message}finally{t.value=!1,c.markInitialDataLoaded()}})}():a<5?setTimeout(()=>e(a+1),100):o.value="Failed to initialize widget configuration"}()},loadYears:U,loadMakes:N,loadModels:j,loadModifications:Y,loadGenerations:G,searchByVehicle:J,resetVehicleSearch:q,clearResults:W,clearError:function(){o.value=null},buildBrandFilterParams:function(){return u.buildBrandFilterParams()},buildRegionParams:function(){return u.buildRegionParams()},executeSearchFromHistory:function(e){return h(this,null,function*(){if(!i)return;const t=i.getSearch(e);if(t)try{W(),yield function(e){return h(this,null,function*(){const t=e.parameters,a=e.flowType||"primary";q();try{"primary"===a?(t.year&&(I.value=t.year,yield N(t.year)),t.make&&(L.value=t.make,yield j(t.make,t.year)),t.model&&(T.value=t.model,yield Y(t.make,t.model,t.year)),t.modification&&(R.value=t.modification)):"alternative"===a?(t.make&&(L.value=t.make,yield j(t.make)),t.model&&(T.value=t.model,yield G(t.make,t.model)),t.generation&&(A.value=t.generation,yield Y(t.make,t.model,t.generation)),t.modification&&(R.value=t.modification)):"year_select"===a&&(t.make&&(L.value=t.make,yield j(t.make)),t.model&&(T.value=t.model,yield U(t.make,t.model)),t.year&&(I.value=t.year,yield Y(t.make,t.model,t.year)),t.modification&&(R.value=t.modification))}catch(o){throw o}})}(t),i.updateSearchTimestamp(e),yield J()}catch(a){a.value="Failed to execute search from history"}})},getSearchHistory:function(){return i},getAnalytics:()=>n,getCrossDomainTracking:()=>s}});function fe(){return function(){const e=g({}),t=g(""),a=g(!1),o=g(!1),r=g(null),l=g([]),i=g(""),n=g(""),s=g(""),c=g(""),d=g(""),u=g([]),h=g([]),v=g([]),f=g([]),y=g([]),p=g(!1),w=g(!1),k=g(!1),S=g(!1),b=g(!1),_=g(!1),M=g(!1),C=g(!1),x=g(!1),E=g(!1),F=g(""),I=m(()=>e.value.flowType||"primary"),L=m(()=>e.value.apiVersion||"v2"),T=m(()=>e.value.widgetResources||{}),R=m(()=>!!i.value),A=m(()=>!!n.value),P=m(()=>!!s.value),z=m(()=>!!d.value),O=m(()=>!!c.value),V=m(()=>!!e.value.widgetResources),B=m(()=>l.value.length>0),D=m(()=>o.value),$=m(()=>!!r.value),H=m(()=>"primary"===I.value||"year_select"===I.value?R.value&&A.value&&P.value:"alternative"===I.value&&A.value&&P.value);function U(){i.value="",n.value="",s.value="",c.value="",d.value="",v.value=[],f.value=[],y.value=[],k.value=!1,b.value=!1,S.value=!1,C.value=!1,E.value=!1,x.value=!1}function N(e,t){return t&&e.find(e=>(null==e?void 0:e.slug)===t||(null==e?void 0:e.id)===t||(null==e?void 0:e.value)===t)||null}return{config:e,outputTemplate:t,loading:a,loadingResults:o,error:r,results:l,selectedYear:i,selectedMake:n,selectedModel:s,selectedModification:c,selectedGeneration:d,years:u,makes:h,models:v,modifications:f,generations:y,loadingYears:p,loadingMakes:w,loadingModels:k,loadingGenerations:S,loadingModifications:b,stateLoadedYears:_,stateLoadedMakes:M,stateLoadedModels:C,stateLoadedGenerations:x,stateLoadedModifications:E,activeSearchSignatureInFlight:F,flowType:I,apiVersion:L,widgetResources:T,hasYearSelected:R,hasMakeSelected:A,hasModelSelected:P,hasGenerationSelected:z,hasModificationSelected:O,isConfigured:V,hasResults:B,isSearching:D,hasError:$,canSearch:H,resetVehicleSearch:U,clearResults:function(){l.value=[],setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},50)},clearError:function(){r.value=null},resetAll:function(){e.value={},t.value="",a.value=!1,o.value=!1,r.value=null,l.value=[],U(),u.value=[],h.value=[],p.value=!1,w.value=!1,_.value=!1,M.value=!1,F.value=""},setConfig:function(a){var o;e.value=a,t.value=(null==(o=a.interface)?void 0:o.outputTemplate)||""},setError:function(e){console.log("🔍 State - setError called with:",e),console.trace("🔍 State - setError stack trace"),r.value=e},findOption:N,toValueObject:function(e,t){var a,o,r,l;if(!t)return null;const i=N(e,t),n=null!=(a=null==i?void 0:i.slug)?a:"string"==typeof t?t:String(t);return{slug:n,title:null!=(l=null!=(r=null!=(o=null==i?void 0:i.title)?o:null==i?void 0:i.name)?r:null==i?void 0:i.value)?l:String(n)}}}}()}class ye{constructor(e,t,a,o){this.api=e,this.state=t,this.filterBuilder=a,this.analytics=o}loadYears(e=null,t=null){return h(this,null,function*(){var a;try{this.state.loadingYears.value=!0,this.state.stateLoadedYears.value=!1;const o={};e&&(o.make=e),t&&(o.model=t),Object.assign(o,this.filterBuilder.getFiltersForEndpoint("year"));const r=yield this.api.call("year",o);return console.log("🔍 loadYears - response:",r),this.state.years.value=(null==(a=r.data)?void 0:a.data)||r.data||[],console.log("🔍 loadYears - years set to:",this.state.years.value),this.state.stateLoadedYears.value=!0,this.analytics&&this.analytics.trackEvent("data_load_complete",{data_type:"years",data_count:this.state.years.value.length,make:e||"",model:t||""}),this.state.years.value}catch(o){throw this.state.setError(o.message),this.analytics&&this.analytics.trackError("data_load_failed",{data_type:"years",error_message:o.message,api_endpoint:"year",make:e||"",model:t||""}),o}finally{this.state.loadingYears.value=!1}})}loadMakes(e=null){return h(this,null,function*(){var t;try{this.state.loadingMakes.value=!0,this.state.stateLoadedMakes.value=!1;const a=e?{year:e}:{};Object.assign(a,this.filterBuilder.getFiltersForEndpoint("make"));const o=yield this.api.call("make",a);return this.state.makes.value=(null==(t=o.data)?void 0:t.data)||o.data||[],this.state.stateLoadedMakes.value=!0,this.state.makes.value}catch(a){throw this.state.setError(a.message),a}finally{this.state.loadingMakes.value=!1}})}loadModels(e,t=null){return h(this,null,function*(){var a;if(!e)throw new Error("Make is required to load models");try{this.state.loadingModels.value=!0,this.state.stateLoadedModels.value=!1;const o={make:e};t&&(o.year=t),Object.assign(o,this.filterBuilder.getFiltersForEndpoint("model"));const r=yield this.api.call("model",o);return this.state.models.value=(null==(a=r.data)?void 0:a.data)||r.data||[],this.state.stateLoadedModels.value=!0,this.state.models.value}catch(o){throw this.state.setError(o.message),o}finally{this.state.loadingModels.value=!1}})}loadModifications(e,t,a=null){return h(this,null,function*(){var o;if(!e||!t)throw new Error("Make and model are required to load modifications");try{this.state.loadingModifications.value=!0,this.state.stateLoadedModifications.value=!1;const r={make:e,model:t};if(a){const e=this.state.flowType.value;"primary"===e||"year_select"===e?r.year=a:"alternative"===e&&(r.generation=a)}Object.assign(r,this.filterBuilder.getFiltersForEndpoint("modification"));const l=yield this.api.call("modification",r);return this.state.modifications.value=(null==(o=l.data)?void 0:o.data)||l.data||[],this.state.stateLoadedModifications.value=!0,this.state.modifications.value}catch(r){throw this.state.setError(r.message),r}finally{this.state.loadingModifications.value=!1}})}loadGenerations(e,t){return h(this,null,function*(){var a;if(!e||!t)throw new Error("Make and model are required to load generations");try{this.state.loadingGenerations.value=!0,this.state.stateLoadedGenerations.value=!1;const o={make:e,model:t};Object.assign(o,this.filterBuilder.getFiltersForEndpoint("generation"));const r=yield this.api.call("generation",o);return this.state.generations.value=(null==(a=r.data)?void 0:a.data)||r.data||[],this.state.stateLoadedGenerations.value=!0,this.state.generations.value}catch(o){throw this.state.setError(o.message),o}finally{this.state.loadingGenerations.value=!1}})}loadInitialData(){return h(this,null,function*(){try{this.state.loading.value=!0,this.state.clearError();return"primary"===this.state.flowType.value?yield this.loadYears():yield this.loadMakes(),!0}catch(e){return this.state.setError(e.message),!1}finally{this.state.loading.value=!1}})}isLoading(){return this.state.loadingYears.value||this.state.loadingMakes.value||this.state.loadingModels.value||this.state.loadingGenerations.value||this.state.loadingModifications.value}getLoadingStatus(){return{years:this.state.loadingYears.value,makes:this.state.loadingMakes.value,models:this.state.loadingModels.value,generations:this.state.loadingGenerations.value,modifications:this.state.loadingModifications.value,anyLoading:this.isLoading()}}getLoadedStatus(){return{years:this.state.stateLoadedYears.value,makes:this.state.stateLoadedMakes.value,models:this.state.stateLoadedModels.value,generations:this.state.stateLoadedGenerations.value,modifications:this.state.stateLoadedModifications.value}}}function pe(e,t,a,o){return new ye(e,t,a,o)}const we=p("finder-refactored-v2",()=>{const e=fe();let t=null,a=null,o=null;const r=te();let l=null,i=null,n=null;function s(){return h(this,null,function*(){var o;const i=performance.now();if(!e.canSearch.value)return void e.setError("Please select all required fields before searching");let n=null;try{n=(yield k(()=>import("./useBotProtection-DKPGrmyF.js"),__vite__mapDeps([0,1]))).useBotProtection()}catch(s){}try{if(e.loadingResults.value=!0,e.clearError(),n&&!n.hasValidChallengeToken()){e.setError("Verifying you are human... This may take a few seconds.");if(!(yield n.solveAndVerifyChallenge()))return e.setError("Unable to verify. Please ensure JavaScript is enabled and try again."),void(e.loadingResults.value=!1);e.clearError()}const s={make:e.selectedMake.value,model:e.selectedModel.value};"primary"===e.flowType.value||"year_select"===e.flowType.value?(s.year=e.selectedYear.value,s.modification=e.selectedModification.value):"alternative"===e.flowType.value&&(s.generation=e.selectedGeneration.value,s.modification=e.selectedModification.value);const c=`search_by_model:${JSON.stringify(s)}`;if(e.activeSearchSignatureInFlight.value===c)return;e.activeSearchSignatureInFlight.value=c,r.emit("search:start",{search_type:"by_vehicle",parameters:{year:e.selectedYear.value,make:e.selectedMake.value,model:e.selectedModel.value,generation:e.selectedGeneration.value,modification:e.selectedModification.value}}),a&&a.trackSearch("search_initiate",{search_type:"by_vehicle",selected_year:e.selectedYear.value,selected_make:e.selectedMake.value,selected_model:e.selectedModel.value,selected_modification:e.selectedModification.value,selected_generation:e.selectedGeneration.value});const d=performance.now(),u=yield l.call("search_by_model",s),g=performance.now();e.results.value=(null==(o=u.data)?void 0:o.data)||u.data||[];const m=performance.now()-i,v=g-d;if(r.emit("search:complete",{search_type:"by_vehicle",results_count:e.results.value.length,timing_ms:{search_total:Math.round(m),api:Math.round(v)}}),a&&(a.trackSearch("search_complete",{search_type:"by_vehicle",selected_year:e.selectedYear.value,selected_make:e.selectedMake.value,selected_model:e.selectedModel.value,selected_modification:e.selectedModification.value,selected_generation:e.selectedGeneration.value,results_count:e.results.value.length,search_completion_time:Math.round(m),api_response_time:Math.round(v)}),a.trackEvent("search_results_view",{results_count:e.results.value.length,search_type:"by_vehicle"})),t&&e.results.value.length>0){const a={flowType:e.flowType.value,year:e.selectedYear.value,make:e.selectedMake.value,model:e.selectedModel.value,modification:e.selectedModification.value,generation:e.selectedGeneration.value,options:{year:e.findOption(e.years.value,e.selectedYear.value),make:e.findOption(e.makes.value,e.selectedMake.value),model:e.findOption(e.models.value,e.selectedModel.value),modification:e.findOption(e.modifications.value,e.selectedModification.value),generation:e.findOption(e.generations.value,e.selectedGeneration.value)}};t.addSearch(a)}setTimeout(()=>h(this,null,function*(){window.parentIFrame&&window.parentIFrame.size(),yield S(),r.emit("results:display",{results_count:e.results.value.length})}),100)}catch(c){e.setError(c.message),r.emit("search:error",{search_type:"by_vehicle",error_message:(null==c?void 0:c.message)||String(c)}),a&&a.trackError("search_failed",{error_message:c.message,search_type:"by_vehicle",api_endpoint:"search_by_model",selected_year:e.selectedYear.value,selected_make:e.selectedMake.value,selected_model:e.selectedModel.value,widget_state:"searching"})}finally{e.loadingResults.value=!1,e.activeSearchSignatureInFlight.value=""}})}return w(e.selectedYear,(t,a)=>{t!==a&&r.emit("change:year",{value:e.toValueObject(e.years.value,t)})}),w(e.selectedMake,(t,a)=>{t!==a&&r.emit("change:make",{value:e.toValueObject(e.makes.value,t)})}),w(e.selectedModel,(t,a)=>{t!==a&&r.emit("change:model",{value:e.toValueObject(e.models.value,t)})}),w(e.selectedGeneration,(t,a)=>{t!==a&&r.emit("change:generation",{value:e.toValueObject(e.generations.value,t)})}),w(e.selectedModification,(t,a)=>{t!==a&&r.emit("change:modification",{value:e.toValueObject(e.modifications.value,t)})}),d(c({},e),{initialize:function(s){e.setConfig(s),l=he(s,s.widgetResources),i=me(s),a=Z(s),n=pe(l,e,i,a);const c=s.id||s.uuid||s.widgetUuid||"default";t=X(c);const d=s.search_history||{};Object.keys(d).length>0&&t.configure(d),o=Q(),r.setConfig(s),r.setContextProvider(()=>({widgetUuid:c,widgetType:"finder-v2",flowType:e.flowType.value,selections:{year:e.selectedYear.value,make:e.selectedMake.value,model:e.selectedModel.value,generation:e.selectedGeneration.value,modification:e.selectedModification.value}})),function t(a=0){e.widgetResources.value&&e.widgetResources.value.year?function(){h(this,null,function*(){(yield n.loadInitialData())&&r.markInitialDataLoaded()})}():a<5?setTimeout(()=>t(a+1),100):e.setError("Failed to initialize widget configuration")}()},searchByVehicle:s,loadYears:(e,t)=>n.loadYears(e,t),loadMakes:e=>n.loadMakes(e),loadModels:(e,t)=>n.loadModels(e,t),loadGenerations:(e,t)=>n.loadGenerations(e,t),loadModifications:(e,t,a)=>n.loadModifications(e,t,a),buildBrandFilterParams:function(){return i.buildBrandFilterParams()},buildRegionParams:function(){return i.buildRegionParams()},executeSearchFromHistory:function(a){return h(this,null,function*(){if(!t)return;const o=t.getSearch(a);if(o)try{e.clearResults(),yield function(t){return h(this,null,function*(){const a=t.parameters,o=t.flowType||"primary";e.resetVehicleSearch();try{"primary"===o?(a.year&&(e.selectedYear.value=a.year,yield n.loadMakes(a.year)),a.make&&(e.selectedMake.value=a.make,yield n.loadModels(a.make,a.year)),a.model&&(e.selectedModel.value=a.model,yield n.loadModifications(a.make,a.model,a.year)),a.modification&&(e.selectedModification.value=a.modification)):"alternative"===o?(a.make&&(e.selectedMake.value=a.make,yield n.loadModels(a.make)),a.model&&(e.selectedModel.value=a.model,yield n.loadGenerations(a.make,a.model)),a.generation&&(e.selectedGeneration.value=a.generation,yield n.loadModifications(a.make,a.model,a.generation)),a.modification&&(e.selectedModification.value=a.modification)):"year_select"===o&&(a.make&&(e.selectedMake.value=a.make,yield n.loadModels(a.make)),a.model&&(e.selectedModel.value=a.model,yield n.loadYears(a.make,a.model)),a.year&&(e.selectedYear.value=a.year,yield n.loadModifications(a.make,a.model,a.year)),a.modification&&(e.selectedModification.value=a.modification))}catch(r){throw r}})}(o),t.updateSearchTimestamp(a),yield s()}catch(r){e.setError("Failed to execute search from history")}})},getSearchHistory:function(){return t},getAnalytics:()=>a,getCrossDomainTracking:()=>o})});class ke{constructor(e,t,a,o,r){this.api=e,this.state=t,this.widgetEvents=a,this.analytics=o,this.searchHistory=r,this.botProtection=null}initBotProtection(){return h(this,null,function*(){if(this.botProtection)return this.botProtection;try{const e=yield k(()=>import("./useBotProtection-DKPGrmyF.js"),__vite__mapDeps([0,1]));return this.botProtection=e.useBotProtection(),this.botProtection}catch(e){return null}})}searchByVehicle(){return h(this,null,function*(){const e=performance.now();if(!this.canSearch())return this.state.setError("Please select all required fields before searching"),null;try{this.state.loadingResults.value=!0,this.state.clearError();if(!(yield this.handleBotProtection()))return null;const t=this.buildSearchParams();if(this.isDuplicateSearch(t))return null;this.emitSearchStart(t),this.trackSearchInitiation(t);const a=performance.now(),o=yield this.api.call("search_by_model",t),r=performance.now(),l=this.processSearchResults(o),i=performance.now(),n={total:Math.round(i-e),api:Math.round(r-a)};return this.emitSearchComplete(l,n),this.trackSearchCompletion(t,l,n),l.length>0&&this.saveSearchToHistory(t),this.scheduleIframeResize(l.length),l}catch(t){return this.handleSearchError(t),null}finally{this.state.loadingResults.value=!1,this.state.activeSearchSignatureInFlight.value=""}})}canSearch(){return this.state.canSearch.value}handleBotProtection(){return h(this,null,function*(){const e=yield this.initBotProtection();if(!e)return!0;if(e.hasValidChallengeToken())return!0;this.state.setError("Verifying you are human... This may take a few seconds.");return(yield e.solveAndVerifyChallenge())?(this.state.clearError(),!0):(this.state.setError("Unable to verify. Please ensure JavaScript is enabled and try again."),this.state.loadingResults.value=!1,!1)})}buildSearchParams(){const e={make:this.state.selectedMake.value,model:this.state.selectedModel.value},t=this.state.flowType.value;return"primary"===t||"year_select"===t?(e.year=this.state.selectedYear.value,this.state.selectedModification.value&&(e.modification=this.state.selectedModification.value)):"alternative"===t&&(this.state.selectedGeneration.value&&(e.generation=this.state.selectedGeneration.value),this.state.selectedModification.value&&(e.modification=this.state.selectedModification.value)),e}isDuplicateSearch(e){const t=this.generateSearchSignature(e);return this.state.activeSearchSignatureInFlight.value===t||(this.state.activeSearchSignatureInFlight.value=t,!1)}generateSearchSignature(e){return`search_by_model:${JSON.stringify(e)}`}processSearchResults(e){var t;const a=(null==(t=e.data)?void 0:t.data)||e.data||[];return this.state.results.value=a,a}emitSearchStart(e){this.widgetEvents.emit("search:start",{search_type:"by_vehicle",parameters:{year:e.year||"",make:e.make||"",model:e.model||"",generation:e.generation||"",modification:e.modification||""}})}emitSearchComplete(e,t){this.widgetEvents.emit("search:complete",{search_type:"by_vehicle",results_count:e.length,timing_ms:t})}trackSearchInitiation(e){this.analytics&&this.analytics.trackSearch("search_initiate",{search_type:"by_vehicle",selected_year:e.year||"",selected_make:e.make||"",selected_model:e.model||"",selected_modification:e.modification||"",selected_generation:e.generation||""})}trackSearchCompletion(e,t,a){this.analytics&&(this.analytics.trackSearch("search_complete",{search_type:"by_vehicle",selected_year:e.year||"",selected_make:e.make||"",selected_model:e.model||"",selected_modification:e.modification||"",selected_generation:e.generation||"",results_count:t.length,search_completion_time:a.total,api_response_time:a.api}),this.analytics.trackEvent("search_results_view",{results_count:t.length,search_type:"by_vehicle"}))}saveSearchToHistory(e){if(!this.searchHistory)return;const t={flowType:this.state.flowType.value,year:e.year||"",make:e.make||"",model:e.model||"",modification:e.modification||"",generation:e.generation||"",options:{year:this.state.findOption(this.state.years.value,e.year),make:this.state.findOption(this.state.makes.value,e.make),model:this.state.findOption(this.state.models.value,e.model),modification:this.state.findOption(this.state.modifications.value,e.modification),generation:this.state.findOption(this.state.generations.value,e.generation)}};this.searchHistory.addSearch(t)}scheduleIframeResize(e){setTimeout(()=>h(this,null,function*(){window.parentIFrame&&window.parentIFrame.size(),yield new Promise(e=>setTimeout(e,0)),this.widgetEvents.emit("results:display",{results_count:e})}),100)}handleSearchError(e){const t=(null==e?void 0:e.message)||String(e);this.state.setError(t),this.widgetEvents.emit("search:error",{search_type:"by_vehicle",error_message:t}),this.analytics&&this.analytics.trackError("search_failed",{error_message:t,search_type:"by_vehicle",api_endpoint:"search_by_model",selected_year:this.state.selectedYear.value,selected_make:this.state.selectedMake.value,selected_model:this.state.selectedModel.value,widget_state:"searching"})}getCurrentSearchParams(){return this.buildSearchParams()}isSearching(){return this.state.loadingResults.value}cancelSearch(){this.state.activeSearchSignatureInFlight.value="",this.state.loadingResults.value=!1}}class Se{constructor(e,t,a,o){this.state=e,this.vehicleLoader=t,this.searchExecutor=a,this.searchHistory=o}executeSearchFromHistory(e){return h(this,null,function*(){if(!this.searchHistory)return console.warn("Search history not initialized"),!1;const t=this.searchHistory.getSearch(e);if(!t)return console.warn("Search not found:",e),!1;try{this.state.clearResults(),yield this.populateFormFromSearch(t),this.searchHistory.updateSearchTimestamp(e);return null!==(yield this.searchExecutor.searchByVehicle())}catch(a){return console.error("Failed to execute search from history:",a),this.state.setError("Failed to execute search from history"),!1}})}populateFormFromSearch(e){return h(this,null,function*(){const t=e.parameters,a=e.flowType||"primary";this.state.resetVehicleSearch();try{switch(a){case"primary":yield this.populatePrimaryFlow(t);break;case"alternative":yield this.populateAlternativeFlow(t);break;case"year_select":yield this.populateYearSelectFlow(t);break;default:throw new Error(`Unknown flow type: ${a}`)}}catch(o){throw console.error("Failed to populate form from search:",o),o}})}populatePrimaryFlow(e){return h(this,null,function*(){e.year&&(this.state.years.value.length||(yield this.vehicleLoader.loadYears()),this.state.selectedYear.value=e.year,yield this.vehicleLoader.loadMakes(e.year)),e.make&&(this.state.selectedMake.value=e.make,yield this.vehicleLoader.loadModels(e.make,e.year)),e.model&&(this.state.selectedModel.value=e.model,yield this.vehicleLoader.loadModifications(e.make,e.model,e.year)),e.modification&&(this.state.selectedModification.value=e.modification)})}populateAlternativeFlow(e){return h(this,null,function*(){e.make&&(this.state.makes.value.length||(yield this.vehicleLoader.loadMakes()),this.state.selectedMake.value=e.make,yield this.vehicleLoader.loadModels(e.make)),e.model&&(this.state.selectedModel.value=e.model,yield this.vehicleLoader.loadGenerations(e.make,e.model)),e.generation&&(this.state.selectedGeneration.value=e.generation,yield this.vehicleLoader.loadModifications(e.make,e.model,e.generation)),e.modification&&(this.state.selectedModification.value=e.modification)})}populateYearSelectFlow(e){return h(this,null,function*(){e.make&&(this.state.makes.value.length||(yield this.vehicleLoader.loadMakes()),this.state.selectedMake.value=e.make,yield this.vehicleLoader.loadModels(e.make)),e.model&&(this.state.selectedModel.value=e.model,yield this.vehicleLoader.loadYears(e.make,e.model)),e.year&&(this.state.selectedYear.value=e.year,yield this.vehicleLoader.loadModifications(e.make,e.model,e.year)),e.modification&&(this.state.selectedModification.value=e.modification)})}getSearchHistory(){return this.searchHistory}getHistoryItems(){return this.searchHistory?this.searchHistory.getHistory():[]}getHistoryItem(e){return this.searchHistory?this.searchHistory.getSearch(e):null}clearHistory(){this.searchHistory&&this.searchHistory.clearHistory()}removeHistoryItem(e){this.searchHistory&&this.searchHistory.removeSearch(e)}hasHistory(){return!!this.searchHistory&&this.searchHistory.getHistory().length>0}getMostRecentSearch(){const e=this.getHistoryItems();return e.length>0?e[0]:null}restoreMostRecentSearch(){return h(this,null,function*(){const e=this.getMostRecentSearch();return!!e&&(yield this.executeSearchFromHistory(e.id))})}getSearchCount(){return this.getHistoryItems().length}isCurrentSearch(e){if(!e)return!1;const t=e.parameters,a=e.flowType||"primary";if(a!==this.state.flowType.value)return!1;if(t.make!==this.state.selectedMake.value)return!1;if(t.model!==this.state.selectedModel.value)return!1;if("primary"===a||"year_select"===a){if(t.year!==this.state.selectedYear.value)return!1;if(t.modification!==this.state.selectedModification.value)return!1}else if("alternative"===a){if(t.generation!==this.state.selectedGeneration.value)return!1;if(t.modification!==this.state.selectedModification.value)return!1}return!0}}const be=p("finder-refactored-final",()=>{const e=fe();let t=null,a=null,o=null;const r=te();let l=null,i=null,n=null,s=null,u=null,g=!1;const m={trackEvent:()=>{},trackInteraction:()=>{},trackError:()=>{},track:()=>{}};function v(t=0){console.log("🔍 FINAL Store - attemptLoadInitialData, attempt:",t),console.log("🔍 FINAL Store - state.widgetResources.value:",e.widgetResources.value);const a=e.widgetResources.value,o=a&&Object.keys(a).length>0&&a.year;console.log("🔍 FINAL Store - hasResources:",o),o?(console.log("🔍 FINAL Store - Calling loadInitialData"),f()):t<5?(console.log("🔍 FINAL Store - Retrying in 100ms"),setTimeout(()=>v(t+1),100)):(console.error("🔍 FINAL Store - Failed to initialize after 5 attempts"),e.setError("Failed to initialize widget configuration"))}function f(){return h(this,null,function*(){console.log("🔍 FINAL Store - loadInitialData called");try{const e=yield n.loadInitialData();console.log("🔍 FINAL Store - loadInitialData success:",e),e&&(r.markInitialDataLoaded(),console.log("🔍 FINAL Store - markInitialDataLoaded called"))}catch(t){console.error("🔍 FINAL Store - loadInitialData error:",t),e.setError(t.message||"Failed to load initial data")}})}return d(c({},e),{initialize:function(c){console.log("🔍 FINAL Store Initialize - widgetConfig:",c),console.log("🔍 FINAL Store Initialize - widgetResources:",c.widgetResources);try{e.setConfig(c),console.log("🔍 FINAL Store - state.setConfig completed"),console.log("🔍 FINAL Store - widgetConfig.widgetResources:",c.widgetResources),console.log("🔍 FINAL Store - state.widgetResources.value:",e.widgetResources.value);const d=c.widgetResources||e.widgetResources.value||{};console.log("🔍 FINAL Store - Using resources for API client:",d),l=he(c,d),console.log("🔍 FINAL Store - apiClient created with resources:",d),i=me(c),console.log("🔍 FINAL Store - filterBuilder created"),a=Z(c),console.log("🔍 FINAL Store - analytics created"),n=pe(l,e,i,a),console.log("🔍 FINAL Store - vehicleLoader created");const h=c.id||c.uuid||c.widgetUuid||"default";t=X(h),console.log("🔍 FINAL Store - searchHistory created");const m=c.search_history||{};Object.keys(m).length>0&&t.configure(m),o=Q(),console.log("🔍 FINAL Store - crossDomainTracking created"),s=function(e,t,a,o,r){return new ke(e,t,a,o,r)}(l,e,r,a,t),console.log("🔍 FINAL Store - searchExecutor created"),u=function(e,t,a,o){return new Se(e,t,a,o)}(e,n,s,t),console.log("🔍 FINAL Store - historyManager created"),r.setConfig(c),r.setContextProvider(()=>({widgetUuid:h,widgetType:"finder-v2",flowType:e.flowType.value,selections:{year:e.selectedYear.value,make:e.selectedMake.value,model:e.selectedModel.value,generation:e.selectedGeneration.value,modification:e.selectedModification.value}})),console.log("🔍 FINAL Store - widgetEvents configured"),function(){try{w(e.selectedYear,(t,a)=>{t!==a&&r.emit("change:year",{value:e.toValueObject(e.years.value,t)})}),w(e.selectedMake,(t,a)=>{t!==a&&r.emit("change:make",{value:e.toValueObject(e.makes.value,t)})}),w(e.selectedModel,(t,a)=>{t!==a&&r.emit("change:model",{value:e.toValueObject(e.models.value,t)})}),w(e.selectedGeneration,(t,a)=>{t!==a&&r.emit("change:generation",{value:e.toValueObject(e.generations.value,t)})}),w(e.selectedModification,(t,a)=>{t!==a&&r.emit("change:modification",{value:e.toValueObject(e.modifications.value,t)})})}catch(t){console.error("🔍 FINAL Store - setupWatchers error:",t)}}(),console.log("🔍 FINAL Store - watchers setup completed"),v(),console.log("🔍 FINAL Store - attemptLoadInitialData called"),g=!0}catch(d){console.error("🔍 FINAL Store - Initialize error:",d,d.stack),e.setError(d.message||"Failed to initialize store"),g=!1}},loadInitialData:f,searchByVehicle:function(){return h(this,null,function*(){return s?yield s.searchByVehicle():(console.warn("searchByVehicle called before initialization"),null)})},clearError:()=>e.clearError(),resetAll:()=>e.resetAll(),loadYears:(e,t)=>n?n.loadYears(e,t):(console.warn("loadYears called before initialization"),Promise.resolve()),loadMakes:e=>n?n.loadMakes(e):(console.warn("loadMakes called before initialization"),Promise.resolve()),loadModels:(e,t)=>n?n.loadModels(e,t):(console.warn("loadModels called before initialization"),Promise.resolve()),loadGenerations:(e,t)=>n?n.loadGenerations(e,t):(console.warn("loadGenerations called before initialization"),Promise.resolve()),loadModifications:(e,t,a)=>n?n.loadModifications(e,t,a):(console.warn("loadModifications called before initialization"),Promise.resolve()),executeSearchFromHistory:function(e){return h(this,null,function*(){return u?yield u.executeSearchFromHistory(e):(console.warn("executeSearchFromHistory called before initialization"),!1)})},getSearchHistory:function(){return u?u.getSearchHistory():(console.warn("getSearchHistory called before initialization"),null)},getHistoryItems:()=>u?u.getHistoryItems():[],clearHistory:()=>u?u.clearHistory():null,hasHistory:()=>!!u&&u.hasHistory(),getMostRecentSearch:()=>u?u.getMostRecentSearch():null,restoreMostRecentSearch:()=>u?u.restoreMostRecentSearch():null,buildBrandFilterParams:function(){return i?i.buildBrandFilterParams():(console.warn("buildBrandFilterParams called before initialization"),{})},buildRegionParams:function(){return i?i.buildRegionParams():(console.warn("buildRegionParams called before initialization"),{})},getFilterSummary:()=>i?i.getFilterSummary():"",hasActiveFilters:()=>!!i&&i.hasActiveFilters(),isSearching:()=>!!s&&s.isSearching(),getCurrentSearchParams:()=>s?s.getCurrentSearchParams():null,getLastSearchParams:()=>s?s.getCurrentSearchParams():null,cancelSearch:()=>s?s.cancelSearch():null,search:e=>s?s.searchByVehicle():(console.warn("search called before initialization"),Promise.resolve(null)),isLoadingData:()=>!!n&&n.isLoading(),getLoadingStatus:()=>n?n.getLoadingStatus():{},getLoadedStatus:()=>n?n.getLoadedStatus():{},getAnalytics:()=>a||m,getCrossDomainTracking:()=>o,getWidgetEvents:()=>r,_modules:{apiClient:()=>l,filterBuilder:()=>i,vehicleLoader:()=>n,searchExecutor:()=>s,historyManager:()=>u,state:()=>e}})});const _e=function(){var e;const t=new URLSearchParams(window.location.search).get("useRefactoredStore");if("0"===t||"false"===t)return console.log("📦 Using legacy store (URL parameter - explicitly requested)"),0;if("3"===t)return console.log("🎯 Using FINAL refactored store (URL parameter)"),3;if("2"===t)return console.log("🚀 Using refactored store Phase 2 (URL parameter)"),2;if("true"===t||"1"===t)return console.log("🔧 Using refactored store Phase 1 (URL parameter)"),1;const a=localStorage.getItem("finder-v2-use-refactored-store");if("0"===a||"false"===a)return console.log("📦 Using legacy store (localStorage - explicitly requested)"),0;if("3"===a)return console.log("🎯 Using FINAL refactored store (localStorage)"),3;if("2"===a)return console.log("🚀 Using refactored store Phase 2 (localStorage)"),2;if("true"===a||"1"===a)return console.log("🔧 Using refactored store Phase 1 (localStorage)"),1;const o=null==(e=window.FinderV2Config)?void 0:e.useRefactoredStore;return 0===o||!1===o?(console.log("📦 Using legacy store (window.FinderV2Config - explicitly requested)"),0):3===o?(console.log("🎯 Using FINAL refactored store (window.FinderV2Config)"),3):2===o?(console.log("🚀 Using refactored store Phase 2 (window.FinderV2Config)"),2):!0===o||1===o?(console.log("🔧 Using refactored store Phase 1 (window.FinderV2Config)"),1):(console.log("🎯 Using FINAL refactored store (default)"),3)}(),Me=3===_e?be:2===_e?we:1===_e?ve:ae;function Ce(){const e=g(""),t=g(0),a=g("initializing"),o=g(null),r=24e4;let l=null,i=0,n=!1;const s=m(()=>{var e;return!0===(null==(e=window.FinderV2Config)?void 0:e.enhancedCSRF)});function c(){return h(this,null,function*(){var r,l,u;if(n)console.warn("Token refresh already in progress");else if(s.value){n=!0,a.value="refreshing";try{const n=((window.FinderV2Config||{}).baseUrl||"").replace(/\/$/,""),s=(null==(r=window.FinderV2Config)?void 0:r.uuid)||(null==(l=window.FinderV2Config)?void 0:l.id)||"",c=yield y.post(`${n}/widget/api/refresh-token/`,{widget_uuid:s,old_token:e.value||void 0},{headers:{"Content-Type":"application/json","X-CSRF-TOKEN":e.value||""},withCredentials:!0});if(!c.data||!c.data.token)throw new Error("Invalid token response");{const r=c.data.token;e.value=r,t.value=0,o.value=Date.now(),a.value="active",i=0,y.defaults.headers.common["X-CSRF-TOKEN"]=r,sessionStorage.setItem("widget_csrf_token",r),sessionStorage.setItem("widget_csrf_timestamp",Date.now().toString())}}catch(h){console.error("Failed to refresh CSRF token:",h),console.error("Error details:",(null==(u=h.response)?void 0:u.data)||h.message),a.value="error";const o=sessionStorage.getItem("widget_csrf_token"),r=sessionStorage.getItem("widget_csrf_timestamp");if(o&&r){const l=Date.now()-parseInt(r);l<36e5&&(e.value=o,t.value=l,y.defaults.headers.common["X-CSRF-TOKEN"]=o,a.value="active")}i<3?(i++,setTimeout(()=>c(),5e3)):(console.error("Max token refresh retries exceeded"),d())}finally{n=!1}}})}function d(){var t;const o=null==(t=window.FinderV2Config)?void 0:t.csrfToken;o?(e.value=o,y.defaults.headers.common["X-CSRF-TOKEN"]=o,a.value="active"):(console.error("No fallback CSRF token available"),a.value="error")}function u(){return h(this,null,function*(){var o;try{if(s.value)yield c(),s.value&&(l=setInterval(()=>{t.value+=r,t.value>=33e5&&c()},r));else{const t=(null==(o=window.FinderV2Config)?void 0:o.csrfToken)||"";e.value=t,y.defaults.headers.common["X-CSRF-TOKEN"]=t,a.value=t?"active":"error"}}catch(i){console.error("CSRF token initialization failed:",i),d()}})}return v(()=>{u()}),f(()=>{l&&(clearInterval(l),l=null)}),{csrfToken:e,tokenStatus:a,tokenAge:t,isEnhancedCSRFEnabled:s,refreshToken:function(){return h(this,null,function*(){i=0,yield c()})},getTokenInfo:function(){return{token:e.value,status:a.value,age:t.value,lastRefresh:o.value,enhanced:s.value}}}}const xe=(e,t)=>{const a=e.__vccOpts||e;for(const[o,r]of t)a[o]=r;return a},Ee={name:"ErrorBoundary",props:{fallback:{type:String,default:"Something went wrong. Please try again."},canRetry:{type:Boolean,default:!0},onError:{type:Function,default:null},errorTitle:{type:String,default:"Oops! Something went wrong"},retryText:{type:String,default:"Try Again"},customErrorMessage:{type:String,default:null}},emits:["error","retry"],setup(e,{emit:t}){const a=g(!1),o=g(null),r=m(()=>{if(e.customErrorMessage)return e.customErrorMessage;if(!o.value)return e.fallback;const t={Network:"Unable to connect. Please check your internet connection.","Failed to fetch":"Unable to connect to the server. Please try again later.",API:"Service temporarily unavailable. Please try again later.",Timeout:"Request timed out. Please try again.",TimeoutError:"The request took too long. Please try again.",Permission:"You don't have permission to access this resource.",NotFound:"The requested resource was not found.",404:"The requested resource was not found.",500:"Server error occurred. Please try again later.",503:"Service temporarily unavailable. Please try again later.",TypeError:"An unexpected error occurred. Please refresh the page.",SyntaxError:"An unexpected error occurred. Please refresh the page."},a=o.value.message||"";for(const[e,o]of Object.entries(t))if(a.includes(e))return o;return e.fallback}),l=m(()=>e.canRetry&&a.value);C((r,l,i)=>(a.value=!0,o.value=r,e.onError&&e.onError(r,l,i),t("error",{error:r,instance:l,info:i}),!1));return{hasError:a,error:o,errorMessage:r,showRetry:l,retry:()=>{a.value=!1,o.value=null,t("retry")},isDevelopment:!1}}},Fe={key:0,class:"error-boundary"},Ie={class:"error-container max-w-md mx-auto text-center p-8"},Le={class:"error-content space-y-4"},Te={class:"error-title text-xl font-semibold text-gray-900"},Re={class:"error-message text-gray-600"},Ae={key:1,class:"error-details mt-4 text-left"},Pe={class:"mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto whitespace-pre-wrap break-words"};const ze=xe(Ee,[["render",function(e,t,a,o,r,l){return o.hasError?(M(),b("div",Fe,[_("div",Ie,[t[2]||(t[2]=_("div",{class:"error-icon mb-4 flex justify-center"},[_("svg",{class:"w-12 h-12 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[_("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})])],-1)),_("div",Le,[_("h3",Te,F(a.errorTitle),1),_("p",Re,F(o.errorMessage),1),o.showRetry?(M(),b("button",{key:0,onClick:t[0]||(t[0]=(...e)=>o.retry&&o.retry(...e)),class:"retry-button px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"},F(a.retryText),1)):E("",!0),o.isDevelopment&&o.error?(M(),b("details",Ae,[t[1]||(t[1]=_("summary",{class:"cursor-pointer text-sm text-gray-500 hover:text-gray-700"},"Error Details",-1)),_("pre",Pe,F(o.error.stack||o.error.message),1)])):E("",!0)])])])):x(e.$slots,"default",{key:1},void 0,!0)}],["__scopeId","data-v-9248cc3a"]]),Oe={name:"CustomSelector",components:{Listbox:R,ListboxButton:T,ListboxOptions:L,ListboxOption:I,CheckIcon:function(e,t){return M(),b("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[_("path",{"fill-rule":"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z","clip-rule":"evenodd"})])},ChevronUpDownIcon:function(e,t){return M(),b("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[_("path",{"fill-rule":"evenodd",d:"M11.47 4.72a.75.75 0 0 1 1.06 0l3.75 3.75a.75.75 0 0 1-1.06 1.06L12 6.31 8.78 9.53a.75.75 0 0 1-1.06-1.06l3.75-3.75Zm-3.75 9.75a.75.75 0 0 1 1.06 0L12 17.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-3.75 3.75a.75.75 0 0 1-1.06 0l-3.75-3.75a.75.75 0 0 1 0-1.06Z","clip-rule":"evenodd"})])},ErrorBoundary:ze},props:{modelValue:{type:[String,Number],default:""},options:{type:Array,default:()=>[]},placeholder:{type:String,default:"Select option"},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},preloader:{type:Boolean,default:!1},stateLoaded:{type:Boolean,default:!1},autoExpand:{type:Boolean,default:!0},selectorType:{type:String,default:null}},emits:["update:modelValue","change"],setup(e,{emit:t}){const a=g(null),o=m({get:()=>e.modelValue,set:e=>{t("update:modelValue",e),t("change",e)}}),r=m(()=>e.modelValue?e.options.find(t=>(t.slug||t.id)===e.modelValue):null);w(()=>e.options,t=>{if(e.modelValue&&t.length>0){t.some(t=>(t.slug||t.id)===e.modelValue)||(o.value="")}});const l=window.matchMedia("(prefers-reduced-motion: reduce)").matches;w(()=>[e.stateLoaded,e.options.length],([t,o],[r])=>{var n,s;if(!r&&t&&o>0&&!e.disabled&&e.autoExpand&&!l){const e=document.activeElement,t=null!=(s=null==(n=a.value)?void 0:n.$el)?s:a.value;if(e&&e!==document.body&&t&&!t.contains(e)&&["INPUT","TEXTAREA"].includes(e.tagName))return;setTimeout(()=>h(this,null,function*(){var e,t;yield S();const o=null!=(t=null==(e=a.value)?void 0:e.$el)?t:a.value;try{o&&"function"==typeof o.click&&(o.focus({preventScroll:!0}),o.click(),i())}catch(r){}}),200)}},{flush:"post"});const i=()=>{window.parentIFrame&&window.parentIFrame.size&&(window.parentIFrame.size(),setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},350))};return w(()=>e.modelValue,()=>{i()}),v(()=>{var e;a.value&&(null!=(e=a.value.$el)?e:a.value).addEventListener("click",i)}),f(()=>{var e;a.value&&(null!=(e=a.value.$el)?e:a.value).removeEventListener("click",i)}),{selectedValue:o,selectedOption:r,getDisplayText:e=>{if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const t=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?`${e.name}, ${t}`:t}return e.name||""},getDisplayData:e=>{var t,a,o,r,l,i;if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const t=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?{isGeneration:!0,isModification:!1,name:e.name,yearRanges:t}:{isGeneration:!1,isModification:!1,name:t,yearRanges:""}}if(e.engine||e.trim||e.generation){const n=[];let s=e.name||"";if(("primary"===(null==(t=window.FinderV2Config)?void 0:t.flowType)||"primary"===(null==(o=null==(a=window.FinderV2Config)?void 0:a.widgetConfig)?void 0:o.flowType)||"year_select"===(null==(r=window.FinderV2Config)?void 0:r.flowType)||"year_select"===(null==(i=null==(l=window.FinderV2Config)?void 0:l.widgetConfig)?void 0:i.flowType))&&e.generation){const t=e.generation;let a="";if(t.name&&""!==t.name.trim()){const e=`${t.start}-${t.end}`;a=`${t.name} (${e})`}else a=`${t.start}-${t.end}`;a&&n.push(a)}if(e.trim&&""!==e.trim.trim()&&(e.trim,e.name),e.engine){const t=[];if(e.engine.capacity&&t.push(e.engine.capacity),e.engine.type&&t.push(e.engine.type),e.engine.fuel){let a=e.engine.fuel;"Petrol"===e.engine.fuel?a="Petrol":"Diesel"===e.engine.fuel?a="Diesel":"Hybrid"===e.engine.fuel&&(a="Hybrid"),t.push(a)}e.engine.power&&e.engine.power.hp&&t.push(`${e.engine.power.hp}HP`),t.length>0&&n.push(t.join(" "))}return{isGeneration:!1,isModification:!0,name:s,details:n.join(" • ")}}return{isGeneration:!1,isModification:!1,name:e.name||"",yearRanges:""}},buttonRef:a,triggerResize:i}}},Ve={class:"flex items-start"},Be={class:"relative"},De={key:0,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},$e={key:1,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},He={class:"truncate"},Ue={class:"truncate text-ws-secondary-500"},Ne={key:1,class:"flex flex-col min-w-0"},je={class:"truncate font-medium"},Ye={key:0,class:"truncate text-xs text-ws-secondary-500"},Ge={key:2,class:"flex items-center"},Je={class:"truncate"},qe={key:2,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},We={class:"flex"},Ke={key:1,class:"flex flex-col min-w-0 w-full"},Xe={key:2,class:"flex items-center"},Ze={class:"min-w-[32px] mt-2 ml-1"},Qe={key:0,class:"spinner-external"};const et={key:0,class:"search-history-container"},tt={class:"search-history-header"},at=["aria-label","aria-expanded","aria-controls"],ot={key:0,class:"search-count-badge"},rt=["id","aria-labelledby"],lt={class:"accordion-content"},it={key:0,class:"search-list"},nt=["onClick","onKeydown","aria-label"],st={class:"search-content"},ct={class:"search-description"},dt={class:"search-time"},ut=["onClick","onKeydown","aria-label"],ht={key:1,class:"show-more-container"},gt={key:2,class:"show-less-container"},mt={key:3,class:"empty-state"},vt={key:4,class:"clear-all-container"},ft={class:"confirmation-buttons"};const yt={class:"vehicle-search"},pt={key:0,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},wt={class:"form-group"},kt={class:"form-label"},St={class:"form-group"},bt={class:"form-label"},_t={class:"form-group"},Mt={class:"form-label"},Ct={class:"form-group"},xt={class:"form-label"},Et={key:1,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ft={class:"form-group"},It={class:"form-label"},Lt={class:"form-group"},Tt={class:"form-label"},Rt={class:"form-group"},At={class:"form-label"},Pt={class:"form-group"},zt={class:"form-label"},Ot={key:2,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Vt={class:"form-group"},Bt={class:"form-label"},Dt={class:"form-group"},$t={class:"form-label"},Ht={class:"form-group"},Ut={class:"form-label"},Nt={class:"form-group"},jt={class:"form-label"},Yt={key:0,class:"error-container"},Gt={class:"error-boundary-style"},Jt={class:"error-icon"},qt={style:{width:"32px",height:"32px",color:"#ef4444"},fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Wt={class:"error-title"},Kt={class:"error-message"};const Xt=e=>{const t=document.createElement("div");return t.textContent=e,t.innerHTML},Zt=(e,t)=>t.split(".").reduce((e,t)=>e&&void 0!==e[t]?e[t]:"",e),Qt=(e,t)=>e.replace(/\{\%\s*for\s+(\w+)\s+in\s+(\w+)\s*\%\}([\s\S]*?)\{\%\s*endfor\s*\%\}/g,(e,a,o,r)=>{const l=t[o];return Array.isArray(l)?l.map(e=>{let o=r;const l=c({[a]:e},t);return o=ea(o,l),o=ta(o,l),o}).join(""):""}),ea=(e,t)=>{let a=e,o=!0;for(;o;){o=!1;const e=/\{\%\s*if\s+(.*?)\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?)(?:\{\%\s*else\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?))?\{\%\s*endif\s*\%\}/g;a=a.replace(e,(e,a,r,l="")=>{o=!0;let i=aa(a,t)?r:l;return i.includes("{% if")&&(i=ea(i,t)),i})}return a},ta=(e,t)=>e.replace(/\{\{\s*(.*?)\s*\}\}/g,(e,a)=>{const o=a.trim(),r=o.match(/^(.+?)\s*\?\s*['"]?(.*?)['"]?\s*:\s*['"]?(.*?)['"]?$/);if(r){const[,e,a,o]=r,l=aa(e.trim(),t),i=a.replace(/^['"]|['"]$/g,""),n=o.replace(/^['"]|['"]$/g,"");return Xt(l?i:n)}const l=Zt(t,o);return Xt(String(l||""))}),aa=(e,t)=>{try{if(e.startsWith("not ")){const a=e.slice(4).trim();return!aa(a,t)}const a=Zt(t,e.trim());return Boolean(a)}catch(a){return!1}},oa={class:"results-display"},ra={key:0,class:"loading"},la={class:"ml-2"},ia={key:1,class:"no-results"},na={class:"no-results-text"},sa={key:2,class:"results-content"},ca={key:0,class:"custom-results"},da=["innerHTML"],ua={key:1,class:"default-results"},ha={class:"text-lg font-semibold mb-2"},ga={class:"font-medium uppercase tracking-wide text-sm mb-1"},ma={class:"grid grid-cols-2 gap-2 text-sm"},va={key:0},fa={key:2,class:"wheel-size-button-container"},ya=["href"];const pa={class:"search-content"},wa={key:0,class:"results-section"};const ka=xe({name:"FinderV2Widget",components:{VehicleSearch:xe({name:"VehicleSearch",components:{CustomSelector:xe(Oe,[["render",function(e,t,a,o,r,l){const i=z("ListboxButton"),n=z("ListboxOption"),s=z("CheckIcon"),c=z("ListboxOptions"),d=z("Listbox"),u=z("ErrorBoundary");return M(),b("div",Ve,[A(u,{"can-retry":!1,"error-title":"Dropdown Error",fallback:`Unable to load ${a.selectorType} selector`},{default:P(()=>[A(d,{modelValue:o.selectedValue,"onUpdate:modelValue":t[0]||(t[0]=e=>o.selectedValue=e),disabled:a.disabled,class:"flex-1"},{default:P(()=>[_("div",Be,[A(i,{as:"button",class:O(["grid w-full cursor-default grid-cols-1 theme-rounded-md bg-white py-1.5 pr-2 pl-3 text-left text-gray-900 border border-gray-300 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-primary-color focus:border-primary-color sm:text-sm/6",a.disabled?"bg-gray-100 text-gray-500 cursor-not-allowed":""]),ref:"buttonRef"},{default:P(()=>[a.loading?(M(),b("span",De,t[1]||(t[1]=[_("svg",{class:"animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[_("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),_("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),_("span",{class:"truncate"},"Loading...",-1)]))):o.selectedOption?(M(),b("span",$e,[o.getDisplayData(o.selectedOption).isGeneration?(M(),b(V,{key:0},[_("span",He,F(o.getDisplayData(o.selectedOption).name),1),_("span",Ue,F(o.getDisplayData(o.selectedOption).yearRanges),1)],64)):o.getDisplayData(o.selectedOption).isModification?(M(),b("div",Ne,[_("span",je,F(o.getDisplayData(o.selectedOption).name),1),o.getDisplayData(o.selectedOption).details?(M(),b("span",Ye,F(o.getDisplayData(o.selectedOption).details),1)):E("",!0)])):(M(),b("div",Ge,["make"===a.selectorType&&o.selectedOption?(M(),b("i",{key:0,class:O(["d-inline-block icon-make mr-2",`icon-l-${o.selectedOption.slug||o.selectedOption.id}`])},null,2)):E("",!0),_("span",Je,F(o.getDisplayText(o.selectedOption)),1)]))])):(M(),b("span",qe,[_("span",{class:O(["truncate",a.disabled?"text-gray-500":"text-gray-900"])},F(a.placeholder),3)])),t[2]||(t[2]=_("svg",{class:"col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 sm:size-4",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true"},[_("path",{"fill-rule":"evenodd",d:"M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z","clip-rule":"evenodd"})],-1))]),_:1,__:[2]},8,["class"]),A(B,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:P(()=>[A(c,{class:"z-10 mt-1 max-h-60 w-full overflow-auto theme-rounded-md bg-white py-1 text-base ring-1 shadow-lg ring-black/5 focus:outline-hidden sm:text-sm"},{default:P(()=>[a.options.length||a.loading?E("",!0):(M(),D(n,{key:0,value:null,class:"relative cursor-default select-none py-2 pr-9 pl-3 text-gray-500",disabled:""},{default:P(()=>t[3]||(t[3]=[$(" No options available ")])),_:1,__:[3]})),(M(!0),b(V,null,H(a.options,e=>(M(),D(n,{key:e.slug||e.id,value:e.slug||e.id,as:"template"},{default:P(({active:t,selected:r})=>[_("li",{class:O([t?"bg-primary-color text-white outline-hidden":"text-gray-900 hover:bg-gray-50","relative cursor-default select-none py-2 pr-9 pl-3"])},[_("div",We,[o.getDisplayData(e).isGeneration?(M(),b(V,{key:0},[_("span",{class:O([r?"font-semibold":"font-normal","truncate"])},F(o.getDisplayData(e).name),3),_("span",{class:O([t?"text-white/80":"text-ws-secondary-500","ml-2 truncate"])},F(o.getDisplayData(e).yearRanges),3)],64)):o.getDisplayData(e).isModification?(M(),b("div",Ke,[_("span",{class:O([r?"font-semibold":"font-medium","truncate"])},F(o.getDisplayData(e).name),3),o.getDisplayData(e).details?(M(),b("span",{key:0,class:O([t?"text-white/80":"text-ws-secondary-500","truncate text-xs"])},F(o.getDisplayData(e).details),3)):E("",!0)])):(M(),b("div",Xe,["make"===a.selectorType?(M(),b("i",{key:0,class:O(["d-inline-block icon-make mr-2",`icon-l-${e.slug||e.id}`])},null,2)):E("",!0),_("span",{class:O([r?"font-semibold":"font-normal","truncate"])},F(o.getDisplayText(e)),3)]))]),r?(M(),b("span",{key:0,class:O([t?"text-white":"text-primary-color","absolute inset-y-0 right-0 flex items-center pr-4"])},[A(s,{class:"size-5","aria-hidden":"true"})],2)):E("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue","disabled"])]),_:1},8,["fallback"]),_("div",Ze,[a.preloader?(M(),b("div",Qe,t[4]||(t[4]=[_("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"w-5 h-5 text-gray-400"},[_("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)]))):E("",!0)])])}],["__scopeId","data-v-96d229b4"]]),SearchHistoryIcon:xe({name:"SearchHistoryIcon",setup(){const e=Me(),{config:t}=U(e),a=g(!1),o=g(!1),r=g(!1),l=g(null),i=`search-history-accordion-${Math.random().toString(36).substr(2,9)}`,n=`search-history-button-${Math.random().toString(36).substr(2,9)}`;function s(){const t=e.getSearchHistory();l.value=t}v(()=>{s()}),w(()=>t.value,()=>{s()},{deep:!0}),w(()=>e.getSearchHistory(),e=>{e&&e!==l.value&&(l.value=e)});const c=e=>G(e)?e.value:e,d=m(()=>{var e,t;const a=null==(e=l.value)?void 0:e.isEnabled;return null!=(t=c(a))&&t}),u=m(()=>{const e=l.value;if(!e)return[];const t=e.searches;return c(t)||[]});w(u,(e,t)=>{},{deep:!0});const f=m(()=>{var e,t;const a=null==(e=l.value)?void 0:e.displaySearches;return null!=(t=c(a))?t:[]}),y=m(()=>{var e,t;const a=null==(e=l.value)?void 0:e.hasMoreSearches;return null!=(t=c(a))&&t}),p=m(()=>u.value.length),k=m(()=>{var e,a;return!1!==(null==(a=null==(e=t.value)?void 0:e.search_history)?void 0:a.enabled)||d.value}),S=m(()=>o.value?u.value:f.value),b=m(()=>Math.max(0,u.value.length-f.value.length));function _(){window.parentIFrame&&window.parentIFrame.size&&(window.parentIFrame.size(),setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},350))}function M(){a.value=!1,o.value=!1,_()}function C(){r.value=!1}function x(e){"Escape"===e.key&&(r.value?C():a.value&&M(),e.preventDefault())}return v(()=>{document.addEventListener("keydown",x)}),N(()=>{document.removeEventListener("keydown",x)}),{isAccordionOpen:a,showAll:o,showClearConfirmation:r,shouldShow:k,allSearches:u,displaySearches:f,hasMoreSearches:y,visibleSearches:S,remainingCount:b,searchCount:p,accordionId:i,buttonId:n,toggleAccordion:function(){a.value=!a.value,a.value&&(o.value=!1),_()},closeAccordion:M,executeSearch:function(t){return h(this,null,function*(){try{M(),yield e.executeSearchFromHistory(t),l.value=e.getSearchHistory(),setTimeout(()=>{const e=document.querySelector(".results-container");e&&e.scrollIntoView({behavior:"smooth",block:"start"})},100)}catch(a){}})},removeSearch:function(e){l.value&&(l.value.removeSearch(e),0===u.value.length&&M())},confirmClearAll:function(){r.value=!0},clearAllHistory:function(){l.value&&(l.value.clearHistory(),r.value=!1,M())},cancelClearAll:C,getRelativeTime:function(e){var t;return(null==(t=l.value)?void 0:t.getRelativeTime(e))||""}}}},[["render",function(e,t,a,o,r,l){return o.shouldShow?(M(),b("div",et,[_("div",tt,[_("button",{onClick:t[0]||(t[0]=(...e)=>o.toggleAccordion&&o.toggleAccordion(...e)),"aria-label":`Search History (${o.searchCount} searches)`,"aria-expanded":o.isAccordionOpen,"aria-controls":o.accordionId,class:O(["history-toggle-button",{"has-history":o.searchCount>0,expanded:o.isAccordionOpen}])},[t[18]||(t[18]=_("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",class:"history-icon"},[_("path",{d:"M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3Z"})],-1)),o.searchCount>0?(M(),b("span",ot,F(o.searchCount),1)):E("",!0)],10,at)]),A(B,{name:"accordion"},{default:P(()=>[o.isAccordionOpen?(M(),b("div",{key:0,id:o.accordionId,class:"search-history-accordion",role:"region","aria-labelledby":o.buttonId},[_("div",lt,[o.allSearches.length>0?(M(),b("div",it,[(M(!0),b(V,null,H(o.visibleSearches,e=>(M(),b("div",{key:e.id,class:"search-item",onClick:t=>o.executeSearch(e.id),onKeydown:[j(t=>o.executeSearch(e.id),["enter"]),j(Y(t=>o.executeSearch(e.id),["prevent"]),["space"])],tabindex:"0",role:"button","aria-label":`Execute search for ${e.description}`},[_("div",st,[_("div",ct,F(e.description),1),_("div",dt,F(o.getRelativeTime(e.timestamp)),1)]),_("button",{class:"remove-button",onClick:Y(t=>o.removeSearch(e.id),["stop"]),onKeydown:[j(Y(t=>o.removeSearch(e.id),["stop"]),["enter"]),j(Y(t=>o.removeSearch(e.id),["stop","prevent"]),["space"])],"aria-label":`Remove search for ${e.description}`,title:"Remove this search"}," ✕ ",40,ut)],40,nt))),128))])):E("",!0),o.hasMoreSearches&&!o.showAll?(M(),b("div",ht,[_("button",{class:"show-more-button",onClick:t[1]||(t[1]=e=>o.showAll=!0),onKeydown:[t[2]||(t[2]=j(e=>o.showAll=!0,["enter"])),t[3]||(t[3]=j(Y(e=>o.showAll=!0,["prevent"]),["space"]))],"aria-label":"Show more search history"}," Show More ("+F(o.remainingCount)+" more) ",33)])):E("",!0),o.showAll&&o.hasMoreSearches?(M(),b("div",gt,[_("button",{class:"show-less-button",onClick:t[4]||(t[4]=e=>o.showAll=!1),onKeydown:[t[5]||(t[5]=j(e=>o.showAll=!1,["enter"])),t[6]||(t[6]=j(Y(e=>o.showAll=!1,["prevent"]),["space"]))],"aria-label":"Show less search history"}," Show Less ",32)])):E("",!0),0===o.allSearches.length?(M(),b("div",mt,t[19]||(t[19]=[_("div",{class:"empty-icon"},"🔍",-1),_("p",{class:"empty-message"},"No search history yet",-1),_("p",{class:"empty-hint"},"Your recent searches will appear here",-1)]))):E("",!0),o.allSearches.length>0?(M(),b("div",vt,[_("button",{class:"clear-all-button",onClick:t[7]||(t[7]=(...e)=>o.confirmClearAll&&o.confirmClearAll(...e)),onKeydown:[t[8]||(t[8]=j((...e)=>o.confirmClearAll&&o.confirmClearAll(...e),["enter"])),t[9]||(t[9]=j(Y((...e)=>o.confirmClearAll&&o.confirmClearAll(...e),["prevent"]),["space"]))],"aria-label":"Clear all search history"}," Clear All History ",32)])):E("",!0)])],8,rt)):E("",!0)]),_:1}),A(B,{name:"modal"},{default:P(()=>[o.showClearConfirmation?(M(),b("div",{key:0,class:"modal-overlay",onClick:t[17]||(t[17]=(...e)=>o.cancelClearAll&&o.cancelClearAll(...e))},[_("div",{class:"confirmation-modal",onClick:t[16]||(t[16]=Y(()=>{},["stop"]))},[t[20]||(t[20]=_("h4",{class:"confirmation-title"},"Clear Search History",-1)),t[21]||(t[21]=_("p",{class:"confirmation-message"}," Are you sure you want to clear all your search history? This action cannot be undone. ",-1)),_("div",ft,[_("button",{class:"confirm-button",onClick:t[10]||(t[10]=(...e)=>o.clearAllHistory&&o.clearAllHistory(...e)),onKeydown:[t[11]||(t[11]=j((...e)=>o.clearAllHistory&&o.clearAllHistory(...e),["enter"])),t[12]||(t[12]=j(Y((...e)=>o.clearAllHistory&&o.clearAllHistory(...e),["prevent"]),["space"]))]}," Clear All ",32),_("button",{class:"cancel-button",onClick:t[13]||(t[13]=(...e)=>o.cancelClearAll&&o.cancelClearAll(...e)),onKeydown:[t[14]||(t[14]=j((...e)=>o.cancelClearAll&&o.cancelClearAll(...e),["enter"])),t[15]||(t[15]=j(Y((...e)=>o.cancelClearAll&&o.cancelClearAll(...e),["prevent"]),["space"]))]}," Cancel ",32)])])])):E("",!0)]),_:1})])):E("",!0)}],["__scopeId","data-v-5863a6f9"]])},props:{translation:{type:Object,default:()=>({})}},setup(){let e,t,a,o,r,l,i,n,s,c,d,u,v,f,y,p,k,S,b,_,M,C,x,E,F;console.log("🔍 VehicleSearch setup - starting");try{e=Me(),console.log("🔍 VehicleSearch setup - finderStore:",e),t=e.getAnalytics(),console.log("🔍 VehicleSearch setup - analytics:",t);const h=U(e);console.log("🔍 VehicleSearch setup - storeToRefs result:",h),a=h.loading,o=h.error,r=h.selectedYear,l=h.selectedMake,i=h.selectedModel,n=h.selectedModification,s=h.selectedGeneration,c=h.years,d=h.makes,u=h.models,v=h.modifications,f=h.generations,y=h.flowType,p=h.loadingYears,k=h.loadingMakes,S=h.loadingModels,b=h.loadingGenerations,_=h.loadingModifications,M=h.stateLoadedYears,C=h.stateLoadedMakes,x=h.stateLoadedModels,E=h.stateLoadedGenerations,F=h.stateLoadedModifications,console.log("🔍 VehicleSearch setup - refs assigned"),console.log("🔍 VehicleSearch setup - error ref:",o),console.log("🔍 VehicleSearch setup - error value:",null==o?void 0:o.value)}catch(R){return console.error("🔍 VehicleSearch setup - ERROR:",R,R.stack),{loading:g(!1),error:g("Failed to initialize component"),selectedYear:g(""),selectedMake:g(""),selectedModel:g(""),selectedModification:g(""),selectedGeneration:g(""),years:g([]),makes:g([]),models:g([]),modifications:g([]),generations:g([]),flowType:g("primary"),loadingYears:g(!1),loadingMakes:g(!1),loadingModels:g(!1),loadingGenerations:g(!1),loadingModifications:g(!1),stateLoadedYears:g(!1),stateLoadedMakes:g(!1),stateLoadedModels:g(!1),stateLoadedGenerations:g(!1),stateLoadedModifications:g(!1),canSearch:g(!1),onYearChange:()=>{},onMakeChange:()=>{},onModelChange:()=>{},onGenerationChange:()=>{},handleSearch:()=>{},getErrorTitle:()=>"Component Error",getErrorMessage:()=>"Failed to initialize component",isRecoverableError:()=>!1,retryLastAction:()=>{}}}const I=m(()=>"primary"===y.value?r.value&&l.value&&i.value&&n.value:"alternative"===y.value?l.value&&i.value&&s.value&&n.value:"year_select"===y.value&&(l.value&&i.value&&r.value&&n.value)),L=g(!1);function T(){return h(this,null,function*(){I.value&&(yield e.searchByVehicle())})}w(I,(e,t)=>h(this,null,function*(){e&&!t&&(L.value=!0,yield T(),setTimeout(()=>{L.value=!1},100))})),w(n,(e,o)=>h(this,null,function*(){t&&e&&e!==o&&t.trackInteraction("modification_selected",{selected_modification:e,selected_make:l.value,selected_model:i.value,selected_year:r.value,selected_generation:s.value,flow_step:4,flow_type:y.value}),!e||e===o||a.value||L.value||(yield T())}));return{loading:a,error:o,selectedYear:r,selectedMake:l,selectedModel:i,selectedModification:n,selectedGeneration:s,years:c,makes:d,models:u,modifications:v,generations:f,flowType:y,loadingYears:p,loadingMakes:k,loadingModels:S,loadingGenerations:b,loadingModifications:_,stateLoadedYears:M,stateLoadedMakes:C,stateLoadedModels:x,stateLoadedGenerations:E,stateLoadedModifications:F,canSearch:I,onYearChange:function(){return h(this,null,function*(){t&&r.value&&t.trackInteraction("year_selected",{selected_year:r.value,flow_step:"primary"===y.value?1:3,flow_type:y.value}),"primary"===y.value?(e.selectedMake="",e.selectedModel="",e.selectedModification="",e.models=[],e.modifications=[],e.stateLoadedMakes=!1,e.stateLoadedModels=!1,e.stateLoadedModifications=!1,r.value&&(yield e.loadMakes(r.value))):"year_select"===y.value&&(e.selectedModification="",e.modifications=[],e.stateLoadedModifications=!1,r.value&&(yield e.loadModifications(l.value,i.value,r.value)))})},onMakeChange:function(){return h(this,null,function*(){t&&l.value&&t.trackInteraction("make_selected",{selected_make:l.value,flow_step:"primary"===y.value?2:1,flow_type:y.value}),e.selectedModel="",e.selectedModification="",e.selectedGeneration="",e.models=[],e.modifications=[],e.generations=[],e.stateLoadedModels=!1,e.stateLoadedModifications=!1,e.stateLoadedGenerations=!1,l.value&&("primary"===y.value?yield e.loadModels(l.value,r.value):"alternative"!==y.value&&"year_select"!==y.value||(yield e.loadModels(l.value)))})},onModelChange:function(){return h(this,null,function*(){t&&i.value&&t.trackInteraction("model_selected",{selected_model:i.value,selected_make:l.value,flow_step:"primary"===y.value?3:2,flow_type:y.value}),e.selectedModification="",e.selectedGeneration="",e.modifications=[],e.generations=[],e.stateLoadedModifications=!1,e.stateLoadedGenerations=!1,"year_select"===y.value&&(e.selectedYear="",e.years=[],e.stateLoadedYears=!1),i.value&&("primary"===y.value?yield e.loadModifications(l.value,i.value,r.value):"alternative"===y.value?yield e.loadGenerations(l.value,i.value):"year_select"===y.value&&(yield e.loadYears(l.value,i.value)))})},onGenerationChange:function(){return h(this,null,function*(){t&&s.value&&t.trackInteraction("generation_selected",{selected_generation:s.value,selected_make:l.value,selected_model:i.value,flow_step:3,flow_type:y.value}),e.selectedModification="",e.modifications=[],e.stateLoadedModifications=!1,s.value&&(yield e.loadModifications(l.value,i.value,s.value))})},handleSearch:T,getErrorTitle:e=>{if(!e)return"";const t=e.toLowerCase();return t.includes("network")||t.includes("failed to fetch")?"Connection Problem":t.includes("timeout")?"Request Timeout":t.includes("404")||t.includes("not found")?"Not Found":t.includes("500")||t.includes("server")?"Server Error":"Something Went Wrong"},getErrorMessage:e=>{if(!e)return"";const t=e.toLowerCase();return t.includes("network")||t.includes("failed to fetch")||t.includes("err_internet_disconnected")?"Unable to connect. Please check your internet connection and try again.":t.includes("timeout")?"The request took too long. Please try again.":t.includes("404")||t.includes("not found")?"The requested resource was not found.":t.includes("500")||t.includes("server")?"Service temporarily unavailable. Please try again later.":"We encountered an error. Please try again."},isRecoverableError:e=>{if(!e)return!1;const t=e.toLowerCase();return!["permission","forbidden","unauthorized","invalid"].some(e=>t.includes(e))},retryLastAction:()=>h(this,null,function*(){e.clearError(),"primary"===y.value?r.value&&0!==c.value.length?r.value&&!l.value?yield e.loadMakes(r.value):l.value&&!i.value?yield e.loadModels(l.value,r.value):i.value&&!n.value?yield e.loadModifications(l.value,i.value,r.value):n.value&&(yield e.searchByVehicle()):yield e.loadYears():"alternative"===y.value?l.value&&0!==d.value.length?l.value&&!i.value?yield e.loadModels(l.value):i.value&&!s.value?yield e.loadGenerations(l.value,i.value):s.value&&!n.value?yield e.loadModifications(l.value,i.value,s.value):n.value&&(yield e.searchByVehicle()):yield e.loadMakes():"year_select"===y.value&&(l.value&&0!==d.value.length?l.value&&!i.value?yield e.loadModels(l.value):i.value&&!r.value?yield e.loadYears(l.value,i.value):r.value&&!n.value?yield e.loadModifications(l.value,i.value,r.value):n.value&&(yield e.searchByVehicle()):yield e.loadMakes())})}}},[["render",function(e,t,a,o,r,l){const i=z("SearchHistoryIcon"),n=z("CustomSelector");return M(),b("div",yt,[A(i),_("form",{onSubmit:t[12]||(t[12]=Y((...e)=>o.handleSearch&&o.handleSearch(...e),["prevent"])),class:"search-form"},["primary"===o.flowType?(M(),b("div",pt,[_("div",wt,[_("label",kt,F(a.translation.year_label||"Year"),1),A(n,{modelValue:o.selectedYear,"onUpdate:modelValue":t[0]||(t[0]=e=>o.selectedYear=e),options:o.years,loading:o.loading&&!o.years.length,preloader:o.loadingYears,"state-loaded":o.stateLoadedYears,"auto-expand":!1,disabled:o.loadingYears&&0===o.years.length,placeholder:a.translation.select_year||"Select Year",onChange:o.onYearChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),_("div",St,[_("label",bt,F(a.translation.make_label||"Make"),1),A(n,{modelValue:o.selectedMake,"onUpdate:modelValue":t[1]||(t[1]=e=>o.selectedMake=e),options:o.makes,loading:o.loading&&o.selectedYear&&!o.makes.length,preloader:o.loadingMakes,"state-loaded":o.stateLoadedMakes,disabled:!o.selectedYear||o.loadingMakes||!o.stateLoadedMakes,placeholder:a.translation.select_make||"Select Make","selector-type":"make",onChange:o.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),_("div",_t,[_("label",Mt,F(a.translation.model_label||"Model"),1),A(n,{modelValue:o.selectedModel,"onUpdate:modelValue":t[2]||(t[2]=e=>o.selectedModel=e),options:o.models,loading:o.loading&&o.selectedMake&&!o.models.length,preloader:o.loadingModels,"state-loaded":o.stateLoadedModels,disabled:!o.selectedMake||o.loadingModels||!o.stateLoadedModels,placeholder:a.translation.select_model||"Select Model",onChange:o.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),_("div",Ct,[_("label",xt,F(a.translation.modification_label||"Modification"),1),A(n,{modelValue:o.selectedModification,"onUpdate:modelValue":t[3]||(t[3]=e=>o.selectedModification=e),options:o.modifications,loading:o.loading&&o.selectedModel&&!o.modifications.length,preloader:o.loadingModifications,"state-loaded":o.stateLoadedModifications,disabled:!o.selectedModel||o.loadingModifications||!o.stateLoadedModifications,placeholder:a.translation.select_modification||"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder"])])])):"alternative"===o.flowType?(M(),b("div",Et,[_("div",Ft,[_("label",It,F(a.translation.make_label||"Make"),1),A(n,{modelValue:o.selectedMake,"onUpdate:modelValue":t[4]||(t[4]=e=>o.selectedMake=e),options:o.makes,loading:o.loading&&!o.makes.length,preloader:o.loadingMakes,"state-loaded":o.stateLoadedMakes,"auto-expand":!1,disabled:o.loadingMakes&&0===o.makes.length,placeholder:a.translation.select_make||"Select Make","selector-type":"make",onChange:o.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),_("div",Lt,[_("label",Tt,F(a.translation.model_label||"Model"),1),A(n,{modelValue:o.selectedModel,"onUpdate:modelValue":t[5]||(t[5]=e=>o.selectedModel=e),options:o.models,loading:o.loading&&o.selectedMake&&!o.models.length,preloader:o.loadingModels,"state-loaded":o.stateLoadedModels,disabled:!o.selectedMake||o.loadingModels||!o.stateLoadedModels,placeholder:a.translation.select_model||"Select Model",onChange:o.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),_("div",Rt,[_("label",At,F(a.translation.generation_label||"Generation"),1),A(n,{modelValue:o.selectedGeneration,"onUpdate:modelValue":t[6]||(t[6]=e=>o.selectedGeneration=e),options:o.generations,loading:o.loading&&o.selectedModel&&!o.generations.length,preloader:o.loadingGenerations,"state-loaded":o.stateLoadedGenerations,disabled:!o.selectedModel||o.loadingGenerations||!o.stateLoadedGenerations,placeholder:a.translation.select_generation||"Select Generation",onChange:o.onGenerationChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),_("div",Pt,[_("label",zt,F(a.translation.modification_label||"Modification"),1),A(n,{modelValue:o.selectedModification,"onUpdate:modelValue":t[7]||(t[7]=e=>o.selectedModification=e),options:o.modifications,loading:o.loading&&o.selectedGeneration&&!o.modifications.length,preloader:o.loadingModifications,"state-loaded":o.stateLoadedModifications,disabled:!o.selectedGeneration||o.loadingModifications||!o.stateLoadedModifications,placeholder:a.translation.select_modification||"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder"])])])):"year_select"===o.flowType?(M(),b("div",Ot,[_("div",Vt,[_("label",Bt,F(a.translation.make_label||"Make"),1),A(n,{modelValue:o.selectedMake,"onUpdate:modelValue":t[8]||(t[8]=e=>o.selectedMake=e),options:o.makes,loading:o.loading&&!o.makes.length,preloader:o.loadingMakes,"state-loaded":o.stateLoadedMakes,"auto-expand":!1,disabled:o.loadingMakes&&0===o.makes.length,placeholder:a.translation.select_make||"Select Make","selector-type":"make",onChange:o.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),_("div",Dt,[_("label",$t,F(a.translation.model_label||"Model"),1),A(n,{modelValue:o.selectedModel,"onUpdate:modelValue":t[9]||(t[9]=e=>o.selectedModel=e),options:o.models,loading:o.loading&&o.selectedMake&&!o.models.length,preloader:o.loadingModels,"state-loaded":o.stateLoadedModels,disabled:!o.selectedMake||o.loadingModels||!o.stateLoadedModels,placeholder:a.translation.select_model||"Select Model",onChange:o.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),_("div",Ht,[_("label",Ut,F(a.translation.year_label||"Year"),1),A(n,{modelValue:o.selectedYear,"onUpdate:modelValue":t[10]||(t[10]=e=>o.selectedYear=e),options:o.years,loading:o.loading&&o.selectedModel&&!o.years.length,preloader:o.loadingYears,"state-loaded":o.stateLoadedYears,disabled:!o.selectedModel||o.loadingYears||!o.stateLoadedYears,placeholder:a.translation.select_year||"Select Year",onChange:o.onYearChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),_("div",Nt,[_("label",jt,F(a.translation.modification_label||"Modification"),1),A(n,{modelValue:o.selectedModification,"onUpdate:modelValue":t[11]||(t[11]=e=>o.selectedModification=e),options:o.modifications,loading:o.loading&&o.selectedYear&&!o.modifications.length,preloader:o.loadingModifications,"state-loaded":o.stateLoadedModifications,disabled:!o.selectedYear||o.loadingModifications||!o.stateLoadedModifications,placeholder:a.translation.select_modification||"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder"])])])):E("",!0)],32),o.error?(M(),b("div",Yt,[_("div",Gt,[_("div",Jt,[(M(),b("svg",qt,t[14]||(t[14]=[_("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"},null,-1)])))]),_("p",Wt,F(o.getErrorTitle(o.error)),1),_("p",Kt,F(o.getErrorMessage(o.error)),1),o.isRecoverableError(o.error)?(M(),b("button",{key:0,onClick:t[13]||(t[13]=(...e)=>o.retryLastAction&&o.retryLastAction(...e)),class:"retry-button"}," Try Again ")):E("",!0)])])):E("",!0)])}],["__scopeId","data-v-52b0dbc4"]]),ResultsDisplay:xe({name:"ResultsDisplay",props:{translation:{type:Object,default:()=>({})}},setup(e){const t=Me(),{loadingResults:a,results:o,config:r,outputTemplate:l}=U(t),i=m(()=>l.value&&l.value.trim().length>0),n=m(()=>{var e;return!(!r.value.subscriptionPaid&&!(null==(e=r.value.widgetConfig)?void 0:e.subscriptionPaid))}),s=m(()=>{var e,t,a,o,l,i;const n=null==(a=null==(t=null==(e=r.value.interface)?void 0:e.blocks)?void 0:t.button_to_ws)?void 0:a.hide,s=null==(i=null==(l=null==(o=r.value.widgetConfig)?void 0:o.blocks)?void 0:l.button_to_ws)?void 0:i.hide;return void 0!==n?n:!!s}),c=m(()=>!n.value&&!s.value),d=m(()=>{var e;return`https://www.wheel-size.com${r.value.utm||(null==(e=r.value.widgetConfig)?void 0:e.utm)||""}`});return{loadingResults:a,results:o,hasCustomTemplate:i,renderCustomTemplate:e=>i.value?((e,t)=>{try{let a=e;return a=Qt(a,t),a=ea(a,t),a=ta(a,t),a}catch(a){return`<div class="text-red-500 text-sm">Template error: ${a.message}</div>`}})(l.value,e):"",showWheelSizeButton:c,wheelSizeUrl:d,translation:e.translation}}},[["render",function(e,t,a,o,r,l){return M(),b("div",oa,[o.loadingResults?(M(),b("div",ra,[t[0]||(t[0]=_("svg",{class:"spinner",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[_("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),_("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),_("span",la,F(o.translation.loading_results||"Loading results..."),1)])):0===o.results.length?(M(),b("div",ia,[_("p",na,F(o.translation.no_results||"No results found. Please try different search criteria."),1)])):(M(),b("div",sa,[o.hasCustomTemplate?(M(),b("div",ca,[(M(!0),b(V,null,H(o.results,(e,t)=>(M(),b("div",{key:t,innerHTML:o.renderCustomTemplate(e)},null,8,da))),128))])):(M(),b("div",ua,[(M(!0),b(V,null,H(o.results,(e,a)=>(M(),b("div",{class:"space-y-4",key:a},[_("h3",ha,F(e.make.name)+" "+F(e.model.name)+" ("+F(e.start_year)+"-"+F(e.end_year)+") ",1),(M(!0),b(V,null,H(e.wheels,(e,a)=>(M(),b("div",{key:a,class:O(["p-3 theme-rounded-md border",e.is_stock?"border-indigo-400 bg-indigo-50":"border-gray-300"])},[_("div",ga,F(e.is_stock?"OE option1":"After-market option1"),1),_("div",ma,[_("div",null,[t[1]||(t[1]=_("span",{class:"text-gray-500"},"Front:",-1)),$(" "+F(e.front.tire)+" – "+F(e.front.rim),1)]),!e.showing_fp_only&&e.rear.tire?(M(),b("div",va,[t[2]||(t[2]=_("span",{class:"text-gray-500"},"Rear:",-1)),$(" "+F(e.rear.tire)+" – "+F(e.rear.rim),1)])):E("",!0)])],2))),128))]))),128))])),o.showWheelSizeButton?(M(),b("div",fa,[_("a",{href:o.wheelSizeUrl,target:"_blank",rel:"noopener noreferrer",class:"inline-block rounded-md bg-white px-3.5 py-2 text-sm font-semibold text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50"},F(o.translation.search_button||"Unlock More Insights at Wheel-Size.com"),9,ya)])):E("",!0)]))])}],["__scopeId","data-v-f4762d5f"]]),ErrorBoundary:ze,ApiErrorBoundary:xe({name:"ApiErrorBoundary",components:{ErrorBoundary:ze},props:{retryAction:{type:Function,default:null},componentName:{type:String,default:"Component"}},setup(e){const t=Me(),a=g(!1),o=g(0);return{errorTitle:m(()=>navigator.onLine?o.value>=3?"Unable to Load Data":"Connection Problem":"No Internet Connection"),fallbackMessage:m(()=>navigator.onLine?o.value>=3?"We're having trouble connecting to our servers. Please try again later or contact support if the problem persists.":"We're having trouble loading the data. Please try again.":"Please check your internet connection and try again."),retryText:m(()=>a.value?"Retrying...":o.value>0?`Try Again (${o.value}/3)`:"Try Again"),handleRetry:()=>h(this,null,function*(){if(!(a.value||o.value>=3)){a.value=!0,o.value++;try{e.retryAction?yield e.retryAction():yield t.loadInitialData(),o.value=0}catch(r){console.error("Retry failed:",r),o.value>=3&&console.error(`Max retries (3) reached for ${e.componentName}`)}finally{a.value=!1}}}),handleApiError:({error:t})=>{var a,r,l,i,n,s;const c={component:e.componentName,endpoint:null==(a=null==t?void 0:t.config)?void 0:a.url,method:null==(r=null==t?void 0:t.config)?void 0:r.method,status:null==(l=null==t?void 0:t.response)?void 0:l.status,statusText:null==(i=null==t?void 0:t.response)?void 0:i.statusText,message:(null==t?void 0:t.message)||"Unknown error",timestamp:(new Date).toISOString(),online:navigator.onLine,retryCount:o.value};console.error("API Error:",c),(null==(s=null==(n=window.FinderV2Config)?void 0:n.analytics)?void 0:s.track)&&window.FinderV2Config.analytics.track("api_error",c),window.dispatchEvent(new CustomEvent("finderv2:api-error",{detail:c}))},isRetrying:a}}},[["render",function(e,t,a,o,r,l){const i=z("ErrorBoundary");return M(),D(i,{"can-retry":!0,"error-title":o.errorTitle,"custom-error-message":o.fallbackMessage,"retry-text":o.retryText,onRetry:o.handleRetry,onError:o.handleApiError},{default:P(()=>[x(e.$slots,"default")]),_:3},8,["error-title","custom-error-message","retry-text","onRetry","onError"])}]])},setup(){const e=Me(),t=window.FinderV2Config||{},a=t.theme||{},{loadingResults:o,results:r}=U(e),{tokenStatus:l,isEnhancedCSRFEnabled:i}=Ce(),{initialize:n,fingerprintCollected:s}=K(),c=m(()=>r.value.length>0),d=m(()=>{var e;const t=[];return a.name&&t.push(`theme-${a.name.toLowerCase().replace(/\s+/g,"-")}`),(null==(e=a.effects)?void 0:e.hoverEffect)&&t.push(`hover-${a.effects.hoverEffect}`),t.join(" ")}),u=m(()=>{const e={};if(a.colors&&(e["--theme-primary"]=a.colors.primary,e["--theme-secondary"]=a.colors.secondary,e["--theme-accent"]=a.colors.accent,e["--theme-background"]=a.colors.background,e["--theme-text"]=a.colors.text,e["--theme-primary-rgb"]=f(a.colors.primary),e["--theme-secondary-rgb"]=f(a.colors.secondary),e["--theme-accent-rgb"]=f(a.colors.accent)),a.typography&&(e["--theme-font-family"]=a.typography.fontFamily,e["--theme-font-size"]=a.typography.fontSize,e["--theme-font-weight"]=a.typography.fontWeight,e["--theme-line-height"]=a.typography.lineHeight,e["--theme-letter-spacing"]=a.typography.letterSpacing),a.spacing&&(e["--theme-padding"]=a.spacing.padding,e["--theme-margin"]=a.spacing.margin),a.effects){e["--theme-border-radius"]=a.effects.borderRadius,e["--theme-border-width"]=a.effects.borderWidth,e["--theme-animation-speed"]=a.effects.animationSpeed;const t=a.effects.shadowIntensity,o={none:"none",light:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",medium:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",heavy:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"};e["--theme-shadow"]=o[t]||o.medium}return e});function f(e){if(!e)return"0, 0, 0";const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?`${parseInt(t[1],16)}, ${parseInt(t[2],16)}, ${parseInt(t[3],16)}`:"0, 0, 0"}const y=m(()=>t.translation||{year_label:"Year",make_label:"Make",model_label:"Model",generation_label:"Generation",modification_label:"Modification",select_year:"Select Year",select_make:"Select Make",select_model:"Select Model",select_generation:"Select Generation",select_modification:"Select Modification",loading:"Loading...",loading_results:"Loading results...",no_results:"No results found. Please try different search criteria.",search_button:"Unlock More Insights at Wheel-Size.com"});v(()=>h(this,null,function*(){if(n().then(()=>{}).catch(e=>{console.warn("Bot protection init failed (non-blocking):",e)}),i.value){let e=0;for(;"active"!==l.value&&e<20;)yield new Promise(e=>setTimeout(e,100)),e++;"active"===l.value||console.warn("Enhanced CSRF token not ready after timeout, proceeding with legacy")}if(e.initialize(t),a.colors||a.typography||a.spacing||a.effects){const e=document.documentElement;Object.entries(u.value).forEach(([t,a])=>{e.style.setProperty(t,a)})}}));const{withRetry:p}=function(){const e=3,t=1e3,a=new Map;return{withRetry:(a,...o)=>h(this,[a,...o],function*(a,o={}){const{retries:r=e,delay:l=t,backoff:i=!0,onRetry:n=null}=o;let s=null,c=0;for(;c<r;)try{return yield a()}catch(d){if(s=d,c++,c<r){n&&n(c,d);const e=i?l*Math.pow(2,c-1):l;yield new Promise(t=>setTimeout(t,e))}}throw s}),withFallback:(e,t)=>h(this,null,function*(){try{return yield e()}catch(a){return"function"==typeof t?t(a):t}}),withCircuitBreaker:(e,t)=>h(this,null,function*(){const o=Date.now(),r=a.get(e)||{count:0,lastFailure:0};if(r.count>=5){if(o-r.lastFailure<6e4)throw new Error(`Circuit breaker open for ${e}. Too many failures.`);a.delete(e)}try{const o=yield t();return a.delete(e),o}catch(l){throw r.count++,r.lastFailure=o,a.set(e,r),l}}),withTimeout:(e,t=5e3)=>h(this,null,function*(){return Promise.race([e(),new Promise((e,a)=>setTimeout(()=>a(new Error(`Operation timed out after ${t}ms`)),t))])}),batchWithErrorHandling:(e,...t)=>h(this,[e,...t],function*(e,t={}){const{stopOnError:a=!1,parallel:o=!1}=t,r=[],l=[];if(o){const t=e.map((e,t)=>h(this,null,function*(){try{return{success:!0,result:yield e(),index:t}}catch(a){return{success:!1,error:a,index:t}}}));(yield Promise.all(t)).forEach(e=>{e.success?r[e.index]=e.result:l[e.index]=e.error})}else for(let n=0;n<e.length;n++)try{r[n]=yield e[n]()}catch(i){if(l[n]=i,a)throw i}return{results:r,errors:l,hasErrors:l.length>0}}),createDebouncedErrorHandler:(e,t=1e3)=>{let a=null,o=[];return r=>{o.push(r),a&&clearTimeout(a),a=setTimeout(()=>{e(o),o=[]},t)}},createErrorAggregator:()=>{const e=g([]),t=g(new Map);return{errors:e,errorCounts:t,addError:(a,o={})=>{const r=`${a.name}:${a.message}`,l=t.value.get(r)||0;t.value.set(r,l+1),e.value.push({error:a,context:o,timestamp:Date.now(),count:l+1}),e.value.length>50&&e.value.shift()},getTopErrors:(e=5)=>Array.from(t.value.entries()).sort((e,t)=>t[1]-e[1]).slice(0,e).map(([e,t])=>({error:e,count:t})),clear:()=>{e.value=[],t.value.clear()}}},resetCircuitBreaker:e=>{e?a.delete(e):a.clear()},getCircuitBreakerStatus:e=>{const t=a.get(e);if(!t)return{status:"closed",failures:0};const o=Date.now();return{status:t.count>=5&&o-t.lastFailure<6e4?"open":"closed",failures:t.count,lastFailure:new Date(t.lastFailure).toISOString()}}}}();return{loadingResults:o,hasResults:c,themeClasses:d,themeStyles:u,translation:y,handleWidgetError:({error:e,instance:t,info:a})=>{var o,r,l;console.error("Widget error caught:",e),(null==(r=null==(o=window.FinderV2Config)?void 0:o.analytics)?void 0:r.track)&&window.FinderV2Config.analytics.track("widget_error",{error:e.message,component:(null==(l=null==t?void 0:t.$options)?void 0:l.name)||"Unknown",info:a})},handleWidgetRetry:()=>h(this,null,function*(){e.resetAll(),yield e.initialize(t)}),retryVehicleSearch:()=>h(this,null,function*(){yield p(()=>e.loadInitialData(),{retries:3,delay:1e3,backoff:!0})}),retryResultsLoad:()=>h(this,null,function*(){const t=e.getLastSearchParams();t&&(yield p(()=>e.search(t),{retries:3,delay:1e3,backoff:!0}))})}}},[["render",function(e,t,a,o,r,l){const i=z("VehicleSearch"),n=z("ApiErrorBoundary"),s=z("ResultsDisplay"),c=z("ErrorBoundary");return M(),D(c,{"error-title":"Widget Error",fallback:"The widget encountered an error. Please refresh the page to try again.",onError:o.handleWidgetError,onRetry:o.handleWidgetRetry},{default:P(()=>[_("div",{class:O(["finder-v2-widget p-1",o.themeClasses]),"data-iframe-height":"",style:J(o.themeStyles)},[_("div",pa,[A(n,{"component-name":"VehicleSearch","retry-action":o.retryVehicleSearch},{default:P(()=>[A(i,{translation:o.translation},null,8,["translation"])]),_:1},8,["retry-action"])]),o.hasResults||o.loadingResults?(M(),b("div",wa,[A(n,{"component-name":"ResultsDisplay","retry-action":o.retryResultsLoad},{default:P(()=>[A(s,{translation:o.translation},null,8,["translation"])]),_:1},8,["retry-action"])])):E("",!0)],6)]),_:1},8,["onError","onRetry"])}],["__scopeId","data-v-4023d4a7"]]);class Sa{constructor(e={}){var t;this.errors=[],this.maxErrors=e.maxErrors||10,this.isDevelopment=!1,this.errorCallback=e.onError||null,this.errorReportingUrl=e.errorReportingUrl||(null==(t=window.FinderV2Config)?void 0:t.errorReportingUrl),this.enableReporting=!1!==e.enableReporting}handleError(e,t,a){const o=this.createErrorInfo(e,t,a);return this.storeError(o),this.isDevelopment&&this.logDevelopmentError(o),!this.isDevelopment&&this.enableReporting&&this.reportError(o),this.errorCallback&&this.errorCallback(o),this.emitErrorEvent(o),o}createErrorInfo(e,t,a){var o,r;return{message:(null==e?void 0:e.message)||"Unknown error",stack:null==e?void 0:e.stack,component:(null==(o=null==t?void 0:t.$options)?void 0:o.name)||(null==(r=null==t?void 0:t.type)?void 0:r.name)||"Unknown",info:a,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent,url:window.location.href,online:navigator.onLine,viewport:{width:window.innerWidth,height:window.innerHeight},config:this.sanitizeConfig(window.FinderV2Config),type:this.classifyError(e),recoverable:this.isRecoverable(e)}}storeError(e){this.errors.push(e),this.errors.length>this.maxErrors&&this.errors.shift()}logDevelopmentError(e){console.group("🔴 Widget Error"),console.error("Error:",e.message),console.log("Component:",e.component),console.log("Type:",e.type),console.log("Recoverable:",e.recoverable),console.log("Info:",e.info),e.stack&&console.log("Stack:",e.stack),console.log("Full Details:",e),console.groupEnd()}reportError(e){return h(this,null,function*(){var t;if(this.errorReportingUrl)try{const a=yield fetch(this.errorReportingUrl,{method:"POST",headers:{"Content-Type":"application/json","X-Widget-Version":(null==(t=window.FinderV2Config)?void 0:t.version)||"unknown"},body:JSON.stringify(d(c({},e),{stack:this.sanitizeStackTrace(e.stack)}))});a.ok||console.warn("Failed to report error:",a.statusText)}catch(a){console.warn("Error reporting failed:",a)}})}emitErrorEvent(e){window.dispatchEvent(new CustomEvent("finderv2:error",{detail:{message:e.message,component:e.component,type:e.type,recoverable:e.recoverable,timestamp:e.timestamp}}))}classifyError(e){const t=(null==e?void 0:e.message)||"",a=(null==e?void 0:e.name)||"";return t.includes("Network")||t.includes("fetch")?"network":t.includes("API")||(null==e?void 0:e.response)?"api":t.includes("Timeout")||a.includes("Timeout")?"timeout":t.includes("Permission")||t.includes("denied")?"permission":"TypeError"===a?"type":"SyntaxError"===a?"syntax":"ReferenceError"===a?"reference":"unknown"}isRecoverable(e){const t=(null==e?void 0:e.message)||"";return!["Maximum call stack","out of memory","SecurityError","CSP","Infinite"].some(e=>t.includes(e))}sanitizeConfig(e){if(!e)return null;const t=c({},e);return delete t.apiKey,delete t.apiSecret,delete t.token,delete t.password,delete t.credentials,t}sanitizeStackTrace(e){return e?e.replace(/file:\/\/[^\s]+/g,"[local-file]").replace(/https?:\/\/[^\/]+\/api\/[^\s]+/g,"[api-endpoint]").replace(/token=[^\s&]+/g,"token=[redacted]").replace(/key=[^\s&]+/g,"key=[redacted]"):null}clearErrors(){this.errors=[]}getErrors(){return[...this.errors]}getErrorsByType(e){return this.errors.filter(t=>t.type===e)}getStatistics(){const e={total:this.errors.length,byType:{},byComponent:{},recoverable:0,nonRecoverable:0};return this.errors.forEach(t=>{e.byType[t.type]=(e.byType[t.type]||0)+1,e.byComponent[t.component]=(e.byComponent[t.component]||0)+1,t.recoverable?e.recoverable++:e.nonRecoverable++}),e}}new Sa;const ba=q(ka),_a=new Sa({maxErrors:20,enableReporting:!0,onError:e=>{"network"!==e.type||navigator.onLine||console.warn("Widget is offline")}});function Ma(){window.parent&&window.parent!==window&&window.parentIFrame&&window.parentIFrame.size()}ba.config.errorHandler=(e,t,a)=>(_a.handleError(e,t,a),!1),window.addEventListener("unhandledrejection",e=>{var t;_a.handleError(new Error((null==(t=e.reason)?void 0:t.message)||e.reason||"Unhandled Promise Rejection"),null,"unhandledrejection"),e.preventDefault()}),window.FinderV2ErrorHandler=_a,ba.use(W()),document.addEventListener("DOMContentLoaded",()=>{var e,t,a;const o=(null==(e=window.FinderV2Config)?void 0:e.csrfToken)||"";o&&!(null==(t=window.FinderV2Config)?void 0:t.enhancedCSRF)&&(y.defaults.headers.common["X-CSRF-TOKEN"]=o),ba.config.globalProperties.$config=window.FinderV2Config||{};const r=document.getElementById("finder-v2-app");r?(ba.mount(r),(null==(a=window.FinderV2Config)?void 0:a.iframeResize)&&setTimeout(()=>{if(window.parentIFrame){Ma();new MutationObserver(()=>{setTimeout(Ma,50)}).observe(r,{childList:!0,subtree:!0,attributes:!0}),window.addEventListener("resize",Ma)}},100),te()):console.error("Finder-v2 widget container not found")}),window.FinderV2App=ba;
