var e=Object.defineProperty,o=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,l=(o,n,t)=>n in o?e(o,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[n]=t,a=(e,a)=>{for(var i in a||(a={}))n.call(a,i)&&l(e,i,a[i]);if(o)for(var i of o(a))t.call(a,i)&&l(e,i,a[i]);return e},i=(e,o,n)=>new Promise((t,l)=>{var a=e=>{try{r(n.next(e))}catch(o){l(o)}},i=e=>{try{r(n.throw(e))}catch(o){l(o)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(a,i);r((n=n.apply(e,o)).next())});import{q as r,r as s,c as d,w as c,n as u,e as f,a as p,v as g,x as h,y as m,z as w,A as y,B as v,C as b,m as k,F as _,D as x,E as C,G as S,H as M,I as V,J as L,K as T,L as R,M as A,N as F,O as $,P as I,Q as z}from"./vue-core-BvvXclHU.js";import{a as E}from"./http-client-5tgu7n7k.js";import{F as B,A as O,j as D,I as U}from"./ui-components-_gLc8WmR.js";E.defaults.paramsSerializer=e=>{const o=new URLSearchParams;return Object.entries(e).forEach(([e,n])=>{Array.isArray(n)?n.forEach(n=>{null!=n&&""!==n&&o.append(e,n)}):null!=n&&""!==n&&o.append(e,n)}),o.toString()};const H=r("finder",()=>{const e=s({}),o=s(!1),n=s(!1),t=s(null),l=s([]),r=s(""),f=s(!1),p=s(!1),g=s(!1),h=s(!1),m=s(!1),w=s(!1),y=s(!1),v=s(!1),b=s(!1),k=s(!1),_=s(new Map),x=s(""),C=s(""),S=s(""),M=s(""),V=s(""),L=s(""),T=s([]),R=s([]),A=s([]),F=s([]),$=s([]),I=d(()=>e.value.flowType||"primary"),z=d(()=>e.value.apiVersion||"v2"),B=d(()=>e.value.widgetResources||{});function O(e=null,o=null){return i(this,null,function*(){var n;try{f.value=!0,w.value=!1;const t={};e&&(t.make=e),o&&(t.model=o);const l=yield G("year",t);T.value=(null==(n=l.data)?void 0:n.data)||l.data||[],w.value=!0}catch(l){t.value=l.message}finally{f.value=!1}})}function D(e=null){return i(this,null,function*(){var o;try{p.value=!0,y.value=!1;const n=e?{year:e}:{};Object.assign(n,U());const t=yield G("make",n);R.value=(null==(o=t.data)?void 0:o.data)||t.data||[],y.value=!0}catch(n){t.value=n.message}finally{p.value=!1}})}function U(){var o,n,t,l,a;const i=(null==(n=null==(o=e.value)?void 0:o.content)?void 0:n.filter)||(null==(t=e.value)?void 0:t.filter)||{},r=i.by||(null==(a=null==(l=e.value)?void 0:l.content)?void 0:a.by)||"",s=e=>"string"==typeof e?e:(null==e?void 0:e.slug)||(null==e?void 0:e.value)||"",d={};if("brands"===r&&Array.isArray(i.brands)&&i.brands.length){const e=i.brands.map(s).filter(Boolean);e.length&&(d.brands=e.join(","))}else if("brands_exclude"===r&&Array.isArray(i.brands_exclude)&&i.brands_exclude.length){const e=i.brands_exclude.map(s).filter(Boolean);e.length&&(d.brands_exclude=e.join(","))}return d}function H(){var o,n;const t=(null==(n=null==(o=e.value)?void 0:o.content)?void 0:n.regions)||[];return t.length?{region:t}:{}}function G(o){return i(this,arguments,function*(o,n={}){const t=function(e,o){const n=Object.keys(o).sort().reduce((e,n)=>(e[n]=o[n],e),{});return`${e}:${JSON.stringify(n)}`}(o,n);if(_.value.has(t))return yield _.value.get(t);const l=function(o){return i(this,arguments,function*(o,n={}){const t=B.value[o];if(!t||!t[1])throw new Error(`API endpoint not configured: ${o}`);let l=t[1];return l.startsWith("/")&&e.value.baseUrl&&(l=e.value.baseUrl+l),["make","model","year","generation","modification"].includes(o)&&Object.assign(n,H()),yield E.get(l,{params:n})})}(o,n);_.value.set(t,l);try{return yield l}finally{_.value.delete(t)}})}return c(C,(e,o)=>{}),c(S,(e,o)=>{}),c(M,(e,o)=>{}),c(L,(e,o)=>{}),c(V,(e,o)=>{}),{config:e,loading:o,loadingResults:n,error:t,results:l,outputTemplate:r,loadingYears:f,loadingMakes:p,loadingModels:g,loadingGenerations:h,loadingModifications:m,stateLoadedYears:w,stateLoadedMakes:y,stateLoadedModels:v,stateLoadedGenerations:b,stateLoadedModifications:k,selectedYear:C,selectedMake:S,selectedModel:M,selectedModification:V,selectedGeneration:L,years:T,makes:R,models:A,modifications:F,generations:$,flowType:I,apiVersion:z,widgetResources:B,initialize:function(n){var l;console.log("🚀 FinderStore.initialize called with config:",n),console.log("🔍 Config has apiUrl?",!!(null==n?void 0:n.apiUrl),"Value:",null==n?void 0:n.apiUrl),e.value=n,r.value=(null==(l=n.interface)?void 0:l.outputTemplate)||"",n.id||n.uuid||n.widgetUuid,n.search_history,function n(l=0){var a,r;const s=(null==(a=e.value)?void 0:a.apiUrl)||(null==(r=e.value)?void 0:r.baseUrl);e.value&&s?(console.log("Widget configuration ready with API URL:",s,"calling loadInitialData()"),e.value.apiUrl=s,function(){i(this,null,function*(){try{o.value=!0,t.value=null,"primary"===I.value?yield O():yield D()}catch(e){t.value=e.message}finally{o.value=!1}})}()):l<5?setTimeout(()=>n(l+1),100):(console.error("Widget resources failed to load after retries"),console.error("Final config.value:",e.value),console.error("Final widgetResources.value:",B.value),t.value="Failed to initialize widget configuration")}()},loadYears:O,loadMakes:D,loadModels:function(e,o=null){return i(this,null,function*(){var n;try{g.value=!0,v.value=!1;const t={make:e};o&&(t.year=o);const l=yield G("model",t);A.value=(null==(n=l.data)?void 0:n.data)||l.data||[],v.value=!0}catch(l){t.value=l.message}finally{g.value=!1}})},loadModifications:function(e,o,n=null){return i(this,null,function*(){var l;try{m.value=!0,k.value=!1;const t={make:e,model:o};n&&("primary"===I.value||"year_select"===I.value?t.year=n:"alternative"===I.value&&(t.generation=n));const a=yield G("modification",t);F.value=(null==(l=a.data)?void 0:l.data)||a.data||[],k.value=!0}catch(a){t.value=a.message}finally{m.value=!1}})},loadGenerations:function(e,o){return i(this,null,function*(){var n;try{h.value=!0,b.value=!1;const t={make:e,model:o},l=yield G("generation",t);$.value=(null==(n=l.data)?void 0:n.data)||l.data||[],b.value=!0}catch(l){t.value=l.message}finally{h.value=!1}})},searchByVehicle:function(){return i(this,null,function*(){var e;performance.now();try{n.value=!0,t.value=null;const o={make:S.value,model:M.value};"primary"===I.value||"year_select"===I.value?(o.year=C.value,o.modification=V.value):"alternative"===I.value&&(o.generation=L.value,o.modification=V.value);const r=a({},o);"primary"===I.value||"year_select"===I.value?(r.year=C.value,r.modification=V.value):"alternative"===I.value&&(r.generation=L.value,r.modification=V.value);const s=`search_by_model:${JSON.stringify(r)}`;if(x.value===s)return;x.value=s,performance.now();const d=yield G("search_by_model",o);performance.now(),l.value=(null==(e=d.data)?void 0:e.data)||d.data||[],performance.now(),setTimeout(()=>i(this,null,function*(){window.parentIFrame&&window.parentIFrame.size(),yield u()}),100)}catch(o){t.value=o.message}finally{n.value=!1,x.value=""}})},resetVehicleSearch:function(){C.value="",S.value="",M.value="",V.value="",L.value="",A.value=[],F.value=[],$.value=[]},clearResults:function(){l.value=[],setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},50)},buildBrandFilterParams:U,buildRegionParams:H,executeSearchFromHistory:function(e){return i(this,null,function*(){})},getSearchHistory:function(){return null},getAnalytics:()=>null,getCrossDomainTracking:()=>null}});function G(){const e=s(""),o=s(0),n=s("initializing"),t=s(null),l=24e4;let a=null,r=0,c=!1;const u=d(()=>{var e;return!0===(null==(e=window.FinderV2Config)?void 0:e.enhancedCSRF)});function g(){return((window.FinderV2Config||{}).baseUrl||"").replace(/\/$/,"")}function h(){return i(this,null,function*(){var l,a,i;if(c)console.warn("Token refresh already in progress");else if(u.value){c=!0,n.value="refreshing";try{const i=g(),s=(null==(l=window.FinderV2Config)?void 0:l.uuid)||(null==(a=window.FinderV2Config)?void 0:a.id)||"";console.log("Attempting to refresh token from:",`${i}/widget/api/refresh-token/`);const d=yield E.post(`${i}/widget/api/refresh-token/`,{widget_uuid:s,old_token:e.value||void 0},{headers:{"Content-Type":"application/json","X-CSRF-TOKEN":e.value||""},withCredentials:!0});if(!d.data||!d.data.token)throw new Error("Invalid token response");{const l=d.data.token;e.value=l,o.value=0,t.value=Date.now(),n.value="active",r=0,E.defaults.headers.common["X-CSRF-TOKEN"]=l,sessionStorage.setItem("widget_csrf_token",l),sessionStorage.setItem("widget_csrf_timestamp",Date.now().toString()),console.log("CSRF token refreshed successfully")}}catch(s){console.error("Failed to refresh CSRF token:",s),console.error("Error details:",(null==(i=s.response)?void 0:i.data)||s.message),n.value="error";const t=sessionStorage.getItem("widget_csrf_token"),l=sessionStorage.getItem("widget_csrf_timestamp");if(t&&l){const a=Date.now()-parseInt(l);a<36e5&&(e.value=t,o.value=a,E.defaults.headers.common["X-CSRF-TOKEN"]=t,n.value="active",console.log("Recovered CSRF token from session storage"))}r<3?(r++,console.log(`Retrying token refresh in 5000ms (attempt ${r}/3)`),setTimeout(()=>h(),5e3)):(console.error("Max token refresh retries exceeded"),m())}finally{c=!1}}else console.log("Enhanced CSRF not enabled, using legacy token")})}function m(){var o;const t=null==(o=window.FinderV2Config)?void 0:o.csrfToken;t?(console.log("Falling back to legacy CSRF token"),e.value=t,E.defaults.headers.common["X-CSRF-TOKEN"]=t,n.value="active"):(console.error("No fallback CSRF token available"),n.value="error")}return f(()=>{!function(){i(this,null,function*(){var t,i;try{if(u.value)console.log("Enhanced CSRF protection enabled"),console.log("Widget UUID:",(null==(t=window.FinderV2Config)?void 0:t.uuid)||"NOT SET"),console.log("Base URL:",g()||"NOT SET"),yield h(),u.value?(a=setInterval(()=>{o.value+=l,o.value>=33e5&&(console.log("Token age limit reached, refreshing..."),h())},l),console.log("CSRF token auto-refresh started")):console.log("Enhanced CSRF not enabled, skipping auto-refresh");else{console.log("Using legacy CSRF protection");const o=(null==(i=window.FinderV2Config)?void 0:i.csrfToken)||"";e.value=o,E.defaults.headers.common["X-CSRF-TOKEN"]=o,n.value=o?"active":"error",console.log("Legacy token configured:",o?"YES":"NO")}}catch(r){console.error("CSRF token initialization failed:",r),m()}})}()}),p(()=>{a&&(clearInterval(a),a=null,console.log("CSRF token auto-refresh stopped"))}),{csrfToken:e,tokenStatus:n,tokenAge:o,isEnhancedCSRFEnabled:u,refreshToken:function(){return i(this,null,function*(){r=0,yield h()})},getTokenInfo:function(){return{token:e.value,status:n.value,age:o.value,lastRefresh:t.value,enhanced:u.value}}}}const P=(e,o)=>{const n=e.o||e;for(const[t,l]of o)n[t]=l;return n},Y={name:"CustomSelector",components:{Listbox:U,ListboxButton:D,ListboxOptions:O,ListboxOption:B,CheckIcon:function(e,o){return m(),g("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z","clip-rule":"evenodd"})])},ChevronUpDownIcon:function(e,o){return m(),g("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"fill-rule":"evenodd",d:"M10.53 3.47a.75.75 0 0 0-1.06 0L6.22 6.72a.75.75 0 0 0 1.06 1.06L10 5.06l2.72 2.72a.75.75 0 1 0 1.06-1.06l-3.25-3.25Zm-4.31 9.81 3.25 3.25a.75.75 0 0 0 1.06 0l3.25-3.25a.75.75 0 1 0-1.06-1.06L10 14.94l-2.72-2.72a.75.75 0 0 0-1.06 1.06Z","clip-rule":"evenodd"})])}},props:{modelValue:{type:[String,Number],default:""},options:{type:Array,default:()=>[]},placeholder:{type:String,default:"Select option"},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},preloader:{type:Boolean,default:!1},stateLoaded:{type:Boolean,default:!1},autoExpand:{type:Boolean,default:!0},selectorType:{type:String,default:null}},emits:["update:modelValue","change"],setup(e,{emit:o}){const n=s(null),t=d({get:()=>e.modelValue,set:e=>{o("update:modelValue",e),o("change",e)}}),l=d(()=>e.modelValue?e.options.find(o=>(o.slug||o.id)===e.modelValue):null);c(()=>e.options,o=>{e.modelValue&&o.length>0&&(o.some(o=>(o.slug||o.id)===e.modelValue)||(t.value=""))});const a=window.matchMedia("(prefers-reduced-motion: reduce)").matches;c(()=>[e.stateLoaded,e.options.length],([o,t],[l])=>{var s,d;if(!l&&o&&t>0&&!e.disabled&&e.autoExpand&&!a){const e=document.activeElement,o=null!=(d=null==(s=n.value)?void 0:s.$el)?d:n.value;if(e&&e!==document.body&&o&&!o.contains(e)&&["INPUT","TEXTAREA"].includes(e.tagName))return;setTimeout(()=>i(this,null,function*(){var e,o;yield u();const t=null!=(o=null==(e=n.value)?void 0:e.$el)?o:n.value;try{t&&"function"==typeof t.click&&(t.focus({preventScroll:!0}),t.click(),r())}catch(l){}}),200)}},{flush:"post"});const r=()=>{window.parentIFrame&&window.parentIFrame.size&&(window.parentIFrame.size(),setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},350))};return c(()=>e.modelValue,()=>{r()}),f(()=>{var e;n.value&&(null!=(e=n.value.$el)?e:n.value).addEventListener("click",r)}),p(()=>{var e;n.value&&(null!=(e=n.value.$el)?e:n.value).removeEventListener("click",r)}),{selectedValue:t,selectedOption:l,getDisplayText:e=>{if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const o=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?`${e.name}, ${o}`:o}return e.name||""},getDisplayData:e=>{var o,n,t,l,a,i;if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const o=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?{isGeneration:!0,isModification:!1,name:e.name,yearRanges:o}:{isGeneration:!1,isModification:!1,name:o,yearRanges:""}}if(e.engine||e.trim||e.generation){const r=[];let s=e.name||"";if(("primary"===(null==(o=window.FinderV2Config)?void 0:o.flowType)||"primary"===(null==(t=null==(n=window.FinderV2Config)?void 0:n.widgetConfig)?void 0:t.flowType)||"year_select"===(null==(l=window.FinderV2Config)?void 0:l.flowType)||"year_select"===(null==(i=null==(a=window.FinderV2Config)?void 0:a.widgetConfig)?void 0:i.flowType))&&e.generation){const o=e.generation;let n="";if(o.name&&""!==o.name.trim()){const e=`${o.start}-${o.end}`;n=`${o.name} (${e})`}else n=`${o.start}-${o.end}`;n&&r.push(n)}if(e.trim&&""!==e.trim.trim()&&(e.trim,e.name),e.engine){const o=[];if(e.engine.capacity&&o.push(e.engine.capacity),e.engine.type&&o.push(e.engine.type),e.engine.fuel){let n=e.engine.fuel;"Petrol"===e.engine.fuel?n="Petrol":"Diesel"===e.engine.fuel?n="Diesel":"Hybrid"===e.engine.fuel&&(n="Hybrid"),o.push(n)}e.engine.power&&e.engine.power.hp&&o.push(`${e.engine.power.hp}HP`),o.length>0&&r.push(o.join(" "))}return{isGeneration:!1,isModification:!0,name:s,details:r.join(" • ")}}return{isGeneration:!1,isModification:!1,name:e.name||"",yearRanges:""}},buttonRef:n,triggerResize:r}}},j={class:"flex items-start"},N={class:"relative"},W={key:0,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},K={key:1,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},J={class:"truncate"},q={class:"truncate text-ws-secondary-500"},Z={key:1,class:"flex flex-col min-w-0"},X={class:"truncate font-medium"},Q={key:0,class:"truncate text-xs text-ws-secondary-500"},ee={key:2,class:"flex items-center"},oe={class:"truncate"},ne={key:2,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},te={class:"flex"},le={key:1,class:"flex flex-col min-w-0 w-full"},ae={key:2,class:"flex items-center"},ie={class:"min-w-[32px] mt-2 ml-1"},re={key:0,class:"spinner-external"},se={key:0,class:"search-history-container"},de={class:"search-history-header"},ce=["aria-label","aria-expanded","aria-controls"],ue={key:0,class:"search-count-badge"},fe=["id","aria-labelledby"],pe={class:"accordion-content"},ge={key:0,class:"search-list"},he=["onClick","onKeydown","aria-label"],me={class:"search-content"},we={class:"search-description"},ye={class:"search-time"},ve=["onClick","onKeydown","aria-label"],be={key:1,class:"show-more-container"},ke={key:2,class:"show-less-container"},_e={key:3,class:"empty-state"},xe={key:4,class:"clear-all-container"},Ce={class:"confirmation-buttons"},Se={class:"vehicle-search"},Me={key:0,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ve={class:"form-group"},Le={class:"form-label"},Te={class:"form-group"},Re={class:"form-label"},Ae={class:"form-group"},Fe={class:"form-label"},$e={class:"form-group"},Ie={class:"form-label"},ze={key:1,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ee={class:"form-group"},Be={class:"form-label"},Oe={class:"form-group"},De={class:"form-label"},Ue={class:"form-group"},He={class:"form-label"},Ge={class:"form-group"},Pe={class:"form-label"},Ye={key:2,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},je={class:"form-group"},Ne={class:"form-label"},We={class:"form-group"},Ke={class:"form-label"},Je={class:"form-group"},qe={class:"form-label"},Ze={class:"form-group"},Xe={class:"form-label"},Qe={key:0,class:"error-container"},eo={class:"error"},oo=e=>{const o=document.createElement("div");return o.textContent=e,o.innerHTML},no=(e,o)=>o.split(".").reduce((e,o)=>e&&void 0!==e[o]?e[o]:"",e),to=(e,o)=>{let n=e,t=!0;for(;t;){t=!1;const e=/\{\%\s*if\s+(.*?)\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?)(?:\{\%\s*else\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?))?\{\%\s*endif\s*\%\}/g;n=n.replace(e,(e,n,l,a="")=>{t=!0;let i=ao(n,o)?l:a;return i.includes("{% if")&&(i=to(i,o)),i})}return n},lo=(e,o)=>e.replace(/\{\{\s*(.*?)\s*\}\}/g,(e,n)=>{const t=n.trim(),l=t.match(/^(.+?)\s*\?\s*['"]?(.*?)['"]?\s*:\s*['"]?(.*?)['"]?$/);if(l){const[,e,n,t]=l,a=ao(e.trim(),o),i=n.replace(/^['"]|['"]$/g,""),r=t.replace(/^['"]|['"]$/g,"");return oo(a?i:r)}const a=no(o,t);return oo(String(a||""))}),ao=(e,o)=>{try{if(e.startsWith("not ")){const n=e.slice(4).trim();return!ao(n,o)}const n=no(o,e.trim());return Boolean(n)}catch(n){return!1}},io={class:"results-display"},ro={key:0,class:"loading"},so={class:"ml-2"},co={key:1,class:"no-results"},uo={class:"no-results-text"},fo={key:2,class:"results-content"},po={key:0,class:"custom-results"},go=["innerHTML"],ho={key:1,class:"default-results"},mo={class:"text-lg font-semibold mb-2"},wo={class:"font-medium uppercase tracking-wide text-sm mb-1"},yo={class:"grid grid-cols-2 gap-2 text-sm"},vo={key:0},bo={key:2,class:"wheel-size-button-container"},ko=["href"],_o={key:0,style:{background:"#f0f0f0",padding:"10px","margin-bottom":"10px",border:"2px solid #333"}},xo={class:"search-content"},Co={key:1,class:"results-section"},So=P({name:"FinderV2Widget",components:{VehicleSearch:P({name:"VehicleSearch",components:{CustomSelector:P(Y,[["render",function(e,o,n,t,l,a){const i=b("ListboxButton"),r=b("ListboxOption"),s=b("CheckIcon"),d=b("ListboxOptions"),c=b("Listbox");return m(),g("div",j,[w(c,{modelValue:t.selectedValue,"onUpdate:modelValue":o[0]||(o[0]=e=>t.selectedValue=e),disabled:n.disabled,class:"flex-1"},{default:y(()=>[h("div",N,[w(i,{as:"button",class:k(["grid w-full cursor-default grid-cols-1 theme-rounded-md bg-white py-1.5 pr-2 pl-3 text-left text-gray-900 border border-gray-300 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-primary-color focus:border-primary-color sm:text-sm/6",n.disabled?"bg-gray-100 text-gray-500 cursor-not-allowed":""]),ref:"buttonRef"},{default:y(()=>[n.loading?(m(),g("span",W,o[1]||(o[1]=[h("svg",{class:"animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[h("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),h("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),h("span",{class:"truncate"},"Loading...",-1)]))):t.selectedOption?(m(),g("span",K,[t.getDisplayData(t.selectedOption).isGeneration?(m(),g(_,{key:0},[h("span",J,x(t.getDisplayData(t.selectedOption).name),1),h("span",q,x(t.getDisplayData(t.selectedOption).yearRanges),1)],64)):t.getDisplayData(t.selectedOption).isModification?(m(),g("div",Z,[h("span",X,x(t.getDisplayData(t.selectedOption).name),1),t.getDisplayData(t.selectedOption).details?(m(),g("span",Q,x(t.getDisplayData(t.selectedOption).details),1)):v("",!0)])):(m(),g("div",ee,["make"===n.selectorType&&t.selectedOption?(m(),g("i",{key:0,class:k(["d-inline-block icon-make mr-2",`icon-l-${t.selectedOption.slug||t.selectedOption.id}`])},null,2)):v("",!0),h("span",oe,x(t.getDisplayText(t.selectedOption)),1)]))])):(m(),g("span",ne,[h("span",{class:k(["truncate",n.disabled?"text-gray-500":"text-gray-900"])},x(n.placeholder),3)])),o[2]||(o[2]=h("svg",{class:"col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 sm:size-4",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true"},[h("path",{"fill-rule":"evenodd",d:"M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z","clip-rule":"evenodd"})],-1))]),t:1,l:[2]},8,["class"]),w(C,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:y(()=>[w(d,{class:"z-10 mt-1 max-h-60 w-full overflow-auto theme-rounded-md bg-white py-1 text-base ring-1 shadow-lg ring-black/5 focus:outline-hidden sm:text-sm"},{default:y(()=>[n.options.length||n.loading?v("",!0):(m(),S(r,{key:0,value:null,class:"relative cursor-default select-none py-2 pr-9 pl-3 text-gray-500",disabled:""},{default:y(()=>o[3]||(o[3]=[M(" No options available ")])),t:1,l:[3]})),(m(!0),g(_,null,V(n.options,e=>(m(),S(r,{key:e.slug||e.id,value:e.slug||e.id,as:"template"},{default:y(({active:o,selected:l})=>[h("li",{class:k([o?"bg-primary-color text-white outline-hidden":"text-gray-900 hover:bg-gray-50","relative cursor-default select-none py-2 pr-9 pl-3"])},[h("div",te,[t.getDisplayData(e).isGeneration?(m(),g(_,{key:0},[h("span",{class:k([l?"font-semibold":"font-normal","truncate"])},x(t.getDisplayData(e).name),3),h("span",{class:k([o?"text-white/80":"text-ws-secondary-500","ml-2 truncate"])},x(t.getDisplayData(e).yearRanges),3)],64)):t.getDisplayData(e).isModification?(m(),g("div",le,[h("span",{class:k([l?"font-semibold":"font-medium","truncate"])},x(t.getDisplayData(e).name),3),t.getDisplayData(e).details?(m(),g("span",{key:0,class:k([o?"text-white/80":"text-ws-secondary-500","truncate text-xs"])},x(t.getDisplayData(e).details),3)):v("",!0)])):(m(),g("div",ae,["make"===n.selectorType?(m(),g("i",{key:0,class:k(["d-inline-block icon-make mr-2",`icon-l-${e.slug||e.id}`])},null,2)):v("",!0),h("span",{class:k([l?"font-semibold":"font-normal","truncate"])},x(t.getDisplayText(e)),3)]))]),l?(m(),g("span",{key:0,class:k([o?"text-white":"text-primary-color","absolute inset-y-0 right-0 flex items-center pr-4"])},[w(s,{class:"size-5","aria-hidden":"true"})],2)):v("",!0)],2)]),t:2},1032,["value"]))),128))]),t:1})]),t:1})])]),t:1},8,["modelValue","disabled"]),h("div",ie,[n.preloader?(m(),g("div",re,o[4]||(o[4]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"w-5 h-5 text-gray-400"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)]))):v("",!0)])])}],["__scopeId","data-v-3d856925"]]),SearchHistoryIcon:P({name:"SearchHistoryIcon",setup(){const e=H(),{config:o}=L(e),n=s(!1),t=s(!1),l=s(!1),a=s(null),r=`search-history-accordion-${Math.random().toString(36).substr(2,9)}`,u=`search-history-button-${Math.random().toString(36).substr(2,9)}`;function p(){const o=e.getSearchHistory();a.value=o}f(()=>{p()}),c(()=>o.value,()=>{p()},{deep:!0}),c(()=>e.getSearchHistory(),e=>{e&&e!==a.value&&(a.value=e)});const g=e=>F(e)?e.value:e,h=d(()=>{var e,o;const n=null==(e=a.value)?void 0:e.isEnabled;return null!=(o=g(n))&&o}),m=d(()=>{const e=a.value;if(!e)return[];const o=e.searches;return g(o)||[]});c(m,(e,o)=>{},{deep:!0});const w=d(()=>{var e,o;const n=null==(e=a.value)?void 0:e.displaySearches;return null!=(o=g(n))?o:[]}),y=d(()=>{var e,o;const n=null==(e=a.value)?void 0:e.hasMoreSearches;return null!=(o=g(n))&&o}),v=d(()=>m.value.length),b=d(()=>{var e,n;return!1!==(null==(n=null==(e=o.value)?void 0:e.search_history)?void 0:n.enabled)||h.value}),k=d(()=>t.value?m.value:w.value),_=d(()=>Math.max(0,m.value.length-w.value.length));function x(){window.parentIFrame&&window.parentIFrame.size&&(window.parentIFrame.size(),setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},350))}function C(){n.value=!1,t.value=!1,x()}function S(){l.value=!1}function M(e){"Escape"===e.key&&(l.value?S():n.value&&C(),e.preventDefault())}return f(()=>{document.addEventListener("keydown",M)}),T(()=>{document.removeEventListener("keydown",M)}),{isAccordionOpen:n,showAll:t,showClearConfirmation:l,shouldShow:b,allSearches:m,displaySearches:w,hasMoreSearches:y,visibleSearches:k,remainingCount:_,searchCount:v,accordionId:r,buttonId:u,toggleAccordion:function(){n.value=!n.value,n.value&&(t.value=!1),x()},closeAccordion:C,executeSearch:function(o){return i(this,null,function*(){try{C(),yield e.executeSearchFromHistory(o),a.value=e.getSearchHistory(),setTimeout(()=>{const e=document.querySelector(".results-container");e&&e.scrollIntoView({behavior:"smooth",block:"start"})},100)}catch(n){console.error("Failed to execute search from history:",n)}})},removeSearch:function(e){a.value&&(a.value.removeSearch(e),0===m.value.length&&C())},confirmClearAll:function(){l.value=!0},clearAllHistory:function(){a.value&&(a.value.clearHistory(),l.value=!1,C())},cancelClearAll:S,getRelativeTime:function(e){var o;return(null==(o=a.value)?void 0:o.getRelativeTime(e))||""}}}},[["render",function(e,o,n,t,l,a){return t.shouldShow?(m(),g("div",se,[h("div",de,[h("button",{onClick:o[0]||(o[0]=(...e)=>t.toggleAccordion&&t.toggleAccordion(...e)),"aria-label":`Search History (${t.searchCount} searches)`,"aria-expanded":t.isAccordionOpen,"aria-controls":t.accordionId,class:k(["history-toggle-button",{"has-history":t.searchCount>0,expanded:t.isAccordionOpen}])},[o[18]||(o[18]=h("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",class:"history-icon"},[h("path",{d:"M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3Z"})],-1)),t.searchCount>0?(m(),g("span",ue,x(t.searchCount),1)):v("",!0)],10,ce)]),w(C,{name:"accordion"},{default:y(()=>[t.isAccordionOpen?(m(),g("div",{key:0,id:t.accordionId,class:"search-history-accordion",role:"region","aria-labelledby":t.buttonId},[h("div",pe,[t.allSearches.length>0?(m(),g("div",ge,[(m(!0),g(_,null,V(t.visibleSearches,e=>(m(),g("div",{key:e.id,class:"search-item",onClick:o=>t.executeSearch(e.id),onKeydown:[R(o=>t.executeSearch(e.id),["enter"]),R(A(o=>t.executeSearch(e.id),["prevent"]),["space"])],tabindex:"0",role:"button","aria-label":`Execute search for ${e.description}`},[h("div",me,[h("div",we,x(e.description),1),h("div",ye,x(t.getRelativeTime(e.timestamp)),1)]),h("button",{class:"remove-button",onClick:A(o=>t.removeSearch(e.id),["stop"]),onKeydown:[R(A(o=>t.removeSearch(e.id),["stop"]),["enter"]),R(A(o=>t.removeSearch(e.id),["stop","prevent"]),["space"])],"aria-label":`Remove search for ${e.description}`,title:"Remove this search"}," ✕ ",40,ve)],40,he))),128))])):v("",!0),t.hasMoreSearches&&!t.showAll?(m(),g("div",be,[h("button",{class:"show-more-button",onClick:o[1]||(o[1]=e=>t.showAll=!0),onKeydown:[o[2]||(o[2]=R(e=>t.showAll=!0,["enter"])),o[3]||(o[3]=R(A(e=>t.showAll=!0,["prevent"]),["space"]))],"aria-label":"Show more search history"}," Show More ("+x(t.remainingCount)+" more) ",33)])):v("",!0),t.showAll&&t.hasMoreSearches?(m(),g("div",ke,[h("button",{class:"show-less-button",onClick:o[4]||(o[4]=e=>t.showAll=!1),onKeydown:[o[5]||(o[5]=R(e=>t.showAll=!1,["enter"])),o[6]||(o[6]=R(A(e=>t.showAll=!1,["prevent"]),["space"]))],"aria-label":"Show less search history"}," Show Less ",32)])):v("",!0),0===t.allSearches.length?(m(),g("div",_e,o[19]||(o[19]=[h("div",{class:"empty-icon"},"🔍",-1),h("p",{class:"empty-message"},"No search history yet",-1),h("p",{class:"empty-hint"},"Your recent searches will appear here",-1)]))):v("",!0),t.allSearches.length>0?(m(),g("div",xe,[h("button",{class:"clear-all-button",onClick:o[7]||(o[7]=(...e)=>t.confirmClearAll&&t.confirmClearAll(...e)),onKeydown:[o[8]||(o[8]=R((...e)=>t.confirmClearAll&&t.confirmClearAll(...e),["enter"])),o[9]||(o[9]=R(A((...e)=>t.confirmClearAll&&t.confirmClearAll(...e),["prevent"]),["space"]))],"aria-label":"Clear all search history"}," Clear All History ",32)])):v("",!0)])],8,fe)):v("",!0)]),t:1}),w(C,{name:"modal"},{default:y(()=>[t.showClearConfirmation?(m(),g("div",{key:0,class:"modal-overlay",onClick:o[17]||(o[17]=(...e)=>t.cancelClearAll&&t.cancelClearAll(...e))},[h("div",{class:"confirmation-modal",onClick:o[16]||(o[16]=A(()=>{},["stop"]))},[o[20]||(o[20]=h("h4",{class:"confirmation-title"},"Clear Search History",-1)),o[21]||(o[21]=h("p",{class:"confirmation-message"}," Are you sure you want to clear all your search history? This action cannot be undone. ",-1)),h("div",Ce,[h("button",{class:"confirm-button",onClick:o[10]||(o[10]=(...e)=>t.clearAllHistory&&t.clearAllHistory(...e)),onKeydown:[o[11]||(o[11]=R((...e)=>t.clearAllHistory&&t.clearAllHistory(...e),["enter"])),o[12]||(o[12]=R(A((...e)=>t.clearAllHistory&&t.clearAllHistory(...e),["prevent"]),["space"]))]}," Clear All ",32),h("button",{class:"cancel-button",onClick:o[13]||(o[13]=(...e)=>t.cancelClearAll&&t.cancelClearAll(...e)),onKeydown:[o[14]||(o[14]=R((...e)=>t.cancelClearAll&&t.cancelClearAll(...e),["enter"])),o[15]||(o[15]=R(A((...e)=>t.cancelClearAll&&t.cancelClearAll(...e),["prevent"]),["space"]))]}," Cancel ",32)])])])):v("",!0)]),t:1})])):v("",!0)}],["__scopeId","data-v-f52afd93"]])},props:{translation:{type:Object,default:()=>({})}},setup(){const e=H(),o=e.getAnalytics(),{loading:n,error:t,selectedYear:l,selectedMake:a,selectedModel:r,selectedModification:u,selectedGeneration:f,years:p,makes:g,models:h,modifications:m,generations:w,flowType:y,loadingYears:v,loadingMakes:b,loadingModels:k,loadingGenerations:_,loadingModifications:x,stateLoadedYears:C,stateLoadedMakes:S,stateLoadedModels:M,stateLoadedGenerations:V,stateLoadedModifications:T}=L(e),R=d(()=>"primary"===y.value?l.value&&a.value&&r.value&&u.value:"alternative"===y.value?a.value&&r.value&&f.value&&u.value:"year_select"===y.value&&a.value&&r.value&&l.value&&u.value),A=s(!1);function F(){return i(this,null,function*(){R.value&&(yield e.searchByVehicle())})}return c(R,(e,o)=>i(this,null,function*(){e&&!o&&(A.value=!0,yield F(),setTimeout(()=>{A.value=!1},100))})),c(u,(e,t)=>i(this,null,function*(){o&&e&&e!==t&&o.trackInteraction("modification_selected",{selected_modification:e,selected_make:a.value,selected_model:r.value,selected_year:l.value,selected_generation:f.value,flow_step:4,flow_type:y.value}),!e||e===t||n.value||A.value||(yield F())})),{loading:n,error:t,selectedYear:l,selectedMake:a,selectedModel:r,selectedModification:u,selectedGeneration:f,years:p,makes:g,models:h,modifications:m,generations:w,flowType:y,loadingYears:v,loadingMakes:b,loadingModels:k,loadingGenerations:_,loadingModifications:x,stateLoadedYears:C,stateLoadedMakes:S,stateLoadedModels:M,stateLoadedGenerations:V,stateLoadedModifications:T,canSearch:R,onYearChange:function(){return i(this,null,function*(){o&&l.value&&o.trackInteraction("year_selected",{selected_year:l.value,flow_step:"primary"===y.value?1:3,flow_type:y.value}),"primary"===y.value?(e.selectedMake="",e.selectedModel="",e.selectedModification="",e.models=[],e.modifications=[],e.stateLoadedMakes=!1,e.stateLoadedModels=!1,e.stateLoadedModifications=!1,l.value&&(yield e.loadMakes(l.value))):"year_select"===y.value&&(e.selectedModification="",e.modifications=[],e.stateLoadedModifications=!1,l.value&&(yield e.loadModifications(a.value,r.value,l.value)))})},onMakeChange:function(){return i(this,null,function*(){o&&a.value&&o.trackInteraction("make_selected",{selected_make:a.value,flow_step:"primary"===y.value?2:1,flow_type:y.value}),e.selectedModel="",e.selectedModification="",e.selectedGeneration="",e.models=[],e.modifications=[],e.generations=[],e.stateLoadedModels=!1,e.stateLoadedModifications=!1,e.stateLoadedGenerations=!1,a.value&&("primary"===y.value?yield e.loadModels(a.value,l.value):"alternative"!==y.value&&"year_select"!==y.value||(yield e.loadModels(a.value)))})},onModelChange:function(){return i(this,null,function*(){o&&r.value&&o.trackInteraction("model_selected",{selected_model:r.value,selected_make:a.value,flow_step:"primary"===y.value?3:2,flow_type:y.value}),e.selectedModification="",e.selectedGeneration="",e.modifications=[],e.generations=[],e.stateLoadedModifications=!1,e.stateLoadedGenerations=!1,"year_select"===y.value&&(e.selectedYear="",e.years=[],e.stateLoadedYears=!1),r.value&&("primary"===y.value?yield e.loadModifications(a.value,r.value,l.value):"alternative"===y.value?yield e.loadGenerations(a.value,r.value):"year_select"===y.value&&(yield e.loadYears(a.value,r.value)))})},onGenerationChange:function(){return i(this,null,function*(){o&&f.value&&o.trackInteraction("generation_selected",{selected_generation:f.value,selected_make:a.value,selected_model:r.value,flow_step:3,flow_type:y.value}),e.selectedModification="",e.modifications=[],e.stateLoadedModifications=!1,f.value&&(yield e.loadModifications(a.value,r.value,f.value))})},handleSearch:F}}},[["render",function(e,o,n,t,l,a){const i=b("SearchHistoryIcon"),r=b("CustomSelector");return m(),g("div",Se,[w(i),h("form",{onSubmit:o[12]||(o[12]=A((...e)=>t.handleSearch&&t.handleSearch(...e),["prevent"])),class:"search-form"},["primary"===t.flowType?(m(),g("div",Me,[h("div",Ve,[h("label",Le,x(n.translation.year_label||"Year"),1),w(r,{modelValue:t.selectedYear,"onUpdate:modelValue":o[0]||(o[0]=e=>t.selectedYear=e),options:t.years,loading:t.loading&&!t.years.length,preloader:t.loadingYears,"state-loaded":t.stateLoadedYears,"auto-expand":!1,disabled:t.loadingYears&&0===t.years.length,placeholder:n.translation.select_year||"Select Year",onChange:t.onYearChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),h("div",Te,[h("label",Re,x(n.translation.make_label||"Make"),1),w(r,{modelValue:t.selectedMake,"onUpdate:modelValue":o[1]||(o[1]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&t.selectedYear&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,disabled:!t.selectedYear||t.loadingMakes||!t.stateLoadedMakes,placeholder:n.translation.select_make||"Select Make","selector-type":"make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),h("div",Ae,[h("label",Fe,x(n.translation.model_label||"Model"),1),w(r,{modelValue:t.selectedModel,"onUpdate:modelValue":o[2]||(o[2]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake||t.loadingModels||!t.stateLoadedModels,placeholder:n.translation.select_model||"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),h("div",$e,[h("label",Ie,x(n.translation.modification_label||"Modification"),1),w(r,{modelValue:t.selectedModification,"onUpdate:modelValue":o[3]||(o[3]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedModel&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedModel||t.loadingModifications||!t.stateLoadedModifications,placeholder:n.translation.select_modification||"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder"])])])):"alternative"===t.flowType?(m(),g("div",ze,[h("div",Ee,[h("label",Be,x(n.translation.make_label||"Make"),1),w(r,{modelValue:t.selectedMake,"onUpdate:modelValue":o[4]||(o[4]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,"auto-expand":!1,disabled:t.loadingMakes&&0===t.makes.length,placeholder:n.translation.select_make||"Select Make","selector-type":"make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),h("div",Oe,[h("label",De,x(n.translation.model_label||"Model"),1),w(r,{modelValue:t.selectedModel,"onUpdate:modelValue":o[5]||(o[5]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake||t.loadingModels||!t.stateLoadedModels,placeholder:n.translation.select_model||"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),h("div",Ue,[h("label",He,x(n.translation.generation_label||"Generation"),1),w(r,{modelValue:t.selectedGeneration,"onUpdate:modelValue":o[6]||(o[6]=e=>t.selectedGeneration=e),options:t.generations,loading:t.loading&&t.selectedModel&&!t.generations.length,preloader:t.loadingGenerations,"state-loaded":t.stateLoadedGenerations,disabled:!t.selectedModel||t.loadingGenerations||!t.stateLoadedGenerations,placeholder:n.translation.select_generation||"Select Generation",onChange:t.onGenerationChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),h("div",Ge,[h("label",Pe,x(n.translation.modification_label||"Modification"),1),w(r,{modelValue:t.selectedModification,"onUpdate:modelValue":o[7]||(o[7]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedGeneration&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedGeneration||t.loadingModifications||!t.stateLoadedModifications,placeholder:n.translation.select_modification||"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder"])])])):"year_select"===t.flowType?(m(),g("div",Ye,[h("div",je,[h("label",Ne,x(n.translation.make_label||"Make"),1),w(r,{modelValue:t.selectedMake,"onUpdate:modelValue":o[8]||(o[8]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,"auto-expand":!1,disabled:t.loadingMakes&&0===t.makes.length,placeholder:n.translation.select_make||"Select Make","selector-type":"make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),h("div",We,[h("label",Ke,x(n.translation.model_label||"Model"),1),w(r,{modelValue:t.selectedModel,"onUpdate:modelValue":o[9]||(o[9]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake||t.loadingModels||!t.stateLoadedModels,placeholder:n.translation.select_model||"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),h("div",Je,[h("label",qe,x(n.translation.year_label||"Year"),1),w(r,{modelValue:t.selectedYear,"onUpdate:modelValue":o[10]||(o[10]=e=>t.selectedYear=e),options:t.years,loading:t.loading&&t.selectedModel&&!t.years.length,preloader:t.loadingYears,"state-loaded":t.stateLoadedYears,disabled:!t.selectedModel||t.loadingYears||!t.stateLoadedYears,placeholder:n.translation.select_year||"Select Year",onChange:t.onYearChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder","onChange"])]),h("div",Ze,[h("label",Xe,x(n.translation.modification_label||"Modification"),1),w(r,{modelValue:t.selectedModification,"onUpdate:modelValue":o[11]||(o[11]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedYear&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedYear||t.loadingModifications||!t.stateLoadedModifications,placeholder:n.translation.select_modification||"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","placeholder"])])])):v("",!0)],32),t.error?(m(),g("div",Qe,[h("p",eo,x(t.error),1)])):v("",!0)])}],["__scopeId","data-v-72803944"]]),ResultsDisplay:P({name:"ResultsDisplay",props:{translation:{type:Object,default:()=>({})}},setup(e){const o=H(),{loadingResults:n,results:t,config:l,outputTemplate:i}=L(o),r=d(()=>i.value&&i.value.trim().length>0),s=d(()=>{var e;return!(!l.value.subscriptionPaid&&!(null==(e=l.value.widgetConfig)?void 0:e.subscriptionPaid))}),c=d(()=>{var e,o,n,t,a,i;const r=null==(n=null==(o=null==(e=l.value.interface)?void 0:e.blocks)?void 0:o.button_to_ws)?void 0:n.hide,s=null==(i=null==(a=null==(t=l.value.widgetConfig)?void 0:t.blocks)?void 0:a.button_to_ws)?void 0:i.hide;return void 0!==r?r:!!s}),u=d(()=>!s.value&&!c.value),f=d(()=>{var e;return`https://www.wheel-size.com${l.value.utm||(null==(e=l.value.widgetConfig)?void 0:e.utm)||""}`});return{loadingResults:n,results:t,hasCustomTemplate:r,renderCustomTemplate:e=>r.value?((e,o)=>{try{let n=e;return n=((e,o)=>e.replace(/\{\%\s*for\s+(\w+)\s+in\s+(\w+)\s*\%\}([\s\S]*?)\{\%\s*endfor\s*\%\}/g,(e,n,t,l)=>{const i=o[t];return Array.isArray(i)?i.map(e=>{let t=l;const i=a({[n]:e},o);return t=to(t,i),t=lo(t,i),t}).join(""):""}))(n,o),n=to(n,o),n=lo(n,o),n}catch(n){return`<div class="text-red-500 text-sm">Template error: ${n.message}</div>`}})(i.value,e):"",showWheelSizeButton:u,wheelSizeUrl:f,translation:e.translation}}},[["render",function(e,o,n,t,l,a){return m(),g("div",io,[t.loadingResults?(m(),g("div",ro,[o[0]||(o[0]=h("svg",{class:"spinner",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[h("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),h("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),h("span",so,x(t.translation.loading_results||"Loading results..."),1)])):0===t.results.length?(m(),g("div",co,[h("p",uo,x(t.translation.no_results||"No results found. Please try different search criteria."),1)])):(m(),g("div",fo,[t.hasCustomTemplate?(m(),g("div",po,[(m(!0),g(_,null,V(t.results,(e,o)=>(m(),g("div",{key:o,innerHTML:t.renderCustomTemplate(e)},null,8,go))),128))])):(m(),g("div",ho,[(m(!0),g(_,null,V(t.results,(e,n)=>(m(),g("div",{class:"space-y-4",key:n},[h("h3",mo,x(e.make.name)+" "+x(e.model.name)+" ("+x(e.start_year)+"-"+x(e.end_year)+") ",1),(m(!0),g(_,null,V(e.wheels,(e,n)=>(m(),g("div",{key:n,class:k(["p-3 theme-rounded-md border",e.is_stock?"border-indigo-400 bg-indigo-50":"border-gray-300"])},[h("div",wo,x(e.is_stock?"OE option1":"After-market option1"),1),h("div",yo,[h("div",null,[o[1]||(o[1]=h("span",{class:"text-gray-500"},"Front:",-1)),M(" "+x(e.front.tire)+" – "+x(e.front.rim),1)]),!e.showing_fp_only&&e.rear.tire?(m(),g("div",vo,[o[2]||(o[2]=h("span",{class:"text-gray-500"},"Rear:",-1)),M(" "+x(e.rear.tire)+" – "+x(e.rear.rim),1)])):v("",!0)])],2))),128))]))),128))])),t.showWheelSizeButton?(m(),g("div",bo,[h("a",{href:t.wheelSizeUrl,target:"_blank",rel:"noopener noreferrer",class:"inline-block rounded-md bg-white px-3.5 py-2 text-sm font-semibold text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50"},x(t.translation.search_button||"Unlock More Insights at Wheel-Size.com"),9,ko)])):v("",!0)]))])}],["__scopeId","data-v-f9660a5a"]])},setup(){const e=H(),{loadingResults:o,results:n,loading:t,error:l}=L(e),a=d(()=>!!e.config),{csrfToken:r,tokenStatus:c,isEnhancedCSRFEnabled:u}=G(),{initialize:p,fingerprintCollected:g}=function(){const e=s(!1),o=s(""),n=s({}),t=s(!1);function a(){var e,o,l,a,i,s,u,f,p;const g={screen_width:(null==(e=window.screen)?void 0:e.width)||0,screen_height:(null==(o=window.screen)?void 0:o.height)||0,screen_available_width:(null==(l=window.screen)?void 0:l.availWidth)||0,screen_available_height:(null==(a=window.screen)?void 0:a.availHeight)||0,color_depth:(null==(i=window.screen)?void 0:i.colorDepth)||0,pixel_depth:(null==(s=window.screen)?void 0:s.pixelDepth)||0,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timezone_offset:(new Date).getTimezoneOffset(),language:navigator.language||"",languages:(null==(u=navigator.languages)?void 0:u.join(","))||"",platform:navigator.platform||"",user_agent:navigator.userAgent||"",cookies_enabled:navigator.cookieEnabled,online:navigator.onLine,do_not_track:navigator.doNotTrack||"unspecified",hardware_concurrency:navigator.hardwareConcurrency||0,max_touch_points:navigator.maxTouchPoints||0,plugins_count:(null==(f=navigator.plugins)?void 0:f.length)||0,canvas_hash:r(),webgl_vendor:d(),webgl_renderer:c(),touch_support:"ontouchstart"in window||navigator.maxTouchPoints>0,media_devices:!!navigator.mediaDevices,webrtc_enabled:!!window.RTCPeerConnection,websocket_enabled:!!window.WebSocket,session_storage:!!window.sessionStorage,local_storage:!!window.localStorage,indexed_db:!!window.indexedDB,connection_type:(null==(p=navigator.connection)?void 0:p.effectiveType)||"unknown",window_outer_width:window.outerWidth,window_outer_height:window.outerHeight,window_inner_width:window.innerWidth,window_inner_height:window.innerHeight,timestamp:Date.now()};return n.value=g,t.value=!0,g}function r(){try{const e=document.createElement("canvas"),o=e.getContext("2d");return o?(e.width=200,e.height=50,o.textBaseline="alphabetic",o.fillStyle="#f60",o.fillRect(125,1,62,20),o.fillStyle="#069",o.font="11pt Arial",o.fillText("Canvas fingerprint 🛡️",2,15),o.fillStyle="rgba(102, 204, 0, 0.7)",o.font="18pt Arial",o.fillText("Widget Protection",4,45),function(e){let o=0;if(0===e.length)return"0";for(let n=0;n<e.length;n++)o=(o<<5)-o+e.charCodeAt(n),o&=o;return Math.abs(o).toString(16)}(e.toDataURL()).substring(0,32)):"canvas_not_available"}catch(e){return console.debug("Canvas fingerprinting blocked:",e.message),"canvas_blocked"}}function d(){try{const e=document.createElement("canvas"),o=e.getContext("webgl")||e.getContext("experimental-webgl");if(!o)return"webgl_not_supported";const n=o.getExtension("WEBGL_debug_renderer_info");return n?o.getParameter(n.UNMASKED_VENDOR_WEBGL)||"unknown":"debug_info_not_available"}catch(e){return console.debug("WebGL vendor detection failed:",e.message),"webgl_blocked"}}function c(){try{const e=document.createElement("canvas"),o=e.getContext("webgl")||e.getContext("experimental-webgl");if(!o)return"webgl_not_supported";const n=o.getExtension("WEBGL_debug_renderer_info");return n?o.getParameter(n.UNMASKED_RENDERER_WEBGL)||"unknown":"debug_info_not_available"}catch(e){return console.debug("WebGL renderer detection failed:",e.message),"webgl_blocked"}}function u(e=4){return i(this,null,function*(){if(!window.crypto||!window.crypto.subtle)throw console.warn("crypto.subtle not available - cannot solve real challenge"),new Error("JavaScript cryptography (crypto.subtle) is required but not available. Please use a modern browser.");const o=Math.random().toString(36).substring(2,15);let n=0;const t=Date.now(),l="0".repeat(e);for(console.log(`Solving challenge with difficulty ${e}...`);;){const i=`${o}:${n}`;let r;try{const e=(new TextEncoder).encode(i),o=yield crypto.subtle.digest("SHA-256",e);r=Array.from(new Uint8Array(o)).map(e=>e.toString(16).padStart(2,"0")).join("")}catch(a){throw new Error("Failed to compute hash: "+a.message)}if(r.startsWith(l)){const l=Date.now()-t;return console.log(`Challenge solved in ${l}ms with nonce ${n}`),{challenge:o,nonce:n,hash:r,duration:l,difficulty:e}}if(n++,n%100==0&&(yield new Promise(e=>setTimeout(e,0))),Date.now()-t>1e4)throw new Error("Challenge timeout - took too long to solve")}})}function p(){return i(this,null,function*(){try{const e=n.value;return JSON.stringify(e),E.defaults.headers.common["X-Client-Features"]=JSON.stringify(e),console.log("Browser fingerprint sent to server"),!0}catch(l){return console.error("Failed to send fingerprint:",l),!1}})}function g(){return i(this,null,function*(){try{console.log("Initializing bot protection...");const e=a();return console.log("Browser features collected:",Object.keys(e).length,"features"),yield p(),console.log("Bot protection initialized successfully"),!0}catch(l){return console.error("Bot protection initialization failed:",l),!1}})}function h(){return i(this,null,function*(){var e,o;try{return(yield E.post("/widget/api/request-challenge/",{widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(o=window.FinderV2Config)?void 0:o.id)})).data}catch(l){throw console.error("Failed to request challenge:",l),l}})}return f(()=>{g().catch(e=>{console.warn("Bot protection auto-init failed:",e)})}),{challengeSolved:e,challengeToken:o,browserFeatures:n,fingerprintCollected:t,initialize:g,collectBrowserFeatures:a,sendFingerprintToServer:p,solveChallenge:u,verifyHuman:function(){return i(this,null,function*(){var n,t;try{const l=a(),i=yield u(4),r=yield E.post("/widget/api/verify-human/",{features:l,solution:i,widget_uuid:(null==(n=window.FinderV2Config)?void 0:n.uuid)||(null==(t=window.FinderV2Config)?void 0:t.id)});return!!r.data.token&&(o.value=r.data.token,e.value=!0,E.defaults.headers.common["X-Challenge-Token"]=o.value,console.log("Human verification successful"),!0)}catch(l){return console.error("Human verification failed:",l),!1}})},requestChallenge:h,solveAndVerifyChallenge:function(){return i(this,null,function*(){var n,t;try{if(!window.crypto||!window.crypto.subtle){if(console.warn("crypto.subtle not available - likely due to insecure context (HTTP)"),"http:"===window.location.protocol)return console.warn("Running in HTTP mode - skipping challenge verification for development"),e.value=!0,o.value="development-bypass",E.defaults.headers.common["X-Challenge-Token"]="development-bypass",!0;throw new Error("Security features are not available. This may be due to browser settings or an outdated browser. Please ensure JavaScript is enabled and try using Chrome, Firefox, Safari, or Edge.")}console.log("Requesting challenge for search/by_model endpoint...");const l=yield h();console.log(`Solving challenge with difficulty ${l.difficulty}...`);const a=yield u(l.difficulty);a.challenge=l.challenge;const i=yield E.post("/widget/api/verify-challenge/",{solution:a,widget_uuid:(null==(n=window.FinderV2Config)?void 0:n.uuid)||(null==(t=window.FinderV2Config)?void 0:t.id)});return!!i.data.success&&(o.value=i.data.token,e.value=!0,E.defaults.headers.common["X-Challenge-Token"]=o.value,console.log("Challenge solved and verified successfully"),console.log(`Token valid for ${i.data.max_uses} uses`),sessionStorage.setItem("challenge_token",o.value),sessionStorage.setItem("challenge_token_time",Date.now()),!0)}catch(l){if(console.error("Challenge verification failed:",l.message),l.message.includes("crypto"))throw new Error("Your browser does not support required security features. Please use Chrome, Firefox, Safari, or Edge.");throw l}})},hasValidChallengeToken:function(){if(o.value&&e.value)return!0;const n=sessionStorage.getItem("challenge_token"),t=sessionStorage.getItem("challenge_token_time");return!!(n&&t&&Date.now()-parseInt(t)<36e5)&&(o.value=n,e.value=!0,E.defaults.headers.common["X-Challenge-Token"]=n,!0)},generateCanvasHash:r,getWebGLVendor:d,getWebGLRenderer:c}}(),h=d(()=>{const e=window.FinderV2Config||{};return console.log("Config data computed:",e),e}),m=d(()=>h.value.theme||{}),w=d(()=>n.value.length>0),y=d(()=>{var e;const o=[];return m.value.name&&o.push(`theme-${m.value.name.toLowerCase().replace(/\s+/g,"-")}`),(null==(e=m.value.effects)?void 0:e.hoverEffect)&&o.push(`hover-${m.value.effects.hoverEffect}`),o.join(" ")}),v=d(()=>{const e={};if(m.value.colors&&(e["--theme-primary"]=m.value.colors.primary,e["--theme-secondary"]=m.value.colors.secondary,e["--theme-accent"]=m.value.colors.accent,e["--theme-background"]=m.value.colors.background,e["--theme-text"]=m.value.colors.text,e["--theme-primary-rgb"]=b(m.value.colors.primary),e["--theme-secondary-rgb"]=b(m.value.colors.secondary),e["--theme-accent-rgb"]=b(m.value.colors.accent)),m.value.typography&&(e["--theme-font-family"]=m.value.typography.fontFamily,e["--theme-font-size"]=m.value.typography.fontSize,e["--theme-font-weight"]=m.value.typography.fontWeight,e["--theme-line-height"]=m.value.typography.lineHeight,e["--theme-letter-spacing"]=m.value.typography.letterSpacing),m.value.spacing&&(e["--theme-padding"]=m.value.spacing.padding,e["--theme-margin"]=m.value.spacing.margin),m.value.effects){e["--theme-border-radius"]=m.value.effects.borderRadius,e["--theme-border-width"]=m.value.effects.borderWidth,e["--theme-animation-speed"]=m.value.effects.animationSpeed;const o=m.value.effects.shadowIntensity,n={none:"none",light:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",medium:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",heavy:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"};e["--theme-shadow"]=n[o]||n.medium}return e});function b(e){if(!e)return"0, 0, 0";const o=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return o?`${parseInt(o[1],16)}, ${parseInt(o[2],16)}, ${parseInt(o[3],16)}`:"0, 0, 0"}const k=d(()=>h.value.translation||{year_label:"Year",make_label:"Make",model_label:"Model",generation_label:"Generation",modification_label:"Modification",select_year:"Select Year",select_make:"Select Make",select_model:"Select Model",select_generation:"Select Generation",select_modification:"Select Modification",loading:"Loading...",loading_results:"Loading results...",no_results:"No results found. Please try different search criteria.",search_button:"Unlock More Insights at Wheel-Size.com"});f(()=>i(this,null,function*(){if(window.FinderV2Debug=window.FinderV2Debug||{},window.FinderV2Debug.componentMounted=!0,window.FinderV2Debug.configAtMount=JSON.parse(JSON.stringify(window.FinderV2Config||{})),p().then(()=>{console.log("Bot protection initialized, fingerprint collected:",g.value)}).catch(e=>{console.warn("Bot protection init failed (non-blocking):",e)}),u.value){console.log("Waiting for enhanced CSRF token...");let e=0;for(;"active"!==c.value&&e<20;)yield new Promise(e=>setTimeout(e,100)),e++;"active"===c.value?console.log("Enhanced CSRF token ready:",r.value.substring(0,10)):console.warn("Enhanced CSRF token not ready after timeout, proceeding with legacy")}else console.log("Using legacy CSRF, no wait needed");if(console.log("Initializing store with config:",h.value),e.initialize(h.value),console.log("CSRF token status:",c.value),m.value.colors||m.value.typography||m.value.spacing||m.value.effects){const e=document.documentElement;Object.entries(v.value).forEach(([o,n])=>{e.style.setProperty(o,n)})}}));const _=d(()=>window.location.search.includes("debug"));return{loadingResults:o,hasResults:w,themeClasses:y,themeStyles:v,translation:k,configData:h,storeInitialized:a,loading:t,error:l,showDebug:_}}},[["render",function(e,o,n,t,l,a){var i,r,s,d;const c=b("VehicleSearch"),u=b("ResultsDisplay");return m(),g("div",{class:k(["finder-v2-widget p-1",t.themeClasses]),"data-iframe-height":"",style:$(t.themeStyles)},[t.showDebug?(m(),g("div",_o,[o[0]||(o[0]=h("strong",null,"🔍 Widget Debug Info:",-1)),o[1]||(o[1]=h("br",null,null,-1)),M(" Config exists: "+x(!!t.configData),1),o[2]||(o[2]=h("br",null,null,-1)),M(" Config UUID: "+x((null==(i=t.configData)?void 0:i.uuid)||(null==(r=t.configData)?void 0:r.id)||"undefined"),1),o[3]||(o[3]=h("br",null,null,-1)),M(" Window.FinderV2Config exists: "+x(!!e.window.FinderV2Config),1),o[4]||(o[4]=h("br",null,null,-1)),M(" Store initialized: "+x(t.storeInitialized),1),o[5]||(o[5]=h("br",null,null,-1)),M(" Loading: "+x(t.loading),1),o[6]||(o[6]=h("br",null,null,-1)),M(" Error: "+x(t.error||"None"),1),o[7]||(o[7]=h("br",null,null,-1)),M(" Theme name: "+x((null==(d=null==(s=t.configData)?void 0:s.theme)?void 0:d.name)||"default"),1),o[8]||(o[8]=h("br",null,null,-1)),M(" Translation loaded: "+x(!!t.translation),1)])):v("",!0),h("div",xo,[w(c,{translation:t.translation},null,8,["translation"])]),t.hasResults||t.loadingResults?(m(),g("div",Co,[w(u,{translation:t.translation},null,8,["translation"])])):v("",!0)],6)}],["__scopeId","data-v-855a0cb6"]]);window.FinderV2Debug={mainLoaded:!0,timestamp:(new Date).toISOString()};const Mo=I(So);function Vo(){window.parent&&window.parent!==window&&window.parentIFrame&&window.parentIFrame.size()}Mo.use(z()),document.addEventListener("DOMContentLoaded",()=>{var e,o,n;window.FinderV2Debug.domReady=!0,window.FinderV2Debug.config=!!window.FinderV2Config;const t=(null==(e=window.FinderV2Config)?void 0:e.csrfToken)||"";t&&!(null==(o=window.FinderV2Config)?void 0:o.enhancedCSRF)&&(E.defaults.headers.common["X-CSRF-TOKEN"]=t,console.log("Legacy CSRF token configured")),Mo.config.globalProperties.$config=window.FinderV2Config||{};const l=document.getElementById("finder-v2-app");if(window.FinderV2Debug.containerFound=!!l,l){try{console.log("🎯 About to mount Vue app on container:",l),Mo.mount(l),window.FinderV2Debug.mounted=!0,window.FinderV2Debug.mountError=null,console.log("✅ Vue app mounted successfully!")}catch(a){window.FinderV2Debug.mounted=!1,window.FinderV2Debug.mountError=a.message,console.error("❌ Mount error:",a);const e=document.createElement("div");e.style.cssText="position:fixed;top:0;left:0;background:red;color:white;padding:10px;z-index:9999",e.textContent=`Mount error: ${a.message}`,document.body.appendChild(e)}(null==(n=window.FinderV2Config)?void 0:n.iframeResize)&&setTimeout(()=>{window.parentIFrame&&(Vo(),new MutationObserver(()=>{setTimeout(Vo,50)}).observe(l,{childList:!0,subtree:!0,attributes:!0}),window.addEventListener("resize",Vo))},100)}else{window.FinderV2Debug.containerNotFound=!0;const e=document.createElement("div");e.style.cssText="position:fixed;top:0;left:0;background:orange;color:white;padding:10px;z-index:9999",e.textContent="Container #finder-v2-app not found!",document.body.appendChild(e)}}),window.FinderV2App=Mo;
