var e=Object.getOwnPropertyNames;import{Q as n,P as o}from"./vue-core-BvvXclHU.js";import{H as r}from"./ui-components-_gLc8WmR.js";import{a as t}from"./http-client-5tgu7n7k.js";var i,l,a=(i={"js/finder-v2-app-libs.js"(e){window.Vue={createApp:o,createPinia:n},window.HeadlessUI=r,window.axios=t,t.defaults.xsrfCookieName="csrftoken",t.defaults.xsrfHeaderName="X-CSRFToken",t.interceptors.request.use(e=>{var n;const o=null==(n=window.FinderV2Config)?void 0:n.csrfToken;return o&&(e.headers["X-CSRFToken"]=o),e}),t.interceptors.response.use(e=>e,n=>{return o=e,r=function*(){var e,o,r;const t=n.config;return 403!==(null==(e=n.response)?void 0:e.status)||"challenge_required"!==(null==(r=null==(o=n.response)?void 0:o.data)?void 0:r.error)||t.o||(console.log("Challenge required for API endpoint. Solving challenge..."),t.o=!0,console.warn("Challenge required but cannot solve outside Vue context")),Promise.reject(n)},new Promise((e,n)=>{var t=e=>{try{l(r.next(e))}catch(o){n(o)}},i=e=>{try{l(r.throw(e))}catch(o){n(o)}},l=n=>n.done?e(n.value):Promise.resolve(n.value).then(t,i);l((r=r.apply(o,null)).next())});var o,r})}},function(){return l||(0,i[e(i)[0]])((l={exports:{}}).exports,l),l.exports});export default a();
