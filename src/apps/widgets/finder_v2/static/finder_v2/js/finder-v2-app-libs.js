const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/useBotProtection-DKPGrmyF.js","js/listbox-Css7ETHV.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=Object.defineProperties,l=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertyNames,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,i=(t,l,n)=>l in t?e(t,l,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[l]=n,u=(e,t)=>{for(var l in t||(t={}))o.call(t,l)&&i(e,l,t[l]);if(a)for(var l of a(t))r.call(t,l)&&i(e,l,t[l]);return e},s=(e,n)=>t(e,l(n)),d=(e,t)=>{var l={};for(var n in e)o.call(e,n)&&t.indexOf(n)<0&&(l[n]=e[n]);if(null!=e&&a)for(var n of a(e))t.indexOf(n)<0&&r.call(e,n)&&(l[n]=e[n]);return l},v=(e,t,l)=>new Promise((n,a)=>{var o=e=>{try{i(l.next(e))}catch(t){a(t)}},r=e=>{try{i(l.throw(e))}catch(t){a(t)}},i=e=>e.done?n(e.value):Promise.resolve(e.value).then(o,r);i((l=l.apply(e,t)).next())});import{c,J as p,K as f,w as m,L as b,M as h,a as g,N as y,O as S,P as x,r as O,Q as P,R as w,S as I,U as T,V as E,W as R,o as C,X as D,Y as F,Z as k,$ as M,a0 as A,a1 as L,a2 as B,s as j,a3 as N,a4 as $,a5 as V,a6 as z,a7 as H,a8 as W,n as _,a9 as G,aa as K,ab as q,ac as U,ad as Y,ae as J,af as Q,ag as X,ah as Z,ai as ee,aj as te,ak as le,al as ne,am as ae,an as oe,ao as re,ap as ie,aq as ue,ar as se,as as de,at as ve,au as ce,av as pe,aw as fe,ax as me,ay as be,az as he,q as ge,I as ye,k as Se,aA as xe,F as Oe,A as Pe,aB as we,H as Ie,G as Te,b as Ee,_ as Re}from"./listbox-Css7ETHV.js";var Ce,De,Fe=(Ce={"js/finder-v2-app-libs.js"(e){function t(e,t,l){var n;let a,o=null!=(n=l.initialDeps)?n:[];function r(){var n,r,i,u;let s;l.key&&(null==(n=l.debug)?void 0:n.call(l))&&(s=Date.now());const d=e();if(d.length===o.length&&!d.some((e,t)=>o[t]!==e))return a;let v;if(o=d,l.key&&(null==(r=l.debug)?void 0:r.call(l))&&(v=Date.now()),a=t(...d),l.key&&(null==(i=l.debug)?void 0:i.call(l))){const e=Math.round(100*(Date.now()-s))/100,t=Math.round(100*(Date.now()-v))/100,n=t/16,a=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${a(t,5)} /${a(e,5)} ms`,`\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0,Math.min(120-120*n,120))}deg 100% 31%);`,null==l?void 0:l.key)}return null==(u=null==l?void 0:l.onChange)||u.call(l,a),a}return r.updateDeps=e=>{o=e},r}function l(e,t){if(void 0===e)throw new Error("Unexpected undefined");return e}const n=(e,t,l)=>{let n;return function(...a){e.clearTimeout(n),n=e.setTimeout(()=>t.apply(this,a),l)}},a=e=>{const{offsetWidth:t,offsetHeight:l}=e;return{width:t,height:l}},o=e=>e,r=e=>{const t=Math.max(e.startIndex-e.overscan,0),l=Math.min(e.endIndex+e.overscan,e.count-1),n=[];for(let a=t;a<=l;a++)n.push(a);return n},i=(e,t)=>{const l=e.scrollElement;if(!l)return;const n=e.targetWindow;if(!n)return;const o=e=>{const{width:l,height:n}=e;t({width:Math.round(l),height:Math.round(n)})};if(o(a(l)),!n.ResizeObserver)return()=>{};const r=new n.ResizeObserver(t=>{const n=()=>{const e=t[0];if(null==e?void 0:e.borderBoxSize){const t=e.borderBoxSize[0];if(t)return void o({width:t.inlineSize,height:t.blockSize})}o(a(l))};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(n):n()});return r.observe(l,{box:"border-box"}),()=>{r.unobserve(l)}},Ce={passive:!0},De="undefined"==typeof window||"onscrollend"in window,Fe=(e,t)=>{const l=e.scrollElement;if(!l)return;const a=e.targetWindow;if(!a)return;let o=0;const r=e.options.useScrollendEvent&&De?()=>{}:n(a,()=>{t(o,!1)},e.options.isScrollingResetDelay),i=n=>()=>{const{horizontal:a,isRtl:i}=e.options;o=a?l.scrollLeft*(i?-1:1):l.scrollTop,r(),t(o,n)},u=i(!0),s=i(!1);s(),l.addEventListener("scroll",u,Ce);const d=e.options.useScrollendEvent&&De;return d&&l.addEventListener("scrollend",s,Ce),()=>{l.removeEventListener("scroll",u),d&&l.removeEventListener("scrollend",s)}},ke=(e,t,l)=>{if(null==t?void 0:t.borderBoxSize){const e=t.borderBoxSize[0];if(e)return Math.round(e[l.options.horizontal?"inlineSize":"blockSize"])}return e[l.options.horizontal?"offsetWidth":"offsetHeight"]},Me=(e,{adjustments:t=0,behavior:l},n)=>{var a,o;const r=e+t;null==(o=null==(a=n.scrollElement)?void 0:a.scrollTo)||o.call(a,{[n.options.horizontal?"left":"top"]:r,behavior:l})};class Ae{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null;const t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver(e=>{e.forEach(e=>{const t=()=>{this._measureElement(e.target,e)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()})}):null);return{disconnect:()=>{var l;null==(l=t())||l.disconnect(),e=null},observe:e=>{var l;return null==(l=t())?void 0:l.observe(e,{box:"border-box"})},unobserve:e=>{var l;return null==(l=t())?void 0:l.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach(([t,l])=>{void 0===l&&delete e[t]}),this.options=u({debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:o,rangeExtractor:r,onChange:()=>{},measureElement:ke,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1},e)},this.notify=e=>{var t,l;null==(l=(t=this.options).onChange)||l.call(t,this,e)},this.maybeNotify=t(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),e=>{this.notify(e)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(e=>e()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e,t;const l=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==l){if(this.cleanup(),!l)return void this.maybeNotify();this.scrollElement=l,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=null!=(e=null==(t=this.scrollElement)?void 0:t.window)?e:null,this.elementsCache.forEach(e=>{this.observer.observe(e)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,e=>{this.scrollRect=e,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()}))}},this.getSize=()=>{var e;return this.options.enabled?(this.scrollRect=null!=(e=this.scrollRect)?e:this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0)},this.getScrollOffset=()=>{var e;return this.options.enabled?(this.scrollOffset=null!=(e=this.scrollOffset)?e:"function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset,this.scrollOffset):(this.scrollOffset=null,0)},this.getFurthestMeasurement=(e,t)=>{const l=new Map,n=new Map;for(let a=t-1;a>=0;a--){const t=e[a];if(l.has(t.lane))continue;const o=n.get(t.lane);if(null==o||t.end>o.end?n.set(t.lane,t):t.end<o.end&&l.set(t.lane,!0),l.size===this.options.lanes)break}return n.size===this.options.lanes?Array.from(n.values()).sort((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end)[0]:void 0},this.getMeasurementOptions=t(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(e,t,l,n,a)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:l,getItemKey:n,enabled:a}),{key:!1}),this.getMeasurements=t(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:e,paddingStart:t,scrollMargin:l,getItemKey:n,enabled:a},o)=>{if(!a)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(e=>{this.itemSizeCache.set(e.key,e.size)}));const r=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const i=this.measurementsCache.slice(0,r);for(let u=r;u<e;u++){const e=n(u),a=1===this.options.lanes?i[u-1]:this.getFurthestMeasurement(i,u),r=a?a.end+this.options.gap:t+l,s=o.get(e),d="number"==typeof s?s:this.options.estimateSize(u),v=r+d,c=a?a.lane:u%this.options.lanes;i[u]={index:u,start:r,size:d,end:v,key:e,lane:c}}return this.measurementsCache=i,i},{key:!1,debug:()=>this.options.debug}),this.calculateRange=t(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(e,t,l,n)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:l,lanes:n}){const a=e.length-1,o=t=>e[t].start;if(e.length<=n)return{startIndex:0,endIndex:a};let r=Le(0,a,o,l),i=r;if(1===n)for(;i<a&&e[i].end<l+t;)i++;else if(n>1){const o=Array(n).fill(0);for(;i<a&&o.some(e=>e<l+t);){const t=e[i];o[t.lane]=t.end,i++}const u=Array(n).fill(l+t);for(;r>=0&&u.some(e=>e>=l);){const t=e[r];u[t.lane]=t.start,r--}r=Math.max(0,r-r%n),i=Math.min(a,i+(n-1-i%n))}return{startIndex:r,endIndex:i}}({measurements:e,outerSize:t,scrollOffset:l,lanes:n}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=t(()=>{let e=null,t=null;const l=this.calculateRange();return l&&(e=l.startIndex,t=l.endIndex),this.maybeNotify.updateDeps([this.isScrolling,e,t]),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,t]},(e,t,l,n,a)=>null===n||null===a?[]:e({startIndex:n,endIndex:a,overscan:t,count:l}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{const t=this.options.indexAttribute,l=e.getAttribute(t);return l?parseInt(l,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{const l=this.indexFromElement(e),n=this.measurementsCache[l];if(!n)return;const a=n.key,o=this.elementsCache.get(a);o!==e&&(o&&this.observer.unobserve(o),this.observer.observe(e),this.elementsCache.set(a,e)),e.isConnected&&this.resizeItem(l,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{var l;const n=this.measurementsCache[e];if(!n)return;const a=t-(null!=(l=this.itemSizeCache.get(n.key))?l:n.size);0!==a&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(n,a,this):"backward"===this.scrollDirection&&n.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=a,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(n.index),this.itemSizeCache=new Map(this.itemSizeCache.set(n.key,t)),this.notify(!1))},this.measureElement=e=>{e?this._measureElement(e,void 0):this.elementsCache.forEach((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))})},this.getVirtualItems=t(()=>[this.getVirtualIndexes(),this.getMeasurements()],(e,t)=>{const l=[];for(let n=0,a=e.length;n<a;n++){const a=t[e[n]];l.push(a)}return l},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{const t=this.getMeasurements();if(0!==t.length)return l(t[Le(0,t.length-1,e=>l(t[e]).start,e)])},this.getOffsetForAlignment=(e,t,l=0)=>{const n=this.getSize(),a=this.getScrollOffset();"auto"===t&&(t=e>=a+n?"end":"start"),"center"===t?e+=(l-n)/2:"end"===t&&(e-=n);const o=this.getTotalSize()-n;return Math.max(Math.min(o,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));const l=this.measurementsCache[e];if(!l)return;const n=this.getSize(),a=this.getScrollOffset();if("auto"===t)if(l.end>=a+n-this.options.scrollPaddingEnd)t="end";else{if(!(l.start<=a+this.options.scrollPaddingStart))return[a,t];t="start"}const o="end"===t?l.end+this.options.scrollPaddingEnd:l.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(o,t,l.size),t]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:l}={})=>{this.cancelScrollToIndex(),"smooth"===l&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:l})},this.scrollToIndex=(e,{align:t="auto",behavior:l}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===l&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const n=this.getOffsetForIndex(e,t);if(!n)return;const[a,o]=n;this._scrollToOffset(a,{adjustments:void 0,behavior:l}),"smooth"!==l&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){const a=this.getOffsetForIndex(e,o);if(!a)return;const[r]=a,i=this.getScrollOffset();t=r,n=i,Math.abs(t-n)<=1||this.scrollToIndex(e,{align:o,behavior:l})}else this.scrollToIndex(e,{align:o,behavior:l});var t,n}))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e,t;const l=this.getMeasurements();let n;if(0===l.length)n=this.options.paddingStart;else if(1===this.options.lanes)n=null!=(e=null==(t=l[l.length-1])?void 0:t.end)?e:0;else{const e=Array(this.options.lanes).fill(null);let t=l.length-1;for(;t>=0&&e.some(e=>null===e);){const n=l[t];null===e[n.lane]&&(e[n.lane]=n.end),t--}n=Math.max(...e.filter(e=>null!==e))}return Math.max(n-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:l})=>{this.options.scrollToFn(e,{behavior:l,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}const Le=(e,t,l,n)=>{for(;e<=t;){const a=(e+t)/2|0,o=l(a);if(o<n)e=a+1;else{if(!(o>n))return a;t=a-1}}return e>0?e-1:0};function Be(e){return function(e){const t=new Ae(p(e)),l=f(t),n=t._didMount();return m(()=>p(e).getScrollElement(),e=>{e&&t._willUpdate()},{immediate:!0}),m(()=>p(e),e=>{t.setOptions(s(u({},e),{onChange:(t,n)=>{var a;h(l),null==(a=e.onChange)||a.call(e,t,n)}})),t._willUpdate(),h(l)},{immediate:!0}),b(n),l}(c(()=>u({observeElementRect:i,observeElementOffset:Fe,scrollToFn:Me},p(e))))}function je(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}function Ne(){let e=[],t={addEventListener:(e,l,n,a)=>(e.addEventListener(l,n,a),t.add(()=>e.removeEventListener(l,n,a))),requestAnimationFrame(...e){let l=requestAnimationFrame(...e);t.add(()=>cancelAnimationFrame(l))},nextFrame(...e){t.requestAnimationFrame(()=>{t.requestAnimationFrame(...e)})},setTimeout(...e){let l=setTimeout(...e);t.add(()=>clearTimeout(l))},microTask(...e){let l={current:!0};return je(()=>{l.current&&e[0]()}),t.add(()=>{l.current=!1})},style(e,t,l){let n=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:l}),this.add(()=>{Object.assign(e.style,{[t]:n})})},group(e){let t=Ne();return e(t),this.add(()=>t.dispose())},add:t=>(e.push(t),()=>{let l=e.indexOf(t);if(l>=0)for(let t of e.splice(l,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function $e(){let e=function(){let e=Ne();return g(()=>e.dispose()),e}();return t=>{e.dispose(),e.nextFrame(t)}}function Ve({container:e,accept:t,walk:l,enabled:n}){y(()=>{let a=e.value;if(!a||void 0!==n&&!n.value)return;let o=S(e);if(!o)return;let r=Object.assign(e=>t(e),{acceptNode:t}),i=o.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,r,!1);for(;i.nextNode();)l(i.currentNode)})}var ze,He=((ze=He||{})[ze.Left=0]="Left",ze[ze.Right=2]="Right",ze);let We=[];!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&We[0]!==e.target&&(We.unshift(e.target),We=We.filter(e=>null!=e&&e.isConnected),We.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var _e,Ge,Ke={},qe=((Ge=qe||{})[Ge.Open=0]="Open",Ge[Ge.Closed=1]="Closed",Ge),Ue=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(Ue||{}),Ye=((_e=Ye||{})[_e.Pointer=0]="Pointer",_e[_e.Focus=1]="Focus",_e[_e.Other=2]="Other",_e);let Je=Symbol("ComboboxContext");function Qe(e){let t=q(Je,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Qe),t}return t}let Xe=Symbol("VirtualContext"),Ze=x({name:"VirtualProvider",setup(e,{slots:t}){let l=Qe("VirtualProvider"),n=c(()=>{let e=R(l.optionsRef);if(!e)return{start:0,end:0};let t=window.getComputedStyle(e);return{start:parseFloat(t.paddingBlockStart||t.paddingTop),end:parseFloat(t.paddingBlockEnd||t.paddingBottom)}}),a=Be(c(()=>({scrollPaddingStart:n.value.start,scrollPaddingEnd:n.value.end,count:l.virtual.value.options.length,estimateSize:()=>40,getScrollElement:()=>R(l.optionsRef),overscan:12}))),o=c(()=>{var e;return null==(e=l.virtual.value)?void 0:e.options}),r=O(0);return m([o],()=>{r.value+=1}),X(Xe,l.virtual.value?a:null),()=>[D("div",{style:{position:"relative",width:"100%",height:`${a.value.getTotalSize()}px`},ref:e=>{if(e){if("undefined"!=typeof process&&void 0!==Ke.JEST_WORKER_ID||0===l.activationTrigger.value)return;null!==l.activeOptionIndex.value&&l.virtual.value.options.length>l.activeOptionIndex.value&&a.value.scrollToIndex(l.activeOptionIndex.value)}}},a.value.getVirtualItems().map(e=>Z(t.default({option:l.virtual.value.options[e.index],open:0===l.comboboxState.value})[0],{key:`${r.value}-${e.index}`,"data-index":e.index,"aria-setsize":l.virtual.value.options.length,"aria-posinset":e.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${e.start}px)`,overflowAnchor:"none"}})))]}}),et=x({name:"Combobox",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],nullable:!0,default:null},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},nullable:{type:Boolean,default:!1},multiple:{type:[Boolean],default:!1},immediate:{type:[Boolean],default:!1},virtual:{type:Object,default:null}},inheritAttrs:!1,setup(e,{slots:t,attrs:l,emit:n}){let a=O(1),o=O(null),r=O(null),i=O(null),s=O(null),v=O({static:!1,hold:!1}),p=O([]),f=O(null),b=O(2),h=O(!1);function g(e=e=>e){let t=null!==f.value?p.value[f.value]:null,l=e(p.value.slice()),n=l.length>0&&null!==l[0].dataRef.order.value?l.sort((e,t)=>e.dataRef.order.value-t.dataRef.order.value):Q(l,e=>R(e.dataRef.domRef)),a=t?n.indexOf(t):null;return-1===a&&(a=null),{options:n,activeOptionIndex:a}}let y=c(()=>e.multiple?1:0),S=c(()=>e.nullable),[x,H]=P(c(()=>e.modelValue),e=>n("update:modelValue",e),c(()=>e.defaultValue)),W=c(()=>void 0===x.value?w(y.value,{1:[],0:void 0}):x.value),_=null,G=null;function K(e){return w(y.value,{0:()=>null==H?void 0:H(e),1:()=>{let t=N(U.value.value).slice(),l=N(e),n=t.findIndex(e=>U.compare(l,N(e)));return-1===n?t.push(l):t.splice(n,1),null==H?void 0:H(t)}})}let q=c(()=>{});m([q],([e],[t])=>{if(U.virtual.value&&e&&t&&null!==f.value){let l=e.indexOf(t[f.value]);f.value=-1!==l?l:null}});let U={comboboxState:a,value:W,mode:y,compare(t,l){if("string"==typeof e.by){let n=e.by;return(null==t?void 0:t[n])===(null==l?void 0:l[n])}return null===e.by?function(e,t){return e===t}(t,l):e.by(t,l)},calculateIndex:t=>U.virtual.value?null===e.by?U.virtual.value.options.indexOf(t):U.virtual.value.options.findIndex(e=>U.compare(e,t)):p.value.findIndex(e=>U.compare(e.dataRef.value,t)),defaultValue:c(()=>e.defaultValue),nullable:S,immediate:c(()=>!1),virtual:c(()=>null),inputRef:r,labelRef:o,buttonRef:i,optionsRef:s,disabled:c(()=>e.disabled),options:p,change(e){H(e)},activeOptionIndex:c(()=>{if(h.value&&null===f.value&&(U.virtual.value?U.virtual.value.options.length>0:p.value.length>0)){if(U.virtual.value){let e=U.virtual.value.options.findIndex(e=>{var t;return!(null!=(t=U.virtual.value)&&t.disabled(e))});if(-1!==e)return e}let e=p.value.findIndex(e=>!e.dataRef.disabled);if(-1!==e)return e}return f.value}),activationTrigger:b,optionsPropsRef:v,closeCombobox(){h.value=!1,!e.disabled&&1!==a.value&&(a.value=1,f.value=null)},openCombobox(){if(h.value=!0,!e.disabled&&0!==a.value){if(U.value.value){let e=U.calculateIndex(U.value.value);-1!==e&&(f.value=e)}a.value=0}},setActivationTrigger(e){b.value=e},goToOption(t,l,n){h.value=!1,null!==_&&cancelAnimationFrame(_),_=requestAnimationFrame(()=>{if(e.disabled||s.value&&!v.value.static&&1===a.value)return;if(U.virtual.value)return f.value=t===V.Specific?l:z({focus:t},{resolveItems:()=>U.virtual.value.options,resolveActiveIndex:()=>{var e,t;return null!=(t=null!=(e=U.activeOptionIndex.value)?e:U.virtual.value.options.findIndex(e=>{var t;return!(null!=(t=U.virtual.value)&&t.disabled(e))}))?t:null},resolveDisabled:e=>U.virtual.value.disabled(e),resolveId(){throw new Error("Function not implemented.")}}),void(b.value=null!=n?n:2);let o=g();if(null===o.activeOptionIndex){let e=o.options.findIndex(e=>!e.dataRef.disabled);-1!==e&&(o.activeOptionIndex=e)}let r=t===V.Specific?l:z({focus:t},{resolveItems:()=>o.options,resolveActiveIndex:()=>o.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.disabled});f.value=r,b.value=null!=n?n:2,p.value=o.options})},selectOption(e){let t=p.value.find(t=>t.id===e);if(!t)return;let{dataRef:l}=t;K(l.value)},selectActiveOption(){if(null!==U.activeOptionIndex.value){if(U.virtual.value)K(U.virtual.value.options[U.activeOptionIndex.value]);else{let{dataRef:e}=p.value[U.activeOptionIndex.value];K(e.value)}U.goToOption(V.Specific,U.activeOptionIndex.value)}},registerOption(e,t){let l=$({id:e,dataRef:t});if(U.virtual.value)return void p.value.push(l);G&&cancelAnimationFrame(G);let n=g(e=>(e.push(l),e));null===f.value&&U.isSelected(t.value.value)&&(n.activeOptionIndex=n.options.indexOf(l)),p.value=n.options,f.value=n.activeOptionIndex,b.value=2,n.options.some(e=>!R(e.dataRef.domRef))&&(G=requestAnimationFrame(()=>{let e=g();p.value=e.options,f.value=e.activeOptionIndex}))},unregisterOption(e,t){if(null!==_&&cancelAnimationFrame(_),t&&(h.value=!0),U.virtual.value)return void(p.value=p.value.filter(t=>t.id!==e));let l=g(t=>{let l=t.findIndex(t=>t.id===e);return-1!==l&&t.splice(l,1),t});p.value=l.options,f.value=l.activeOptionIndex,b.value=2},isSelected:e=>w(y.value,{0:()=>U.compare(N(U.value.value),N(e)),1:()=>N(U.value.value).some(t=>U.compare(N(t),N(e)))}),isActive:e=>f.value===U.calculateIndex(e)};I([r,i,s],()=>U.closeCombobox(),c(()=>0===a.value)),X(Je,U),T(c(()=>w(a.value,{0:E.Open,1:E.Closed})));let Y=c(()=>{var e;return null==(e=R(r))?void 0:e.closest("form")});return C(()=>{m([Y],()=>{if(Y.value&&void 0!==e.defaultValue)return Y.value.addEventListener("reset",t),()=>{var e;null==(e=Y.value)||e.removeEventListener("reset",t)};function t(){U.change(e.defaultValue)}},{immediate:!0})}),()=>{var n,o,r;let i=e,{name:s,disabled:v,form:c}=i,p=d(i,["name","disabled","form"]),f={open:0===a.value,disabled:v,activeIndex:U.activeOptionIndex.value,activeOption:null===U.activeOptionIndex.value?null:U.virtual.value?U.virtual.value.options[null!=(n=U.activeOptionIndex.value)?n:0]:null!=(r=null==(o=U.options.value[U.activeOptionIndex.value])?void 0:o.dataRef.value)?r:null,value:W.value};return D(j,[...null!=s&&null!=W.value?F({[s]:W.value}).map(([e,t])=>D(M,A({features:L.Hidden,key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:c,disabled:v,name:e,value:t}))):[],k({theirProps:u(u({},l),B(p,["by","defaultValue","immediate","modelValue","multiple","nullable","onUpdate:modelValue","virtual"])),ourProps:{},slot:f,slots:t,attrs:l,name:"Combobox"})])}}}),tt=x({name:"ComboboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l}){var n;let a=null!=(n=e.id)?n:`headlessui-combobox-label-${H()}`,o=Qe("ComboboxLabel");function r(){var e;null==(e=R(o.inputRef))||e.focus({preventScroll:!0})}return()=>{let n={open:0===o.comboboxState.value,disabled:o.disabled.value},i=d(e,[]),u={id:a,ref:o.labelRef,onClick:r};return k({ourProps:u,theirProps:i,slot:n,attrs:t,slots:l,name:"ComboboxLabel"})}}}),lt=x({name:"ComboboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-combobox-button-${H()}`,r=Qe("ComboboxButton");function i(e){r.disabled.value||(0===r.comboboxState.value?r.closeCombobox():(e.preventDefault(),r.openCombobox()),_(()=>{var e;return null==(e=R(r.inputRef))?void 0:e.focus({preventScroll:!0})}))}function u(e){switch(e.key){case G.ArrowDown:return e.preventDefault(),e.stopPropagation(),1===r.comboboxState.value&&r.openCombobox(),void _(()=>{var e;return null==(e=r.inputRef.value)?void 0:e.focus({preventScroll:!0})});case G.ArrowUp:return e.preventDefault(),e.stopPropagation(),1===r.comboboxState.value&&(r.openCombobox(),_(()=>{r.value.value||r.goToOption(V.Last)})),void _(()=>{var e;return null==(e=r.inputRef.value)?void 0:e.focus({preventScroll:!0})});case G.Escape:if(0!==r.comboboxState.value)return;return e.preventDefault(),r.optionsRef.value&&!r.optionsPropsRef.value.static&&e.stopPropagation(),r.closeCombobox(),void _(()=>{var e;return null==(e=r.inputRef.value)?void 0:e.focus({preventScroll:!0})})}}n({el:r.buttonRef,$el:r.buttonRef});let s=W(c(()=>({as:e.as,type:t.type})),r.buttonRef);return()=>{var n,a;let v={open:0===r.comboboxState.value,disabled:r.disabled.value,value:r.value.value},c=d(e,[]),p={ref:r.buttonRef,id:o,type:s.value,tabindex:"-1","aria-haspopup":"listbox","aria-controls":null==(n=R(r.optionsRef))?void 0:n.id,"aria-expanded":0===r.comboboxState.value,"aria-labelledby":r.labelRef.value?[null==(a=R(r.labelRef))?void 0:a.id,o].join(" "):void 0,disabled:!0===r.disabled.value||void 0,onKeydown:u,onClick:i};return k({ourProps:p,theirProps:c,slot:v,attrs:t,slots:l,name:"ComboboxButton"})}}}),nt=x({name:"ComboboxInput",props:{as:{type:[Object,String],default:"input"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},displayValue:{type:Function},defaultValue:{type:String,default:void 0},id:{type:String,default:null}},emits:{change:e=>!0},setup(e,{emit:t,attrs:l,slots:n,expose:a}){var o;let r=null!=(o=e.id)?o:`headlessui-combobox-input-${H()}`,i=Qe("ComboboxInput"),u=c(()=>S(R(i.inputRef))),s={value:!1};function v(){i.change(null);let e=R(i.optionsRef);e&&(e.scrollTop=0),i.goToOption(V.Nothing)}a({el:i.inputRef,$el:i.inputRef});let p=c(()=>{var t;let l=i.value.value;return R(i.inputRef)?void 0!==e.displayValue&&void 0!==l?null!=(t=e.displayValue(l))?t:"":"string"==typeof l?l:"":""});C(()=>{m([p,i.comboboxState,u],([e,t],[l,n])=>{if(s.value)return;let a=R(i.inputRef);a&&((0===n&&1===t||e!==l)&&(a.value=e),requestAnimationFrame(()=>{var e;if(s.value||!a||(null==(e=u.value)?void 0:e.activeElement)!==a)return;let{selectionStart:t,selectionEnd:l}=a;0===Math.abs((null!=l?l:0)-(null!=t?t:0))&&0===t&&a.setSelectionRange(a.value.length,a.value.length)}))},{immediate:!0}),m([i.comboboxState],([e],[t])=>{if(0===e&&1===t){if(s.value)return;let e=R(i.inputRef);if(!e)return;let t=e.value,{selectionStart:l,selectionEnd:n,selectionDirection:a}=e;e.value="",e.value=t,null!==a?e.setSelectionRange(l,n,a):e.setSelectionRange(l,n)}})});let f=O(!1);function b(){f.value=!0}function h(){Ne().nextFrame(()=>{f.value=!1})}let g=$e();function y(e){switch(s.value=!0,g(()=>{s.value=!1}),e.key){case G.Enter:if(s.value=!1,0!==i.comboboxState.value||f.value)return;if(e.preventDefault(),e.stopPropagation(),null===i.activeOptionIndex.value)return void i.closeCombobox();i.selectActiveOption(),0===i.mode.value&&i.closeCombobox();break;case G.ArrowDown:return s.value=!1,e.preventDefault(),e.stopPropagation(),w(i.comboboxState.value,{0:()=>i.goToOption(V.Next),1:()=>i.openCombobox()});case G.ArrowUp:return s.value=!1,e.preventDefault(),e.stopPropagation(),w(i.comboboxState.value,{0:()=>i.goToOption(V.Previous),1:()=>{i.openCombobox(),_(()=>{i.value.value||i.goToOption(V.Last)})}});case G.Home:if(e.shiftKey)break;return s.value=!1,e.preventDefault(),e.stopPropagation(),i.goToOption(V.First);case G.PageUp:return s.value=!1,e.preventDefault(),e.stopPropagation(),i.goToOption(V.First);case G.End:if(e.shiftKey)break;return s.value=!1,e.preventDefault(),e.stopPropagation(),i.goToOption(V.Last);case G.PageDown:return s.value=!1,e.preventDefault(),e.stopPropagation(),i.goToOption(V.Last);case G.Escape:if(s.value=!1,0!==i.comboboxState.value)return;e.preventDefault(),i.optionsRef.value&&!i.optionsPropsRef.value.static&&e.stopPropagation(),i.nullable.value&&0===i.mode.value&&null===i.value.value&&v(),i.closeCombobox();break;case G.Tab:if(s.value=!1,0!==i.comboboxState.value)return;0===i.mode.value&&1!==i.activationTrigger.value&&i.selectActiveOption(),i.closeCombobox()}}function x(e){t("change",e),i.nullable.value&&0===i.mode.value&&""===e.target.value&&v(),i.openCombobox()}function P(e){var t,l,n;let a=null!=(t=e.relatedTarget)?t:We.find(t=>t!==e.currentTarget);if(s.value=!1,!(null!=(l=R(i.optionsRef))&&l.contains(a)||null!=(n=R(i.buttonRef))&&n.contains(a)||0!==i.comboboxState.value))return e.preventDefault(),0===i.mode.value&&(i.nullable.value&&null===i.value.value?v():1!==i.activationTrigger.value&&i.selectActiveOption()),i.closeCombobox()}function I(e){var t,l,n;let a=null!=(t=e.relatedTarget)?t:We.find(t=>t!==e.currentTarget);null!=(l=R(i.buttonRef))&&l.contains(a)||null!=(n=R(i.optionsRef))&&n.contains(a)||i.disabled.value||i.immediate.value&&0!==i.comboboxState.value&&(i.openCombobox(),Ne().nextFrame(()=>{i.setActivationTrigger(1)}))}let T=c(()=>{var t,l,n,a;return null!=(a=null!=(n=null!=(l=e.defaultValue)?l:void 0!==i.defaultValue.value?null==(t=e.displayValue)?void 0:t.call(e,i.defaultValue.value):null)?n:i.defaultValue.value)?a:""});return()=>{var t,a,o,u,s,v,c;let p={open:0===i.comboboxState.value},f=e,{displayValue:m,onChange:g}=f,S=d(f,["displayValue","onChange"]),O={"aria-controls":null==(t=i.optionsRef.value)?void 0:t.id,"aria-expanded":0===i.comboboxState.value,"aria-activedescendant":null===i.activeOptionIndex.value?void 0:i.virtual.value?null==(a=i.options.value.find(e=>!i.virtual.value.disabled(e.dataRef.value)&&i.compare(e.dataRef.value,i.virtual.value.options[i.activeOptionIndex.value])))?void 0:a.id:null==(o=i.options.value[i.activeOptionIndex.value])?void 0:o.id,"aria-labelledby":null!=(v=null==(u=R(i.labelRef))?void 0:u.id)?v:null==(s=R(i.buttonRef))?void 0:s.id,"aria-autocomplete":"list",id:r,onCompositionstart:b,onCompositionend:h,onKeydown:y,onInput:x,onFocus:I,onBlur:P,role:"combobox",type:null!=(c=l.type)?c:"text",tabIndex:0,ref:i.inputRef,defaultValue:T.value,disabled:!0===i.disabled.value||void 0};return k({ourProps:O,theirProps:S,slot:p,attrs:l,slots:n,features:K.RenderStrategy|K.Static,name:"ComboboxInput"})}}}),at=x({name:"ComboboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},hold:{type:[Boolean],default:!1}},setup(e,{attrs:t,slots:l,expose:n}){let a=Qe("ComboboxOptions"),o=`headlessui-combobox-options-${H()}`;n({el:a.optionsRef,$el:a.optionsRef}),y(()=>{a.optionsPropsRef.value.static=e.static}),y(()=>{a.optionsPropsRef.value.hold=e.hold});let r=J(),i=c(()=>null!==r?(r.value&E.Open)===E.Open:0===a.comboboxState.value);function d(e){e.preventDefault()}return Ve({container:c(()=>R(a.optionsRef)),enabled:c(()=>0===a.comboboxState.value),accept:e=>"option"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}}),()=>{var n,r,v;let c={open:0===a.comboboxState.value},p={"aria-labelledby":null!=(v=null==(n=R(a.labelRef))?void 0:n.id)?v:null==(r=R(a.buttonRef))?void 0:r.id,id:o,ref:a.optionsRef,role:"listbox","aria-multiselectable":1===a.mode.value||void 0,onMousedown:d},f=B(e,["hold"]);return k({ourProps:p,theirProps:f,slot:c,attrs:t,slots:a.virtual.value&&0===a.comboboxState.value?s(u({},l),{default:()=>[D(Ze,{},l.default)]}):l,features:K.RenderStrategy|K.Static,visible:i.value,name:"ComboboxOptions"})}}}),ot=x({name:"ComboboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},order:{type:[Number],default:null}},setup(e,{slots:t,attrs:l,expose:n}){let a=Qe("ComboboxOption"),o=`headlessui-combobox-option-${H()}`,r=O(null),i=c(()=>e.disabled);n({el:r,$el:r});let u=c(()=>{var t;return a.virtual.value?a.activeOptionIndex.value===a.calculateIndex(e.value):null!==a.activeOptionIndex.value&&(null==(t=a.options.value[a.activeOptionIndex.value])?void 0:t.id)===o}),s=c(()=>a.isSelected(e.value)),d=q(Xe,null),v=c(()=>({disabled:e.disabled,value:e.value,domRef:r,order:c(()=>e.order)}));function p(e){e.preventDefault(),e.button===He.Left&&(i.value||(a.selectOption(o),Y()||requestAnimationFrame(()=>{var e;return null==(e=R(a.inputRef))?void 0:e.focus({preventScroll:!0})}),0===a.mode.value&&a.closeCombobox()))}function f(){var t;if(e.disabled||null!=(t=a.virtual.value)&&t.disabled(e.value))return a.goToOption(V.Nothing);let l=a.calculateIndex(e.value);a.goToOption(V.Specific,l)}C(()=>a.registerOption(o,v)),g(()=>a.unregisterOption(o,u.value)),y(()=>{let e=R(r);e&&(null==d||d.value.measureElement(e))}),y(()=>{0===a.comboboxState.value&&u.value&&(a.virtual.value||0!==a.activationTrigger.value&&_(()=>{var e,t;return null==(t=null==(e=R(r))?void 0:e.scrollIntoView)?void 0:t.call(e,{block:"nearest"})}))});let m=U();function b(e){m.update(e)}function h(t){var l;if(!m.wasMoved(t)||e.disabled||null!=(l=a.virtual.value)&&l.disabled(e.value)||u.value)return;let n=a.calculateIndex(e.value);a.goToOption(V.Specific,n,0)}function S(t){var l;m.wasMoved(t)&&(e.disabled||null!=(l=a.virtual.value)&&l.disabled(e.value)||u.value&&(a.optionsPropsRef.value.hold||a.goToOption(V.Nothing)))}return()=>{let{disabled:n}=e,a={active:u.value,selected:s.value,disabled:n},i={id:o,ref:r,role:"option",tabIndex:!0===n?void 0:-1,"aria-disabled":!0===n||void 0,"aria-selected":s.value,disabled:void 0,onMousedown:p,onFocus:f,onPointerenter:b,onMouseenter:b,onPointermove:h,onMousemove:h,onPointerleave:S,onMouseleave:S},d=B(e,["order","value"]);return k({ourProps:i,theirProps:d,slot:a,attrs:l,slots:t,name:"ComboboxOption"})}}});function rt(e,t,l,n){ee.isServer||y(a=>{(e=null!=e?e:window).addEventListener(t,l,n),a(()=>e.removeEventListener(t,l,n))})}var it=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(it||{});function ut(){let e=O(0);return te("keydown",t=>{"Tab"===t.key&&(e.value=t.shiftKey?1:0)}),e}function st(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let l of e.value){let e=R(l);e instanceof HTMLElement&&t.add(e)}return t}var dt,vt=((dt=vt||{})[dt.None=1]="None",dt[dt.InitialFocus=2]="InitialFocus",dt[dt.TabLock=4]="TabLock",dt[dt.FocusLock=8]="FocusLock",dt[dt.RestoreFocus=16]="RestoreFocus",dt[dt.All=30]="All",dt);let ct=Object.assign(x({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:O(new Set)}},inheritAttrs:!1,setup(e,{attrs:t,slots:l,expose:n}){let a=O(null);n({el:a,$el:a});let o=c(()=>S(a)),r=O(!1);C(()=>r.value=!0),g(()=>r.value=!1),function({ownerDocument:e},t){let l=function(e){let t=O(We.slice());return m([e],([e],[l])=>{!0===l&&!1===e?je(()=>{t.value.splice(0)}):!1===l&&!0===e&&(t.value=We.slice())},{flush:"post"}),()=>{var e;return null!=(e=t.value.find(e=>null!=e&&e.isConnected))?e:null}}(t);C(()=>{y(()=>{var n,a;t.value||(null==(n=e.value)?void 0:n.activeElement)===(null==(a=e.value)?void 0:a.body)&&ae(l())},{flush:"post"})}),g(()=>{t.value&&ae(l())})}({ownerDocument:o},c(()=>r.value&&Boolean(16&e.features)));let i=function({ownerDocument:e,container:t,initialFocus:l},n){let a=O(null),o=O(!1);return C(()=>o.value=!0),g(()=>o.value=!1),C(()=>{m([t,l,n],(r,i)=>{if(r.every((e,t)=>(null==i?void 0:i[t])===e)||!n.value)return;let u=R(t);u&&je(()=>{var t,n;if(!o.value)return;let r=R(l),i=null==(t=e.value)?void 0:t.activeElement;if(r){if(r===i)return void(a.value=i)}else if(u.contains(i))return void(a.value=i);r?ae(r):le(u,ne.First|ne.NoScroll)===oe.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),a.value=null==(n=e.value)?void 0:n.activeElement})},{immediate:!0,flush:"post"})}),a}({ownerDocument:o,container:a,initialFocus:c(()=>e.initialFocus)},c(()=>r.value&&Boolean(2&e.features)));!function({ownerDocument:e,container:t,containers:l,previousActiveElement:n},a){var o;rt(null==(o=e.value)?void 0:o.defaultView,"focus",e=>{if(!a.value)return;let o=st(l);R(t)instanceof HTMLElement&&o.add(R(t));let r=n.value;if(!r)return;let i=e.target;i&&i instanceof HTMLElement?pt(o,i)?(n.value=i,ae(i)):(e.preventDefault(),e.stopPropagation(),ae(r)):ae(n.value)},!0)}({ownerDocument:o,container:a,containers:e.containers,previousActiveElement:i},c(()=>r.value&&Boolean(8&e.features)));let s=ut();function v(e){let t=R(a);t&&w(s.value,{[it.Forwards]:()=>{le(t,ne.First,{skipElements:[e.relatedTarget]})},[it.Backwards]:()=>{le(t,ne.Last,{skipElements:[e.relatedTarget]})}})}let p=O(!1);function f(e){"Tab"===e.key&&(p.value=!0,requestAnimationFrame(()=>{p.value=!1}))}function b(t){if(!r.value)return;let l=st(e.containers);R(a)instanceof HTMLElement&&l.add(R(a));let n=t.relatedTarget;n instanceof HTMLElement&&"true"!==n.dataset.headlessuiFocusGuard&&(pt(l,n)||(p.value?le(R(a),w(s.value,{[it.Forwards]:()=>ne.Next,[it.Backwards]:()=>ne.Previous})|ne.WrapAround,{relativeTo:t.target}):t.target instanceof HTMLElement&&ae(t.target)))}return()=>{let n={ref:a,onKeydown:f,onFocusout:b},o=e,{features:r,initialFocus:i,containers:s}=o,c=d(o,["features","initialFocus","containers"]);return D(j,[Boolean(4&r)&&D(M,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:v,features:L.Focusable}),k({ourProps:n,theirProps:u(u({},t),c),slot:{},attrs:t,slots:l,name:"FocusTrap"}),Boolean(4&r)&&D(M,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:v,features:L.Focusable})])}}}),{features:vt});function pt(e,t){for(let l of e)if(l.contains(t))return!0;return!1}function ft(){let e;return{before({doc:t}){var l;let n=t.documentElement;e=(null!=(l=t.defaultView)?l:window).innerWidth-n.clientWidth},after({doc:t,d:l}){let n=t.documentElement,a=n.clientWidth-n.offsetWidth,o=e-a;l.style(n,"paddingRight",`${o}px`)}}}function mt(e){let t={};for(let l of e)Object.assign(t,l(t));return t}let bt=function(e,t){let l=e(),n=new Set;return{getSnapshot:()=>l,subscribe:e=>(n.add(e),()=>n.delete(e)),dispatch(e,...a){let o=t[e].call(l,...a);o&&(l=o,n.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var l;let n=null!=(l=this.get(e))?l:{doc:e,count:0,d:Ne(),meta:new Set};return n.count++,n.meta.add(t),this.set(e,n),this},POP(e,t){let l=this.get(e);return l&&(l.count--,l.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:l}){let n={doc:e,d:t,meta:mt(l)},a=[re()?{before({doc:t,d:l,meta:n}){function a(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}l.microTask(()=>{var n;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=Ne();e.style(t.documentElement,"scrollBehavior","auto"),l.add(()=>l.microTask(()=>e.dispose()))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,r=null;l.addEventListener(t,"click",l=>{if(l.target instanceof HTMLElement)try{let e=l.target.closest("a");if(!e)return;let{hash:n}=new URL(e.href),o=t.querySelector(n);o&&!a(o)&&(r=o)}catch(e){}},!0),l.addEventListener(t,"touchstart",e=>{if(e.target instanceof HTMLElement)if(a(e.target)){let t=e.target;for(;t.parentElement&&a(t.parentElement);)t=t.parentElement;l.style(t,"overscrollBehavior","contain")}else l.style(e.target,"touchAction","none")}),l.addEventListener(t,"touchmove",e=>{if(e.target instanceof HTMLElement){if("INPUT"===e.target.tagName)return;if(a(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),l.add(()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),r&&r.isConnected&&(r.scrollIntoView({block:"nearest"}),r=null)})})}}:{},ft(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];a.forEach(({before:e})=>null==e?void 0:e(n)),a.forEach(({after:e})=>null==e?void 0:e(n))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});function ht(e,t,l){let n=function(e){let t=f(e.getSnapshot());return g(e.subscribe(()=>{t.value=e.getSnapshot()})),t}(bt),a=c(()=>{let t=e.value?n.value.get(e.value):void 0;return!!t&&t.count>0});return m([e,t],([e,t],[n],a)=>{if(!e||!t)return;bt.dispatch("PUSH",e,l);let o=!1;a(()=>{o||(bt.dispatch("POP",null!=n?n:e,l),o=!0)})},{immediate:!0}),a}bt.subscribe(()=>{let e=bt.getSnapshot(),t=new Map;for(let[l]of e)t.set(l,l.documentElement.style.overflow);for(let l of e.values()){let e="hidden"===t.get(l.doc),n=0!==l.count;(n&&!e||!n&&e)&&bt.dispatch(l.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",l),0===l.count&&bt.dispatch("TEARDOWN",l)}});let gt=new Map,yt=new Map;function St(e,t=O(!0)){y(l=>{var n;if(!t.value)return;let a=R(e);if(!a)return;l(function(){var e;if(!a)return;let t=null!=(e=yt.get(a))?e:1;if(1===t?yt.delete(a):yt.set(a,t-1),1!==t)return;let l=gt.get(a);l&&(null===l["aria-hidden"]?a.removeAttribute("aria-hidden"):a.setAttribute("aria-hidden",l["aria-hidden"]),a.inert=l.inert,gt.delete(a))});let o=null!=(n=yt.get(a))?n:0;yt.set(a,o+1),0===o&&(gt.set(a,{"aria-hidden":a.getAttribute("aria-hidden"),inert:a.inert}),a.setAttribute("aria-hidden","true"),a.inert=!0)})}function xt({defaultContainers:e=[],portals:t,mainTreeNodeRef:l}={}){let n=O(null),a=S(n);function o(){var l,o,r;let i=[];for(let t of e)null!==t&&(t instanceof HTMLElement?i.push(t):"value"in t&&t.value instanceof HTMLElement&&i.push(t.value));if(null!=t&&t.value)for(let e of t.value)i.push(e);for(let e of null!=(l=null==a?void 0:a.querySelectorAll("html > *, body > *"))?l:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(R(n))||e.contains(null==(r=null==(o=R(n))?void 0:o.getRootNode())?void 0:r.host)||i.some(t=>e.contains(t))||i.push(e));return i}return{resolveContainers:o,contains:e=>o().some(t=>t.contains(e)),mainTreeNodeRef:n,MainTreeNode:()=>null!=l?null:D(M,{features:L.Hidden,ref:n})}}let Ot=Symbol("ForcePortalRootContext"),Pt=x({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup:(e,{slots:t,attrs:l})=>(X(Ot,e.force),()=>{let n=e,{force:a}=n,o=d(n,["force"]);return k({theirProps:o,ourProps:{},slot:{},slots:t,attrs:l,name:"ForcePortalRoot"})})}),wt=Symbol("StackContext");var It=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(It||{});function Tt({type:e,enabled:t,element:l,onUpdate:n}){let a=q(wt,()=>{});function o(...e){null==n||n(...e),a(...e)}C(()=>{m(t,(t,n)=>{t?o(0,e,l):!0===n&&o(1,e,l)},{immediate:!0,flush:"sync"})}),g(()=>{t.value&&o(1,e,l)}),X(wt,o)}let Et=Symbol("DescriptionContext");function Rt({slot:e=O({}),name:t="Description",props:l={}}={}){let n=O([]);return X(Et,{register:function(e){return n.value.push(e),()=>{let t=n.value.indexOf(e);-1!==t&&n.value.splice(t,1)}},slot:e,name:t,props:l}),c(()=>n.value.length>0?n.value.join(" "):void 0)}let Ct=x({name:"Description",props:{as:{type:[Object,String],default:"p"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l}){var n;let a=null!=(n=e.id)?n:`headlessui-description-${H()}`,o=function(){let e=q(Et,null);if(null===e)throw new Error("Missing parent");return e}();return C(()=>g(o.register(a))),()=>{let{name:n="Description",slot:r=O({}),props:i={}}=o,v=d(e,[]),c=s(u({},Object.entries(i).reduce((e,[t,l])=>Object.assign(e,{[t]:p(l)}),{})),{id:a});return k({ourProps:c,theirProps:v,slot:r.value,attrs:t,slots:l,name:n})}}});const Dt=new WeakMap;function Ft(e,t){let l=t(function(e){var t;return null!=(t=Dt.get(e))?t:0}(e));return l<=0?Dt.delete(e):Dt.set(e,l),l}let kt=x({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:l}){let n=O(null),a=c(()=>S(n)),o=q(Ot,!1),r=q(Lt,null),i=O(!0===o||null==r?function(e){let t=S(e);if(!t){if(null===e)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let l=t.getElementById("headlessui-portal-root");if(l)return l;let n=t.createElement("div");return n.setAttribute("id","headlessui-portal-root"),t.body.appendChild(n)}(n.value):r.resolveTarget());i.value&&Ft(i.value,e=>e+1);let u=O(!1);C(()=>{u.value=!0}),y(()=>{o||null!=r&&(i.value=r.resolveTarget())});let s=q(Mt,null),d=!1,v=ue();return m(n,()=>{if(d||!s)return;let e=R(n);e&&(g(s.register(e),v),d=!0)}),g(()=>{var e,t;let l=null==(e=a.value)?void 0:e.getElementById("headlessui-portal-root");!l||i.value!==l||Ft(i.value,e=>e-1)||i.value.children.length>0||null==(t=i.value.parentElement)||t.removeChild(i.value)}),()=>{if(!u.value||null===i.value)return null;let a={ref:n,"data-headlessui-portal":""};return D(ie,{to:i.value},k({ourProps:a,theirProps:e,slot:{},attrs:l,slots:t,name:"Portal"}))}}}),Mt=Symbol("PortalParentContext");function At(){let e=q(Mt,null),t=O([]);function l(l){let n=t.value.indexOf(l);-1!==n&&t.value.splice(n,1),e&&e.unregister(l)}let n={register:function(n){return t.value.push(n),e&&e.register(n),()=>l(n)},unregister:l,portals:t};return[t,x({name:"PortalWrapper",setup:(e,{slots:t})=>(X(Mt,n),()=>{var e;return null==(e=t.default)?void 0:e.call(t)})})]}let Lt=Symbol("PortalGroupContext"),Bt=x({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:l}){let n=$({resolveTarget:()=>e.target});return X(Lt,n),()=>{let n=e,{target:a}=n,o=d(n,["target"]);return k({theirProps:o,ourProps:{},slot:{},attrs:t,slots:l,name:"PortalGroup"})}}});var jt,Nt=((jt=Nt||{})[jt.Open=0]="Open",jt[jt.Closed=1]="Closed",jt);let $t=Symbol("DialogContext");function Vt(e){let t=q($t,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Vt),t}return t}let zt="DC8F892D-2EBD-447C-A4C8-A03058436FF4",Ht=x({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:zt},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:"dialog"}},emits:{close:e=>!0},setup(e,{emit:t,attrs:l,slots:n,expose:a}){var o,r;let i=null!=(o=e.id)?o:`headlessui-dialog-${H()}`,v=O(!1);C(()=>{v.value=!0});let p=!1,f=c(()=>"dialog"===e.role||"alertdialog"===e.role?e.role:(p||(p=!0,console.warn(`Invalid role [${f}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")),m=O(0),b=J(),h=c(()=>e.open===zt&&null!==b?(b.value&E.Open)===E.Open:e.open),g=O(null),x=c(()=>S(g));if(a({el:g,$el:g}),e.open===zt&&null===b)throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if("boolean"!=typeof h.value)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${h.value===zt?void 0:e.open}`);let P=c(()=>v.value&&h.value?0:1),T=c(()=>0===P.value),F=c(()=>m.value>1),M=null!==q($t,null),[A,L]=At(),{resolveContainers:B,mainTreeNodeRef:j,MainTreeNode:N}=xt({portals:A,defaultContainers:[c(()=>{var e;return null!=(e=ee.panelRef.value)?e:g.value})]}),$=c(()=>F.value?"parent":"leaf"),V=c(()=>null!==b&&(b.value&E.Closing)===E.Closing),z=c(()=>!M&&!V.value&&T.value),W=c(()=>{var e,t,l;return null!=(l=Array.from(null!=(t=null==(e=x.value)?void 0:e.querySelectorAll("body > *"))?t:[]).find(e=>"headlessui-portal-root"!==e.id&&e.contains(R(j))&&e instanceof HTMLElement))?l:null});St(W,z);let U=c(()=>!!F.value||T.value),Y=c(()=>{var e,t,l;return null!=(l=Array.from(null!=(t=null==(e=x.value)?void 0:e.querySelectorAll("[data-headlessui-portal]"))?t:[]).find(e=>e.contains(R(j))&&e instanceof HTMLElement))?l:null});St(Y,U),Tt({type:"Dialog",enabled:c(()=>0===P.value),element:g,onUpdate:(e,t)=>{if("Dialog"===t)return w(e,{[It.Add]:()=>m.value+=1,[It.Remove]:()=>m.value-=1})}});let Q=Rt({name:"DialogDescription",slot:c(()=>({open:h.value}))}),Z=O(null),ee={titleId:Z,panelRef:O(null),dialogState:P,setTitleId(e){Z.value!==e&&(Z.value=e)},close(){t("close",!1)}};X($t,ee);let te=c(()=>!(!T.value||F.value));I(B,(e,t)=>{e.preventDefault(),ee.close(),_(()=>null==t?void 0:t.focus())},te);let le=c(()=>!(F.value||0!==P.value));rt(null==(r=x.value)?void 0:r.defaultView,"keydown",e=>{le.value&&(e.defaultPrevented||e.key===G.Escape&&(e.preventDefault(),e.stopPropagation(),ee.close()))});let ne=c(()=>!(V.value||0!==P.value||M));return ht(x,ne,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],B]}}),y(e=>{if(0!==P.value)return;let t=R(g);if(!t)return;let l=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&ee.close()}});l.observe(t),e(()=>l.disconnect())}),()=>{let t=e,{open:a,initialFocus:o}=t,r=d(t,["open","initialFocus"]),v=s(u({},l),{ref:g,id:i,role:f.value,"aria-modal":0===P.value||void 0,"aria-labelledby":Z.value,"aria-describedby":Q.value}),c={open:0===P.value};return D(Pt,{force:!0},()=>[D(kt,()=>D(Bt,{target:g.value},()=>D(Pt,{force:!1},()=>D(ct,{initialFocus:o,containers:B,features:T.value?w($.value,{parent:ct.features.RestoreFocus,leaf:ct.features.All&~ct.features.FocusLock}):ct.features.None},()=>D(L,{},()=>k({ourProps:v,theirProps:u(u({},r),l),slot:c,attrs:l,slots:n,visible:0===P.value,features:K.RenderStrategy|K.Static,name:"Dialog"})))))),D(N)])}}}),Wt=x({name:"DialogOverlay",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l}){var n;let a=null!=(n=e.id)?n:`headlessui-dialog-overlay-${H()}`,o=Vt("DialogOverlay");function r(e){e.target===e.currentTarget&&(e.preventDefault(),e.stopPropagation(),o.close())}return()=>{let n=d(e,[]);return k({ourProps:{id:a,"aria-hidden":!0,onClick:r},theirProps:n,slot:{open:0===o.dialogState.value},attrs:t,slots:l,name:"DialogOverlay"})}}}),_t=x({name:"DialogBackdrop",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-dialog-backdrop-${H()}`,r=Vt("DialogBackdrop"),i=O(null);return n({el:i,$el:i}),C(()=>{if(null===r.panelRef.value)throw new Error("A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.")}),()=>{let n=d(e,[]),a={id:o,ref:i,"aria-hidden":!0};return D(Pt,{force:!0},()=>D(kt,()=>k({ourProps:a,theirProps:u(u({},t),n),slot:{open:0===r.dialogState.value},attrs:t,slots:l,name:"DialogBackdrop"})))}}}),Gt=x({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-dialog-panel-${H()}`,r=Vt("DialogPanel");function i(e){e.stopPropagation()}return n({el:r.panelRef,$el:r.panelRef}),()=>{let n=d(e,[]),a={id:o,ref:r.panelRef,onClick:i};return k({ourProps:a,theirProps:n,slot:{open:0===r.dialogState.value},attrs:t,slots:l,name:"DialogPanel"})}}}),Kt=x({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l}){var n;let a=null!=(n=e.id)?n:`headlessui-dialog-title-${H()}`,o=Vt("DialogTitle");return C(()=>{o.setTitleId(a),g(()=>o.setTitleId(null))}),()=>{let n=d(e,[]);return k({ourProps:{id:a},theirProps:n,slot:{open:0===o.dialogState.value},attrs:t,slots:l,name:"DialogTitle"})}}}),qt=Ct;var Ut,Yt=((Ut=Yt||{})[Ut.Open=0]="Open",Ut[Ut.Closed=1]="Closed",Ut);let Jt=Symbol("DisclosureContext");function Qt(e){let t=q(Jt,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Qt),t}return t}let Xt=Symbol("DisclosurePanelContext"),Zt=x({name:"Disclosure",props:{as:{type:[Object,String],default:"template"},defaultOpen:{type:[Boolean],default:!1}},setup(e,{slots:t,attrs:l}){let n=O(e.defaultOpen?0:1),a=O(null),o=O(null),r={buttonId:O(`headlessui-disclosure-button-${H()}`),panelId:O(`headlessui-disclosure-panel-${H()}`),disclosureState:n,panel:a,button:o,toggleDisclosure(){n.value=w(n.value,{0:1,1:0})},closeDisclosure(){1!==n.value&&(n.value=1)},close(e){r.closeDisclosure();let t=e?e instanceof HTMLElement?e:e.value instanceof HTMLElement?R(e):R(r.button):R(r.button);null==t||t.focus()}};return X(Jt,r),T(c(()=>w(n.value,{0:E.Open,1:E.Closed}))),()=>{let a=e,{defaultOpen:o}=a,i=d(a,["defaultOpen"]),u={open:0===n.value,close:r.close};return k({theirProps:i,ourProps:{},slot:u,slots:t,attrs:l,name:"Disclosure"})}}}),el=x({name:"DisclosureButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){let a=Qt("DisclosureButton"),o=q(Xt,null),r=c(()=>null!==o&&o.value===a.panelId.value);C(()=>{r.value||null!==e.id&&(a.buttonId.value=e.id)}),g(()=>{r.value||(a.buttonId.value=null)});let i=O(null);n({el:i,$el:i}),r.value||y(()=>{a.button.value=i.value});let u=W(c(()=>({as:e.as,type:t.type})),i);function s(){var t;e.disabled||(r.value?(a.toggleDisclosure(),null==(t=R(a.button))||t.focus()):a.toggleDisclosure())}function v(t){var l;if(!e.disabled)if(r.value)switch(t.key){case G.Space:case G.Enter:t.preventDefault(),t.stopPropagation(),a.toggleDisclosure(),null==(l=R(a.button))||l.focus()}else switch(t.key){case G.Space:case G.Enter:t.preventDefault(),t.stopPropagation(),a.toggleDisclosure()}}function p(e){e.key===G.Space&&e.preventDefault()}return()=>{var n;let o={open:0===a.disclosureState.value},c=e,{id:f}=c,m=d(c,["id"]),b=r.value?{ref:i,type:u.value,onClick:s,onKeydown:v}:{id:null!=(n=a.buttonId.value)?n:f,ref:i,type:u.value,"aria-expanded":0===a.disclosureState.value,"aria-controls":0===a.disclosureState.value||R(a.panel)?a.panelId.value:void 0,disabled:!!e.disabled||void 0,onClick:s,onKeydown:v,onKeyup:p};return k({ourProps:b,theirProps:m,slot:o,attrs:t,slots:l,name:"DisclosureButton"})}}}),tl=x({name:"DisclosurePanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){let a=Qt("DisclosurePanel");C(()=>{null!==e.id&&(a.panelId.value=e.id)}),g(()=>{a.panelId.value=null}),n({el:a.panel,$el:a.panel}),X(Xt,a.panelId);let o=J(),r=c(()=>null!==o?(o.value&E.Open)===E.Open:0===a.disclosureState.value);return()=>{var n;let o={open:0===a.disclosureState.value,close:a.close},i=e,{id:u}=i,s=d(i,["id"]),v={id:null!=(n=a.panelId.value)?n:u,ref:a.panel};return k({ourProps:v,theirProps:s,slot:o,attrs:t,slots:l,features:K.RenderStrategy|K.Static,visible:r.value,name:"DisclosurePanel"})}}});var ll,nl=((ll=nl||{})[ll.Open=0]="Open",ll[ll.Closed=1]="Closed",ll),al=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(al||{});let ol=Symbol("MenuContext");function rl(e){let t=q(ol,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,rl),t}return t}let il=x({name:"Menu",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:t,attrs:l}){let n=O(1),a=O(null),o=O(null),r=O([]),i=O(""),u=O(null),s=O(1);function d(e=e=>e){let t=null!==u.value?r.value[u.value]:null,l=Q(e(r.value.slice()),e=>R(e.dataRef.domRef)),n=t?l.indexOf(t):null;return-1===n&&(n=null),{items:l,activeItemIndex:n}}let v={menuState:n,buttonRef:a,itemsRef:o,items:r,searchQuery:i,activeItemIndex:u,activationTrigger:s,closeMenu:()=>{n.value=1,u.value=null},openMenu:()=>n.value=0,goToItem(e,t,l){let n=d(),a=z(e===V.Specific?{focus:V.Specific,id:t}:{focus:e},{resolveItems:()=>n.items,resolveActiveIndex:()=>n.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.disabled});i.value="",u.value=a,s.value=null!=l?l:1,r.value=n.items},search(e){let t=""!==i.value?0:1;i.value+=e.toLowerCase();let l=(null!==u.value?r.value.slice(u.value+t).concat(r.value.slice(0,u.value+t)):r.value).find(e=>e.dataRef.textValue.startsWith(i.value)&&!e.dataRef.disabled),n=l?r.value.indexOf(l):-1;-1===n||n===u.value||(u.value=n,s.value=1)},clearSearch(){i.value=""},registerItem(e,t){let l=d(l=>[...l,{id:e,dataRef:t}]);r.value=l.items,u.value=l.activeItemIndex,s.value=1},unregisterItem(e){let t=d(t=>{let l=t.findIndex(t=>t.id===e);return-1!==l&&t.splice(l,1),t});r.value=t.items,u.value=t.activeItemIndex,s.value=1}};return I([a,o],(e,t)=>{var l;v.closeMenu(),se(t,de.Loose)||(e.preventDefault(),null==(l=R(a))||l.focus())},c(()=>0===n.value)),X(ol,v),T(c(()=>w(n.value,{0:E.Open,1:E.Closed}))),()=>{let a={open:0===n.value,close:v.closeMenu};return k({ourProps:{},theirProps:e,slot:a,slots:t,attrs:l,name:"Menu"})}}}),ul=x({name:"MenuButton",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-menu-button-${H()}`,r=rl("MenuButton");function i(e){switch(e.key){case G.Space:case G.Enter:case G.ArrowDown:e.preventDefault(),e.stopPropagation(),r.openMenu(),_(()=>{var e;null==(e=R(r.itemsRef))||e.focus({preventScroll:!0}),r.goToItem(V.First)});break;case G.ArrowUp:e.preventDefault(),e.stopPropagation(),r.openMenu(),_(()=>{var e;null==(e=R(r.itemsRef))||e.focus({preventScroll:!0}),r.goToItem(V.Last)})}}function u(e){e.key===G.Space&&e.preventDefault()}function s(t){e.disabled||(0===r.menuState.value?(r.closeMenu(),_(()=>{var e;return null==(e=R(r.buttonRef))?void 0:e.focus({preventScroll:!0})})):(t.preventDefault(),r.openMenu(),function(e){requestAnimationFrame(()=>requestAnimationFrame(e))}(()=>{var e;return null==(e=R(r.itemsRef))?void 0:e.focus({preventScroll:!0})})))}n({el:r.buttonRef,$el:r.buttonRef});let v=W(c(()=>({as:e.as,type:t.type})),r.buttonRef);return()=>{var n;let a={open:0===r.menuState.value},c=d(e,[]),p={ref:r.buttonRef,id:o,type:v.value,"aria-haspopup":"menu","aria-controls":null==(n=R(r.itemsRef))?void 0:n.id,"aria-expanded":0===r.menuState.value,onKeydown:i,onKeyup:u,onClick:s};return k({ourProps:p,theirProps:c,slot:a,attrs:t,slots:l,name:"MenuButton"})}}}),sl=x({name:"MenuItems",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-menu-items-${H()}`,r=rl("MenuItems"),i=O(null);function u(e){var t;switch(i.value&&clearTimeout(i.value),e.key){case G.Space:if(""!==r.searchQuery.value)return e.preventDefault(),e.stopPropagation(),r.search(e.key);case G.Enter:if(e.preventDefault(),e.stopPropagation(),null!==r.activeItemIndex.value){let e=r.items.value[r.activeItemIndex.value];null==(t=R(e.dataRef.domRef))||t.click()}r.closeMenu(),ce(R(r.buttonRef));break;case G.ArrowDown:return e.preventDefault(),e.stopPropagation(),r.goToItem(V.Next);case G.ArrowUp:return e.preventDefault(),e.stopPropagation(),r.goToItem(V.Previous);case G.Home:case G.PageUp:return e.preventDefault(),e.stopPropagation(),r.goToItem(V.First);case G.End:case G.PageDown:return e.preventDefault(),e.stopPropagation(),r.goToItem(V.Last);case G.Escape:e.preventDefault(),e.stopPropagation(),r.closeMenu(),_(()=>{var e;return null==(e=R(r.buttonRef))?void 0:e.focus({preventScroll:!0})});break;case G.Tab:e.preventDefault(),e.stopPropagation(),r.closeMenu(),_(()=>pe(R(r.buttonRef),e.shiftKey?ne.Previous:ne.Next));break;default:1===e.key.length&&(r.search(e.key),i.value=setTimeout(()=>r.clearSearch(),350))}}function s(e){e.key===G.Space&&e.preventDefault()}n({el:r.itemsRef,$el:r.itemsRef}),Ve({container:c(()=>R(r.itemsRef)),enabled:c(()=>0===r.menuState.value),accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let v=J(),p=c(()=>null!==v?(v.value&E.Open)===E.Open:0===r.menuState.value);return()=>{var n,a;let i={open:0===r.menuState.value},v=d(e,[]),c={"aria-activedescendant":null===r.activeItemIndex.value||null==(n=r.items.value[r.activeItemIndex.value])?void 0:n.id,"aria-labelledby":null==(a=R(r.buttonRef))?void 0:a.id,id:o,onKeydown:u,onKeyup:s,role:"menu",tabIndex:0,ref:r.itemsRef};return k({ourProps:c,theirProps:v,slot:i,attrs:t,slots:l,features:K.RenderStrategy|K.Static,visible:p.value,name:"MenuItems"})}}}),dl=x({name:"MenuItem",inheritAttrs:!1,props:{as:{type:[Object,String],default:"template"},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-menu-item-${H()}`,r=rl("MenuItem"),i=O(null);n({el:i,$el:i});let s=c(()=>null!==r.activeItemIndex.value&&r.items.value[r.activeItemIndex.value].id===o),v=ve(i),p=c(()=>({disabled:e.disabled,get textValue(){return v()},domRef:i}));function f(t){if(e.disabled)return t.preventDefault();r.closeMenu(),ce(R(r.buttonRef))}function m(){if(e.disabled)return r.goToItem(V.Nothing);r.goToItem(V.Specific,o)}C(()=>r.registerItem(o,p)),g(()=>r.unregisterItem(o)),y(()=>{0===r.menuState.value&&s.value&&0!==r.activationTrigger.value&&_(()=>{var e,t;return null==(t=null==(e=R(i))?void 0:e.scrollIntoView)?void 0:t.call(e,{block:"nearest"})})});let b=U();function h(e){b.update(e)}function S(t){b.wasMoved(t)&&(e.disabled||s.value||r.goToItem(V.Specific,o,0))}function x(t){b.wasMoved(t)&&(e.disabled||s.value&&r.goToItem(V.Nothing))}return()=>{let n=e,{disabled:a}=n,v=d(n,["disabled"]),c={active:s.value,disabled:a,close:r.closeMenu};return k({ourProps:{id:o,ref:i,role:"menuitem",tabIndex:!0===a?void 0:-1,"aria-disabled":!0===a||void 0,onClick:f,onFocus:m,onPointerenter:h,onMouseenter:h,onPointermove:S,onMousemove:S,onPointerleave:x,onMouseleave:x},theirProps:u(u({},l),v),slot:c,attrs:l,slots:t,name:"MenuItem"})}}});var vl,cl=((vl=cl||{})[vl.Open=0]="Open",vl[vl.Closed=1]="Closed",vl);let pl=Symbol("PopoverContext");function fl(e){let t=q(pl,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <${gl.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fl),t}return t}let ml=Symbol("PopoverGroupContext");function bl(){return q(ml,null)}let hl=Symbol("PopoverPanelContext"),gl=x({name:"Popover",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:l,expose:n}){var a;let o=O(null);n({el:o,$el:o});let r=O(1),i=O(null),s=O(null),d=O(null),v=O(null),p=c(()=>S(o)),f=c(()=>{var e,t;if(!R(i)||!R(v))return!1;for(let s of document.querySelectorAll("body > *"))if(Number(null==s?void 0:s.contains(R(i)))^Number(null==s?void 0:s.contains(R(v))))return!0;let l=fe(),n=l.indexOf(R(i)),a=(n+l.length-1)%l.length,o=(n+1)%l.length,r=l[a],u=l[o];return!(null!=(e=R(v))&&e.contains(r)||null!=(t=R(v))&&t.contains(u))}),m={popoverState:r,buttonId:O(null),panelId:O(null),panel:v,button:i,isPortalled:f,beforePanelSentinel:s,afterPanelSentinel:d,togglePopover(){r.value=w(r.value,{0:1,1:0})},closePopover(){1!==r.value&&(r.value=1)},close(e){m.closePopover();let t=e?e instanceof HTMLElement?e:e.value instanceof HTMLElement?R(e):R(m.button):R(m.button);null==t||t.focus()}};X(pl,m),T(c(()=>w(r.value,{0:E.Open,1:E.Closed})));let b={buttonId:m.buttonId,panelId:m.panelId,close(){m.closePopover()}},h=bl(),g=null==h?void 0:h.registerPopover,[x,P]=At(),C=xt({mainTreeNodeRef:null==h?void 0:h.mainTreeNodeRef,portals:x,defaultContainers:[i,v]});return y(()=>null==g?void 0:g(b)),rt(null==(a=p.value)?void 0:a.defaultView,"focus",e=>{var t,l;e.target!==window&&e.target instanceof HTMLElement&&0===r.value&&(function(){var e,t,l,n;return null!=(n=null==h?void 0:h.isFocusWithinPopoverGroup())?n:(null==(e=p.value)?void 0:e.activeElement)&&((null==(t=R(i))?void 0:t.contains(p.value.activeElement))||(null==(l=R(v))?void 0:l.contains(p.value.activeElement)))}()||i&&v&&(C.contains(e.target)||null!=(t=R(m.beforePanelSentinel))&&t.contains(e.target)||null!=(l=R(m.afterPanelSentinel))&&l.contains(e.target)||m.closePopover()))},!0),I(C.resolveContainers,(e,t)=>{var l;m.closePopover(),se(t,de.Loose)||(e.preventDefault(),null==(l=R(i))||l.focus())},c(()=>0===r.value)),()=>{let n={open:0===r.value,close:m.close};return D(j,[D(P,{},()=>k({theirProps:u(u({},e),l),ourProps:{ref:o},slot:n,slots:t,attrs:l,name:"Popover"})),D(C.MainTreeNode)])}}}),yl=x({name:"PopoverButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-popover-button-${H()}`,r=fl("PopoverButton"),i=c(()=>S(r.button));n({el:r.button,$el:r.button}),C(()=>{r.buttonId.value=o}),g(()=>{r.buttonId.value=null});let s=bl(),v=null==s?void 0:s.closeOthers,p=q(hl,null),f=c(()=>null!==p&&p.value===r.panelId.value),m=O(null),b=`headlessui-focus-sentinel-${H()}`;f.value||y(()=>{r.button.value=R(m)});let h=W(c(()=>({as:e.as,type:t.type})),m);function x(e){var t,l,n,a,o;if(f.value){if(1===r.popoverState.value)return;switch(e.key){case G.Space:case G.Enter:e.preventDefault(),null==(l=(t=e.target).click)||l.call(t),r.closePopover(),null==(n=R(r.button))||n.focus()}}else switch(e.key){case G.Space:case G.Enter:e.preventDefault(),e.stopPropagation(),1===r.popoverState.value&&(null==v||v(r.buttonId.value)),r.togglePopover();break;case G.Escape:if(0!==r.popoverState.value)return null==v?void 0:v(r.buttonId.value);if(!R(r.button)||null!=(a=i.value)&&a.activeElement&&(null==(o=R(r.button))||!o.contains(i.value.activeElement)))return;e.preventDefault(),e.stopPropagation(),r.closePopover()}}function P(e){f.value||e.key===G.Space&&e.preventDefault()}function I(t){var l,n;e.disabled||(f.value?(r.closePopover(),null==(l=R(r.button))||l.focus()):(t.preventDefault(),t.stopPropagation(),1===r.popoverState.value&&(null==v||v(r.buttonId.value)),r.togglePopover(),null==(n=R(r.button))||n.focus()))}function T(e){e.preventDefault(),e.stopPropagation()}let E=ut();function F(){let e=R(r.panel);e&&w(E.value,{[it.Forwards]:()=>le(e,ne.First),[it.Backwards]:()=>le(e,ne.Last)})===oe.Error&&le(fe().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),w(E.value,{[it.Forwards]:ne.Next,[it.Backwards]:ne.Previous}),{relativeTo:R(r.button)})}return()=>{let n=0===r.popoverState.value,a={open:n},i=d(e,[]),s=f.value?{ref:m,type:h.value,onKeydown:x,onClick:I}:{ref:m,id:o,type:h.value,"aria-expanded":0===r.popoverState.value,"aria-controls":R(r.panel)?r.panelId.value:void 0,disabled:!!e.disabled||void 0,onKeydown:x,onKeyup:P,onClick:I,onMousedown:T};return D(j,[k({ourProps:s,theirProps:u(u({},t),i),slot:a,attrs:t,slots:l,name:"PopoverButton"}),n&&!f.value&&r.isPortalled.value&&D(M,{id:b,features:L.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:F})])}}}),Sl=x({name:"PopoverOverlay",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0}},setup(e,{attrs:t,slots:l}){let n=fl("PopoverOverlay"),a=`headlessui-popover-overlay-${H()}`,o=J(),r=c(()=>null!==o?(o.value&E.Open)===E.Open:0===n.popoverState.value);function i(){n.closePopover()}return()=>{let o={open:0===n.popoverState.value};return k({ourProps:{id:a,"aria-hidden":!0,onClick:i},theirProps:e,slot:o,attrs:t,slots:l,features:K.RenderStrategy|K.Static,visible:r.value,name:"PopoverOverlay"})}}}),xl=x({name:"PopoverPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-popover-panel-${H()}`,{focus:r}=e,i=fl("PopoverPanel"),v=c(()=>S(i.panel)),p=`headlessui-focus-sentinel-before-${H()}`,f=`headlessui-focus-sentinel-after-${H()}`;n({el:i.panel,$el:i.panel}),C(()=>{i.panelId.value=o}),g(()=>{i.panelId.value=null}),X(hl,i.panelId),y(()=>{var e,t;if(!r||0!==i.popoverState.value||!i.panel)return;let l=null==(e=v.value)?void 0:e.activeElement;null!=(t=R(i.panel))&&t.contains(l)||le(R(i.panel),ne.First)});let m=J(),b=c(()=>null!==m?(m.value&E.Open)===E.Open:0===i.popoverState.value);function h(e){var t,l;if(e.key===G.Escape){if(0!==i.popoverState.value||!R(i.panel)||v.value&&(null==(t=R(i.panel))||!t.contains(v.value.activeElement)))return;e.preventDefault(),e.stopPropagation(),i.closePopover(),null==(l=R(i.button))||l.focus()}}function x(e){var t,l,n,a,o;let r=e.relatedTarget;r&&R(i.panel)&&(null!=(t=R(i.panel))&&t.contains(r)||(i.closePopover(),(null!=(n=null==(l=R(i.beforePanelSentinel))?void 0:l.contains)&&n.call(l,r)||null!=(o=null==(a=R(i.afterPanelSentinel))?void 0:a.contains)&&o.call(a,r))&&r.focus({preventScroll:!0})))}let O=ut();function P(){let e=R(i.panel);e&&w(O.value,{[it.Forwards]:()=>{var t;le(e,ne.First)===oe.Error&&(null==(t=R(i.afterPanelSentinel))||t.focus())},[it.Backwards]:()=>{var e;null==(e=R(i.button))||e.focus({preventScroll:!0})}})}function I(){let e=R(i.panel);e&&w(O.value,{[it.Forwards]:()=>{let e=R(i.button),t=R(i.panel);if(!e)return;let l=fe(),n=l.indexOf(e),a=l.slice(0,n+1),o=[...l.slice(n+1),...a];for(let r of o.slice())if("true"===r.dataset.headlessuiFocusGuard||null!=t&&t.contains(r)){let e=o.indexOf(r);-1!==e&&o.splice(e,1)}le(o,ne.First,{sorted:!1})},[it.Backwards]:()=>{var t;le(e,ne.Previous)===oe.Error&&(null==(t=R(i.button))||t.focus())}})}return()=>{let n={open:0===i.popoverState.value,close:i.close},a=e,{focus:v}=a,c=d(a,["focus"]),m={ref:i.panel,id:o,onKeydown:h,onFocusout:r&&0===i.popoverState.value?x:void 0,tabIndex:-1};return k({ourProps:m,theirProps:u(u({},t),c),attrs:t,slot:n,slots:s(u({},l),{default:(...e)=>{var t;return[D(j,[b.value&&i.isPortalled.value&&D(M,{id:p,ref:i.beforePanelSentinel,features:L.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:P}),null==(t=l.default)?void 0:t.call(l,...e),b.value&&i.isPortalled.value&&D(M,{id:f,ref:i.afterPanelSentinel,features:L.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:I})])]}}),features:K.RenderStrategy|K.Static,visible:b.value,name:"PopoverPanel"})}}}),Ol=x({name:"PopoverGroup",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:t,slots:l,expose:n}){let a=O(null),o=f([]),r=c(()=>S(a)),i=function(){let e=O(null);return{mainTreeNodeRef:e,MainTreeNode:()=>D(M,{features:L.Hidden,ref:e})}}();function s(e){let t=o.value.indexOf(e);-1!==t&&o.value.splice(t,1)}return n({el:a,$el:a}),X(ml,{registerPopover:function(e){return o.value.push(e),()=>{s(e)}},unregisterPopover:s,isFocusWithinPopoverGroup:function(){var e;let t=r.value;if(!t)return!1;let l=t.activeElement;return!(null==(e=R(a))||!e.contains(l))||o.value.some(e=>{var n,a;return(null==(n=t.getElementById(e.buttonId.value))?void 0:n.contains(l))||(null==(a=t.getElementById(e.panelId.value))?void 0:a.contains(l))})},closeOthers:function(e){for(let t of o.value)t.buttonId.value!==e&&t.close()},mainTreeNodeRef:i.mainTreeNodeRef}),()=>D(j,[k({ourProps:{ref:a},theirProps:u(u({},e),t),slot:{},attrs:t,slots:l,name:"PopoverGroup"}),D(i.MainTreeNode)])}}),Pl=Symbol("LabelContext");function wl(){let e=q(Pl,null);if(null===e){let e=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,wl),e}return e}function Il({slot:e={},name:t="Label",props:l={}}={}){let n=O([]);return X(Pl,{register:function(e){return n.value.push(e),()=>{let t=n.value.indexOf(e);-1!==t&&n.value.splice(t,1)}},slot:e,name:t,props:l}),c(()=>n.value.length>0?n.value.join(" "):void 0)}let Tl=x({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:l}){var n;let a=null!=(n=e.id)?n:`headlessui-label-${H()}`,o=wl();return C(()=>g(o.register(a))),()=>{let{name:n="Label",slot:r={},props:i={}}=o,v=e,{passive:c}=v,f=d(v,["passive"]),m=s(u({},Object.entries(i).reduce((e,[t,l])=>Object.assign(e,{[t]:p(l)}),{})),{id:a});return c&&(delete m.onClick,delete m.htmlFor,delete f.onClick),k({ourProps:m,theirProps:f,slot:r,attrs:l,slots:t,name:n})}}});function El(e,t){return e===t}let Rl=Symbol("RadioGroupContext");function Cl(e){let t=q(Rl,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Cl),t}return t}let Dl=x({name:"RadioGroup",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"div"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>El},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{emit:t,attrs:l,slots:n,expose:a}){var o;let r=null!=(o=e.id)?o:`headlessui-radiogroup-${H()}`,i=O(null),s=O([]),v=Il({name:"RadioGroupLabel"}),p=Rt({name:"RadioGroupDescription"});a({el:i,$el:i});let[f,b]=P(c(()=>e.modelValue),e=>t("update:modelValue",e),c(()=>e.defaultValue)),h={options:s,value:f,disabled:c(()=>e.disabled),firstOption:c(()=>s.value.find(e=>!e.propsRef.disabled)),containsCheckedOption:c(()=>s.value.some(t=>h.compare(N(t.propsRef.value),N(e.modelValue)))),compare(t,l){if("string"==typeof e.by){let n=e.by;return(null==t?void 0:t[n])===(null==l?void 0:l[n])}return e.by(t,l)},change(t){var l;if(e.disabled||h.compare(N(f.value),N(t)))return!1;let n=null==(l=s.value.find(e=>h.compare(N(e.propsRef.value),N(t))))?void 0:l.propsRef;return!(null!=n&&n.disabled||(b(t),0))},registerOption(e){s.value.push(e),s.value=Q(s.value,e=>e.element)},unregisterOption(e){let t=s.value.findIndex(t=>t.id===e);-1!==t&&s.value.splice(t,1)}};function g(e){if(!i.value||!i.value.contains(e.target))return;let t=s.value.filter(e=>!1===e.propsRef.disabled).map(e=>e.element);switch(e.key){case G.Enter:me(e.currentTarget);break;case G.ArrowLeft:case G.ArrowUp:if(e.preventDefault(),e.stopPropagation(),le(t,ne.Previous|ne.WrapAround)===oe.Success){let e=s.value.find(e=>{var t;return e.element===(null==(t=S(i))?void 0:t.activeElement)});e&&h.change(e.propsRef.value)}break;case G.ArrowRight:case G.ArrowDown:if(e.preventDefault(),e.stopPropagation(),le(t,ne.Next|ne.WrapAround)===oe.Success){let e=s.value.find(e=>{var t;return e.element===(null==(t=S(e.element))?void 0:t.activeElement)});e&&h.change(e.propsRef.value)}break;case G.Space:{e.preventDefault(),e.stopPropagation();let t=s.value.find(e=>{var t;return e.element===(null==(t=S(e.element))?void 0:t.activeElement)});t&&h.change(t.propsRef.value)}}}X(Rl,h),Ve({container:c(()=>R(i)),accept:e=>"radio"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let y=c(()=>{var e;return null==(e=R(i))?void 0:e.closest("form")});return C(()=>{m([y],()=>{if(y.value&&void 0!==e.defaultValue)return y.value.addEventListener("reset",t),()=>{var e;null==(e=y.value)||e.removeEventListener("reset",t)};function t(){h.change(e.defaultValue)}},{immediate:!0})}),()=>{let t=e,{disabled:a,name:o,form:s}=t,c=d(t,["disabled","name","form"]),m={ref:i,id:r,role:"radiogroup","aria-labelledby":v.value,"aria-describedby":p.value,onKeydown:g};return D(j,[...null!=o&&null!=f.value?F({[o]:f.value}).map(([e,t])=>D(M,A({features:L.Hidden,key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:s,disabled:a,name:e,value:t}))):[],k({ourProps:m,theirProps:u(u({},l),B(c,["modelValue","defaultValue","by"])),slot:{},attrs:l,slots:n,name:"RadioGroup"})])}}});var Fl,kl=((Fl=kl||{})[Fl.Empty=1]="Empty",Fl[Fl.Active=2]="Active",Fl);let Ml=x({name:"RadioGroupOption",props:{as:{type:[Object,String],default:"div"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-radiogroup-option-${H()}`,r=Cl("RadioGroupOption"),i=Il({name:"RadioGroupLabel"}),u=Rt({name:"RadioGroupDescription"}),s=O(null),v=c(()=>({value:e.value,disabled:e.disabled})),p=O(1);n({el:s,$el:s});let f=c(()=>R(s));C(()=>r.registerOption({id:o,element:f,propsRef:v})),g(()=>r.unregisterOption(o));let m=c(()=>{var e;return(null==(e=r.firstOption.value)?void 0:e.id)===o}),b=c(()=>r.disabled.value||e.disabled),h=c(()=>r.compare(N(r.value.value),N(e.value))),y=c(()=>b.value?-1:h.value||!r.containsCheckedOption.value&&m.value?0:-1);function S(){var t;r.change(e.value)&&(p.value|=2,null==(t=R(s))||t.focus())}function x(){p.value|=2}function P(){p.value&=-3}return()=>{let n=e,{value:a,disabled:r}=n,v=d(n,["value","disabled"]),c={checked:h.value,disabled:b.value,active:Boolean(2&p.value)},f={id:o,ref:s,role:"radio","aria-checked":h.value?"true":"false","aria-labelledby":i.value,"aria-describedby":u.value,"aria-disabled":!!b.value||void 0,tabIndex:y.value,onClick:b.value?void 0:S,onFocus:b.value?void 0:x,onBlur:b.value?void 0:P};return k({ourProps:f,theirProps:v,slot:c,attrs:t,slots:l,name:"RadioGroupOption"})}}}),Al=Tl,Ll=Ct,Bl=Symbol("GroupContext"),jl=x({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:t,attrs:l}){let n=O(null),a=Il({name:"SwitchLabel",props:{htmlFor:c(()=>{var e;return null==(e=n.value)?void 0:e.id}),onClick(e){n.value&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),n.value.click(),n.value.focus({preventScroll:!0}))}}}),o=Rt({name:"SwitchDescription"});return X(Bl,{switchRef:n,labelledby:a,describedby:o}),()=>k({theirProps:e,ourProps:{},slot:{},slots:t,attrs:l,name:"SwitchGroup"})}}),Nl=x({name:"Switch",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(e,{emit:t,attrs:l,slots:n,expose:a}){var o;let r=null!=(o=e.id)?o:`headlessui-switch-${H()}`,i=q(Bl,null),[s,v]=P(c(()=>e.modelValue),e=>t("update:modelValue",e),c(()=>e.defaultChecked));function p(){v(!s.value)}let f=O(null),b=null===i?f:i.switchRef,h=W(c(()=>({as:e.as,type:l.type})),b);function g(e){e.preventDefault(),p()}function y(e){e.key===G.Space?(e.preventDefault(),p()):e.key===G.Enter&&me(e.currentTarget)}function S(e){e.preventDefault()}a({el:b,$el:b});let x=c(()=>{var e,t;return null==(t=null==(e=R(b))?void 0:e.closest)?void 0:t.call(e,"form")});return C(()=>{m([x],()=>{if(x.value&&void 0!==e.defaultChecked)return x.value.addEventListener("reset",t),()=>{var e;null==(e=x.value)||e.removeEventListener("reset",t)};function t(){v(e.defaultChecked)}},{immediate:!0})}),()=>{let t=e,{name:a,value:o,form:v,tabIndex:c}=t,p=d(t,["name","value","form","tabIndex"]),f={checked:s.value},m={id:r,ref:b,role:"switch",type:h.value,tabIndex:-1===c?0:c,"aria-checked":s.value,"aria-labelledby":null==i?void 0:i.labelledby.value,"aria-describedby":null==i?void 0:i.describedby.value,onClick:g,onKeyup:y,onKeypress:S};return D(j,[null!=a&&null!=s.value?D(M,A({features:L.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:s.value,form:v,disabled:p.disabled,name:a,value:o})):null,k({ourProps:m,theirProps:u(u({},l),B(p,["modelValue","defaultChecked"])),slot:f,attrs:l,slots:n,name:"Switch"})])}}}),$l=Tl,Vl=Ct,zl=x({props:{onFocus:{type:Function,required:!0}},setup(e){let t=O(!0);return()=>t.value?D(M,{as:"button",type:"button",features:L.Focusable,onFocus(l){l.preventDefault();let n,a=50;n=requestAnimationFrame(function l(){var o;if(!(a--<=0))return null!=(o=e.onFocus)&&o.call(e)?(t.value=!1,void cancelAnimationFrame(n)):void(n=requestAnimationFrame(l));n&&cancelAnimationFrame(n)})}}):null}});var Hl,Wl=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(Wl||{}),_l=((Hl=_l||{})[Hl.Less=-1]="Less",Hl[Hl.Equal=0]="Equal",Hl[Hl.Greater=1]="Greater",Hl);let Gl=Symbol("TabsContext");function Kl(e){let t=q(Gl,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Kl),t}return t}let ql=Symbol("TabsSSRContext"),Ul=x({name:"TabGroup",emits:{change:e=>!0},props:{as:{type:[Object,String],default:"template"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:t,attrs:l,emit:n}){var a;let o=O(null!=(a=e.selectedIndex)?a:e.defaultIndex),r=O([]),i=O([]),s=c(()=>null!==e.selectedIndex),d=c(()=>s.value?e.selectedIndex:o.value);function v(e){var t;let l=Q(p.tabs.value,R),n=Q(p.panels.value,R),a=l.filter(e=>{var t;return!(null!=(t=R(e))&&t.hasAttribute("disabled"))});if(e<0||e>l.length-1){let t=w(null===o.value?0:Math.sign(e-o.value),{[-1]:()=>1,0:()=>w(Math.sign(e),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0}),r=w(t,{0:()=>l.indexOf(a[0]),1:()=>l.indexOf(a[a.length-1])});-1!==r&&(o.value=r),p.tabs.value=l,p.panels.value=n}else{let r=l.slice(0,e),i=[...l.slice(e),...r].find(e=>a.includes(e));if(!i)return;let u=null!=(t=l.indexOf(i))?t:p.selectedIndex.value;-1===u&&(u=p.selectedIndex.value),o.value=u,p.tabs.value=l,p.panels.value=n}}let p={selectedIndex:c(()=>{var t,l;return null!=(l=null!=(t=o.value)?t:e.defaultIndex)?l:null}),orientation:c(()=>e.vertical?"vertical":"horizontal"),activation:c(()=>e.manual?"manual":"auto"),tabs:r,panels:i,setSelectedIndex(e){d.value!==e&&n("change",e),s.value||v(e)},registerTab(e){var t;if(r.value.includes(e))return;let l=r.value[o.value];if(r.value.push(e),r.value=Q(r.value,R),!s.value){let e=null!=(t=r.value.indexOf(l))?t:o.value;-1!==e&&(o.value=e)}},unregisterTab(e){let t=r.value.indexOf(e);-1!==t&&r.value.splice(t,1)},registerPanel(e){i.value.includes(e)||(i.value.push(e),i.value=Q(i.value,R))},unregisterPanel(e){let t=i.value.indexOf(e);-1!==t&&i.value.splice(t,1)}};X(Gl,p);let f=O({tabs:[],panels:[]}),b=O(!1);C(()=>{b.value=!0}),X(ql,c(()=>b.value?null:f.value));let h=c(()=>e.selectedIndex);return C(()=>{m([h],()=>{var t;return v(null!=(t=e.selectedIndex)?t:e.defaultIndex)},{immediate:!0})}),y(()=>{if(!s.value||null==d.value||p.tabs.value.length<=0)return;let e=Q(p.tabs.value,R);e.some((e,t)=>R(p.tabs.value[t])!==R(e))&&p.setSelectedIndex(e.findIndex(e=>R(e)===R(p.tabs.value[d.value])))}),()=>{let n={selectedIndex:o.value};return D(j,[r.value.length<=0&&D(zl,{onFocus:()=>{for(let e of r.value){let t=R(e);if(0===(null==t?void 0:t.tabIndex))return t.focus(),!0}return!1}}),k({theirProps:u(u({},l),B(e,["selectedIndex","defaultIndex","manual","vertical","onChange"])),ourProps:{},slot:n,slots:t,attrs:l,name:"TabGroup"})])}}}),Yl=x({name:"TabList",props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:t,slots:l}){let n=Kl("TabList");return()=>{let a={selectedIndex:n.selectedIndex.value},o={role:"tablist","aria-orientation":n.orientation.value};return k({ourProps:o,theirProps:e,slot:a,attrs:t,slots:l,name:"TabList"})}}}),Jl=x({name:"Tab",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-tabs-tab-${H()}`,r=Kl("Tab"),i=O(null);n({el:i,$el:i}),C(()=>r.registerTab(i)),g(()=>r.unregisterTab(i));let u=q(ql),s=c(()=>{if(u.value){let e=u.value.tabs.indexOf(o);return-1===e?u.value.tabs.push(o)-1:e}return-1}),v=c(()=>{let e=r.tabs.value.indexOf(i);return-1===e?s.value:e}),p=c(()=>v.value===r.selectedIndex.value);function f(e){var t;let l=e();if(l===oe.Success&&"auto"===r.activation.value){let e=null==(t=S(i))?void 0:t.activeElement,l=r.tabs.value.findIndex(t=>R(t)===e);-1!==l&&r.setSelectedIndex(l)}return l}function m(e){let t=r.tabs.value.map(e=>R(e)).filter(Boolean);if(e.key===G.Space||e.key===G.Enter)return e.preventDefault(),e.stopPropagation(),void r.setSelectedIndex(v.value);switch(e.key){case G.Home:case G.PageUp:return e.preventDefault(),e.stopPropagation(),f(()=>le(t,ne.First));case G.End:case G.PageDown:return e.preventDefault(),e.stopPropagation(),f(()=>le(t,ne.Last))}return f(()=>w(r.orientation.value,{vertical:()=>e.key===G.ArrowUp?le(t,ne.Previous|ne.WrapAround):e.key===G.ArrowDown?le(t,ne.Next|ne.WrapAround):oe.Error,horizontal:()=>e.key===G.ArrowLeft?le(t,ne.Previous|ne.WrapAround):e.key===G.ArrowRight?le(t,ne.Next|ne.WrapAround):oe.Error}))===oe.Success?e.preventDefault():void 0}let b=O(!1);function h(){var t;b.value||(b.value=!0,!e.disabled&&(null==(t=R(i))||t.focus({preventScroll:!0}),r.setSelectedIndex(v.value),je(()=>{b.value=!1})))}function y(e){e.preventDefault()}let x=W(c(()=>({as:e.as,type:t.type})),i);return()=>{var n,a;let u={selected:p.value,disabled:null!=(n=e.disabled)&&n},s=d(e,[]),c={ref:i,onKeydown:m,onMousedown:y,onClick:h,id:o,role:"tab",type:x.value,"aria-controls":null==(a=R(r.panels.value[v.value]))?void 0:a.id,"aria-selected":p.value,tabIndex:p.value?0:-1,disabled:!!e.disabled||void 0};return k({ourProps:c,theirProps:s,slot:u,attrs:t,slots:l,name:"Tab"})}}}),Ql=x({name:"TabPanels",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:l}){let n=Kl("TabPanels");return()=>{let a={selectedIndex:n.selectedIndex.value};return k({theirProps:e,ourProps:{},slot:a,attrs:l,slots:t,name:"TabPanels"})}}}),Xl=x({name:"TabPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null},tabIndex:{type:Number,default:0}},setup(e,{attrs:t,slots:l,expose:n}){var a;let o=null!=(a=e.id)?a:`headlessui-tabs-panel-${H()}`,r=Kl("TabPanel"),i=O(null);n({el:i,$el:i}),C(()=>r.registerPanel(i)),g(()=>r.unregisterPanel(i));let s=q(ql),v=c(()=>{if(s.value){let e=s.value.panels.indexOf(o);return-1===e?s.value.panels.push(o)-1:e}return-1}),p=c(()=>{let e=r.panels.value.indexOf(i);return-1===e?v.value:e}),f=c(()=>p.value===r.selectedIndex.value);return()=>{var n;let a={selected:f.value},s=e,{tabIndex:v}=s,c=d(s,["tabIndex"]),m={ref:i,id:o,role:"tabpanel","aria-labelledby":null==(n=R(r.tabs.value[p.value]))?void 0:n.id,tabIndex:f.value?v:-1};return f.value||!e.unmount||e.static?k({ourProps:m,theirProps:c,slot:a,attrs:t,slots:l,features:K.Static|K.RenderStrategy,visible:f.value,name:"TabPanel"}):D(M,u({as:"span","aria-hidden":!0},m))}}});function Zl(e,...t){e&&t.length>0&&e.classList.add(...t)}function en(e,...t){e&&t.length>0&&e.classList.remove(...t)}var tn=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(tn||{});function ln(e,t,l,n,a,o){let r=Ne(),i=void 0!==o?function(e){let t={called:!1};return(...l)=>{if(!t.called)return t.called=!0,e(...l)}}(o):()=>{};return en(e,...a),Zl(e,...t,...l),r.nextFrame(()=>{en(e,...l),Zl(e,...n),r.add(function(e,t){let l=Ne();if(!e)return l.dispose;let{transitionDuration:n,transitionDelay:a}=getComputedStyle(e),[o,r]=[n,a].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t});return 0!==o?l.setTimeout(()=>t("finished"),o+r):t("finished"),l.add(()=>t("cancelled")),l.dispose}(e,l=>(en(e,...n,...t),Zl(e,...a),i(l))))}),r.add(()=>en(e,...t,...l,...n,...a)),r.add(()=>i("cancelled")),r.dispose}function nn(e=""){return e.split(/\s+/).filter(e=>e.length>1)}let an=Symbol("TransitionContext");var on,rn=((on=rn||{}).Visible="visible",on.Hidden="hidden",on);let un=Symbol("NestingContext");function sn(e){return"children"in e?sn(e.children):e.value.filter(({state:e})=>"visible"===e).length>0}function dn(e){let t=O([]),l=O(!1);function n(n,a=he.Hidden){let o=t.value.findIndex(({id:e})=>e===n);-1!==o&&(w(a,{[he.Unmount](){t.value.splice(o,1)},[he.Hidden](){t.value[o].state="hidden"}}),!sn(t)&&l.value&&(null==e||e()))}return C(()=>l.value=!0),g(()=>l.value=!1),{children:t,register:function(e){let l=t.value.find(({id:t})=>t===e);return l?"visible"!==l.state&&(l.state="visible"):t.value.push({id:e,state:"visible"}),()=>n(e,he.Unmount)},unregister:n}}let vn=K.RenderStrategy,cn=x({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:l,slots:n,expose:a}){let o=O(0);function r(){o.value|=E.Opening,t("beforeEnter")}function i(){o.value&=~E.Opening,t("afterEnter")}function v(){o.value|=E.Closing,t("beforeLeave")}function p(){o.value&=~E.Closing,t("afterLeave")}if(null===q(an,null)&&be())return()=>D(fn,s(u({},e),{onBeforeEnter:r,onAfterEnter:i,onBeforeLeave:v,onAfterLeave:p}),n);let f=O(null),b=c(()=>e.unmount?he.Unmount:he.Hidden);a({el:f,$el:f});let{show:h,appear:S}=function(){let e=q(an,null);if(null===e)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}(),{register:x,unregister:P}=function(){let e=q(un,null);if(null===e)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}(),I=O(h.value?"visible":"hidden"),F={value:!0},M=H(),A={value:!1},L=dn(()=>{!A.value&&"hidden"!==I.value&&(I.value="hidden",P(M),p())});C(()=>{let e=x(M);g(e)}),y(()=>{if(b.value===he.Hidden&&M){if(h.value&&"visible"!==I.value)return void(I.value="visible");w(I.value,{hidden:()=>P(M),visible:()=>x(M)})}});let B=nn(e.enter),j=nn(e.enterFrom),N=nn(e.enterTo),$=nn(e.entered),V=nn(e.leave),z=nn(e.leaveFrom),W=nn(e.leaveTo);return C(()=>{y(()=>{if("visible"===I.value){let e=R(f);if(e instanceof Comment&&""===e.data)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})}),C(()=>{m([h],(e,t,l)=>{(function(e){let t=F.value&&!S.value,l=R(f);!l||!(l instanceof HTMLElement)||t||(A.value=!0,h.value&&r(),h.value||v(),e(h.value?ln(l,B,j,N,$,e=>{A.value=!1,e===tn.Finished&&i()}):ln(l,V,z,W,$,e=>{A.value=!1,e===tn.Finished&&(sn(L)||(I.value="hidden",P(M),p()))})))})(l),F.value=!1},{immediate:!0})}),X(un,L),T(c(()=>w(I.value,{visible:E.Open,hidden:E.Closed})|o.value)),()=>{let t=e,{appear:a,show:o,enter:r,enterFrom:i,enterTo:s,entered:v,leave:c,leaveFrom:p,leaveTo:m}=t,b=d(t,["appear","show","enter","enterFrom","enterTo","entered","leave","leaveFrom","leaveTo"]),g={ref:f},y=u(u({},b),S.value&&h.value&&ee.isServer?{class:ge([l.class,b.class,...B,...j])}:{});return k({theirProps:y,ourProps:g,slot:{},slots:n,attrs:l,features:vn,visible:"visible"===I.value,name:"TransitionChild"})}}}),pn=cn,fn=x({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:l,slots:n}){let a=J(),o=c(()=>null===e.show&&null!==a?(a.value&E.Open)===E.Open:e.show);y(()=>{if(![!0,!1].includes(o.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let r=O(o.value?"visible":"hidden"),i=dn(()=>{r.value="hidden"}),d=O(!0),v={show:o,appear:c(()=>e.appear||!d.value)};return C(()=>{y(()=>{d.value=!1,o.value?r.value="visible":sn(i)||(r.value="hidden")})}),X(un,i),X(an,v),()=>{let a=B(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),o={unmount:e.unmount};return k({ourProps:s(u({},o),{as:"template"}),theirProps:{},slot:{},slots:s(u({},n),{default:()=>[D(pn,u(u(u({onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave")},l),o),a),n.default)]}),attrs:{},features:vn,visible:"visible"===r.value,name:"Transition"})}}});const mn=Object.freeze(Object.defineProperty({__proto__:null,Combobox:et,ComboboxButton:lt,ComboboxInput:nt,ComboboxLabel:tt,ComboboxOption:ot,ComboboxOptions:at,Dialog:Ht,DialogBackdrop:_t,DialogDescription:qt,DialogOverlay:Wt,DialogPanel:Gt,DialogTitle:Kt,Disclosure:Zt,DisclosureButton:el,DisclosurePanel:tl,FocusTrap:ct,Listbox:ye,ListboxButton:Se,ListboxLabel:xe,ListboxOption:Oe,ListboxOptions:Pe,Menu:il,MenuButton:ul,MenuItem:dl,MenuItems:sl,Popover:gl,PopoverButton:yl,PopoverGroup:Ol,PopoverOverlay:Sl,PopoverPanel:xl,Portal:kt,PortalGroup:Bt,RadioGroup:Dl,RadioGroupDescription:Ll,RadioGroupLabel:Al,RadioGroupOption:Ml,Switch:Nl,SwitchDescription:Vl,SwitchGroup:jl,SwitchLabel:$l,Tab:Jl,TabGroup:Ul,TabList:Yl,TabPanel:Xl,TabPanels:Ql,TransitionChild:cn,TransitionRoot:fn,provideUseId:we},Symbol.toStringTag,{value:"Module"}));window.Vue={createApp:Te,createPinia:Ie},window.HeadlessUI=mn,window.axios=Ee,Ee.defaults.xsrfCookieName="csrftoken",Ee.defaults.xsrfHeaderName="X-CSRFToken",Ee.interceptors.request.use(e=>{var t;const l=null==(t=window.FinderV2Config)?void 0:t.csrfToken;return l&&(e.headers["X-CSRFToken"]=l),e}),Ee.interceptors.response.use(e=>e,t=>v(e,null,function*(){var l,n,a;const o=t.config;if(403===(null==(l=t.response)?void 0:l.status)&&"challenge_required"===(null==(a=null==(n=t.response)?void 0:n.data)?void 0:a.error)&&!o._challengeRetry){o._challengeRetry=!0;const{useBotProtection:t}=yield Re(()=>v(e,null,function*(){const{useBotProtection:e}=yield import("./useBotProtection-DKPGrmyF.js");return{useBotProtection:e}}),__vite__mapDeps([0,1])),l=t();if(l.fingerprintCollected.value||(yield l.initialize()),yield l.solveAndVerifyChallenge())return Ee.request(o)}return Promise.reject(t)}))}},function(){return De||(0,Ce[n(Ce)[0]])((De={exports:{}}).exports,De),De.exports});export default Fe();
