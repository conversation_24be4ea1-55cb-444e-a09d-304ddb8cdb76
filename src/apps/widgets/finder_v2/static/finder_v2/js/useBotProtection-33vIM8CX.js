var e=(e,n,o)=>new Promise((t,i)=>{var r=e=>{try{a(o.next(e))}catch(n){i(n)}},l=e=>{try{a(o.throw(e))}catch(n){i(n)}},a=e=>e.done?t(e.value):Promise.resolve(e.value).then(r,l);a((o=o.apply(e,n)).next())});import{a as n}from"./http-client-5tgu7n7k.js";import{r as o,e as t}from"./vue-core-BvvXclHU.js";function i(){const i=o(!1),r=o(""),l=o({}),a=o(!1);function c(){var e,n,o,t,i,r,c,w,g;const h={screen_width:(null==(e=window.screen)?void 0:e.width)||0,screen_height:(null==(n=window.screen)?void 0:n.height)||0,screen_available_width:(null==(o=window.screen)?void 0:o.availWidth)||0,screen_available_height:(null==(t=window.screen)?void 0:t.availHeight)||0,color_depth:(null==(i=window.screen)?void 0:i.colorDepth)||0,pixel_depth:(null==(r=window.screen)?void 0:r.pixelDepth)||0,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timezone_offset:(new Date).getTimezoneOffset(),language:navigator.language||"",languages:(null==(c=navigator.languages)?void 0:c.join(","))||"",platform:navigator.platform||"",user_agent:navigator.userAgent||"",cookies_enabled:navigator.cookieEnabled,online:navigator.onLine,do_not_track:navigator.doNotTrack||"unspecified",hardware_concurrency:navigator.hardwareConcurrency||0,max_touch_points:navigator.maxTouchPoints||0,plugins_count:(null==(w=navigator.plugins)?void 0:w.length)||0,canvas_hash:s(),webgl_vendor:u(),webgl_renderer:d(),touch_support:"ontouchstart"in window||navigator.maxTouchPoints>0,media_devices:!!navigator.mediaDevices,webrtc_enabled:!!window.RTCPeerConnection,websocket_enabled:!!window.WebSocket,session_storage:!!window.sessionStorage,local_storage:!!window.localStorage,indexed_db:!!window.indexedDB,connection_type:(null==(g=navigator.connection)?void 0:g.effectiveType)||"unknown",window_outer_width:window.outerWidth,window_outer_height:window.outerHeight,window_inner_width:window.innerWidth,window_inner_height:window.innerHeight,timestamp:Date.now()};return l.value=h,a.value=!0,h}function s(){try{const e=document.createElement("canvas"),n=e.getContext("2d");return n?(e.width=200,e.height=50,n.textBaseline="alphabetic",n.fillStyle="#f60",n.fillRect(125,1,62,20),n.fillStyle="#069",n.font="11pt Arial",n.fillText("Canvas fingerprint 🛡️",2,15),n.fillStyle="rgba(102, 204, 0, 0.7)",n.font="18pt Arial",n.fillText("Widget Protection",4,45),function(e){let n=0;if(0===e.length)return"0";for(let o=0;o<e.length;o++)n=(n<<5)-n+e.charCodeAt(o),n&=n;return Math.abs(n).toString(16)}(e.toDataURL()).substring(0,32)):"canvas_not_available"}catch(e){return console.debug("Canvas fingerprinting blocked:",e.message),"canvas_blocked"}}function u(){try{const e=document.createElement("canvas"),n=e.getContext("webgl")||e.getContext("experimental-webgl");if(!n)return"webgl_not_supported";const o=n.getExtension("WEBGL_debug_renderer_info");return o?n.getParameter(o.UNMASKED_VENDOR_WEBGL)||"unknown":"debug_info_not_available"}catch(e){return console.debug("WebGL vendor detection failed:",e.message),"webgl_blocked"}}function d(){try{const e=document.createElement("canvas"),n=e.getContext("webgl")||e.getContext("experimental-webgl");if(!n)return"webgl_not_supported";const o=n.getExtension("WEBGL_debug_renderer_info");return o?n.getParameter(o.UNMASKED_RENDERER_WEBGL)||"unknown":"debug_info_not_available"}catch(e){return console.debug("WebGL renderer detection failed:",e.message),"webgl_blocked"}}function w(n=4){return e(this,null,function*(){if(!window.crypto||!window.crypto.subtle)throw console.warn("crypto.subtle not available - cannot solve real challenge"),new Error("JavaScript cryptography (crypto.subtle) is required but not available. Please use a modern browser.");const e=Math.random().toString(36).substring(2,15);let o=0;const t=Date.now(),i="0".repeat(n);for(console.log(`Solving challenge with difficulty ${n}...`);;){const l=`${e}:${o}`;let a;try{const e=(new TextEncoder).encode(l),n=yield crypto.subtle.digest("SHA-256",e);a=Array.from(new Uint8Array(n)).map(e=>e.toString(16).padStart(2,"0")).join("")}catch(r){throw new Error("Failed to compute hash: "+r.message)}if(a.startsWith(i)){const i=Date.now()-t;return console.log(`Challenge solved in ${i}ms with nonce ${o}`),{challenge:e,nonce:o,hash:a,duration:i,difficulty:n}}if(o++,o%100==0&&(yield new Promise(e=>setTimeout(e,0))),Date.now()-t>1e4)throw new Error("Challenge timeout - took too long to solve")}})}function g(){return e(this,null,function*(){try{const e=l.value;return JSON.stringify(e),n.defaults.headers.common["X-Client-Features"]=JSON.stringify(e),console.log("Browser fingerprint sent to server"),!0}catch(e){return console.error("Failed to send fingerprint:",e),!1}})}function h(){return e(this,null,function*(){try{console.log("Initializing bot protection...");const e=c();return console.log("Browser features collected:",Object.keys(e).length,"features"),yield g(),console.log("Bot protection initialized successfully"),!0}catch(e){return console.error("Bot protection initialization failed:",e),!1}})}function f(){return e(this,null,function*(){var e,o;try{return(yield n.post("/widget/api/request-challenge/",{widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(o=window.FinderV2Config)?void 0:o.id)})).data}catch(t){throw console.error("Failed to request challenge:",t),t}})}return t(()=>{h().catch(e=>{console.warn("Bot protection auto-init failed:",e)})}),{challengeSolved:i,challengeToken:r,browserFeatures:l,fingerprintCollected:a,initialize:h,collectBrowserFeatures:c,sendFingerprintToServer:g,solveChallenge:w,verifyHuman:function(){return e(this,null,function*(){var e,o;try{const t=c(),l=yield w(4),a=yield n.post("/widget/api/verify-human/",{features:t,solution:l,widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(o=window.FinderV2Config)?void 0:o.id)});return!!a.data.token&&(r.value=a.data.token,i.value=!0,n.defaults.headers.common["X-Challenge-Token"]=r.value,console.log("Human verification successful"),!0)}catch(t){return console.error("Human verification failed:",t),!1}})},requestChallenge:f,solveAndVerifyChallenge:function(){return e(this,null,function*(){var e,o;try{if(!window.crypto||!window.crypto.subtle){if(console.warn("crypto.subtle not available - likely due to insecure context (HTTP)"),"http:"===window.location.protocol)return console.warn("Running in HTTP mode - skipping challenge verification for development"),i.value=!0,r.value="development-bypass",n.defaults.headers.common["X-Challenge-Token"]="development-bypass",!0;throw new Error("Security features are not available. This may be due to browser settings or an outdated browser. Please ensure JavaScript is enabled and try using Chrome, Firefox, Safari, or Edge.")}console.log("Requesting challenge for search/by_model endpoint...");const t=yield f();console.log(`Solving challenge with difficulty ${t.difficulty}...`);const l=yield w(t.difficulty);l.challenge=t.challenge;const a=yield n.post("/widget/api/verify-challenge/",{solution:l,widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(o=window.FinderV2Config)?void 0:o.id)});return!!a.data.success&&(r.value=a.data.token,i.value=!0,n.defaults.headers.common["X-Challenge-Token"]=r.value,console.log("Challenge solved and verified successfully"),console.log(`Token valid for ${a.data.max_uses} uses`),sessionStorage.setItem("challenge_token",r.value),sessionStorage.setItem("challenge_token_time",Date.now()),!0)}catch(t){if(console.error("Challenge verification failed:",t.message),t.message.includes("crypto"))throw new Error("Your browser does not support required security features. Please use Chrome, Firefox, Safari, or Edge.");throw t}})},hasValidChallengeToken:function(){if(r.value&&i.value)return!0;const e=sessionStorage.getItem("challenge_token"),o=sessionStorage.getItem("challenge_token_time");return!!(e&&o&&Date.now()-parseInt(o)<36e5)&&(r.value=e,i.value=!0,n.defaults.headers.common["X-Challenge-Token"]=e,!0)},generateCanvasHash:s,getWebGLVendor:u,getWebGLRenderer:d}}export{i as useBotProtection};
