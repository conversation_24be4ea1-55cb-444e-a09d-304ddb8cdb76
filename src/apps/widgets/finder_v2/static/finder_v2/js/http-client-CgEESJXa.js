var t=Object.defineProperty,e=Object.defineProperties,n=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,s=(t,e)=>(e=Symbol[t])?e:Symbol.for("Symbol."+t),u=(e,n,r)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[n]=r,c=(t,e)=>{for(var n in e||(e={}))o.call(e,n)&&u(t,n,e[n]);if(r)for(var n of r(e))i.call(e,n)&&u(t,n,e[n]);return t},a=(t,e,n)=>new Promise((r,o)=>{var i=t=>{try{u(n.next(t))}catch(e){o(e)}},s=t=>{try{u(n.throw(t))}catch(e){o(e)}},u=t=>t.done?r(t.value):Promise.resolve(t.value).then(i,s);u((n=n.apply(t,e)).next())}),l=function(t,e){this[0]=t,this[1]=e},f=(t,e,n)=>{var r=(t,e,o,i)=>{try{var s=n[t](e),u=(e=s.value)instanceof l,c=s.done;Promise.resolve(u?e[0]:e).then(n=>u?r("return"===t?t:"next",e[1]?{done:n.done,value:n.value}:n,o,i):o({value:n,done:c})).catch(t=>r("throw",t,o,i))}catch(a){i(a)}},o=t=>i[t]=e=>new Promise((n,o)=>r(t,e,n,o)),i={};return n=n.apply(t,e),i[s("asyncIterator")]=()=>i,o("next"),o("throw"),o("return"),i},d=t=>{var e,n=t[s("asyncIterator")],r=!1,o={};return null==n?(n=t[s("iterator")](),e=t=>o[t]=e=>n[t](e)):(n=n.call(t),e=t=>o[t]=e=>{if(r){if(r=!1,"throw"===t)throw e;return e}return r=!0,{done:!1,value:new l(new Promise(r=>{var o=n[t](e);o instanceof Object||(()=>{throw TypeError("Object expected")})(),r(o)}),1)}}),o[s("iterator")]=()=>o,e("next"),"throw"in n?e("throw"):o.throw=t=>{throw t},"return"in n&&e("return"),o};function h(t,e){return function(){return t.apply(e,arguments)}}const{toString:p}=Object.prototype,{getPrototypeOf:m}=Object,{iterator:w,toStringTag:b}=Symbol,y=(t=>e=>{const n=p.call(e);return t[n]||(t[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),g=t=>(t=t.toLowerCase(),e=>y(e)===t),R=t=>e=>typeof e===t,{isArray:O}=Array,v=R("undefined"),E=g("ArrayBuffer"),S=R("string"),T=R("function"),j=R("number"),A=t=>null!==t&&"object"==typeof t,x=t=>{if("object"!==y(t))return!1;const e=m(t);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||b in t||w in t)},N=g("Date"),P=g("File"),C=g("Blob"),F=g("FileList"),U=g("URLSearchParams"),[B,D,_,k]=["ReadableStream","Request","Response","Headers"].map(g);function q(t,e,{allOwnKeys:n=!1}={}){if(null==t)return;let r,o;if("object"!=typeof t&&(t=[t]),O(t))for(r=0,o=t.length;r<o;r++)e.call(null,t[r],r,t);else{const o=n?Object.getOwnPropertyNames(t):Object.keys(t),i=o.length;let s;for(r=0;r<i;r++)s=o[r],e.call(null,t[s],s,t)}}function L(t,e){e=e.toLowerCase();const n=Object.keys(t);let r,o=n.length;for(;o-- >0;)if(r=n[o],e===r.toLowerCase())return r;return null}const I="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,M=t=>!v(t)&&t!==I,z=(t=>e=>t&&e instanceof t)("undefined"!=typeof Uint8Array&&m(Uint8Array)),H=g("HTMLFormElement"),J=(({hasOwnProperty:t})=>(e,n)=>t.call(e,n))(Object.prototype),$=g("RegExp"),K=(t,e)=>{const n=Object.getOwnPropertyDescriptors(t),r={};q(n,(n,o)=>{let i;!1!==(i=e(n,o,t))&&(r[o]=i||n)}),Object.defineProperties(t,r)},V=g("AsyncFunction"),X=(W="function"==typeof setImmediate,G=T(I.postMessage),W?setImmediate:G?(Z=`axios@${Math.random()}`,Q=[],I.addEventListener("message",({source:t,data:e})=>{t===I&&e===Z&&Q.length&&Q.shift()()},!1),t=>{Q.push(t),I.postMessage(Z,"*")}):t=>setTimeout(t));var W,G,Z,Q;const Y="undefined"!=typeof queueMicrotask?queueMicrotask.bind(I):"undefined"!=typeof process&&process.nextTick||X,tt={isArray:O,isArrayBuffer:E,isBuffer:function(t){return null!==t&&!v(t)&&null!==t.constructor&&!v(t.constructor)&&T(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||T(t.append)&&("formdata"===(e=y(t))||"object"===e&&T(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return e="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&E(t.buffer),e},isString:S,isNumber:j,isBoolean:t=>!0===t||!1===t,isObject:A,isPlainObject:x,isReadableStream:B,isRequest:D,isResponse:_,isHeaders:k,isUndefined:v,isDate:N,isFile:P,isBlob:C,isRegExp:$,isFunction:T,isStream:t=>A(t)&&T(t.pipe),isURLSearchParams:U,isTypedArray:z,isFileList:F,forEach:q,merge:function t(){const{caseless:e}=M(this)&&this||{},n={},r=(r,o)=>{const i=e&&L(n,o)||o;x(n[i])&&x(r)?n[i]=t(n[i],r):x(r)?n[i]=t({},r):O(r)?n[i]=r.slice():n[i]=r};for(let o=0,i=arguments.length;o<i;o++)arguments[o]&&q(arguments[o],r);return n},extend:(t,e,n,{allOwnKeys:r}={})=>(q(e,(e,r)=>{n&&T(e)?t[r]=h(e,n):t[r]=e},{allOwnKeys:r}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,n,r)=>{t.prototype=Object.create(e.prototype,r),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),n&&Object.assign(t.prototype,n)},toFlatObject:(t,e,n,r)=>{let o,i,s;const u={};if(e=e||{},null==t)return e;do{for(o=Object.getOwnPropertyNames(t),i=o.length;i-- >0;)s=o[i],r&&!r(s,t,e)||u[s]||(e[s]=t[s],u[s]=!0);t=!1!==n&&m(t)}while(t&&(!n||n(t,e))&&t!==Object.prototype);return e},kindOf:y,kindOfTest:g,endsWith:(t,e,n)=>{t=String(t),(void 0===n||n>t.length)&&(n=t.length),n-=e.length;const r=t.indexOf(e,n);return-1!==r&&r===n},toArray:t=>{if(!t)return null;if(O(t))return t;let e=t.length;if(!j(e))return null;const n=new Array(e);for(;e-- >0;)n[e]=t[e];return n},forEachEntry:(t,e)=>{const n=(t&&t[w]).call(t);let r;for(;(r=n.next())&&!r.done;){const n=r.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let n;const r=[];for(;null!==(n=t.exec(e));)r.push(n);return r},isHTMLForm:H,hasOwnProperty:J,hasOwnProp:J,reduceDescriptors:K,freezeMethods:t=>{K(t,(e,n)=>{if(T(t)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=t[n];T(r)&&(e.enumerable=!1,"writable"in e?e.writable=!1:e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(t,e)=>{const n={},r=t=>{t.forEach(t=>{n[t]=!0})};return O(t)?r(t):r(String(t).split(e)),n},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,e,n){return e.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t=+t)?t:e,findKey:L,global:I,isContextDefined:M,isSpecCompliantForm:function(t){return!!(t&&T(t.append)&&"FormData"===t[b]&&t[w])},toJSONObject:t=>{const e=new Array(10),n=(t,r)=>{if(A(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[r]=t;const o=O(t)?[]:{};return q(t,(t,e)=>{const i=n(t,r+1);!v(i)&&(o[e]=i)}),e[r]=void 0,o}}return t};return n(t,0)},isAsyncFn:V,isThenable:t=>t&&(A(t)||T(t))&&T(t.then)&&T(t.catch),setImmediate:X,asap:Y,isIterable:t=>null!=t&&T(t[w])};function et(t,e,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=t,this.name="AxiosError",e&&(this.code=e),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}tt.inherits(et,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:tt.toJSONObject(this.config),code:this.code,status:this.status}}});const nt=et.prototype,rt={};function ot(t){return tt.isPlainObject(t)||tt.isArray(t)}function it(t){return tt.endsWith(t,"[]")?t.slice(0,-2):t}function st(t,e,n){return t?t.concat(e).map(function(t,e){return t=it(t),!n&&e?"["+t+"]":t}).join(n?".":""):e}["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{rt[t]={value:t}}),Object.defineProperties(et,rt),Object.defineProperty(nt,"isAxiosError",{value:!0}),et.from=(t,e,n,r,o,i)=>{const s=Object.create(nt);return tt.toFlatObject(t,s,function(t){return t!==Error.prototype},t=>"isAxiosError"!==t),et.call(s,t.message,e,n,r,o),s.cause=t,s.name=t.name,i&&Object.assign(s,i),s};const ut=tt.toFlatObject(tt,{},null,function(t){return/^is[A-Z]/.test(t)});function ct(t,e,n){if(!tt.isObject(t))throw new TypeError("target must be an object");e=e||new FormData;const r=(n=tt.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(t,e){return!tt.isUndefined(e[t])})).metaTokens,o=n.visitor||a,i=n.dots,s=n.indexes,u=(n.Blob||"undefined"!=typeof Blob&&Blob)&&tt.isSpecCompliantForm(e);if(!tt.isFunction(o))throw new TypeError("visitor must be a function");function c(t){if(null===t)return"";if(tt.isDate(t))return t.toISOString();if(tt.isBoolean(t))return t.toString();if(!u&&tt.isBlob(t))throw new et("Blob is not supported. Use a Buffer instead.");return tt.isArrayBuffer(t)||tt.isTypedArray(t)?u&&"function"==typeof Blob?new Blob([t]):Buffer.from(t):t}function a(t,n,o){let u=t;if(t&&!o&&"object"==typeof t)if(tt.endsWith(n,"{}"))n=r?n:n.slice(0,-2),t=JSON.stringify(t);else if(tt.isArray(t)&&function(t){return tt.isArray(t)&&!t.some(ot)}(t)||(tt.isFileList(t)||tt.endsWith(n,"[]"))&&(u=tt.toArray(t)))return n=it(n),u.forEach(function(t,r){!tt.isUndefined(t)&&null!==t&&e.append(!0===s?st([n],r,i):null===s?n:n+"[]",c(t))}),!1;return!!ot(t)||(e.append(st(o,n,i),c(t)),!1)}const l=[],f=Object.assign(ut,{defaultVisitor:a,convertValue:c,isVisitable:ot});if(!tt.isObject(t))throw new TypeError("data must be an object");return function t(n,r){if(!tt.isUndefined(n)){if(-1!==l.indexOf(n))throw Error("Circular reference detected in "+r.join("."));l.push(n),tt.forEach(n,function(n,i){!0===(!(tt.isUndefined(n)||null===n)&&o.call(e,n,tt.isString(i)?i.trim():i,r,f))&&t(n,r?r.concat(i):[i])}),l.pop()}}(t),e}function at(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(t){return e[t]})}function lt(t,e){this.t=[],t&&ct(t,this,e)}const ft=lt.prototype;function dt(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ht(t,e,n){if(!e)return t;const r=n&&n.encode||dt;tt.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let i;if(i=o?o(e,n):tt.isURLSearchParams(e)?e.toString():new lt(e,n).toString(r),i){const e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+i}return t}ft.append=function(t,e){this.t.push([t,e])},ft.toString=function(t){const e=t?function(e){return t.call(this,e,at)}:at;return this.t.map(function(t){return e(t[0])+"="+e(t[1])},"").join("&")};class pt{constructor(){this.handlers=[]}use(t,e,n){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){tt.forEach(this.handlers,function(e){null!==e&&t(e)})}}const mt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},wt={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:lt,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},bt="undefined"!=typeof window&&"undefined"!=typeof document,yt="object"==typeof navigator&&navigator||void 0,gt=bt&&(!yt||["ReactNative","NativeScript","NS"].indexOf(yt.product)<0),Rt="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Ot=bt&&window.location.href||"http://localhost",vt=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:bt,hasStandardBrowserEnv:gt,hasStandardBrowserWebWorkerEnv:Rt,navigator:yt,origin:Ot},Symbol.toStringTag,{value:"Module"})),Et=c(c({},vt),wt);function St(t){function e(t,n,r,o){let i=t[o++];if("__proto__"===i)return!0;const s=Number.isFinite(+i),u=o>=t.length;return i=!i&&tt.isArray(r)?r.length:i,u?(tt.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!s):(r[i]&&tt.isObject(r[i])||(r[i]=[]),e(t,n,r[i],o)&&tt.isArray(r[i])&&(r[i]=function(t){const e={},n=Object.keys(t);let r;const o=n.length;let i;for(r=0;r<o;r++)i=n[r],e[i]=t[i];return e}(r[i])),!s)}if(tt.isFormData(t)&&tt.isFunction(t.entries)){const n={};return tt.forEachEntry(t,(t,r)=>{e(function(t){return tt.matchAll(/\w+|\[(\w*)]/g,t).map(t=>"[]"===t[0]?"":t[1]||t[0])}(t),r,n,0)}),n}return null}const Tt={transitional:mt,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){const n=e.getContentType()||"",r=n.indexOf("application/json")>-1,o=tt.isObject(t);if(o&&tt.isHTMLForm(t)&&(t=new FormData(t)),tt.isFormData(t))return r?JSON.stringify(St(t)):t;if(tt.isArrayBuffer(t)||tt.isBuffer(t)||tt.isStream(t)||tt.isFile(t)||tt.isBlob(t)||tt.isReadableStream(t))return t;if(tt.isArrayBufferView(t))return t.buffer;if(tt.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let i;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(t,e){return ct(t,new Et.classes.URLSearchParams,Object.assign({visitor:function(t,e,n,r){return Et.isNode&&tt.isBuffer(t)?(this.append(e,t.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}(t,this.formSerializer).toString();if((i=tt.isFileList(t))||n.indexOf("multipart/form-data")>-1){const e=this.env&&this.env.FormData;return ct(i?{"files[]":t}:t,e&&new e,this.formSerializer)}}return o||r?(e.setContentType("application/json",!1),function(t){if(tt.isString(t))try{return(0,JSON.parse)(t),tt.trim(t)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){const e=this.transitional||Tt.transitional,n=e&&e.forcedJSONParsing,r="json"===this.responseType;if(tt.isResponse(t)||tt.isReadableStream(t))return t;if(t&&tt.isString(t)&&(n&&!this.responseType||r)){const n=!(e&&e.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(o){if(n){if("SyntaxError"===o.name)throw et.from(o,et.ERR_BAD_RESPONSE,this,null,this.response);throw o}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Et.classes.FormData,Blob:Et.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};tt.forEach(["delete","get","head","post","put","patch"],t=>{Tt.headers[t]={}});const jt=tt.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),At=Symbol("internals");function xt(t){return t&&String(t).trim().toLowerCase()}function Nt(t){return!1===t||null==t?t:tt.isArray(t)?t.map(Nt):String(t)}function Pt(t,e,n,r,o){return tt.isFunction(r)?r.call(this,e,n):(o&&(e=n),tt.isString(e)?tt.isString(r)?-1!==e.indexOf(r):tt.isRegExp(r)?r.test(e):void 0:void 0)}let Ct=class{constructor(t){t&&this.set(t)}set(t,e,n){const r=this;function o(t,e,n){const o=xt(e);if(!o)throw new Error("header name must be a non-empty string");const i=tt.findKey(r,o);(!i||void 0===r[i]||!0===n||void 0===n&&!1!==r[i])&&(r[i||e]=Nt(t))}const i=(t,e)=>tt.forEach(t,(t,n)=>o(t,n,e));if(tt.isPlainObject(t)||t instanceof this.constructor)i(t,e);else if(tt.isString(t)&&(t=t.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim()))i((t=>{const e={};let n,r,o;return t&&t.split("\n").forEach(function(t){o=t.indexOf(":"),n=t.substring(0,o).trim().toLowerCase(),r=t.substring(o+1).trim(),!n||e[n]&&jt[n]||("set-cookie"===n?e[n]?e[n].push(r):e[n]=[r]:e[n]=e[n]?e[n]+", "+r:r)}),e})(t),e);else if(tt.isObject(t)&&tt.isIterable(t)){let n,r,o={};for(const e of t){if(!tt.isArray(e))throw TypeError("Object iterator must return a key-value pair");o[r=e[0]]=(n=o[r])?tt.isArray(n)?[...n,e[1]]:[n,e[1]]:e[1]}i(o,e)}else null!=t&&o(e,t,n);return this}get(t,e){if(t=xt(t)){const n=tt.findKey(this,t);if(n){const t=this[n];if(!e)return t;if(!0===e)return function(t){const e=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(t);)e[r[1]]=r[2];return e}(t);if(tt.isFunction(e))return e.call(this,t,n);if(tt.isRegExp(e))return e.exec(t);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=xt(t)){const n=tt.findKey(this,t);return!(!n||void 0===this[n]||e&&!Pt(0,this[n],n,e))}return!1}delete(t,e){const n=this;let r=!1;function o(t){if(t=xt(t)){const o=tt.findKey(n,t);!o||e&&!Pt(0,n[o],o,e)||(delete n[o],r=!0)}}return tt.isArray(t)?t.forEach(o):o(t),r}clear(t){const e=Object.keys(this);let n=e.length,r=!1;for(;n--;){const o=e[n];t&&!Pt(0,this[o],o,t,!0)||(delete this[o],r=!0)}return r}normalize(t){const e=this,n={};return tt.forEach(this,(r,o)=>{const i=tt.findKey(n,o);if(i)return e[i]=Nt(r),void delete e[o];const s=t?function(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,e,n)=>e.toUpperCase()+n)}(o):String(o).trim();s!==o&&delete e[o],e[s]=Nt(r),n[s]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const e=Object.create(null);return tt.forEach(this,(n,r)=>{null!=n&&!1!==n&&(e[r]=t&&tt.isArray(n)?n.join(", "):n)}),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,e])=>t+": "+e).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){const n=new this(t);return e.forEach(t=>n.set(t)),n}static accessor(t){const e=(this[At]=this[At]={accessors:{}}).accessors,n=this.prototype;function r(t){const r=xt(t);e[r]||(function(t,e){const n=tt.toCamelCase(" "+e);["get","set","has"].forEach(r=>{Object.defineProperty(t,r+n,{value:function(t,n,o){return this[r].call(this,e,t,n,o)},configurable:!0})})}(n,t),e[r]=!0)}return tt.isArray(t)?t.forEach(r):r(t),this}};function Ft(t,e){const n=this||Tt,r=e||n,o=Ct.from(r.headers);let i=r.data;return tt.forEach(t,function(t){i=t.call(n,i,o.normalize(),e?e.status:void 0)}),o.normalize(),i}function Ut(t){return!(!t||!t.o)}function Bt(t,e,n){et.call(this,null==t?"canceled":t,et.ERR_CANCELED,e,n),this.name="CanceledError"}function Dt(t,e,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?e(new et("Request failed with status code "+n.status,[et.ERR_BAD_REQUEST,et.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):t(n)}Ct.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),tt.reduceDescriptors(Ct.prototype,({value:t},e)=>{let n=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[n]=t}}}),tt.freezeMethods(Ct),tt.inherits(Bt,et,{o:!0});const _t=(t,e,n=3)=>{let r=0;const o=function(t,e){t=t||10;const n=new Array(t),r=new Array(t);let o,i=0,s=0;return e=void 0!==e?e:1e3,function(u){const c=Date.now(),a=r[s];o||(o=c),n[i]=u,r[i]=c;let l=s,f=0;for(;l!==i;)f+=n[l++],l%=t;if(i=(i+1)%t,i===s&&(s=(s+1)%t),c-o<e)return;const d=a&&c-a;return d?Math.round(1e3*f/d):void 0}}(50,250);return function(t,e){let n,r,o=0,i=1e3/e;const s=(e,i=Date.now())=>{o=i,n=null,r&&(clearTimeout(r),r=null),t.apply(null,e)};return[(...t)=>{const e=Date.now(),u=e-o;u>=i?s(t,e):(n=t,r||(r=setTimeout(()=>{r=null,s(n)},i-u)))},()=>n&&s(n)]}(n=>{const i=n.loaded,s=n.lengthComputable?n.total:void 0,u=i-r,c=o(u);r=i,t({loaded:i,total:s,progress:s?i/s:void 0,bytes:u,rate:c||void 0,estimated:c&&s&&i<=s?(s-i)/c:void 0,event:n,lengthComputable:null!=s,[e?"download":"upload"]:!0})},n)},kt=(t,e)=>{const n=null!=t;return[r=>e[0]({lengthComputable:n,total:t,loaded:r}),e[1]]},qt=t=>(...e)=>tt.asap(()=>t(...e)),Lt=Et.hasStandardBrowserEnv?((t,e)=>n=>(n=new URL(n,Et.origin),t.protocol===n.protocol&&t.host===n.host&&(e||t.port===n.port)))(new URL(Et.origin),Et.navigator&&/(msie|trident)/i.test(Et.navigator.userAgent)):()=>!0,It=Et.hasStandardBrowserEnv?{write(t,e,n,r,o,i){const s=[t+"="+encodeURIComponent(e)];tt.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),tt.isString(r)&&s.push("path="+r),tt.isString(o)&&s.push("domain="+o),!0===i&&s.push("secure"),document.cookie=s.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Mt(t,e,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e);return t&&(r||0==n)?function(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}(t,e):e}const zt=t=>t instanceof Ct?c({},t):t;function Ht(t,e){e=e||{};const n={};function r(t,e,n,r){return tt.isPlainObject(t)&&tt.isPlainObject(e)?tt.merge.call({caseless:r},t,e):tt.isPlainObject(e)?tt.merge({},e):tt.isArray(e)?e.slice():e}function o(t,e,n,o){return tt.isUndefined(e)?tt.isUndefined(t)?void 0:r(void 0,t,0,o):r(t,e,0,o)}function i(t,e){if(!tt.isUndefined(e))return r(void 0,e)}function s(t,e){return tt.isUndefined(e)?tt.isUndefined(t)?void 0:r(void 0,t):r(void 0,e)}function u(n,o,i){return i in e?r(n,o):i in t?r(void 0,n):void 0}const c={url:i,method:i,data:i,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:u,headers:(t,e,n)=>o(zt(t),zt(e),0,!0)};return tt.forEach(Object.keys(Object.assign({},t,e)),function(r){const i=c[r]||o,s=i(t[r],e[r],r);tt.isUndefined(s)&&i!==u||(n[r]=s)}),n}const Jt=t=>{const e=Ht({},t);let n,{data:r,withXSRFToken:o,xsrfHeaderName:i,xsrfCookieName:s,headers:u,auth:c}=e;if(e.headers=u=Ct.from(u),e.url=ht(Mt(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),c&&u.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),tt.isFormData(r))if(Et.hasStandardBrowserEnv||Et.hasStandardBrowserWebWorkerEnv)u.setContentType(void 0);else if(!1!==(n=u.getContentType())){const[t,...e]=n?n.split(";").map(t=>t.trim()).filter(Boolean):[];u.setContentType([t||"multipart/form-data",...e].join("; "))}if(Et.hasStandardBrowserEnv&&(o&&tt.isFunction(o)&&(o=o(e)),o||!1!==o&&Lt(e.url))){const t=i&&s&&It.read(s);t&&u.set(i,t)}return e},$t="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise(function(e,n){const r=Jt(t);let o=r.data;const i=Ct.from(r.headers).normalize();let s,u,c,a,l,{responseType:f,onUploadProgress:d,onDownloadProgress:h}=r;function p(){a&&a(),l&&l(),r.cancelToken&&r.cancelToken.unsubscribe(s),r.signal&&r.signal.removeEventListener("abort",s)}let m=new XMLHttpRequest;function w(){if(!m)return;const r=Ct.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Dt(function(t){e(t),p()},function(t){n(t),p()},{data:f&&"text"!==f&&"json"!==f?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:t,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=w:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(w)},m.onabort=function(){m&&(n(new et("Request aborted",et.ECONNABORTED,t,m)),m=null)},m.onerror=function(){n(new et("Network Error",et.ERR_NETWORK,t,m)),m=null},m.ontimeout=function(){let e=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||mt;r.timeoutErrorMessage&&(e=r.timeoutErrorMessage),n(new et(e,o.clarifyTimeoutError?et.ETIMEDOUT:et.ECONNABORTED,t,m)),m=null},void 0===o&&i.setContentType(null),"setRequestHeader"in m&&tt.forEach(i.toJSON(),function(t,e){m.setRequestHeader(e,t)}),tt.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),f&&"json"!==f&&(m.responseType=r.responseType),h&&([c,l]=_t(h,!0),m.addEventListener("progress",c)),d&&m.upload&&([u,a]=_t(d),m.upload.addEventListener("progress",u),m.upload.addEventListener("loadend",a)),(r.cancelToken||r.signal)&&(s=e=>{m&&(n(!e||e.type?new Bt(null,t,m):e),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(s),r.signal&&(r.signal.aborted?s():r.signal.addEventListener("abort",s)));const b=function(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(r.url);b&&-1===Et.protocols.indexOf(b)?n(new et("Unsupported protocol "+b+":",et.ERR_BAD_REQUEST,t)):m.send(o||null)})},Kt=(t,e)=>{const{length:n}=t=t?t.filter(Boolean):[];if(e||n){let n,r=new AbortController;const o=function(t){if(!n){n=!0,s();const e=t instanceof Error?t:this.reason;r.abort(e instanceof et?e:new Bt(e instanceof Error?e.message:e))}};let i=e&&setTimeout(()=>{i=null,o(new et(`timeout ${e} of ms exceeded`,et.ETIMEDOUT))},e);const s=()=>{t&&(i&&clearTimeout(i),i=null,t.forEach(t=>{t.unsubscribe?t.unsubscribe(o):t.removeEventListener("abort",o)}),t=null)};t.forEach(t=>t.addEventListener("abort",o));const{signal:u}=r;return u.unsubscribe=()=>tt.asap(s),u}},Vt=function*(t,e){let n=t.byteLength;if(n<e)return void(yield t);let r,o=0;for(;o<n;)r=o+e,yield t.slice(o,r),o=r},Xt=function(t){return f(this,null,function*(){if(t[Symbol.asyncIterator])return void(yield*d(t));const e=t.getReader();try{for(;;){const{done:t,value:n}=yield new l(e.read());if(t)break;yield n}}finally{yield new l(e.cancel())}})},Wt=(t,e,n,r)=>{const o=function(t,e){return f(this,null,function*(){try{for(var n,r,o,i=((t,e,n)=>(e=t[s("asyncIterator")])?e.call(t):(t=t[s("iterator")](),e={},(n=(n,r)=>(r=t[n])&&(e[n]=e=>new Promise((n,o,i)=>(e=r.call(t,e),i=e.done,Promise.resolve(e.value).then(t=>n({value:t,done:i}),o)))))("next"),n("return"),e))(Xt(t));n=!(r=yield new l(i.next())).done;n=!1){const t=r.value;yield*d(Vt(t,e))}}catch(r){o=[r]}finally{try{n&&(r=i.return)&&(yield new l(r.call(i)))}finally{if(o)throw o[0]}}})}(t,e);let i,u=0,c=t=>{i||(i=!0,r&&r(t))};return new ReadableStream({pull(t){return a(this,null,function*(){try{const{done:e,value:r}=yield o.next();if(e)return c(),void t.close();let i=r.byteLength;if(n){let t=u+=i;n(t)}t.enqueue(new Uint8Array(r))}catch(e){throw c(e),e}})},cancel:t=>(c(t),o.return())},{highWaterMark:2})},Gt="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Zt=Gt&&"function"==typeof ReadableStream,Qt=Gt&&("function"==typeof TextEncoder?(t=>e=>t.encode(e))(new TextEncoder):t=>a(void 0,null,function*(){return new Uint8Array(yield new Response(t).arrayBuffer())})),Yt=(t,...e)=>{try{return!!t(...e)}catch(n){return!1}},te=Zt&&Yt(()=>{let t=!1;const e=new Request(Et.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),ee=Zt&&Yt(()=>tt.isReadableStream(new Response("").body)),ne={stream:ee&&(t=>t.body)};var re;Gt&&(re=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ne[t]&&(ne[t]=tt.isFunction(re[t])?e=>e[t]():(e,n)=>{throw new et(`Response type '${t}' is not supported`,et.ERR_NOT_SUPPORT,n)})}));const oe={http:null,xhr:$t,fetch:Gt&&(t=>a(void 0,null,function*(){let{url:r,method:o,data:i,signal:s,cancelToken:u,timeout:l,onDownloadProgress:f,onUploadProgress:d,responseType:h,headers:p,withCredentials:m="same-origin",fetchOptions:w}=Jt(t);h=h?(h+"").toLowerCase():"text";let b,y=Kt([s,u&&u.toAbortSignal()],l);const g=y&&y.unsubscribe&&(()=>{y.unsubscribe()});let R;try{if(d&&te&&"get"!==o&&"head"!==o&&0!==(R=yield((t,e)=>a(void 0,null,function*(){const n=tt.toFiniteNumber(t.getContentLength());return null==n?(t=>a(void 0,null,function*(){if(null==t)return 0;if(tt.isBlob(t))return t.size;if(tt.isSpecCompliantForm(t)){const e=new Request(Et.origin,{method:"POST",body:t});return(yield e.arrayBuffer()).byteLength}return tt.isArrayBufferView(t)||tt.isArrayBuffer(t)?t.byteLength:(tt.isURLSearchParams(t)&&(t+=""),tt.isString(t)?(yield Qt(t)).byteLength:void 0)}))(e):n}))(p,i))){let t,e=new Request(r,{method:"POST",body:i,duplex:"half"});if(tt.isFormData(i)&&(t=e.headers.get("content-type"))&&p.setContentType(t),e.body){const[t,n]=kt(R,_t(qt(d)));i=Wt(e.body,65536,t,n)}}tt.isString(m)||(m=m?"include":"omit");const s="credentials"in Request.prototype;b=new Request(r,(O=c({},w),v={signal:y,method:o.toUpperCase(),headers:p.normalize().toJSON(),body:i,duplex:"half",credentials:s?m:void 0},e(O,n(v))));let u=yield fetch(b,w);const l=ee&&("stream"===h||"response"===h);if(ee&&(f||l&&g)){const t={};["status","statusText","headers"].forEach(e=>{t[e]=u[e]});const e=tt.toFiniteNumber(u.headers.get("content-length")),[n,r]=f&&kt(e,_t(qt(f),!0))||[];u=new Response(Wt(u.body,65536,n,()=>{r&&r(),g&&g()}),t)}h=h||"text";let E=yield ne[tt.findKey(ne,h)||"text"](u,t);return!l&&g&&g(),yield new Promise((e,n)=>{Dt(e,n,{data:E,headers:Ct.from(u.headers),status:u.status,statusText:u.statusText,config:t,request:b})})}catch(E){if(g&&g(),E&&"TypeError"===E.name&&/Load failed|fetch/i.test(E.message))throw Object.assign(new et("Network Error",et.ERR_NETWORK,t,b),{cause:E.cause||E});throw et.from(E,E&&E.code,t,b)}var O,v}))};tt.forEach(oe,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(n){}Object.defineProperty(t,"adapterName",{value:e})}});const ie=t=>`- ${t}`,se=t=>tt.isFunction(t)||null===t||!1===t,ue=t=>{t=tt.isArray(t)?t:[t];const{length:e}=t;let n,r;const o={};for(let i=0;i<e;i++){let e;if(n=t[i],r=n,!se(n)&&(r=oe[(e=String(n)).toLowerCase()],void 0===r))throw new et(`Unknown adapter '${e}'`);if(r)break;o[e||"#"+i]=r}if(!r){const t=Object.entries(o).map(([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build"));throw new et("There is no suitable adapter to dispatch the request "+(e?t.length>1?"since :\n"+t.map(ie).join("\n"):" "+ie(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function ce(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new Bt(null,t)}function ae(t){return ce(t),t.headers=Ct.from(t.headers),t.data=Ft.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1),ue(t.adapter||Tt.adapter)(t).then(function(e){return ce(t),e.data=Ft.call(t,t.transformResponse,e),e.headers=Ct.from(e.headers),e},function(e){return Ut(e)||(ce(t),e&&e.response&&(e.response.data=Ft.call(t,t.transformResponse,e.response),e.response.headers=Ct.from(e.response.headers))),Promise.reject(e)})}const le="1.10.0",fe={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{fe[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}});const de={};fe.transitional=function(t,e,n){return(r,o,i)=>{if(!1===t)throw new et(function(t,e){return"[Axios v"+le+"] Transitional option '"+t+"'"+e+(n?". "+n:"")}(o," has been removed"+(e?" in "+e:"")),et.ERR_DEPRECATED);return e&&!de[o]&&(de[o]=!0),!t||t(r,o,i)}},fe.spelling=function(t){return(t,e)=>!0};const he={assertOptions:function(t,e,n){if("object"!=typeof t)throw new et("options must be an object",et.ERR_BAD_OPTION_VALUE);const r=Object.keys(t);let o=r.length;for(;o-- >0;){const i=r[o],s=e[i];if(s){const e=t[i],n=void 0===e||s(e,i,t);if(!0!==n)throw new et("option "+i+" must be "+n,et.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new et("Unknown option "+i,et.ERR_BAD_OPTION)}},validators:fe},pe=he.validators;let me=class{constructor(t){this.defaults=t||{},this.interceptors={request:new pt,response:new pt}}request(t,e){return a(this,null,function*(){try{return yield this.i(t,e)}catch(n){if(n instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=new Error;const e=t.stack?t.stack.replace(/^.+\n/,""):"";try{n.stack?e&&!String(n.stack).endsWith(e.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+e):n.stack=e}catch(r){}}throw n}})}i(t,e){"string"==typeof t?(e=e||{}).url=t:e=t||{},e=Ht(this.defaults,e);const{transitional:n,paramsSerializer:r,headers:o}=e;void 0!==n&&he.assertOptions(n,{silentJSONParsing:pe.transitional(pe.boolean),forcedJSONParsing:pe.transitional(pe.boolean),clarifyTimeoutError:pe.transitional(pe.boolean)},!1),null!=r&&(tt.isFunction(r)?e.paramsSerializer={serialize:r}:he.assertOptions(r,{encode:pe.function,serialize:pe.function},!0)),void 0!==e.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),he.assertOptions(e,{baseUrl:pe.spelling("baseURL"),withXsrfToken:pe.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let i=o&&tt.merge(o.common,o[e.method]);o&&tt.forEach(["delete","get","head","post","put","patch","common"],t=>{delete o[t]}),e.headers=Ct.concat(i,o);const s=[];let u=!0;this.interceptors.request.forEach(function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(u=u&&t.synchronous,s.unshift(t.fulfilled,t.rejected))});const c=[];let a;this.interceptors.response.forEach(function(t){c.push(t.fulfilled,t.rejected)});let l,f=0;if(!u){const t=[ae.bind(this),void 0];for(t.unshift.apply(t,s),t.push.apply(t,c),l=t.length,a=Promise.resolve(e);f<l;)a=a.then(t[f++],t[f++]);return a}l=s.length;let d=e;for(f=0;f<l;){const t=s[f++],e=s[f++];try{d=t(d)}catch(h){e.call(this,h);break}}try{a=ae.call(this,d)}catch(h){return Promise.reject(h)}for(f=0,l=c.length;f<l;)a=a.then(c[f++],c[f++]);return a}getUri(t){return ht(Mt((t=Ht(this.defaults,t)).baseURL,t.url,t.allowAbsoluteUrls),t.params,t.paramsSerializer)}};tt.forEach(["delete","get","head","options"],function(t){me.prototype[t]=function(e,n){return this.request(Ht(n||{},{method:t,url:e,data:(n||{}).data}))}}),tt.forEach(["post","put","patch"],function(t){function e(e){return function(n,r,o){return this.request(Ht(o||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}me.prototype[t]=e(),me.prototype[t+"Form"]=e(!0)});const we={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(we).forEach(([t,e])=>{we[e]=t});const be=function t(e){const n=new me(e),r=h(me.prototype.request,n);return tt.extend(r,me.prototype,n,{allOwnKeys:!0}),tt.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return t(Ht(e,n))},r}(Tt);be.Axios=me,be.CanceledError=Bt,be.CancelToken=class t{constructor(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");let e;this.promise=new Promise(function(t){e=t});const n=this;this.promise.then(t=>{if(!n.u)return;let e=n.u.length;for(;e-- >0;)n.u[e](t);n.u=null}),this.promise.then=t=>{let e;const r=new Promise(t=>{n.subscribe(t),e=t}).then(t);return r.cancel=function(){n.unsubscribe(e)},r},t(function(t,r,o){n.reason||(n.reason=new Bt(t,r,o),e(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){this.reason?t(this.reason):this.u?this.u.push(t):this.u=[t]}unsubscribe(t){if(!this.u)return;const e=this.u.indexOf(t);-1!==e&&this.u.splice(e,1)}toAbortSignal(){const t=new AbortController,e=e=>{t.abort(e)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let e;return{token:new t(function(t){e=t}),cancel:e}}},be.isCancel=Ut,be.VERSION=le,be.toFormData=ct,be.AxiosError=et,be.Cancel=be.CanceledError,be.all=function(t){return Promise.all(t)},be.spread=function(t){return function(e){return t.apply(null,e)}},be.isAxiosError=function(t){return tt.isObject(t)&&!0===t.isAxiosError},be.mergeConfig=Ht,be.AxiosHeaders=Ct,be.formToJSON=t=>St(tt.isHTMLForm(t)?new FormData(t):t),be.getAdapter=ue,be.HttpStatusCode=we,be.default=be;const{Axios:ye,AxiosError:ge,CanceledError:Re,isCancel:Oe,CancelToken:ve,VERSION:Ee,all:Se,Cancel:Te,isAxiosError:je,spread:Ae,toFormData:xe,AxiosHeaders:Ne,HttpStatusCode:Pe,formToJSON:Ce,getAdapter:Fe,mergeConfig:Ue}=be;export{be as a};
