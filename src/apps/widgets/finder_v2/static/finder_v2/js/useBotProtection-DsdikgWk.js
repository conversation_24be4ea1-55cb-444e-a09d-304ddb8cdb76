var e=(e,n,t)=>new Promise((r,o)=>{var i=e=>{try{l(t.next(e))}catch(n){o(n)}},a=e=>{try{l(t.throw(e))}catch(n){o(n)}},l=e=>e.done?r(e.value):Promise.resolve(e.value).then(i,a);l((t=t.apply(e,n)).next())});import{a as n}from"./http-client-CgEESJXa.js";import{r as t,e as r}from"./vue-core-Bz-XfZu_.js";function o(){const o=t(!1),i=t(""),a=t({}),l=t(!1);function u(){var e,n,t,r,o,i,u,w,g;const h={screen_width:(null==(e=window.screen)?void 0:e.width)||0,screen_height:(null==(n=window.screen)?void 0:n.height)||0,screen_available_width:(null==(t=window.screen)?void 0:t.availWidth)||0,screen_available_height:(null==(r=window.screen)?void 0:r.availHeight)||0,color_depth:(null==(o=window.screen)?void 0:o.colorDepth)||0,pixel_depth:(null==(i=window.screen)?void 0:i.pixelDepth)||0,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timezone_offset:(new Date).getTimezoneOffset(),language:navigator.language||"",languages:(null==(u=navigator.languages)?void 0:u.join(","))||"",platform:navigator.platform||"",user_agent:navigator.userAgent||"",cookies_enabled:navigator.cookieEnabled,online:navigator.onLine,do_not_track:navigator.doNotTrack||"unspecified",hardware_concurrency:navigator.hardwareConcurrency||0,max_touch_points:navigator.maxTouchPoints||0,plugins_count:(null==(w=navigator.plugins)?void 0:w.length)||0,canvas_hash:d(),webgl_vendor:s(),webgl_renderer:c(),touch_support:"ontouchstart"in window||navigator.maxTouchPoints>0,media_devices:!!navigator.mediaDevices,webrtc_enabled:!!window.RTCPeerConnection,websocket_enabled:!!window.WebSocket,session_storage:!!window.sessionStorage,local_storage:!!window.localStorage,indexed_db:!!window.indexedDB,connection_type:(null==(g=navigator.connection)?void 0:g.effectiveType)||"unknown",window_outer_width:window.outerWidth,window_outer_height:window.outerHeight,window_inner_width:window.innerWidth,window_inner_height:window.innerHeight,timestamp:Date.now()};return a.value=h,l.value=!0,h}function d(){try{const e=document.createElement("canvas"),n=e.getContext("2d");return n?(e.width=200,e.height=50,n.textBaseline="alphabetic",n.fillStyle="#f60",n.fillRect(125,1,62,20),n.fillStyle="#069",n.font="11pt Arial",n.fillText("Canvas fingerprint 🛡️",2,15),n.fillStyle="rgba(102, 204, 0, 0.7)",n.font="18pt Arial",n.fillText("Widget Protection",4,45),function(e){let n=0;if(0===e.length)return"0";for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n&=n;return Math.abs(n).toString(16)}(e.toDataURL()).substring(0,32)):"canvas_not_available"}catch(e){return"canvas_blocked"}}function s(){try{const e=document.createElement("canvas"),n=e.getContext("webgl")||e.getContext("experimental-webgl");if(!n)return"webgl_not_supported";const t=n.getExtension("WEBGL_debug_renderer_info");return t?n.getParameter(t.UNMASKED_VENDOR_WEBGL)||"unknown":"debug_info_not_available"}catch(e){return"webgl_blocked"}}function c(){try{const e=document.createElement("canvas"),n=e.getContext("webgl")||e.getContext("experimental-webgl");if(!n)return"webgl_not_supported";const t=n.getExtension("WEBGL_debug_renderer_info");return t?n.getParameter(t.UNMASKED_RENDERER_WEBGL)||"unknown":"debug_info_not_available"}catch(e){return"webgl_blocked"}}function w(n=4){return e(this,null,function*(){if(!window.crypto||!window.crypto.subtle)throw new Error("JavaScript cryptography (crypto.subtle) is required but not available. Please use a modern browser.");const e=Math.random().toString(36).substring(2,15);let t=0;const r=Date.now(),o="0".repeat(n);for(;;){const a=`${e}:${t}`;let l;try{const e=(new TextEncoder).encode(a),n=yield crypto.subtle.digest("SHA-256",e);l=Array.from(new Uint8Array(n)).map(e=>e.toString(16).padStart(2,"0")).join("")}catch(i){throw new Error("Failed to compute hash: "+i.message)}if(l.startsWith(o))return{challenge:e,nonce:t,hash:l,duration:Date.now()-r,difficulty:n};if(t++,t%100==0&&(yield new Promise(e=>setTimeout(e,0))),Date.now()-r>1e4)throw new Error("Challenge timeout - took too long to solve")}})}function g(){return e(this,null,function*(){try{const e=a.value;return JSON.stringify(e),n.defaults.headers.common["X-Client-Features"]=JSON.stringify(e),!0}catch(e){return!1}})}function h(){return e(this,null,function*(){try{return u(),yield g(),!0}catch(e){return!1}})}function _(){return e(this,null,function*(){var e,t;try{return(yield n.post("/widget/api/request-challenge/",{widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(t=window.FinderV2Config)?void 0:t.id)})).data}catch(r){throw r}})}return r(()=>{h().catch(e=>{})}),{challengeSolved:o,challengeToken:i,browserFeatures:a,fingerprintCollected:l,initialize:h,collectBrowserFeatures:u,sendFingerprintToServer:g,solveChallenge:w,verifyHuman:function(){return e(this,null,function*(){var e,t;try{const r=u(),a=yield w(4),l=yield n.post("/widget/api/verify-human/",{features:r,solution:a,widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(t=window.FinderV2Config)?void 0:t.id)});return!!l.data.token&&(i.value=l.data.token,o.value=!0,n.defaults.headers.common["X-Challenge-Token"]=i.value,!0)}catch(r){return!1}})},requestChallenge:_,solveAndVerifyChallenge:function(){return e(this,null,function*(){var e,t;try{if(!window.crypto||!window.crypto.subtle){if("http:"===window.location.protocol)return o.value=!0,i.value="development-bypass",n.defaults.headers.common["X-Challenge-Token"]="development-bypass",!0;throw new Error("Security features are not available. This may be due to browser settings or an outdated browser. Please ensure JavaScript is enabled and try using Chrome, Firefox, Safari, or Edge.")}const r=yield _(),a=yield w(r.difficulty);a.challenge=r.challenge;const l=yield n.post("/widget/api/verify-challenge/",{solution:a,widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(t=window.FinderV2Config)?void 0:t.id)});return!!l.data.success&&(i.value=l.data.token,o.value=!0,n.defaults.headers.common["X-Challenge-Token"]=i.value,sessionStorage.setItem("challenge_token",i.value),sessionStorage.setItem("challenge_token_time",Date.now()),!0)}catch(r){if(r.message.includes("crypto"))throw new Error("Your browser does not support required security features. Please use Chrome, Firefox, Safari, or Edge.");throw r}})},hasValidChallengeToken:function(){if(i.value&&o.value)return!0;const e=sessionStorage.getItem("challenge_token"),t=sessionStorage.getItem("challenge_token_time");return!!(e&&t&&Date.now()-parseInt(t)<36e5)&&(i.value=e,o.value=!0,n.defaults.headers.common["X-Challenge-Token"]=e,!0)},generateCanvasHash:d,getWebGLVendor:s,getWebGLRenderer:c}}export{o as useBotProtection};
