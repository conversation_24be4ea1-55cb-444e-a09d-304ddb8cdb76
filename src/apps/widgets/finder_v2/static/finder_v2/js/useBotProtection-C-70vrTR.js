var e=(e,n,t)=>new Promise((o,r)=>{var i=e=>{try{l(t.next(e))}catch(n){r(n)}},a=e=>{try{l(t.throw(e))}catch(n){r(n)}},l=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,a);l((t=t.apply(e,n)).next())});import{r as n,o as t,b as o}from"./listbox-BfBpv6Zk.js";function r(){const r=n(!1),i=n(""),a=n({}),l=n(!1);function u(){var e,n,t,o,r,i,u,g,w;const h={screen_width:(null==(e=window.screen)?void 0:e.width)||0,screen_height:(null==(n=window.screen)?void 0:n.height)||0,screen_available_width:(null==(t=window.screen)?void 0:t.availWidth)||0,screen_available_height:(null==(o=window.screen)?void 0:o.availHeight)||0,color_depth:(null==(r=window.screen)?void 0:r.colorDepth)||0,pixel_depth:(null==(i=window.screen)?void 0:i.pixelDepth)||0,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timezone_offset:(new Date).getTimezoneOffset(),language:navigator.language||"",languages:(null==(u=navigator.languages)?void 0:u.join(","))||"",platform:navigator.platform||"",user_agent:navigator.userAgent||"",cookies_enabled:navigator.cookieEnabled,online:navigator.onLine,do_not_track:navigator.doNotTrack||"unspecified",hardware_concurrency:navigator.hardwareConcurrency||0,max_touch_points:navigator.maxTouchPoints||0,plugins_count:(null==(g=navigator.plugins)?void 0:g.length)||0,canvas_hash:s(),webgl_vendor:d(),webgl_renderer:c(),touch_support:"ontouchstart"in window||navigator.maxTouchPoints>0,media_devices:!!navigator.mediaDevices,webrtc_enabled:!!window.RTCPeerConnection,websocket_enabled:!!window.WebSocket,session_storage:!!window.sessionStorage,local_storage:!!window.localStorage,indexed_db:!!window.indexedDB,connection_type:(null==(w=navigator.connection)?void 0:w.effectiveType)||"unknown",window_outer_width:window.outerWidth,window_outer_height:window.outerHeight,window_inner_width:window.innerWidth,window_inner_height:window.innerHeight,timestamp:Date.now()};return a.value=h,l.value=!0,h}function s(){try{const e=document.createElement("canvas"),n=e.getContext("2d");if(!n)return"canvas_not_available";e.width=200,e.height=50,n.textBaseline="alphabetic",n.fillStyle="#f60",n.fillRect(125,1,62,20),n.fillStyle="#069",n.font="11pt Arial",n.fillText("Canvas fingerprint 🛡️",2,15),n.fillStyle="rgba(102, 204, 0, 0.7)",n.font="18pt Arial",n.fillText("Widget Protection",4,45);return function(e){let n=0;if(0===e.length)return"0";for(let t=0;t<e.length;t++){n=(n<<5)-n+e.charCodeAt(t),n&=n}return Math.abs(n).toString(16)}(e.toDataURL()).substring(0,32)}catch(e){return"canvas_blocked"}}function d(){try{const e=document.createElement("canvas"),n=e.getContext("webgl")||e.getContext("experimental-webgl");if(!n)return"webgl_not_supported";const t=n.getExtension("WEBGL_debug_renderer_info");return t?n.getParameter(t.UNMASKED_VENDOR_WEBGL)||"unknown":"debug_info_not_available"}catch(e){return"webgl_blocked"}}function c(){try{const e=document.createElement("canvas"),n=e.getContext("webgl")||e.getContext("experimental-webgl");if(!n)return"webgl_not_supported";const t=n.getExtension("WEBGL_debug_renderer_info");return t?n.getParameter(t.UNMASKED_RENDERER_WEBGL)||"unknown":"debug_info_not_available"}catch(e){return"webgl_blocked"}}function g(n=4){return e(this,null,function*(){if(!(window.crypto&&window.crypto.subtle))throw new Error("JavaScript cryptography (crypto.subtle) is required but not available. Please use a modern browser.");const e=Math.random().toString(36).substring(2,15);let t=0;const o=Date.now(),r="0".repeat(n);for(;;){const a=`${e}:${t}`;let l;try{const e=(new TextEncoder).encode(a),n=yield crypto.subtle.digest("SHA-256",e);l=Array.from(new Uint8Array(n)).map(e=>e.toString(16).padStart(2,"0")).join("")}catch(i){throw new Error("Failed to compute hash: "+i.message)}if(l.startsWith(r)){return{challenge:e,nonce:t,hash:l,duration:Date.now()-o,difficulty:n}}if(t++,t%100==0&&(yield new Promise(e=>setTimeout(e,0))),Date.now()-o>1e4)throw new Error("Challenge timeout - took too long to solve")}})}function w(){return e(this,null,function*(){try{const e=a.value;JSON.stringify(e);return o.defaults.headers.common["X-Client-Features"]=JSON.stringify(e),!0}catch(e){return!1}})}function h(){return e(this,null,function*(){try{u();return yield w(),!0}catch(e){return!1}})}function f(){return e(this,null,function*(){var e,n;try{return(yield o.post("/widget/api/request-challenge/",{widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(n=window.FinderV2Config)?void 0:n.id)})).data}catch(t){throw t}})}return t(()=>{h().catch(e=>{})}),{challengeSolved:r,challengeToken:i,browserFeatures:a,fingerprintCollected:l,initialize:h,collectBrowserFeatures:u,sendFingerprintToServer:w,solveChallenge:g,verifyHuman:function(){return e(this,null,function*(){var e,n;try{const t=u(),a=yield g(4),l=yield o.post("/widget/api/verify-human/",{features:t,solution:a,widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(n=window.FinderV2Config)?void 0:n.id)});return!!l.data.token&&(i.value=l.data.token,r.value=!0,o.defaults.headers.common["X-Challenge-Token"]=i.value,!0)}catch(t){return!1}})},requestChallenge:f,solveAndVerifyChallenge:function(){return e(this,null,function*(){var e,n;try{if(!(window.crypto&&window.crypto.subtle)){if("http:"===window.location.protocol)return r.value=!0,i.value="development-bypass",o.defaults.headers.common["X-Challenge-Token"]="development-bypass",!0;throw new Error("Security features are not available. This may be due to browser settings or an outdated browser. Please ensure JavaScript is enabled and try using Chrome, Firefox, Safari, or Edge.")}const t=yield f(),a=yield g(t.difficulty);a.challenge=t.challenge;const l=yield o.post("/widget/api/verify-challenge/",{solution:a,widget_uuid:(null==(e=window.FinderV2Config)?void 0:e.uuid)||(null==(n=window.FinderV2Config)?void 0:n.id)});return!!l.data.success&&(i.value=l.data.token,r.value=!0,o.defaults.headers.common["X-Challenge-Token"]=i.value,sessionStorage.setItem("challenge_token",i.value),sessionStorage.setItem("challenge_token_time",Date.now()),!0)}catch(t){if(t.message.includes("crypto"))throw new Error("Your browser does not support required security features. Please use Chrome, Firefox, Safari, or Edge.");throw t}})},hasValidChallengeToken:function(){if(i.value&&r.value)return!0;const e=sessionStorage.getItem("challenge_token"),n=sessionStorage.getItem("challenge_token_time");if(e&&n){if(Date.now()-parseInt(n)<36e5)return i.value=e,r.value=!0,o.defaults.headers.common["X-Challenge-Token"]=e,!0}return!1},generateCanvasHash:s,getWebGLVendor:d,getWebGLRenderer:c}}export{r as useBotProtection};
