const e={},n=function(n,o,r){let t=Promise.resolve();if(o&&o.length>0){document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),r=(null==n?void 0:n.nonce)||(null==n?void 0:n.getAttribute("nonce"));t=Promise.allSettled(o.map(n=>{if((n=function(e){return"/static/finder_v2/"+e}(n))in e)return;e[n]=!0;const o=n.endsWith(".css"),t=o?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${n}"]${t}`))return;const c=document.createElement("link");return c.rel=o?"stylesheet":"modulepreload",o||(c.as="script"),c.crossOrigin="",c.href=n,r&&c.setAttribute("nonce",r),document.head.appendChild(c),o?new Promise((e,o)=>{c.addEventListener("load",e),c.addEventListener("error",()=>o(new Error(`Unable to preload CSS for ${n}`)))}):void 0}))}function c(e){const n=new Event("vite:preloadError",{cancelable:!0});if(n.payload=e,window.dispatchEvent(n),!n.defaultPrevented)throw e}return t.then(e=>{for(const n of e||[])"rejected"===n.status&&c(n.reason);return n().catch(c)})};export{n as _};
