var e=Object.defineProperty,t=Object.defineProperties,n=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,i=(t,n,l)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[n]=l,u=(e,t)=>{for(var n in t||(t={}))r.call(t,n)&&i(e,n,t[n]);if(l)for(var n of l(t))o.call(t,n)&&i(e,n,t[n]);return e},a=(e,l)=>t(e,n(l)),s=(e,t)=>{var n={};for(var i in e)r.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&l)for(var i of l(e))t.indexOf(i)<0&&o.call(e,i)&&(n[i]=e[i]);return n};import{c as d,u as c,s as f,w as p,o as v,t as h,r as b,a as m,b as y,i as g,p as w,n as S,d as P,e as x,f as O,h as F,F as T,g as E,j as M,k as B,T as C,l as L,m as j}from"./vue-core-Bz-XfZu_.js";function D(e,t,n){var l;let r,o=null!=(l=n.initialDeps)?l:[];function i(){var l,i,u,a;let s;n.key&&(null==(l=n.debug)?void 0:l.call(n))&&(s=Date.now());const d=e();if(d.length===o.length&&!d.some((e,t)=>o[t]!==e))return r;let c;return o=d,n.key&&(null==(i=n.debug)?void 0:i.call(n))&&(c=Date.now()),r=t(...d),n.key&&(null==(u=n.debug)?void 0:u.call(n))&&(Math.round(100*(Date.now()-s)),Math.round(100*(Date.now()-c))),null==(a=null==n?void 0:n.onChange)||a.call(n,r),r}return i.updateDeps=e=>{o=e},i}function k(e,t){if(void 0===e)throw new Error("Unexpected undefined");return e}const I=(e,t,n)=>{let l;return function(...r){e.clearTimeout(l),l=e.setTimeout(()=>t.apply(this,r),n)}},A=e=>{const{offsetWidth:t,offsetHeight:n}=e;return{width:t,height:n}},$=e=>e,R=e=>{const t=Math.max(e.startIndex-e.overscan,0),n=Math.min(e.endIndex+e.overscan,e.count-1),l=[];for(let r=t;r<=n;r++)l.push(r);return l},N=(e,t)=>{const n=e.scrollElement;if(!n)return;const l=e.targetWindow;if(!l)return;const r=e=>{const{width:n,height:l}=e;t({width:Math.round(n),height:Math.round(l)})};if(r(A(n)),!l.ResizeObserver)return()=>{};const o=new l.ResizeObserver(t=>{const l=()=>{const e=t[0];if(null==e?void 0:e.borderBoxSize){const t=e.borderBoxSize[0];if(t)return void r({width:t.inlineSize,height:t.blockSize})}r(A(n))};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(l):l()});return o.observe(n,{box:"border-box"}),()=>{o.unobserve(n)}},V={passive:!0},H="undefined"==typeof window||"onscrollend"in window,G=(e,t)=>{const n=e.scrollElement;if(!n)return;const l=e.targetWindow;if(!l)return;let r=0;const o=e.options.useScrollendEvent&&H?()=>{}:I(l,()=>{t(r,!1)},e.options.isScrollingResetDelay),i=l=>()=>{const{horizontal:i,isRtl:u}=e.options;r=i?n.scrollLeft*(u?-1:1):n.scrollTop,o(),t(r,l)},u=i(!0),a=i(!1);a(),n.addEventListener("scroll",u,V);const s=e.options.useScrollendEvent&&H;return s&&n.addEventListener("scrollend",a,V),()=>{n.removeEventListener("scroll",u),s&&n.removeEventListener("scrollend",a)}},K=(e,t,n)=>{if(null==t?void 0:t.borderBoxSize){const e=t.borderBoxSize[0];if(e)return Math.round(e[n.options.horizontal?"inlineSize":"blockSize"])}return e[n.options.horizontal?"offsetWidth":"offsetHeight"]},q=(e,{adjustments:t=0,behavior:n},l)=>{var r,o;const i=e+t;null==(o=null==(r=l.scrollElement)?void 0:r.scrollTo)||o.call(r,{[l.options.horizontal?"left":"top"]:i,behavior:n})};class U{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null;const t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver(e=>{e.forEach(e=>{const t=()=>{this.t(e.target,e)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()})}):null);return{disconnect:()=>{var n;null==(n=t())||n.disconnect(),e=null},observe:e=>{var n;return null==(n=t())?void 0:n.observe(e,{box:"border-box"})},unobserve:e=>{var n;return null==(n=t())?void 0:n.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach(([t,n])=>{void 0===n&&delete e[t]}),this.options=u({debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:$,rangeExtractor:R,onChange:()=>{},measureElement:K,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1},e)},this.notify=e=>{var t,n;null==(n=(t=this.options).onChange)||n.call(t,this,e)},this.maybeNotify=D(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),e=>{this.notify(e)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(e=>e()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this.l=()=>()=>{this.cleanup()},this.o=()=>{var e,t;const n=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==n){if(this.cleanup(),!n)return void this.maybeNotify();this.scrollElement=n,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=null!=(e=null==(t=this.scrollElement)?void 0:t.window)?e:null,this.elementsCache.forEach(e=>{this.observer.observe(e)}),this.i(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,e=>{this.scrollRect=e,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()}))}},this.getSize=()=>{var e;return this.options.enabled?(this.scrollRect=null!=(e=this.scrollRect)?e:this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0)},this.getScrollOffset=()=>{var e;return this.options.enabled?(this.scrollOffset=null!=(e=this.scrollOffset)?e:"function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset,this.scrollOffset):(this.scrollOffset=null,0)},this.getFurthestMeasurement=(e,t)=>{const n=new Map,l=new Map;for(let r=t-1;r>=0;r--){const t=e[r];if(n.has(t.lane))continue;const o=l.get(t.lane);if(null==o||t.end>o.end?l.set(t.lane,t):t.end<o.end&&n.set(t.lane,!0),n.size===this.options.lanes)break}return l.size===this.options.lanes?Array.from(l.values()).sort((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end)[0]:void 0},this.getMeasurementOptions=D(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(e,t,n,l,r)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:n,getItemKey:l,enabled:r}),{key:!1}),this.getMeasurements=D(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:e,paddingStart:t,scrollMargin:n,getItemKey:l,enabled:r},o)=>{if(!r)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(e=>{this.itemSizeCache.set(e.key,e.size)}));const i=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const u=this.measurementsCache.slice(0,i);for(let a=i;a<e;a++){const e=l(a),r=1===this.options.lanes?u[a-1]:this.getFurthestMeasurement(u,a),i=r?r.end+this.options.gap:t+n,s=o.get(e),d="number"==typeof s?s:this.options.estimateSize(a),c=i+d,f=r?r.lane:a%this.options.lanes;u[a]={index:a,start:i,size:d,end:c,key:e,lane:f}}return this.measurementsCache=u,u},{key:!1,debug:()=>this.options.debug}),this.calculateRange=D(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(e,t,n,l)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:n,lanes:l}){const r=e.length-1;if(e.length<=l)return{startIndex:0,endIndex:r};let o=z(0,r,t=>e[t].start,n),i=o;if(1===l)for(;i<r&&e[i].end<n+t;)i++;else if(l>1){const u=Array(l).fill(0);for(;i<r&&u.some(e=>e<n+t);){const t=e[i];u[t.lane]=t.end,i++}const a=Array(l).fill(n+t);for(;o>=0&&a.some(e=>e>=n);){const t=e[o];a[t.lane]=t.start,o--}o=Math.max(0,o-o%l),i=Math.min(r,i+(l-1-i%l))}return{startIndex:o,endIndex:i}}({measurements:e,outerSize:t,scrollOffset:n,lanes:l}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=D(()=>{let e=null,t=null;const n=this.calculateRange();return n&&(e=n.startIndex,t=n.endIndex),this.maybeNotify.updateDeps([this.isScrolling,e,t]),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,t]},(e,t,n,l,r)=>null===l||null===r?[]:e({startIndex:l,endIndex:r,overscan:t,count:n}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{const t=this.options.indexAttribute,n=e.getAttribute(t);return n?parseInt(n,10):-1},this.t=(e,t)=>{const n=this.indexFromElement(e),l=this.measurementsCache[n];if(!l)return;const r=l.key,o=this.elementsCache.get(r);o!==e&&(o&&this.observer.unobserve(o),this.observer.observe(e),this.elementsCache.set(r,e)),e.isConnected&&this.resizeItem(n,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{var n;const l=this.measurementsCache[e];if(!l)return;const r=t-(null!=(n=this.itemSizeCache.get(l.key))?n:l.size);0!==r&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(l,r,this):"backward"===this.scrollDirection&&l.start<this.getScrollOffset()+this.scrollAdjustments)&&this.i(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=r,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(l.index),this.itemSizeCache=new Map(this.itemSizeCache.set(l.key,t)),this.notify(!1))},this.measureElement=e=>{e?this.t(e,void 0):this.elementsCache.forEach((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))})},this.getVirtualItems=D(()=>[this.getVirtualIndexes(),this.getMeasurements()],(e,t)=>{const n=[];for(let l=0,r=e.length;l<r;l++){const r=t[e[l]];n.push(r)}return n},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{const t=this.getMeasurements();if(0!==t.length)return k(t[z(0,t.length-1,e=>k(t[e]).start,e)])},this.getOffsetForAlignment=(e,t,n=0)=>{const l=this.getSize(),r=this.getScrollOffset();"auto"===t&&(t=e>=r+l?"end":"start"),"center"===t?e+=(n-l)/2:"end"===t&&(e-=l);const o=this.getTotalSize()-l;return Math.max(Math.min(o,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));const n=this.measurementsCache[e];if(!n)return;const l=this.getSize(),r=this.getScrollOffset();if("auto"===t)if(n.end>=r+l-this.options.scrollPaddingEnd)t="end";else{if(!(n.start<=r+this.options.scrollPaddingStart))return[r,t];t="start"}const o="end"===t?n.end+this.options.scrollPaddingEnd:n.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(o,t,n.size),t]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:n}={})=>{this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode(),this.i(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:n})},this.scrollToIndex=(e,{align:t="auto",behavior:n}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode();const l=this.getOffsetForIndex(e,t);if(!l)return;const[r,o]=l;this.i(r,{adjustments:void 0,behavior:n}),"smooth"!==n&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){const r=this.getOffsetForIndex(e,o);if(!r)return;const[i]=r;t=i,l=this.getScrollOffset(),Math.abs(t-l)<=1||this.scrollToIndex(e,{align:o,behavior:n})}else this.scrollToIndex(e,{align:o,behavior:n});var t,l}))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode(),this.i(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e,t;const n=this.getMeasurements();let l;if(0===n.length)l=this.options.paddingStart;else if(1===this.options.lanes)l=null!=(e=null==(t=n[n.length-1])?void 0:t.end)?e:0;else{const e=Array(this.options.lanes).fill(null);let t=n.length-1;for(;t>=0&&e.some(e=>null===e);){const l=n[t];null===e[l.lane]&&(e[l.lane]=l.end),t--}l=Math.max(...e.filter(e=>null!==e))}return Math.max(l-this.options.scrollMargin+this.options.paddingEnd,0)},this.i=(e,{adjustments:t,behavior:n})=>{this.options.scrollToFn(e,{behavior:n,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}const z=(e,t,n,l)=>{for(;e<=t;){const r=(e+t)/2|0,o=n(r);if(o<l)e=r+1;else{if(!(o>l))return r;t=r-1}}return e>0?e-1:0};function W(e,t,n){let l=b(null==n?void 0:n.value),r=d(()=>void 0!==e.value);return[d(()=>r.value?e.value:l.value),function(e){return r.value||(l.value=e),null==t?void 0:t(e)}]}function _(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}function Y(){let e=[],t={addEventListener:(e,n,l,r)=>(e.addEventListener(n,l,r),t.add(()=>e.removeEventListener(n,l,r))),requestAnimationFrame(...e){let n=requestAnimationFrame(...e);t.add(()=>cancelAnimationFrame(n))},nextFrame(...e){t.requestAnimationFrame(()=>{t.requestAnimationFrame(...e)})},setTimeout(...e){let n=setTimeout(...e);t.add(()=>clearTimeout(n))},microTask(...e){let n={current:!0};return _(()=>{n.current&&e[0]()}),t.add(()=>{n.current=!1})},style(e,t,n){let l=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:l})})},group(e){let t=Y();return e(t),this.add(()=>t.dispose())},add:t=>(e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}var Q;let J=Symbol("headlessui.useid"),X=0;const Z=null!=(Q=y)?Q:function(){return g(J,()=>""+ ++X)()};function ee(e){var t;if(null==e||null==e.value)return null;let n=null!=(t=e.value.$el)?t:e.value;return n instanceof Node?n:null}function te(e,t,...n){if(e in t){let l=t[e];return"function"==typeof l?l(...n):l}let l=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,te),l}var ne=Object.defineProperty,le=(e,t,n)=>(((e,t,n)=>{t in e?ne(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let re=new class{constructor(){le(this,"current",this.detect()),le(this,"currentId",0)}set(e){this.current!==e&&(this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}};function oe(e){if(re.isServer)return null;if(e instanceof Node)return e.ownerDocument;if(null!=e&&e.hasOwnProperty("value")){let t=ee(e);if(t)return t.ownerDocument}return document}let ie=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var ue,ae,se,de=((se=de||{})[se.First=1]="First",se[se.Previous=2]="Previous",se[se.Next=4]="Next",se[se.Last=8]="Last",se[se.WrapAround=16]="WrapAround",se[se.NoScroll=32]="NoScroll",se),ce=((ae=ce||{})[ae.Error=0]="Error",ae[ae.Overflow=1]="Overflow",ae[ae.Success=2]="Success",ae[ae.Underflow=3]="Underflow",ae),fe=((ue=fe||{})[ue.Previous=-1]="Previous",ue[ue.Next=1]="Next",ue);function pe(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(ie)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var ve=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(ve||{});function he(e,t=0){var n;return e!==(null==(n=oe(e))?void 0:n.body)&&te(t,{0:()=>e.matches(ie),1(){let t=e;for(;null!==t;){if(t.matches(ie))return!0;t=t.parentElement}return!1}})}function be(e){let t=oe(e);S(()=>{t&&!he(t.activeElement,0)&&ye(e)})}var me=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(me||{});function ye(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));let ge=["textarea","input"].join(",");function we(e,t=e=>e){return e.slice().sort((e,n)=>{let l=t(e),r=t(n);if(null===l||null===r)return 0;let o=l.compareDocumentPosition(r);return o&Node.DOCUMENT_POSITION_FOLLOWING?-1:o&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function Se(e,t,{sorted:n=!0,relativeTo:l=null,skipElements:r=[]}={}){var o;let i=null!=(o=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:null==e?void 0:e.ownerDocument)?o:document,u=Array.isArray(e)?n?we(e):e:pe(e);r.length>0&&u.length>1&&(u=u.filter(e=>!r.includes(e))),l=null!=l?l:i.activeElement;let a,s=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,u.indexOf(l))-1;if(4&t)return Math.max(0,u.indexOf(l))+1;if(8&t)return u.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},f=0,p=u.length;do{if(f>=p||f+p<=0)return 0;let e=d+f;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}a=u[e],null==a||a.focus(c),f+=s}while(a!==i.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,ge))&&n}(a)&&a.select(),2}function Pe(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function xe(){return Pe()||/Android/gi.test(window.navigator.userAgent)}function Oe(e,t,n){re.isServer||P(l=>{document.addEventListener(e,t,n),l(()=>document.removeEventListener(e,t,n))})}function Fe(e,t,n){re.isServer||P(l=>{window.addEventListener(e,t,n),l(()=>window.removeEventListener(e,t,n))})}function Te(e,t,n=d(()=>!0)){function l(l,r){if(!n.value||l.defaultPrevented)return;let o=r(l);if(null===o||!o.getRootNode().contains(o))return;let i=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e);for(let e of i){if(null===e)continue;let t=e instanceof HTMLElement?e:ee(e);if(null!=t&&t.contains(o)||l.composed&&l.composedPath().includes(t))return}return!he(o,ve.Loose)&&-1!==o.tabIndex&&l.preventDefault(),t(l,o)}let r=b(null);Oe("pointerdown",e=>{var t,l;n.value&&(r.value=(null==(l=null==(t=e.composedPath)?void 0:t.call(e))?void 0:l[0])||e.target)},!0),Oe("mousedown",e=>{var t,l;n.value&&(r.value=(null==(l=null==(t=e.composedPath)?void 0:t.call(e))?void 0:l[0])||e.target)},!0),Oe("click",e=>{xe()||r.value&&(l(e,()=>r.value),r.value=null)},!0),Oe("touchend",e=>l(e,()=>e.target instanceof HTMLElement?e.target:null),!0),Fe("blur",e=>l(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function Ee(e,t){if(e)return e;let n=null!=t?t:"button";return"string"==typeof n&&"button"===n.toLowerCase()?"button":void 0}function Me(e,t){let n=b(Ee(e.value.type,e.value.as));return x(()=>{n.value=Ee(e.value.type,e.value.as)}),P(()=>{var e;n.value||ee(t)&&ee(t)instanceof HTMLButtonElement&&(null==(e=ee(t))||!e.hasAttribute("type"))&&(n.value="button")}),n}function Be(e){return[e.screenX,e.screenY]}function Ce(){let e=b([-1,-1]);return{wasMoved(t){let n=Be(t);return(e.value[0]!==n[0]||e.value[1]!==n[1])&&(e.value=n,!0)},update(t){e.value=Be(t)}}}function Le({container:e,accept:t,walk:n,enabled:l}){P(()=>{let r=e.value;if(!r||void 0!==l&&!l.value)return;let o=oe(e);if(!o)return;let i=Object.assign(e=>t(e),{acceptNode:t}),u=o.createTreeWalker(r,NodeFilter.SHOW_ELEMENT,i,!1);for(;u.nextNode();)n(u.currentNode)})}var je,De=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(De||{}),ke=((je=ke||{})[je.Unmount=0]="Unmount",je[je.Hidden=1]="Hidden",je);function Ie(e){var t,n=e,{visible:l=!0,features:r=0,ourProps:o,theirProps:i}=n,d=s(n,["visible","features","ourProps","theirProps"]);let c=Re(i,o),f=Object.assign(d,{props:c});return l||2&r&&c.static?Ae(f):1&r?te(null==(t=c.unmount)||t?0:1,{0:()=>null,1:()=>Ae(a(u({},d),{props:a(u({},c),{hidden:!0,style:{display:"none"}})}))}):Ae(f)}function Ae({props:e,attrs:t,slots:n,slot:l,name:r}){var o,i;let u=Ve(e,["unmount","static"]),{as:a}=u,d=s(u,["as"]),c=null==(o=n.default)?void 0:o.call(n,l),f={};if(l){let e=!1,t=[];for(let[n,r]of Object.entries(l))"boolean"==typeof r&&(e=!0),!0===r&&t.push(n);e&&(f["data-headlessui-state"]=t.join(" "))}if("template"===a){if(c=$e(null!=c?c:[]),Object.keys(d).length>0||Object.keys(t).length>0){let[e,...n]=null!=c?c:[];if(!function(e){return null!=e&&("string"==typeof e.type||"object"==typeof e.type||"function"==typeof e.type)}(e)||n.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${r} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(d).concat(Object.keys(t)).map(e=>e.trim()).filter((e,t,n)=>n.indexOf(e)===t).sort((e,t)=>e.localeCompare(t)).map(e=>`  - ${e}`).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join("\n")].join("\n"));let l=Re(null!=(i=e.props)?i:{},d,f),o=O(e,l,!0);for(let t in l)t.startsWith("on")&&(o.props||(o.props={}),o.props[t]=l[t]);return o}return Array.isArray(c)&&1===c.length?c[0]:c}return F(a,Object.assign({},d,f),{default:()=>c})}function $e(e){return e.flatMap(e=>e.type===T?$e(e.children):[e])}function Re(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let l of e)for(let e in l)e.startsWith("on")&&"function"==typeof l[e]?(null!=n[e]||(n[e]=[]),n[e].push(l[e])):t[e]=l[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(e=>[e,void 0])));for(let l in n)Object.assign(t,{[l](e,...t){let r=n[l];for(let n of r){if(e instanceof Event&&e.defaultPrevented)return;n(e,...t)}}});return t}function Ne(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function Ve(e,t=[]){let n=Object.assign({},e);for(let l of t)l in n&&delete n[l];return n}var He=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(He||{});let Ge=E({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup:(e,{slots:t,attrs:n})=>()=>{var l;let r=e,{features:o}=r,i=s(r,["features"]);return Ie({ourProps:{"aria-hidden":!(2&~o)||(null!=(l=i["aria-hidden"])?l:void 0),hidden:!(4&~o)||void 0,style:u({position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},!(4&~o)&&!!(2&~o)&&{display:"none"})},theirProps:i,slot:{},attrs:n,slots:t,name:"Hidden"})}}),Ke=Symbol("Context");var qe=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(qe||{});function Ue(){return g(Ke,null)}function ze(e){w(Ke,e)}var We,_e,Ye=((We=Ye||{}).Space=" ",We.Enter="Enter",We.Escape="Escape",We.Backspace="Backspace",We.Delete="Delete",We.ArrowLeft="ArrowLeft",We.ArrowUp="ArrowUp",We.ArrowRight="ArrowRight",We.ArrowDown="ArrowDown",We.Home="Home",We.End="End",We.PageUp="PageUp",We.PageDown="PageDown",We.Tab="Tab",We),Qe=((_e=Qe||{})[_e.Left=0]="Left",_e[_e.Right=2]="Right",_e);let Je=[];!function(){function e(){"loading"!==document.readyState&&((()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&Je[0]!==e.target&&(Je.unshift(e.target),Je=Je.filter(e=>null!=e&&e.isConnected),Je.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})})(),document.removeEventListener("DOMContentLoaded",e))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",e),e())}();var Xe,Ze=((Xe=Ze||{})[Xe.First=0]="First",Xe[Xe.Previous=1]="Previous",Xe[Xe.Next=2]="Next",Xe[Xe.Last=3]="Last",Xe[Xe.Specific=4]="Specific",Xe[Xe.Nothing=5]="Nothing",Xe);function et(e,t){let n=t.resolveItems();if(n.length<=0)return null;let l=t.resolveActiveIndex(),r=null!=l?l:-1;switch(e.focus){case 0:for(let e=0;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return l;case 1:-1===r&&(r=n.length);for(let e=r-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return l;case 2:for(let e=r+1;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return l;case 3:for(let e=n.length-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return l;case 4:for(let l=0;l<n.length;++l)if(t.resolveId(n[l],l,n)===e.id)return l;return l;case 5:return null;default:!function(e){throw new Error("Unexpected object: "+e)}(e)}}function tt(e={},t=null,n=[]){for(let[l,r]of Object.entries(e))lt(n,nt(t,l),r);return n}function nt(e,t){return e?e+"["+t+"]":t}function lt(e,t,n){if(Array.isArray(n))for(let[l,r]of n.entries())lt(e,nt(t,l.toString()),r);else n instanceof Date?e.push([t,n.toISOString()]):"boolean"==typeof n?e.push([t,n?"1":"0"]):"string"==typeof n?e.push([t,n]):"number"==typeof n?e.push([t,`${n}`]):null==n?e.push([t,""]):tt(n,t,e)}function rt(e){var t,n;let l=null!=(t=null==e?void 0:e.form)?t:e.closest("form");if(l){for(let t of l.elements)if(t!==e&&("INPUT"===t.tagName&&"submit"===t.type||"BUTTON"===t.tagName&&"submit"===t.type||"INPUT"===t.nodeName&&"image"===t.type))return void t.click();null==(n=l.requestSubmit)||n.call(l)}}var ot,it={},ut=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(ut||{}),at=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(at||{}),st=((ot=st||{})[ot.Pointer=0]="Pointer",ot[ot.Focus=1]="Focus",ot[ot.Other=2]="Other",ot);let dt=Symbol("ComboboxContext");function ct(e){let t=g(dt,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ct),t}return t}let ft=Symbol("VirtualContext"),pt=E({name:"VirtualProvider",setup(e,{slots:t}){let n=ct("VirtualProvider"),l=d(()=>{let e=ee(n.optionsRef);if(!e)return{start:0,end:0};let t=window.getComputedStyle(e);return{start:parseFloat(t.paddingBlockStart||t.paddingTop),end:parseFloat(t.paddingBlockEnd||t.paddingBottom)}}),r=(s=d(()=>({scrollPaddingStart:l.value.start,scrollPaddingEnd:l.value.end,count:n.virtual.value.options.length,estimateSize:()=>40,getScrollElement:()=>ee(n.optionsRef),overscan:12})),function(e){const t=new U(c(e)),n=f(t),l=t.l();return p(()=>c(e).getScrollElement(),e=>{e&&t.o()},{immediate:!0}),p(()=>c(e),e=>{t.setOptions(a(u({},e),{onChange:(t,l)=>{var r;h(n),null==(r=e.onChange)||r.call(e,t,l)}})),t.o(),h(n)},{immediate:!0}),v(l),n}(d(()=>u({observeElementRect:N,observeElementOffset:G,scrollToFn:q},c(s))))),o=d(()=>{var e;return null==(e=n.virtual.value)?void 0:e.options}),i=b(0);var s;return p([o],()=>{i.value+=1}),w(ft,n.virtual.value?r:null),()=>[F("div",{style:{position:"relative",width:"100%",height:`${r.value.getTotalSize()}px`},ref:e=>{if(e){if("undefined"!=typeof process&&void 0!==it.JEST_WORKER_ID||0===n.activationTrigger.value)return;null!==n.activeOptionIndex.value&&n.virtual.value.options.length>n.activeOptionIndex.value&&r.value.scrollToIndex(n.activeOptionIndex.value)}}},r.value.getVirtualItems().map(e=>O(t.default({option:n.virtual.value.options[e.index],open:0===n.comboboxState.value})[0],{key:`${i.value}-${e.index}`,"data-index":e.index,"aria-setsize":n.virtual.value.options.length,"aria-posinset":e.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${e.start}px)`,overflowAnchor:"none"}})))]}}),vt=E({name:"Combobox",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],nullable:!0,default:null},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},nullable:{type:Boolean,default:!1},multiple:{type:[Boolean],default:!1},immediate:{type:[Boolean],default:!1},virtual:{type:Object,default:null}},inheritAttrs:!1,setup(e,{slots:t,attrs:n,emit:l}){let r=b(1),o=b(null),i=b(null),a=b(null),c=b(null),f=b({static:!1,hold:!1}),v=b([]),h=b(null),m=b(2),y=b(!1);function g(e=e=>e){let t=null!==h.value?v.value[h.value]:null,n=e(v.value.slice()),l=n.length>0&&null!==n[0].dataRef.order.value?n.sort((e,t)=>e.dataRef.order.value-t.dataRef.order.value):we(n,e=>ee(e.dataRef.domRef)),r=t?l.indexOf(t):null;return-1===r&&(r=null),{options:l,activeOptionIndex:r}}let S=d(()=>e.multiple?1:0),P=d(()=>e.nullable),[O,E]=W(d(()=>e.modelValue),e=>l("update:modelValue",e),d(()=>e.defaultValue)),C=d(()=>void 0===O.value?te(S.value,{1:[],0:void 0}):O.value),L=null,j=null;function D(e){return te(S.value,{0:()=>null==E?void 0:E(e),1:()=>{let t=M(I.value.value).slice(),n=M(e),l=t.findIndex(e=>I.compare(n,M(e)));return-1===l?t.push(n):t.splice(l,1),null==E?void 0:E(t)}})}let k=d(()=>{});p([k],([e],[t])=>{if(I.virtual.value&&e&&t&&null!==h.value){let n=e.indexOf(t[h.value]);h.value=-1!==n?n:null}});let I={comboboxState:r,value:C,mode:S,compare(t,n){if("string"==typeof e.by){let l=e.by;return(null==t?void 0:t[l])===(null==n?void 0:n[l])}return null===e.by?function(e,t){return e===t}(t,n):e.by(t,n)},calculateIndex:t=>I.virtual.value?null===e.by?I.virtual.value.options.indexOf(t):I.virtual.value.options.findIndex(e=>I.compare(e,t)):v.value.findIndex(e=>I.compare(e.dataRef.value,t)),defaultValue:d(()=>e.defaultValue),nullable:P,immediate:d(()=>!1),virtual:d(()=>null),inputRef:i,labelRef:o,buttonRef:a,optionsRef:c,disabled:d(()=>e.disabled),options:v,change(e){E(e)},activeOptionIndex:d(()=>{if(y.value&&null===h.value&&(I.virtual.value?I.virtual.value.options.length>0:v.value.length>0)){if(I.virtual.value){let e=I.virtual.value.options.findIndex(e=>{var t;return!(null!=(t=I.virtual.value)&&t.disabled(e))});if(-1!==e)return e}let e=v.value.findIndex(e=>!e.dataRef.disabled);if(-1!==e)return e}return h.value}),activationTrigger:m,optionsPropsRef:f,closeCombobox(){y.value=!1,!e.disabled&&1!==r.value&&(r.value=1,h.value=null)},openCombobox(){if(y.value=!0,!e.disabled&&0!==r.value){if(I.value.value){let e=I.calculateIndex(I.value.value);-1!==e&&(h.value=e)}r.value=0}},setActivationTrigger(e){m.value=e},goToOption(t,n,l){y.value=!1,null!==L&&cancelAnimationFrame(L),L=requestAnimationFrame(()=>{if(e.disabled||c.value&&!f.value.static&&1===r.value)return;if(I.virtual.value)return h.value=t===Ze.Specific?n:et({focus:t},{resolveItems:()=>I.virtual.value.options,resolveActiveIndex:()=>{var e,t;return null!=(t=null!=(e=I.activeOptionIndex.value)?e:I.virtual.value.options.findIndex(e=>{var t;return!(null!=(t=I.virtual.value)&&t.disabled(e))}))?t:null},resolveDisabled:e=>I.virtual.value.disabled(e),resolveId(){throw new Error("Function not implemented.")}}),void(m.value=null!=l?l:2);let o=g();if(null===o.activeOptionIndex){let e=o.options.findIndex(e=>!e.dataRef.disabled);-1!==e&&(o.activeOptionIndex=e)}let i=t===Ze.Specific?n:et({focus:t},{resolveItems:()=>o.options,resolveActiveIndex:()=>o.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.disabled});h.value=i,m.value=null!=l?l:2,v.value=o.options})},selectOption(e){let t=v.value.find(t=>t.id===e);if(!t)return;let{dataRef:n}=t;D(n.value)},selectActiveOption(){if(null!==I.activeOptionIndex.value){if(I.virtual.value)D(I.virtual.value.options[I.activeOptionIndex.value]);else{let{dataRef:e}=v.value[I.activeOptionIndex.value];D(e.value)}I.goToOption(Ze.Specific,I.activeOptionIndex.value)}},registerOption(e,t){let n=B({id:e,dataRef:t});if(I.virtual.value)return void v.value.push(n);j&&cancelAnimationFrame(j);let l=g(e=>(e.push(n),e));null===h.value&&I.isSelected(t.value.value)&&(l.activeOptionIndex=l.options.indexOf(n)),v.value=l.options,h.value=l.activeOptionIndex,m.value=2,l.options.some(e=>!ee(e.dataRef.domRef))&&(j=requestAnimationFrame(()=>{let e=g();v.value=e.options,h.value=e.activeOptionIndex}))},unregisterOption(e,t){if(null!==L&&cancelAnimationFrame(L),t&&(y.value=!0),I.virtual.value)return void(v.value=v.value.filter(t=>t.id!==e));let n=g(t=>{let n=t.findIndex(t=>t.id===e);return-1!==n&&t.splice(n,1),t});v.value=n.options,h.value=n.activeOptionIndex,m.value=2},isSelected:e=>te(S.value,{0:()=>I.compare(M(I.value.value),M(e)),1:()=>M(I.value.value).some(t=>I.compare(M(t),M(e)))}),isActive:e=>h.value===I.calculateIndex(e)};Te([i,a,c],()=>I.closeCombobox(),d(()=>0===r.value)),w(dt,I),ze(d(()=>te(r.value,{0:qe.Open,1:qe.Closed})));let A=d(()=>{var e;return null==(e=ee(i))?void 0:e.closest("form")});return x(()=>{p([A],()=>{if(A.value&&void 0!==e.defaultValue)return A.value.addEventListener("reset",t),()=>{var e;null==(e=A.value)||e.removeEventListener("reset",t)};function t(){I.change(e.defaultValue)}},{immediate:!0})}),()=>{var l,o,i;let a=e,{name:d,disabled:c,form:f}=a,p=s(a,["name","disabled","form"]),v={open:0===r.value,disabled:c,activeIndex:I.activeOptionIndex.value,activeOption:null===I.activeOptionIndex.value?null:I.virtual.value?I.virtual.value.options[null!=(l=I.activeOptionIndex.value)?l:0]:null!=(i=null==(o=I.options.value[I.activeOptionIndex.value])?void 0:o.dataRef.value)?i:null,value:C.value};return F(T,[...null!=d&&null!=C.value?tt({[d]:C.value}).map(([e,t])=>F(Ge,Ne({features:He.Hidden,key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:f,disabled:c,name:e,value:t}))):[],Ie({theirProps:u(u({},n),Ve(p,["by","defaultValue","immediate","modelValue","multiple","nullable","onUpdate:modelValue","virtual"])),ourProps:{},slot:v,slots:t,attrs:n,name:"Combobox"})])}}}),ht=E({name:"ComboboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n}){var l;let r=null!=(l=e.id)?l:`headlessui-combobox-label-${Z()}`,o=ct("ComboboxLabel");function i(){var e;null==(e=ee(o.inputRef))||e.focus({preventScroll:!0})}return()=>{let l={open:0===o.comboboxState.value,disabled:o.disabled.value},u=s(e,[]);return Ie({ourProps:{id:r,ref:o.labelRef,onClick:i},theirProps:u,slot:l,attrs:t,slots:n,name:"ComboboxLabel"})}}}),bt=E({name:"ComboboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-combobox-button-${Z()}`,i=ct("ComboboxButton");function u(e){i.disabled.value||(0===i.comboboxState.value?i.closeCombobox():(e.preventDefault(),i.openCombobox()),S(()=>{var e;return null==(e=ee(i.inputRef))?void 0:e.focus({preventScroll:!0})}))}function a(e){switch(e.key){case Ye.ArrowDown:return e.preventDefault(),e.stopPropagation(),1===i.comboboxState.value&&i.openCombobox(),void S(()=>{var e;return null==(e=i.inputRef.value)?void 0:e.focus({preventScroll:!0})});case Ye.ArrowUp:return e.preventDefault(),e.stopPropagation(),1===i.comboboxState.value&&(i.openCombobox(),S(()=>{i.value.value||i.goToOption(Ze.Last)})),void S(()=>{var e;return null==(e=i.inputRef.value)?void 0:e.focus({preventScroll:!0})});case Ye.Escape:if(0!==i.comboboxState.value)return;return e.preventDefault(),i.optionsRef.value&&!i.optionsPropsRef.value.static&&e.stopPropagation(),i.closeCombobox(),void S(()=>{var e;return null==(e=i.inputRef.value)?void 0:e.focus({preventScroll:!0})})}}l({el:i.buttonRef,$el:i.buttonRef});let c=Me(d(()=>({as:e.as,type:t.type})),i.buttonRef);return()=>{var l,r;let d={open:0===i.comboboxState.value,disabled:i.disabled.value,value:i.value.value},f=s(e,[]);return Ie({ourProps:{ref:i.buttonRef,id:o,type:c.value,tabindex:"-1","aria-haspopup":"listbox","aria-controls":null==(l=ee(i.optionsRef))?void 0:l.id,"aria-expanded":0===i.comboboxState.value,"aria-labelledby":i.labelRef.value?[null==(r=ee(i.labelRef))?void 0:r.id,o].join(" "):void 0,disabled:!0===i.disabled.value||void 0,onKeydown:a,onClick:u},theirProps:f,slot:d,attrs:t,slots:n,name:"ComboboxButton"})}}}),mt=E({name:"ComboboxInput",props:{as:{type:[Object,String],default:"input"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},displayValue:{type:Function},defaultValue:{type:String,default:void 0},id:{type:String,default:null}},emits:{change:e=>!0},setup(e,{emit:t,attrs:n,slots:l,expose:r}){var o;let i=null!=(o=e.id)?o:`headlessui-combobox-input-${Z()}`,u=ct("ComboboxInput"),a=d(()=>oe(ee(u.inputRef))),c={value:!1};function f(){u.change(null);let e=ee(u.optionsRef);e&&(e.scrollTop=0),u.goToOption(Ze.Nothing)}r({el:u.inputRef,$el:u.inputRef});let v=d(()=>{var t;let n=u.value.value;return ee(u.inputRef)?void 0!==e.displayValue&&void 0!==n?null!=(t=e.displayValue(n))?t:"":"string"==typeof n?n:"":""});x(()=>{p([v,u.comboboxState,a],([e,t],[n,l])=>{if(c.value)return;let r=ee(u.inputRef);r&&((0===l&&1===t||e!==n)&&(r.value=e),requestAnimationFrame(()=>{var e;if(c.value||!r||(null==(e=a.value)?void 0:e.activeElement)!==r)return;let{selectionStart:t,selectionEnd:n}=r;0===Math.abs((null!=n?n:0)-(null!=t?t:0))&&0===t&&r.setSelectionRange(r.value.length,r.value.length)}))},{immediate:!0}),p([u.comboboxState],([e],[t])=>{if(0===e&&1===t){if(c.value)return;let e=ee(u.inputRef);if(!e)return;let t=e.value,{selectionStart:n,selectionEnd:l,selectionDirection:r}=e;e.value="",e.value=t,null!==r?e.setSelectionRange(n,l,r):e.setSelectionRange(n,l)}})});let h=b(!1);function y(){h.value=!0}function g(){Y().nextFrame(()=>{h.value=!1})}let w=function(){let e=function(){let e=Y();return m(()=>e.dispose()),e}();return t=>{e.dispose(),e.nextFrame(t)}}();function P(e){switch(c.value=!0,w(()=>{c.value=!1}),e.key){case Ye.Enter:if(c.value=!1,0!==u.comboboxState.value||h.value)return;if(e.preventDefault(),e.stopPropagation(),null===u.activeOptionIndex.value)return void u.closeCombobox();u.selectActiveOption(),0===u.mode.value&&u.closeCombobox();break;case Ye.ArrowDown:return c.value=!1,e.preventDefault(),e.stopPropagation(),te(u.comboboxState.value,{0:()=>u.goToOption(Ze.Next),1:()=>u.openCombobox()});case Ye.ArrowUp:return c.value=!1,e.preventDefault(),e.stopPropagation(),te(u.comboboxState.value,{0:()=>u.goToOption(Ze.Previous),1:()=>{u.openCombobox(),S(()=>{u.value.value||u.goToOption(Ze.Last)})}});case Ye.Home:if(e.shiftKey)break;return c.value=!1,e.preventDefault(),e.stopPropagation(),u.goToOption(Ze.First);case Ye.PageUp:return c.value=!1,e.preventDefault(),e.stopPropagation(),u.goToOption(Ze.First);case Ye.End:if(e.shiftKey)break;return c.value=!1,e.preventDefault(),e.stopPropagation(),u.goToOption(Ze.Last);case Ye.PageDown:return c.value=!1,e.preventDefault(),e.stopPropagation(),u.goToOption(Ze.Last);case Ye.Escape:if(c.value=!1,0!==u.comboboxState.value)return;e.preventDefault(),u.optionsRef.value&&!u.optionsPropsRef.value.static&&e.stopPropagation(),u.nullable.value&&0===u.mode.value&&null===u.value.value&&f(),u.closeCombobox();break;case Ye.Tab:if(c.value=!1,0!==u.comboboxState.value)return;0===u.mode.value&&1!==u.activationTrigger.value&&u.selectActiveOption(),u.closeCombobox()}}function O(e){t("change",e),u.nullable.value&&0===u.mode.value&&""===e.target.value&&f(),u.openCombobox()}function F(e){var t,n,l;let r=null!=(t=e.relatedTarget)?t:Je.find(t=>t!==e.currentTarget);if(c.value=!1,!(null!=(n=ee(u.optionsRef))&&n.contains(r)||null!=(l=ee(u.buttonRef))&&l.contains(r)||0!==u.comboboxState.value))return e.preventDefault(),0===u.mode.value&&(u.nullable.value&&null===u.value.value?f():1!==u.activationTrigger.value&&u.selectActiveOption()),u.closeCombobox()}function T(e){var t,n,l;let r=null!=(t=e.relatedTarget)?t:Je.find(t=>t!==e.currentTarget);null!=(n=ee(u.buttonRef))&&n.contains(r)||null!=(l=ee(u.optionsRef))&&l.contains(r)||u.disabled.value||u.immediate.value&&0!==u.comboboxState.value&&(u.openCombobox(),Y().nextFrame(()=>{u.setActivationTrigger(1)}))}let E=d(()=>{var t,n,l,r;return null!=(r=null!=(l=null!=(n=e.defaultValue)?n:void 0!==u.defaultValue.value?null==(t=e.displayValue)?void 0:t.call(e,u.defaultValue.value):null)?l:u.defaultValue.value)?r:""});return()=>{var t,r,o,a,d,c,f;let p={open:0===u.comboboxState.value},v=e,{displayValue:h,onChange:b}=v,m=s(v,["displayValue","onChange"]);return Ie({ourProps:{"aria-controls":null==(t=u.optionsRef.value)?void 0:t.id,"aria-expanded":0===u.comboboxState.value,"aria-activedescendant":null===u.activeOptionIndex.value?void 0:u.virtual.value?null==(r=u.options.value.find(e=>!u.virtual.value.disabled(e.dataRef.value)&&u.compare(e.dataRef.value,u.virtual.value.options[u.activeOptionIndex.value])))?void 0:r.id:null==(o=u.options.value[u.activeOptionIndex.value])?void 0:o.id,"aria-labelledby":null!=(c=null==(a=ee(u.labelRef))?void 0:a.id)?c:null==(d=ee(u.buttonRef))?void 0:d.id,"aria-autocomplete":"list",id:i,onCompositionstart:y,onCompositionend:g,onKeydown:P,onInput:O,onFocus:T,onBlur:F,role:"combobox",type:null!=(f=n.type)?f:"text",tabIndex:0,ref:u.inputRef,defaultValue:E.value,disabled:!0===u.disabled.value||void 0},theirProps:m,slot:p,attrs:n,slots:l,features:De.RenderStrategy|De.Static,name:"ComboboxInput"})}}}),yt=E({name:"ComboboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},hold:{type:[Boolean],default:!1}},setup(e,{attrs:t,slots:n,expose:l}){let r=ct("ComboboxOptions"),o=`headlessui-combobox-options-${Z()}`;l({el:r.optionsRef,$el:r.optionsRef}),P(()=>{r.optionsPropsRef.value.static=e.static}),P(()=>{r.optionsPropsRef.value.hold=e.hold});let i=Ue(),s=d(()=>null!==i?(i.value&qe.Open)===qe.Open:0===r.comboboxState.value);function c(e){e.preventDefault()}return Le({container:d(()=>ee(r.optionsRef)),enabled:d(()=>0===r.comboboxState.value),accept:e=>"option"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}}),()=>{var l,i,d;let f={open:0===r.comboboxState.value};return Ie({ourProps:{"aria-labelledby":null!=(d=null==(l=ee(r.labelRef))?void 0:l.id)?d:null==(i=ee(r.buttonRef))?void 0:i.id,id:o,ref:r.optionsRef,role:"listbox","aria-multiselectable":1===r.mode.value||void 0,onMousedown:c},theirProps:Ve(e,["hold"]),slot:f,attrs:t,slots:r.virtual.value&&0===r.comboboxState.value?a(u({},n),{default:()=>[F(pt,{},n.default)]}):n,features:De.RenderStrategy|De.Static,visible:s.value,name:"ComboboxOptions"})}}}),gt=E({name:"ComboboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},order:{type:[Number],default:null}},setup(e,{slots:t,attrs:n,expose:l}){let r=ct("ComboboxOption"),o=`headlessui-combobox-option-${Z()}`,i=b(null),u=d(()=>e.disabled);l({el:i,$el:i});let a=d(()=>{var t;return r.virtual.value?r.activeOptionIndex.value===r.calculateIndex(e.value):null!==r.activeOptionIndex.value&&(null==(t=r.options.value[r.activeOptionIndex.value])?void 0:t.id)===o}),s=d(()=>r.isSelected(e.value)),c=g(ft,null),f=d(()=>({disabled:e.disabled,value:e.value,domRef:i,order:d(()=>e.order)}));function p(e){e.preventDefault(),e.button===Qe.Left&&(u.value||(r.selectOption(o),xe()||requestAnimationFrame(()=>{var e;return null==(e=ee(r.inputRef))?void 0:e.focus({preventScroll:!0})}),0===r.mode.value&&r.closeCombobox()))}function v(){var t;if(e.disabled||null!=(t=r.virtual.value)&&t.disabled(e.value))return r.goToOption(Ze.Nothing);let n=r.calculateIndex(e.value);r.goToOption(Ze.Specific,n)}x(()=>r.registerOption(o,f)),m(()=>r.unregisterOption(o,a.value)),P(()=>{let e=ee(i);e&&(null==c||c.value.measureElement(e))}),P(()=>{0===r.comboboxState.value&&a.value&&(r.virtual.value||0!==r.activationTrigger.value&&S(()=>{var e,t;return null==(t=null==(e=ee(i))?void 0:e.scrollIntoView)?void 0:t.call(e,{block:"nearest"})}))});let h=Ce();function y(e){h.update(e)}function w(t){var n;if(!h.wasMoved(t)||e.disabled||null!=(n=r.virtual.value)&&n.disabled(e.value)||a.value)return;let l=r.calculateIndex(e.value);r.goToOption(Ze.Specific,l,0)}function O(t){var n;h.wasMoved(t)&&(e.disabled||null!=(n=r.virtual.value)&&n.disabled(e.value)||a.value&&(r.optionsPropsRef.value.hold||r.goToOption(Ze.Nothing)))}return()=>{let{disabled:l}=e,r={active:a.value,selected:s.value,disabled:l};return Ie({ourProps:{id:o,ref:i,role:"option",tabIndex:!0===l?void 0:-1,"aria-disabled":!0===l||void 0,"aria-selected":s.value,disabled:void 0,onMousedown:p,onFocus:v,onPointerenter:y,onMouseenter:y,onPointermove:w,onMousemove:w,onPointerleave:O,onMouseleave:O},theirProps:Ve(e,["order","value"]),slot:r,attrs:n,slots:t,name:"ComboboxOption"})}}});function wt(e,t,n,l){re.isServer||P(r=>{(e=null!=e?e:window).addEventListener(t,n,l),r(()=>e.removeEventListener(t,n,l))})}var St=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(St||{});function Pt(){let e=b(0);return Fe("keydown",t=>{"Tab"===t.key&&(e.value=t.shiftKey?1:0)}),e}function xt(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.value){let e=ee(n);e instanceof HTMLElement&&t.add(e)}return t}var Ot=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(Ot||{});let Ft=Object.assign(E({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:b(new Set)}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:l}){let r=b(null);l({el:r,$el:r});let o=d(()=>oe(r)),i=b(!1);x(()=>i.value=!0),m(()=>i.value=!1),function({ownerDocument:e},t){let n=function(e){let t=b(Je.slice());return p([e],([e],[n])=>{!0===n&&!1===e?_(()=>{t.value.splice(0)}):!1===n&&!0===e&&(t.value=Je.slice())},{flush:"post"}),()=>{var e;return null!=(e=t.value.find(e=>null!=e&&e.isConnected))?e:null}}(t);x(()=>{P(()=>{var l,r;t.value||(null==(l=e.value)?void 0:l.activeElement)===(null==(r=e.value)?void 0:r.body)&&ye(n())},{flush:"post"})}),m(()=>{t.value&&ye(n())})}({ownerDocument:o},d(()=>i.value&&Boolean(16&e.features)));let a=function({ownerDocument:e,container:t,initialFocus:n},l){let r=b(null),o=b(!1);return x(()=>o.value=!0),m(()=>o.value=!1),x(()=>{p([t,n,l],(i,u)=>{if(i.every((e,t)=>(null==u?void 0:u[t])===e)||!l.value)return;let a=ee(t);a&&_(()=>{var t,l;if(!o.value)return;let i=ee(n),u=null==(t=e.value)?void 0:t.activeElement;if(i){if(i===u)return void(r.value=u)}else if(a.contains(u))return void(r.value=u);i?ye(i):(Se(a,de.First|de.NoScroll),ce.Error),r.value=null==(l=e.value)?void 0:l.activeElement})},{immediate:!0,flush:"post"})}),r}({ownerDocument:o,container:r,initialFocus:d(()=>e.initialFocus)},d(()=>i.value&&Boolean(2&e.features)));!function({ownerDocument:e,container:t,containers:n,previousActiveElement:l},r){var o;wt(null==(o=e.value)?void 0:o.defaultView,"focus",e=>{if(!r.value)return;let o=xt(n);ee(t)instanceof HTMLElement&&o.add(ee(t));let i=l.value;if(!i)return;let u=e.target;u&&u instanceof HTMLElement?Tt(o,u)?(l.value=u,ye(u)):(e.preventDefault(),e.stopPropagation(),ye(i)):ye(l.value)},!0)}({ownerDocument:o,container:r,containers:e.containers,previousActiveElement:a},d(()=>i.value&&Boolean(8&e.features)));let c=Pt();function f(e){let t=ee(r);t&&te(c.value,{[St.Forwards]:()=>{Se(t,de.First,{skipElements:[e.relatedTarget]})},[St.Backwards]:()=>{Se(t,de.Last,{skipElements:[e.relatedTarget]})}})}let v=b(!1);function h(e){"Tab"===e.key&&(v.value=!0,requestAnimationFrame(()=>{v.value=!1}))}function y(t){if(!i.value)return;let n=xt(e.containers);ee(r)instanceof HTMLElement&&n.add(ee(r));let l=t.relatedTarget;l instanceof HTMLElement&&"true"!==l.dataset.headlessuiFocusGuard&&(Tt(n,l)||(v.value?Se(ee(r),te(c.value,{[St.Forwards]:()=>de.Next,[St.Backwards]:()=>de.Previous})|de.WrapAround,{relativeTo:t.target}):t.target instanceof HTMLElement&&ye(t.target)))}return()=>{let l={ref:r,onKeydown:h,onFocusout:y},o=e,{features:i,initialFocus:a,containers:d}=o,c=s(o,["features","initialFocus","containers"]);return F(T,[Boolean(4&i)&&F(Ge,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:He.Focusable}),Ie({ourProps:l,theirProps:u(u({},t),c),slot:{},attrs:t,slots:n,name:"FocusTrap"}),Boolean(4&i)&&F(Ge,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:He.Focusable})])}}}),{features:Ot});function Tt(e,t){for(let n of e)if(n.contains(t))return!0;return!1}function Et(){let e;return{before({doc:t}){var n;let l=t.documentElement;e=(null!=(n=t.defaultView)?n:window).innerWidth-l.clientWidth},after({doc:t,d:n}){let l=t.documentElement,r=l.clientWidth-l.offsetWidth,o=e-r;n.style(l,"paddingRight",`${o}px`)}}}function Mt(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Bt=function(e,t){let n=new Map,l=new Set;return{getSnapshot:()=>n,subscribe:e=>(l.add(e),()=>l.delete(e)),dispatch(e,...r){let o=t[e].call(n,...r);o&&(n=o,l.forEach(e=>e()))}}}(0,{PUSH(e,t){var n;let l=null!=(n=this.get(e))?n:{doc:e,count:0,d:Y(),meta:new Set};return l.count++,l.meta.add(t),this.set(e,l),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let l={doc:e,d:t,meta:Mt(n)},r=[Pe()?{before({doc:t,d:n,meta:l}){function r(e){return l.containers.flatMap(e=>e()).some(t=>t.contains(e))}n.microTask(()=>{var l;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=Y();e.style(t.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>e.dispose()))}let o=null!=(l=window.scrollY)?l:window.pageYOffset,i=null;n.addEventListener(t,"click",n=>{if(n.target instanceof HTMLElement)try{let e=n.target.closest("a");if(!e)return;let{hash:l}=new URL(e.href),o=t.querySelector(l);o&&!r(o)&&(i=o)}catch(e){}},!0),n.addEventListener(t,"touchstart",e=>{if(e.target instanceof HTMLElement)if(r(e.target)){let t=e.target;for(;t.parentElement&&r(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")}),n.addEventListener(t,"touchmove",e=>{if(e.target instanceof HTMLElement){if("INPUT"===e.target.tagName)return;if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),n.add(()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{},Et(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];r.forEach(({before:e})=>null==e?void 0:e(l)),r.forEach(({after:e})=>null==e?void 0:e(l))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});Bt.subscribe(()=>{let e=Bt.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),l=0!==n.count;(l&&!e||!l&&e)&&Bt.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&Bt.dispatch("TEARDOWN",n)}});let Ct=new Map,Lt=new Map;function jt(e,t=b(!0)){P(n=>{var l;if(!t.value)return;let r=ee(e);if(!r)return;n(function(){var e;if(!r)return;let t=null!=(e=Lt.get(r))?e:1;if(1===t?Lt.delete(r):Lt.set(r,t-1),1!==t)return;let n=Ct.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,Ct.delete(r))});let o=null!=(l=Lt.get(r))?l:0;Lt.set(r,o+1),0===o&&(Ct.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0)})}function Dt({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){let l=b(null),r=oe(l);function o(){var n,o,i;let u=[];for(let t of e)null!==t&&(t instanceof HTMLElement?u.push(t):"value"in t&&t.value instanceof HTMLElement&&u.push(t.value));if(null!=t&&t.value)for(let e of t.value)u.push(e);for(let e of null!=(n=null==r?void 0:r.querySelectorAll("html > *, body > *"))?n:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(ee(l))||e.contains(null==(i=null==(o=ee(l))?void 0:o.getRootNode())?void 0:i.host)||u.some(t=>e.contains(t))||u.push(e));return u}return{resolveContainers:o,contains:e=>o().some(t=>t.contains(e)),mainTreeNodeRef:l,MainTreeNode:()=>null!=n?null:F(Ge,{features:He.Hidden,ref:l})}}let kt=Symbol("ForcePortalRootContext"),It=E({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup:(e,{slots:t,attrs:n})=>(w(kt,e.force),()=>{let l=e,{force:r}=l;return Ie({theirProps:s(l,["force"]),ourProps:{},slot:{},slots:t,attrs:n,name:"ForcePortalRoot"})})}),At=Symbol("StackContext");var $t=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))($t||{});let Rt=Symbol("DescriptionContext");function Nt({slot:e=b({}),name:t="Description",props:n={}}={}){let l=b([]);return w(Rt,{register:function(e){return l.value.push(e),()=>{let t=l.value.indexOf(e);-1!==t&&l.value.splice(t,1)}},slot:e,name:t,props:n}),d(()=>l.value.length>0?l.value.join(" "):void 0)}let Vt=E({name:"Description",props:{as:{type:[Object,String],default:"p"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n}){var l;let r=null!=(l=e.id)?l:`headlessui-description-${Z()}`,o=function(){let e=g(Rt,null);if(null===e)throw new Error("Missing parent");return e}();return x(()=>m(o.register(r))),()=>{let{name:l="Description",slot:i=b({}),props:d={}}=o,f=s(e,[]);return Ie({ourProps:a(u({},Object.entries(d).reduce((e,[t,n])=>Object.assign(e,{[t]:c(n)}),{})),{id:r}),theirProps:f,slot:i.value,attrs:t,slots:n,name:l})}}});const Ht=new WeakMap;function Gt(e,t){let n=t(function(e){var t;return null!=(t=Ht.get(e))?t:0}(e));return n<=0?Ht.delete(e):Ht.set(e,n),n}let Kt=E({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:n}){let l=b(null),r=d(()=>oe(l)),o=g(kt,!1),i=g(zt,null),u=b(!0===o||null==i?function(e){let t=oe(e);if(!t){if(null===e)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let n=t.getElementById("headlessui-portal-root");if(n)return n;let l=t.createElement("div");return l.setAttribute("id","headlessui-portal-root"),t.body.appendChild(l)}(l.value):i.resolveTarget());u.value&&Gt(u.value,e=>e+1);let a=b(!1);x(()=>{a.value=!0}),P(()=>{o||null!=i&&(u.value=i.resolveTarget())});let s=g(qt,null),c=!1,f=L();return p(l,()=>{if(c||!s)return;let e=ee(l);e&&(m(s.register(e),f),c=!0)}),m(()=>{var e,t;let n=null==(e=r.value)?void 0:e.getElementById("headlessui-portal-root");!n||u.value!==n||Gt(u.value,e=>e-1)||u.value.children.length>0||null==(t=u.value.parentElement)||t.removeChild(u.value)}),()=>{if(!a.value||null===u.value)return null;let r={ref:l,"data-headlessui-portal":""};return F(C,{to:u.value},Ie({ourProps:r,theirProps:e,slot:{},attrs:n,slots:t,name:"Portal"}))}}}),qt=Symbol("PortalParentContext");function Ut(){let e=g(qt,null),t=b([]);function n(n){let l=t.value.indexOf(n);-1!==l&&t.value.splice(l,1),e&&e.unregister(n)}let l={register:function(l){return t.value.push(l),e&&e.register(l),()=>n(l)},unregister:n,portals:t};return[t,E({name:"PortalWrapper",setup:(e,{slots:t})=>(w(qt,l),()=>{var e;return null==(e=t.default)?void 0:e.call(t)})})]}let zt=Symbol("PortalGroupContext"),Wt=E({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:n}){let l=B({resolveTarget:()=>e.target});return w(zt,l),()=>{let l=e,{target:r}=l;return Ie({theirProps:s(l,["target"]),ourProps:{},slot:{},attrs:t,slots:n,name:"PortalGroup"})}}});var _t,Yt=((_t=Yt||{})[_t.Open=0]="Open",_t[_t.Closed=1]="Closed",_t);let Qt=Symbol("DialogContext");function Jt(e){let t=g(Qt,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Jt),t}return t}let Xt="DC8F892D-2EBD-447C-A4C8-A03058436FF4",Zt=E({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:Xt},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:"dialog"}},emits:{close:e=>!0},setup(e,{emit:t,attrs:n,slots:l,expose:r}){var o,i;let c=null!=(o=e.id)?o:`headlessui-dialog-${Z()}`,v=b(!1);x(()=>{v.value=!0});let h=!1,y=d(()=>"dialog"===e.role||"alertdialog"===e.role?e.role:(h||(h=!0),"dialog")),O=b(0),T=Ue(),E=d(()=>e.open===Xt&&null!==T?(T.value&qe.Open)===qe.Open:e.open),M=b(null),B=d(()=>oe(M));if(r({el:M,$el:M}),e.open===Xt&&null===T)throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if("boolean"!=typeof E.value)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${E.value===Xt?void 0:e.open}`);let C=d(()=>v.value&&E.value?0:1),L=d(()=>0===C.value),j=d(()=>O.value>1),D=null!==g(Qt,null),[k,I]=Ut(),{resolveContainers:A,mainTreeNodeRef:$,MainTreeNode:R}=Dt({portals:k,defaultContainers:[d(()=>{var e;return null!=(e=W.panelRef.value)?e:M.value})]}),N=d(()=>j.value?"parent":"leaf"),V=d(()=>null!==T&&(T.value&qe.Closing)===qe.Closing),H=d(()=>!D&&!V.value&&L.value),G=d(()=>{var e,t,n;return null!=(n=Array.from(null!=(t=null==(e=B.value)?void 0:e.querySelectorAll("body > *"))?t:[]).find(e=>"headlessui-portal-root"!==e.id&&e.contains(ee($))&&e instanceof HTMLElement))?n:null});jt(G,H);let K=d(()=>!!j.value||L.value),q=d(()=>{var e,t,n;return null!=(n=Array.from(null!=(t=null==(e=B.value)?void 0:e.querySelectorAll("[data-headlessui-portal]"))?t:[]).find(e=>e.contains(ee($))&&e instanceof HTMLElement))?n:null});jt(q,K),function({type:e,enabled:t,element:n,onUpdate:l}){let r=g(At,()=>{});function o(...e){null==l||l(...e),r(...e)}x(()=>{p(t,(t,l)=>{t?o(0,e,n):!0===l&&o(1,e,n)},{immediate:!0,flush:"sync"})}),m(()=>{t.value&&o(1,e,n)}),w(At,o)}({type:"Dialog",enabled:d(()=>0===C.value),element:M,onUpdate:(e,t)=>{if("Dialog"===t)return te(e,{[$t.Add]:()=>O.value+=1,[$t.Remove]:()=>O.value-=1})}});let U=Nt({name:"DialogDescription",slot:d(()=>({open:E.value}))}),z=b(null),W={titleId:z,panelRef:b(null),dialogState:C,setTitleId(e){z.value!==e&&(z.value=e)},close(){t("close",!1)}};w(Qt,W);let _=d(()=>!(!L.value||j.value));Te(A,(e,t)=>{e.preventDefault(),W.close(),S(()=>null==t?void 0:t.focus())},_);let Y=d(()=>!(j.value||0!==C.value));wt(null==(i=B.value)?void 0:i.defaultView,"keydown",e=>{Y.value&&(e.defaultPrevented||e.key===Ye.Escape&&(e.preventDefault(),e.stopPropagation(),W.close()))});let Q=d(()=>!(V.value||0!==C.value||D));return function(e,t,n){let l=function(e){let t=f(e.getSnapshot());return m(e.subscribe(()=>{t.value=e.getSnapshot()})),t}(Bt),r=d(()=>{let t=e.value?l.value.get(e.value):void 0;return!!t&&t.count>0});p([e,t],([e,t],[l],r)=>{if(!e||!t)return;Bt.dispatch("PUSH",e,n);let o=!1;r(()=>{o||(Bt.dispatch("POP",null!=l?l:e,n),o=!0)})},{immediate:!0})}(B,Q,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],A]}}),P(e=>{if(0!==C.value)return;let t=ee(M);if(!t)return;let n=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&W.close()}});n.observe(t),e(()=>n.disconnect())}),()=>{let t=e,{open:r,initialFocus:o}=t,i=s(t,["open","initialFocus"]),d=a(u({},n),{ref:M,id:c,role:y.value,"aria-modal":0===C.value||void 0,"aria-labelledby":z.value,"aria-describedby":U.value}),f={open:0===C.value};return F(It,{force:!0},()=>[F(Kt,()=>F(Wt,{target:M.value},()=>F(It,{force:!1},()=>F(Ft,{initialFocus:o,containers:A,features:L.value?te(N.value,{parent:Ft.features.RestoreFocus,leaf:Ft.features.All&~Ft.features.FocusLock}):Ft.features.None},()=>F(I,{},()=>Ie({ourProps:d,theirProps:u(u({},i),n),slot:f,attrs:n,slots:l,visible:0===C.value,features:De.RenderStrategy|De.Static,name:"Dialog"})))))),F(R)])}}}),en=E({name:"DialogOverlay",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n}){var l;let r=null!=(l=e.id)?l:`headlessui-dialog-overlay-${Z()}`,o=Jt("DialogOverlay");function i(e){e.target===e.currentTarget&&(e.preventDefault(),e.stopPropagation(),o.close())}return()=>{let l=s(e,[]);return Ie({ourProps:{id:r,"aria-hidden":!0,onClick:i},theirProps:l,slot:{open:0===o.dialogState.value},attrs:t,slots:n,name:"DialogOverlay"})}}}),tn=E({name:"DialogBackdrop",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-dialog-backdrop-${Z()}`,i=Jt("DialogBackdrop"),a=b(null);return l({el:a,$el:a}),x(()=>{if(null===i.panelRef.value)throw new Error("A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.")}),()=>{let l=s(e,[]),r={id:o,ref:a,"aria-hidden":!0};return F(It,{force:!0},()=>F(Kt,()=>Ie({ourProps:r,theirProps:u(u({},t),l),slot:{open:0===i.dialogState.value},attrs:t,slots:n,name:"DialogBackdrop"})))}}}),nn=E({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-dialog-panel-${Z()}`,i=Jt("DialogPanel");function u(e){e.stopPropagation()}return l({el:i.panelRef,$el:i.panelRef}),()=>{let l=s(e,[]);return Ie({ourProps:{id:o,ref:i.panelRef,onClick:u},theirProps:l,slot:{open:0===i.dialogState.value},attrs:t,slots:n,name:"DialogPanel"})}}}),ln=E({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n}){var l;let r=null!=(l=e.id)?l:`headlessui-dialog-title-${Z()}`,o=Jt("DialogTitle");return x(()=>{o.setTitleId(r),m(()=>o.setTitleId(null))}),()=>{let l=s(e,[]);return Ie({ourProps:{id:r},theirProps:l,slot:{open:0===o.dialogState.value},attrs:t,slots:n,name:"DialogTitle"})}}}),rn=Vt;var on=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(on||{});let un=Symbol("DisclosureContext");function an(e){let t=g(un,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,an),t}return t}let sn=Symbol("DisclosurePanelContext"),dn=E({name:"Disclosure",props:{as:{type:[Object,String],default:"template"},defaultOpen:{type:[Boolean],default:!1}},setup(e,{slots:t,attrs:n}){let l=b(e.defaultOpen?0:1),r=b(null),o=b(null),i={buttonId:b(`headlessui-disclosure-button-${Z()}`),panelId:b(`headlessui-disclosure-panel-${Z()}`),disclosureState:l,panel:r,button:o,toggleDisclosure(){l.value=te(l.value,{0:1,1:0})},closeDisclosure(){1!==l.value&&(l.value=1)},close(e){i.closeDisclosure();let t=e?e instanceof HTMLElement?e:e.value instanceof HTMLElement?ee(e):ee(i.button):ee(i.button);null==t||t.focus()}};return w(un,i),ze(d(()=>te(l.value,{0:qe.Open,1:qe.Closed}))),()=>{let r=e,{defaultOpen:o}=r;return Ie({theirProps:s(r,["defaultOpen"]),ourProps:{},slot:{open:0===l.value,close:i.close},slots:t,attrs:n,name:"Disclosure"})}}}),cn=E({name:"DisclosureButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){let r=an("DisclosureButton"),o=g(sn,null),i=d(()=>null!==o&&o.value===r.panelId.value);x(()=>{i.value||null!==e.id&&(r.buttonId.value=e.id)}),m(()=>{i.value||(r.buttonId.value=null)});let u=b(null);l({el:u,$el:u}),i.value||P(()=>{r.button.value=u.value});let a=Me(d(()=>({as:e.as,type:t.type})),u);function c(){var t;e.disabled||(i.value?(r.toggleDisclosure(),null==(t=ee(r.button))||t.focus()):r.toggleDisclosure())}function f(t){var n;if(!e.disabled)if(i.value)switch(t.key){case Ye.Space:case Ye.Enter:t.preventDefault(),t.stopPropagation(),r.toggleDisclosure(),null==(n=ee(r.button))||n.focus()}else switch(t.key){case Ye.Space:case Ye.Enter:t.preventDefault(),t.stopPropagation(),r.toggleDisclosure()}}function p(e){e.key===Ye.Space&&e.preventDefault()}return()=>{var l;let o={open:0===r.disclosureState.value},d=e,{id:v}=d,h=s(d,["id"]);return Ie({ourProps:i.value?{ref:u,type:a.value,onClick:c,onKeydown:f}:{id:null!=(l=r.buttonId.value)?l:v,ref:u,type:a.value,"aria-expanded":0===r.disclosureState.value,"aria-controls":0===r.disclosureState.value||ee(r.panel)?r.panelId.value:void 0,disabled:!!e.disabled||void 0,onClick:c,onKeydown:f,onKeyup:p},theirProps:h,slot:o,attrs:t,slots:n,name:"DisclosureButton"})}}}),fn=E({name:"DisclosurePanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){let r=an("DisclosurePanel");x(()=>{null!==e.id&&(r.panelId.value=e.id)}),m(()=>{r.panelId.value=null}),l({el:r.panel,$el:r.panel}),w(sn,r.panelId);let o=Ue(),i=d(()=>null!==o?(o.value&qe.Open)===qe.Open:0===r.disclosureState.value);return()=>{var l;let o={open:0===r.disclosureState.value,close:r.close},u=e,{id:a}=u,d=s(u,["id"]);return Ie({ourProps:{id:null!=(l=r.panelId.value)?l:a,ref:r.panel},theirProps:d,slot:o,attrs:t,slots:n,features:De.RenderStrategy|De.Static,visible:i.value,name:"DisclosurePanel"})}}}),pn=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function vn(e){var t,n;let l=null!=(t=e.innerText)?t:"",r=e.cloneNode(!0);if(!(r instanceof HTMLElement))return l;let o=!1;for(let u of r.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))u.remove(),o=!0;let i=o?null!=(n=r.innerText)?n:"":l;return pn.test(i)&&(i=i.replace(pn,"")),i}function hn(e){let t=b(""),n=b("");return()=>{let l=ee(e);if(!l)return"";let r=l.innerText;if(t.value===r)return n.value;let o=function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map(e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():vn(t).trim()}return null}).filter(Boolean);if(e.length>0)return e.join(", ")}return vn(e).trim()}(l).trim().toLowerCase();return t.value=r,n.value=o,o}}function bn(e,t){return e===t}var mn=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(mn||{}),yn=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(yn||{}),gn=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(gn||{});let wn=Symbol("ListboxContext");function Sn(e){let t=g(wn,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Sn),t}return t}let Pn=E({name:"Listbox",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>bn},horizontal:{type:[Boolean],default:!1},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:t,attrs:n,emit:l}){let r=b(1),o=b(null),i=b(null),a=b(null),c=b([]),f=b(""),v=b(null),h=b(1);function m(e=e=>e){let t=null!==v.value?c.value[v.value]:null,n=we(e(c.value.slice()),e=>ee(e.dataRef.domRef)),l=t?n.indexOf(t):null;return-1===l&&(l=null),{options:n,activeOptionIndex:l}}let y=d(()=>e.multiple?1:0),[g,S]=W(d(()=>e.modelValue),e=>l("update:modelValue",e),d(()=>e.defaultValue)),P=d(()=>void 0===g.value?te(y.value,{1:[],0:void 0}):g.value),O={listboxState:r,value:P,mode:y,compare(t,n){if("string"==typeof e.by){let l=e.by;return(null==t?void 0:t[l])===(null==n?void 0:n[l])}return e.by(t,n)},orientation:d(()=>e.horizontal?"horizontal":"vertical"),labelRef:o,buttonRef:i,optionsRef:a,disabled:d(()=>e.disabled),options:c,searchQuery:f,activeOptionIndex:v,activationTrigger:h,closeListbox(){e.disabled||1!==r.value&&(r.value=1,v.value=null)},openListbox(){e.disabled||0!==r.value&&(r.value=0)},goToOption(t,n,l){if(e.disabled||1===r.value)return;let o=m(),i=et(t===Ze.Specific?{focus:Ze.Specific,id:n}:{focus:t},{resolveItems:()=>o.options,resolveActiveIndex:()=>o.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.disabled});f.value="",v.value=i,h.value=null!=l?l:1,c.value=o.options},search(t){if(e.disabled||1===r.value)return;let n=""!==f.value?0:1;f.value+=t.toLowerCase();let l=(null!==v.value?c.value.slice(v.value+n).concat(c.value.slice(0,v.value+n)):c.value).find(e=>e.dataRef.textValue.startsWith(f.value)&&!e.dataRef.disabled),o=l?c.value.indexOf(l):-1;-1===o||o===v.value||(v.value=o,h.value=1)},clearSearch(){e.disabled||1!==r.value&&""!==f.value&&(f.value="")},registerOption(e,t){let n=m(n=>[...n,{id:e,dataRef:t}]);c.value=n.options,v.value=n.activeOptionIndex},unregisterOption(e){let t=m(t=>{let n=t.findIndex(t=>t.id===e);return-1!==n&&t.splice(n,1),t});c.value=t.options,v.value=t.activeOptionIndex,h.value=1},theirOnChange(t){e.disabled||S(t)},select(t){e.disabled||S(te(y.value,{0:()=>t,1:()=>{let e=M(O.value.value).slice(),n=M(t),l=e.findIndex(e=>O.compare(n,M(e)));return-1===l?e.push(n):e.splice(l,1),e}}))}};Te([i,a],(e,t)=>{var n;O.closeListbox(),he(t,ve.Loose)||(e.preventDefault(),null==(n=ee(i))||n.focus())},d(()=>0===r.value)),w(wn,O),ze(d(()=>te(r.value,{0:qe.Open,1:qe.Closed})));let E=d(()=>{var e;return null==(e=ee(i))?void 0:e.closest("form")});return x(()=>{p([E],()=>{if(E.value&&void 0!==e.defaultValue)return E.value.addEventListener("reset",t),()=>{var e;null==(e=E.value)||e.removeEventListener("reset",t)};function t(){O.theirOnChange(e.defaultValue)}},{immediate:!0})}),()=>{let l=e,{name:o,modelValue:i,disabled:a,form:d}=l,c=s(l,["name","modelValue","disabled","form"]),f={open:0===r.value,disabled:a,value:P.value};return F(T,[...null!=o&&null!=P.value?tt({[o]:P.value}).map(([e,t])=>F(Ge,Ne({features:He.Hidden,key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:d,disabled:a,name:e,value:t}))):[],Ie({ourProps:{},theirProps:u(u({},n),Ve(c,["defaultValue","onUpdate:modelValue","horizontal","multiple","by"])),slot:f,slots:t,attrs:n,name:"Listbox"})])}}}),xn=E({name:"ListboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n}){var l;let r=null!=(l=e.id)?l:`headlessui-listbox-label-${Z()}`,o=Sn("ListboxLabel");function i(){var e;null==(e=ee(o.buttonRef))||e.focus({preventScroll:!0})}return()=>{let l={open:0===o.listboxState.value,disabled:o.disabled.value},u=s(e,[]);return Ie({ourProps:{id:r,ref:o.labelRef,onClick:i},theirProps:u,slot:l,attrs:t,slots:n,name:"ListboxLabel"})}}}),On=E({name:"ListboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-listbox-button-${Z()}`,i=Sn("ListboxButton");function u(e){switch(e.key){case Ye.Space:case Ye.Enter:case Ye.ArrowDown:e.preventDefault(),i.openListbox(),S(()=>{var e;null==(e=ee(i.optionsRef))||e.focus({preventScroll:!0}),i.value.value||i.goToOption(Ze.First)});break;case Ye.ArrowUp:e.preventDefault(),i.openListbox(),S(()=>{var e;null==(e=ee(i.optionsRef))||e.focus({preventScroll:!0}),i.value.value||i.goToOption(Ze.Last)})}}function a(e){e.key===Ye.Space&&e.preventDefault()}function c(e){i.disabled.value||(0===i.listboxState.value?(i.closeListbox(),S(()=>{var e;return null==(e=ee(i.buttonRef))?void 0:e.focus({preventScroll:!0})})):(e.preventDefault(),i.openListbox(),function(e){requestAnimationFrame(()=>requestAnimationFrame(e))}(()=>{var e;return null==(e=ee(i.optionsRef))?void 0:e.focus({preventScroll:!0})})))}l({el:i.buttonRef,$el:i.buttonRef});let f=Me(d(()=>({as:e.as,type:t.type})),i.buttonRef);return()=>{var l,r;let d={open:0===i.listboxState.value,disabled:i.disabled.value,value:i.value.value},p=s(e,[]);return Ie({ourProps:{ref:i.buttonRef,id:o,type:f.value,"aria-haspopup":"listbox","aria-controls":null==(l=ee(i.optionsRef))?void 0:l.id,"aria-expanded":0===i.listboxState.value,"aria-labelledby":i.labelRef.value?[null==(r=ee(i.labelRef))?void 0:r.id,o].join(" "):void 0,disabled:!0===i.disabled.value||void 0,onKeydown:u,onKeyup:a,onClick:c},theirProps:p,slot:d,attrs:t,slots:n,name:"ListboxButton"})}}}),Fn=E({name:"ListboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-listbox-options-${Z()}`,i=Sn("ListboxOptions"),u=b(null);function a(e){switch(u.value&&clearTimeout(u.value),e.key){case Ye.Space:if(""!==i.searchQuery.value)return e.preventDefault(),e.stopPropagation(),i.search(e.key);case Ye.Enter:if(e.preventDefault(),e.stopPropagation(),null!==i.activeOptionIndex.value){let e=i.options.value[i.activeOptionIndex.value];i.select(e.dataRef.value)}0===i.mode.value&&(i.closeListbox(),S(()=>{var e;return null==(e=ee(i.buttonRef))?void 0:e.focus({preventScroll:!0})}));break;case te(i.orientation.value,{vertical:Ye.ArrowDown,horizontal:Ye.ArrowRight}):return e.preventDefault(),e.stopPropagation(),i.goToOption(Ze.Next);case te(i.orientation.value,{vertical:Ye.ArrowUp,horizontal:Ye.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),i.goToOption(Ze.Previous);case Ye.Home:case Ye.PageUp:return e.preventDefault(),e.stopPropagation(),i.goToOption(Ze.First);case Ye.End:case Ye.PageDown:return e.preventDefault(),e.stopPropagation(),i.goToOption(Ze.Last);case Ye.Escape:e.preventDefault(),e.stopPropagation(),i.closeListbox(),S(()=>{var e;return null==(e=ee(i.buttonRef))?void 0:e.focus({preventScroll:!0})});break;case Ye.Tab:e.preventDefault(),e.stopPropagation();break;default:1===e.key.length&&(i.search(e.key),u.value=setTimeout(()=>i.clearSearch(),350))}}l({el:i.optionsRef,$el:i.optionsRef});let c=Ue(),f=d(()=>null!==c?(c.value&qe.Open)===qe.Open:0===i.listboxState.value);return()=>{var l,r;let u={open:0===i.listboxState.value},d=s(e,[]);return Ie({ourProps:{"aria-activedescendant":null===i.activeOptionIndex.value||null==(l=i.options.value[i.activeOptionIndex.value])?void 0:l.id,"aria-multiselectable":1===i.mode.value||void 0,"aria-labelledby":null==(r=ee(i.buttonRef))?void 0:r.id,"aria-orientation":i.orientation.value,id:o,onKeydown:a,role:"listbox",tabIndex:0,ref:i.optionsRef},theirProps:d,slot:u,attrs:t,slots:n,features:De.RenderStrategy|De.Static,visible:f.value,name:"ListboxOptions"})}}}),Tn=E({name:"ListboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-listbox-option-${Z()}`,i=Sn("ListboxOption"),u=b(null);l({el:u,$el:u});let a=d(()=>null!==i.activeOptionIndex.value&&i.options.value[i.activeOptionIndex.value].id===o),c=d(()=>te(i.mode.value,{0:()=>i.compare(M(i.value.value),M(e.value)),1:()=>M(i.value.value).some(t=>i.compare(M(t),M(e.value)))})),f=d(()=>te(i.mode.value,{1:()=>{var e;let t=M(i.value.value);return(null==(e=i.options.value.find(e=>t.some(t=>i.compare(M(t),M(e.dataRef.value)))))?void 0:e.id)===o},0:()=>c.value})),v=hn(u),h=d(()=>({disabled:e.disabled,value:e.value,get textValue(){return v()},domRef:u}));function y(t){if(e.disabled)return t.preventDefault();i.select(e.value),0===i.mode.value&&(i.closeListbox(),S(()=>{var e;return null==(e=ee(i.buttonRef))?void 0:e.focus({preventScroll:!0})}))}function g(){if(e.disabled)return i.goToOption(Ze.Nothing);i.goToOption(Ze.Specific,o)}x(()=>i.registerOption(o,h)),m(()=>i.unregisterOption(o)),x(()=>{p([i.listboxState,c],()=>{0===i.listboxState.value&&c.value&&te(i.mode.value,{1:()=>{f.value&&i.goToOption(Ze.Specific,o)},0:()=>{i.goToOption(Ze.Specific,o)}})},{immediate:!0})}),P(()=>{0===i.listboxState.value&&a.value&&0!==i.activationTrigger.value&&S(()=>{var e,t;return null==(t=null==(e=ee(u))?void 0:e.scrollIntoView)?void 0:t.call(e,{block:"nearest"})})});let w=Ce();function O(e){w.update(e)}function F(t){w.wasMoved(t)&&(e.disabled||a.value||i.goToOption(Ze.Specific,o,0))}function T(t){w.wasMoved(t)&&(e.disabled||a.value&&i.goToOption(Ze.Nothing))}return()=>{let{disabled:l}=e,r={active:a.value,selected:c.value,disabled:l},i=e,{value:d,disabled:f}=i,p=s(i,["value","disabled"]);return Ie({ourProps:{id:o,ref:u,role:"option",tabIndex:!0===l?void 0:-1,"aria-disabled":!0===l||void 0,"aria-selected":c.value,disabled:void 0,onClick:y,onFocus:g,onPointerenter:O,onMouseenter:O,onPointermove:F,onMousemove:F,onPointerleave:T,onMouseleave:T},theirProps:p,slot:r,attrs:n,slots:t,name:"ListboxOption"})}}});var En=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(En||{}),Mn=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(Mn||{});let Bn=Symbol("MenuContext");function Cn(e){let t=g(Bn,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Cn),t}return t}let Ln=E({name:"Menu",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:t,attrs:n}){let l=b(1),r=b(null),o=b(null),i=b([]),u=b(""),a=b(null),s=b(1);function c(e=e=>e){let t=null!==a.value?i.value[a.value]:null,n=we(e(i.value.slice()),e=>ee(e.dataRef.domRef)),l=t?n.indexOf(t):null;return-1===l&&(l=null),{items:n,activeItemIndex:l}}let f={menuState:l,buttonRef:r,itemsRef:o,items:i,searchQuery:u,activeItemIndex:a,activationTrigger:s,closeMenu:()=>{l.value=1,a.value=null},openMenu:()=>l.value=0,goToItem(e,t,n){let l=c(),r=et(e===Ze.Specific?{focus:Ze.Specific,id:t}:{focus:e},{resolveItems:()=>l.items,resolveActiveIndex:()=>l.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.disabled});u.value="",a.value=r,s.value=null!=n?n:1,i.value=l.items},search(e){let t=""!==u.value?0:1;u.value+=e.toLowerCase();let n=(null!==a.value?i.value.slice(a.value+t).concat(i.value.slice(0,a.value+t)):i.value).find(e=>e.dataRef.textValue.startsWith(u.value)&&!e.dataRef.disabled),l=n?i.value.indexOf(n):-1;-1===l||l===a.value||(a.value=l,s.value=1)},clearSearch(){u.value=""},registerItem(e,t){let n=c(n=>[...n,{id:e,dataRef:t}]);i.value=n.items,a.value=n.activeItemIndex,s.value=1},unregisterItem(e){let t=c(t=>{let n=t.findIndex(t=>t.id===e);return-1!==n&&t.splice(n,1),t});i.value=t.items,a.value=t.activeItemIndex,s.value=1}};return Te([r,o],(e,t)=>{var n;f.closeMenu(),he(t,ve.Loose)||(e.preventDefault(),null==(n=ee(r))||n.focus())},d(()=>0===l.value)),w(Bn,f),ze(d(()=>te(l.value,{0:qe.Open,1:qe.Closed}))),()=>{let r={open:0===l.value,close:f.closeMenu};return Ie({ourProps:{},theirProps:e,slot:r,slots:t,attrs:n,name:"Menu"})}}}),jn=E({name:"MenuButton",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-menu-button-${Z()}`,i=Cn("MenuButton");function u(e){switch(e.key){case Ye.Space:case Ye.Enter:case Ye.ArrowDown:e.preventDefault(),e.stopPropagation(),i.openMenu(),S(()=>{var e;null==(e=ee(i.itemsRef))||e.focus({preventScroll:!0}),i.goToItem(Ze.First)});break;case Ye.ArrowUp:e.preventDefault(),e.stopPropagation(),i.openMenu(),S(()=>{var e;null==(e=ee(i.itemsRef))||e.focus({preventScroll:!0}),i.goToItem(Ze.Last)})}}function a(e){e.key===Ye.Space&&e.preventDefault()}function c(t){e.disabled||(0===i.menuState.value?(i.closeMenu(),S(()=>{var e;return null==(e=ee(i.buttonRef))?void 0:e.focus({preventScroll:!0})})):(t.preventDefault(),i.openMenu(),function(e){requestAnimationFrame(()=>requestAnimationFrame(e))}(()=>{var e;return null==(e=ee(i.itemsRef))?void 0:e.focus({preventScroll:!0})})))}l({el:i.buttonRef,$el:i.buttonRef});let f=Me(d(()=>({as:e.as,type:t.type})),i.buttonRef);return()=>{var l;let r={open:0===i.menuState.value},d=s(e,[]);return Ie({ourProps:{ref:i.buttonRef,id:o,type:f.value,"aria-haspopup":"menu","aria-controls":null==(l=ee(i.itemsRef))?void 0:l.id,"aria-expanded":0===i.menuState.value,onKeydown:u,onKeyup:a,onClick:c},theirProps:d,slot:r,attrs:t,slots:n,name:"MenuButton"})}}}),Dn=E({name:"MenuItems",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-menu-items-${Z()}`,i=Cn("MenuItems"),u=b(null);function a(e){var t;switch(u.value&&clearTimeout(u.value),e.key){case Ye.Space:if(""!==i.searchQuery.value)return e.preventDefault(),e.stopPropagation(),i.search(e.key);case Ye.Enter:e.preventDefault(),e.stopPropagation(),null!==i.activeItemIndex.value&&(null==(t=ee(i.items.value[i.activeItemIndex.value].dataRef.domRef))||t.click()),i.closeMenu(),be(ee(i.buttonRef));break;case Ye.ArrowDown:return e.preventDefault(),e.stopPropagation(),i.goToItem(Ze.Next);case Ye.ArrowUp:return e.preventDefault(),e.stopPropagation(),i.goToItem(Ze.Previous);case Ye.Home:case Ye.PageUp:return e.preventDefault(),e.stopPropagation(),i.goToItem(Ze.First);case Ye.End:case Ye.PageDown:return e.preventDefault(),e.stopPropagation(),i.goToItem(Ze.Last);case Ye.Escape:e.preventDefault(),e.stopPropagation(),i.closeMenu(),S(()=>{var e;return null==(e=ee(i.buttonRef))?void 0:e.focus({preventScroll:!0})});break;case Ye.Tab:e.preventDefault(),e.stopPropagation(),i.closeMenu(),S(()=>function(e,t){return Se(pe(),t,{relativeTo:e})}(ee(i.buttonRef),e.shiftKey?de.Previous:de.Next));break;default:1===e.key.length&&(i.search(e.key),u.value=setTimeout(()=>i.clearSearch(),350))}}function c(e){e.key===Ye.Space&&e.preventDefault()}l({el:i.itemsRef,$el:i.itemsRef}),Le({container:d(()=>ee(i.itemsRef)),enabled:d(()=>0===i.menuState.value),accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let f=Ue(),p=d(()=>null!==f?(f.value&qe.Open)===qe.Open:0===i.menuState.value);return()=>{var l,r;let u={open:0===i.menuState.value},d=s(e,[]);return Ie({ourProps:{"aria-activedescendant":null===i.activeItemIndex.value||null==(l=i.items.value[i.activeItemIndex.value])?void 0:l.id,"aria-labelledby":null==(r=ee(i.buttonRef))?void 0:r.id,id:o,onKeydown:a,onKeyup:c,role:"menu",tabIndex:0,ref:i.itemsRef},theirProps:d,slot:u,attrs:t,slots:n,features:De.RenderStrategy|De.Static,visible:p.value,name:"MenuItems"})}}}),kn=E({name:"MenuItem",inheritAttrs:!1,props:{as:{type:[Object,String],default:"template"},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-menu-item-${Z()}`,i=Cn("MenuItem"),a=b(null);l({el:a,$el:a});let c=d(()=>null!==i.activeItemIndex.value&&i.items.value[i.activeItemIndex.value].id===o),f=hn(a),p=d(()=>({disabled:e.disabled,get textValue(){return f()},domRef:a}));function v(t){if(e.disabled)return t.preventDefault();i.closeMenu(),be(ee(i.buttonRef))}function h(){if(e.disabled)return i.goToItem(Ze.Nothing);i.goToItem(Ze.Specific,o)}x(()=>i.registerItem(o,p)),m(()=>i.unregisterItem(o)),P(()=>{0===i.menuState.value&&c.value&&0!==i.activationTrigger.value&&S(()=>{var e,t;return null==(t=null==(e=ee(a))?void 0:e.scrollIntoView)?void 0:t.call(e,{block:"nearest"})})});let y=Ce();function g(e){y.update(e)}function w(t){y.wasMoved(t)&&(e.disabled||c.value||i.goToItem(Ze.Specific,o,0))}function O(t){y.wasMoved(t)&&(e.disabled||c.value&&i.goToItem(Ze.Nothing))}return()=>{let l=e,{disabled:r}=l,d=s(l,["disabled"]),f={active:c.value,disabled:r,close:i.closeMenu};return Ie({ourProps:{id:o,ref:a,role:"menuitem",tabIndex:!0===r?void 0:-1,"aria-disabled":!0===r||void 0,onClick:v,onFocus:h,onPointerenter:g,onMouseenter:g,onPointermove:w,onMousemove:w,onPointerleave:O,onMouseleave:O},theirProps:u(u({},n),d),slot:f,attrs:n,slots:t,name:"MenuItem"})}}});var In,An=((In=An||{})[In.Open=0]="Open",In[In.Closed=1]="Closed",In);let $n=Symbol("PopoverContext");function Rn(e){let t=g($n,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <${Gn.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Rn),t}return t}let Nn=Symbol("PopoverGroupContext");function Vn(){return g(Nn,null)}let Hn=Symbol("PopoverPanelContext"),Gn=E({name:"Popover",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:n,expose:l}){var r;let o=b(null);l({el:o,$el:o});let i=b(1),a=b(null),s=b(null),c=b(null),f=b(null),p=d(()=>oe(o)),v=d(()=>{var e,t;if(!ee(a)||!ee(f))return!1;for(let s of document.querySelectorAll("body > *"))if(Number(null==s?void 0:s.contains(ee(a)))^Number(null==s?void 0:s.contains(ee(f))))return!0;let n=pe(),l=n.indexOf(ee(a)),r=(l+n.length-1)%n.length,o=(l+1)%n.length,i=n[r],u=n[o];return!(null!=(e=ee(f))&&e.contains(i)||null!=(t=ee(f))&&t.contains(u))}),h={popoverState:i,buttonId:b(null),panelId:b(null),panel:f,button:a,isPortalled:v,beforePanelSentinel:s,afterPanelSentinel:c,togglePopover(){i.value=te(i.value,{0:1,1:0})},closePopover(){1!==i.value&&(i.value=1)},close(e){h.closePopover();let t=e?e instanceof HTMLElement?e:e.value instanceof HTMLElement?ee(e):ee(h.button):ee(h.button);null==t||t.focus()}};w($n,h),ze(d(()=>te(i.value,{0:qe.Open,1:qe.Closed})));let m={buttonId:h.buttonId,panelId:h.panelId,close(){h.closePopover()}},y=Vn(),g=null==y?void 0:y.registerPopover,[S,x]=Ut(),O=Dt({mainTreeNodeRef:null==y?void 0:y.mainTreeNodeRef,portals:S,defaultContainers:[a,f]});return P(()=>null==g?void 0:g(m)),wt(null==(r=p.value)?void 0:r.defaultView,"focus",e=>{var t,n;e.target!==window&&e.target instanceof HTMLElement&&0===i.value&&(function(){var e,t,n,l;return null!=(l=null==y?void 0:y.isFocusWithinPopoverGroup())?l:(null==(e=p.value)?void 0:e.activeElement)&&((null==(t=ee(a))?void 0:t.contains(p.value.activeElement))||(null==(n=ee(f))?void 0:n.contains(p.value.activeElement)))}()||a&&f&&(O.contains(e.target)||null!=(t=ee(h.beforePanelSentinel))&&t.contains(e.target)||null!=(n=ee(h.afterPanelSentinel))&&n.contains(e.target)||h.closePopover()))},!0),Te(O.resolveContainers,(e,t)=>{var n;h.closePopover(),he(t,ve.Loose)||(e.preventDefault(),null==(n=ee(a))||n.focus())},d(()=>0===i.value)),()=>{let l={open:0===i.value,close:h.close};return F(T,[F(x,{},()=>Ie({theirProps:u(u({},e),n),ourProps:{ref:o},slot:l,slots:t,attrs:n,name:"Popover"})),F(O.MainTreeNode)])}}}),Kn=E({name:"PopoverButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-popover-button-${Z()}`,i=Rn("PopoverButton"),a=d(()=>oe(i.button));l({el:i.button,$el:i.button}),x(()=>{i.buttonId.value=o}),m(()=>{i.buttonId.value=null});let c=Vn(),f=null==c?void 0:c.closeOthers,p=g(Hn,null),v=d(()=>null!==p&&p.value===i.panelId.value),h=b(null),y=`headlessui-focus-sentinel-${Z()}`;v.value||P(()=>{i.button.value=ee(h)});let w=Me(d(()=>({as:e.as,type:t.type})),h);function S(e){var t,n,l,r,o;if(v.value){if(1===i.popoverState.value)return;switch(e.key){case Ye.Space:case Ye.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),i.closePopover(),null==(l=ee(i.button))||l.focus()}}else switch(e.key){case Ye.Space:case Ye.Enter:e.preventDefault(),e.stopPropagation(),1===i.popoverState.value&&(null==f||f(i.buttonId.value)),i.togglePopover();break;case Ye.Escape:if(0!==i.popoverState.value)return null==f?void 0:f(i.buttonId.value);if(!ee(i.button)||null!=(r=a.value)&&r.activeElement&&(null==(o=ee(i.button))||!o.contains(a.value.activeElement)))return;e.preventDefault(),e.stopPropagation(),i.closePopover()}}function O(e){v.value||e.key===Ye.Space&&e.preventDefault()}function E(t){var n,l;e.disabled||(v.value?(i.closePopover(),null==(n=ee(i.button))||n.focus()):(t.preventDefault(),t.stopPropagation(),1===i.popoverState.value&&(null==f||f(i.buttonId.value)),i.togglePopover(),null==(l=ee(i.button))||l.focus()))}function M(e){e.preventDefault(),e.stopPropagation()}let B=Pt();function C(){let e=ee(i.panel);e&&te(B.value,{[St.Forwards]:()=>Se(e,de.First),[St.Backwards]:()=>Se(e,de.Last)})===ce.Error&&Se(pe().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),te(B.value,{[St.Forwards]:de.Next,[St.Backwards]:de.Previous}),{relativeTo:ee(i.button)})}return()=>{let l=0===i.popoverState.value,r={open:l},a=s(e,[]),d=v.value?{ref:h,type:w.value,onKeydown:S,onClick:E}:{ref:h,id:o,type:w.value,"aria-expanded":0===i.popoverState.value,"aria-controls":ee(i.panel)?i.panelId.value:void 0,disabled:!!e.disabled||void 0,onKeydown:S,onKeyup:O,onClick:E,onMousedown:M};return F(T,[Ie({ourProps:d,theirProps:u(u({},t),a),slot:r,attrs:t,slots:n,name:"PopoverButton"}),l&&!v.value&&i.isPortalled.value&&F(Ge,{id:y,features:He.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:C})])}}}),qn=E({name:"PopoverOverlay",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0}},setup(e,{attrs:t,slots:n}){let l=Rn("PopoverOverlay"),r=`headlessui-popover-overlay-${Z()}`,o=Ue(),i=d(()=>null!==o?(o.value&qe.Open)===qe.Open:0===l.popoverState.value);function u(){l.closePopover()}return()=>{let o={open:0===l.popoverState.value};return Ie({ourProps:{id:r,"aria-hidden":!0,onClick:u},theirProps:e,slot:o,attrs:t,slots:n,features:De.RenderStrategy|De.Static,visible:i.value,name:"PopoverOverlay"})}}}),Un=E({name:"PopoverPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-popover-panel-${Z()}`,{focus:i}=e,c=Rn("PopoverPanel"),f=d(()=>oe(c.panel)),p=`headlessui-focus-sentinel-before-${Z()}`,v=`headlessui-focus-sentinel-after-${Z()}`;l({el:c.panel,$el:c.panel}),x(()=>{c.panelId.value=o}),m(()=>{c.panelId.value=null}),w(Hn,c.panelId),P(()=>{var e,t;if(!i||0!==c.popoverState.value||!c.panel)return;let n=null==(e=f.value)?void 0:e.activeElement;null!=(t=ee(c.panel))&&t.contains(n)||Se(ee(c.panel),de.First)});let h=Ue(),b=d(()=>null!==h?(h.value&qe.Open)===qe.Open:0===c.popoverState.value);function y(e){var t,n;if(e.key===Ye.Escape){if(0!==c.popoverState.value||!ee(c.panel)||f.value&&(null==(t=ee(c.panel))||!t.contains(f.value.activeElement)))return;e.preventDefault(),e.stopPropagation(),c.closePopover(),null==(n=ee(c.button))||n.focus()}}function g(e){var t,n,l,r,o;let i=e.relatedTarget;i&&ee(c.panel)&&(null!=(t=ee(c.panel))&&t.contains(i)||(c.closePopover(),(null!=(l=null==(n=ee(c.beforePanelSentinel))?void 0:n.contains)&&l.call(n,i)||null!=(o=null==(r=ee(c.afterPanelSentinel))?void 0:r.contains)&&o.call(r,i))&&i.focus({preventScroll:!0})))}let S=Pt();function O(){let e=ee(c.panel);e&&te(S.value,{[St.Forwards]:()=>{var t;Se(e,de.First)===ce.Error&&(null==(t=ee(c.afterPanelSentinel))||t.focus())},[St.Backwards]:()=>{var e;null==(e=ee(c.button))||e.focus({preventScroll:!0})}})}function E(){let e=ee(c.panel);e&&te(S.value,{[St.Forwards]:()=>{let e=ee(c.button),t=ee(c.panel);if(!e)return;let n=pe(),l=n.indexOf(e),r=n.slice(0,l+1),o=[...n.slice(l+1),...r];for(let i of o.slice())if("true"===i.dataset.headlessuiFocusGuard||null!=t&&t.contains(i)){let e=o.indexOf(i);-1!==e&&o.splice(e,1)}Se(o,de.First,{sorted:!1})},[St.Backwards]:()=>{var t;Se(e,de.Previous)===ce.Error&&(null==(t=ee(c.button))||t.focus())}})}return()=>{let l={open:0===c.popoverState.value,close:c.close},r=e,{focus:d}=r,f=s(r,["focus"]);return Ie({ourProps:{ref:c.panel,id:o,onKeydown:y,onFocusout:i&&0===c.popoverState.value?g:void 0,tabIndex:-1},theirProps:u(u({},t),f),attrs:t,slot:l,slots:a(u({},n),{default:(...e)=>{var t;return[F(T,[b.value&&c.isPortalled.value&&F(Ge,{id:p,ref:c.beforePanelSentinel,features:He.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:O}),null==(t=n.default)?void 0:t.call(n,...e),b.value&&c.isPortalled.value&&F(Ge,{id:v,ref:c.afterPanelSentinel,features:He.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:E})])]}}),features:De.RenderStrategy|De.Static,visible:b.value,name:"PopoverPanel"})}}}),zn=E({name:"PopoverGroup",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:t,slots:n,expose:l}){let r=b(null),o=f([]),i=d(()=>oe(r)),a=function(){let e=b(null);return{mainTreeNodeRef:e,MainTreeNode:()=>F(Ge,{features:He.Hidden,ref:e})}}();function s(e){let t=o.value.indexOf(e);-1!==t&&o.value.splice(t,1)}return l({el:r,$el:r}),w(Nn,{registerPopover:function(e){return o.value.push(e),()=>{s(e)}},unregisterPopover:s,isFocusWithinPopoverGroup:function(){var e;let t=i.value;if(!t)return!1;let n=t.activeElement;return!(null==(e=ee(r))||!e.contains(n))||o.value.some(e=>{var l,r;return(null==(l=t.getElementById(e.buttonId.value))?void 0:l.contains(n))||(null==(r=t.getElementById(e.panelId.value))?void 0:r.contains(n))})},closeOthers:function(e){for(let t of o.value)t.buttonId.value!==e&&t.close()},mainTreeNodeRef:a.mainTreeNodeRef}),()=>F(T,[Ie({ourProps:{ref:r},theirProps:u(u({},e),t),slot:{},attrs:t,slots:n,name:"PopoverGroup"}),F(a.MainTreeNode)])}}),Wn=Symbol("LabelContext");function _n(){let e=g(Wn,null);if(null===e){let e=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,_n),e}return e}function Yn({slot:e={},name:t="Label",props:n={}}={}){let l=b([]);return w(Wn,{register:function(e){return l.value.push(e),()=>{let t=l.value.indexOf(e);-1!==t&&l.value.splice(t,1)}},slot:e,name:t,props:n}),d(()=>l.value.length>0?l.value.join(" "):void 0)}let Qn=E({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:n}){var l;let r=null!=(l=e.id)?l:`headlessui-label-${Z()}`,o=_n();return x(()=>m(o.register(r))),()=>{let{name:l="Label",slot:i={},props:d={}}=o,f=e,{passive:p}=f,v=s(f,["passive"]),h=a(u({},Object.entries(d).reduce((e,[t,n])=>Object.assign(e,{[t]:c(n)}),{})),{id:r});return p&&(delete h.onClick,delete h.htmlFor,delete v.onClick),Ie({ourProps:h,theirProps:v,slot:i,attrs:n,slots:t,name:l})}}});function Jn(e,t){return e===t}let Xn=Symbol("RadioGroupContext");function Zn(e){let t=g(Xn,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Zn),t}return t}let el=E({name:"RadioGroup",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"div"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>Jn},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{emit:t,attrs:n,slots:l,expose:r}){var o;let i=null!=(o=e.id)?o:`headlessui-radiogroup-${Z()}`,a=b(null),c=b([]),f=Yn({name:"RadioGroupLabel"}),v=Nt({name:"RadioGroupDescription"});r({el:a,$el:a});let[h,m]=W(d(()=>e.modelValue),e=>t("update:modelValue",e),d(()=>e.defaultValue)),y={options:c,value:h,disabled:d(()=>e.disabled),firstOption:d(()=>c.value.find(e=>!e.propsRef.disabled)),containsCheckedOption:d(()=>c.value.some(t=>y.compare(M(t.propsRef.value),M(e.modelValue)))),compare(t,n){if("string"==typeof e.by){let l=e.by;return(null==t?void 0:t[l])===(null==n?void 0:n[l])}return e.by(t,n)},change(t){var n;if(e.disabled||y.compare(M(h.value),M(t)))return!1;let l=null==(n=c.value.find(e=>y.compare(M(e.propsRef.value),M(t))))?void 0:n.propsRef;return!(null!=l&&l.disabled||(m(t),0))},registerOption(e){c.value.push(e),c.value=we(c.value,e=>e.element)},unregisterOption(e){let t=c.value.findIndex(t=>t.id===e);-1!==t&&c.value.splice(t,1)}};function g(e){if(!a.value||!a.value.contains(e.target))return;let t=c.value.filter(e=>!1===e.propsRef.disabled).map(e=>e.element);switch(e.key){case Ye.Enter:rt(e.currentTarget);break;case Ye.ArrowLeft:case Ye.ArrowUp:if(e.preventDefault(),e.stopPropagation(),Se(t,de.Previous|de.WrapAround)===ce.Success){let e=c.value.find(e=>{var t;return e.element===(null==(t=oe(a))?void 0:t.activeElement)});e&&y.change(e.propsRef.value)}break;case Ye.ArrowRight:case Ye.ArrowDown:if(e.preventDefault(),e.stopPropagation(),Se(t,de.Next|de.WrapAround)===ce.Success){let e=c.value.find(e=>{var t;return e.element===(null==(t=oe(e.element))?void 0:t.activeElement)});e&&y.change(e.propsRef.value)}break;case Ye.Space:{e.preventDefault(),e.stopPropagation();let t=c.value.find(e=>{var t;return e.element===(null==(t=oe(e.element))?void 0:t.activeElement)});t&&y.change(t.propsRef.value)}}}w(Xn,y),Le({container:d(()=>ee(a)),accept:e=>"radio"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let S=d(()=>{var e;return null==(e=ee(a))?void 0:e.closest("form")});return x(()=>{p([S],()=>{if(S.value&&void 0!==e.defaultValue)return S.value.addEventListener("reset",t),()=>{var e;null==(e=S.value)||e.removeEventListener("reset",t)};function t(){y.change(e.defaultValue)}},{immediate:!0})}),()=>{let t=e,{disabled:r,name:o,form:d}=t,c=s(t,["disabled","name","form"]),p={ref:a,id:i,role:"radiogroup","aria-labelledby":f.value,"aria-describedby":v.value,onKeydown:g};return F(T,[...null!=o&&null!=h.value?tt({[o]:h.value}).map(([e,t])=>F(Ge,Ne({features:He.Hidden,key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:d,disabled:r,name:e,value:t}))):[],Ie({ourProps:p,theirProps:u(u({},n),Ve(c,["modelValue","defaultValue","by"])),slot:{},attrs:n,slots:l,name:"RadioGroup"})])}}});var tl,nl=((tl=nl||{})[tl.Empty=1]="Empty",tl[tl.Active=2]="Active",tl);let ll=E({name:"RadioGroupOption",props:{as:{type:[Object,String],default:"div"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-radiogroup-option-${Z()}`,i=Zn("RadioGroupOption"),u=Yn({name:"RadioGroupLabel"}),a=Nt({name:"RadioGroupDescription"}),c=b(null),f=d(()=>({value:e.value,disabled:e.disabled})),p=b(1);l({el:c,$el:c});let v=d(()=>ee(c));x(()=>i.registerOption({id:o,element:v,propsRef:f})),m(()=>i.unregisterOption(o));let h=d(()=>{var e;return(null==(e=i.firstOption.value)?void 0:e.id)===o}),y=d(()=>i.disabled.value||e.disabled),g=d(()=>i.compare(M(i.value.value),M(e.value))),w=d(()=>y.value?-1:g.value||!i.containsCheckedOption.value&&h.value?0:-1);function S(){var t;i.change(e.value)&&(p.value|=2,null==(t=ee(c))||t.focus())}function P(){p.value|=2}function O(){p.value&=-3}return()=>{let l=e,{value:r,disabled:i}=l,d=s(l,["value","disabled"]),f={checked:g.value,disabled:y.value,active:Boolean(2&p.value)};return Ie({ourProps:{id:o,ref:c,role:"radio","aria-checked":g.value?"true":"false","aria-labelledby":u.value,"aria-describedby":a.value,"aria-disabled":!!y.value||void 0,tabIndex:w.value,onClick:y.value?void 0:S,onFocus:y.value?void 0:P,onBlur:y.value?void 0:O},theirProps:d,slot:f,attrs:t,slots:n,name:"RadioGroupOption"})}}}),rl=Qn,ol=Vt,il=Symbol("GroupContext"),ul=E({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:t,attrs:n}){let l=b(null),r=Yn({name:"SwitchLabel",props:{htmlFor:d(()=>{var e;return null==(e=l.value)?void 0:e.id}),onClick(e){l.value&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),l.value.click(),l.value.focus({preventScroll:!0}))}}}),o=Nt({name:"SwitchDescription"});return w(il,{switchRef:l,labelledby:r,describedby:o}),()=>Ie({theirProps:e,ourProps:{},slot:{},slots:t,attrs:n,name:"SwitchGroup"})}}),al=E({name:"Switch",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(e,{emit:t,attrs:n,slots:l,expose:r}){var o;let i=null!=(o=e.id)?o:`headlessui-switch-${Z()}`,a=g(il,null),[c,f]=W(d(()=>e.modelValue),e=>t("update:modelValue",e),d(()=>e.defaultChecked));function v(){f(!c.value)}let h=b(null),m=null===a?h:a.switchRef,y=Me(d(()=>({as:e.as,type:n.type})),m);function w(e){e.preventDefault(),v()}function S(e){e.key===Ye.Space?(e.preventDefault(),v()):e.key===Ye.Enter&&rt(e.currentTarget)}function P(e){e.preventDefault()}r({el:m,$el:m});let O=d(()=>{var e,t;return null==(t=null==(e=ee(m))?void 0:e.closest)?void 0:t.call(e,"form")});return x(()=>{p([O],()=>{if(O.value&&void 0!==e.defaultChecked)return O.value.addEventListener("reset",t),()=>{var e;null==(e=O.value)||e.removeEventListener("reset",t)};function t(){f(e.defaultChecked)}},{immediate:!0})}),()=>{let t=e,{name:r,value:o,form:d,tabIndex:f}=t,p=s(t,["name","value","form","tabIndex"]),v={checked:c.value},h={id:i,ref:m,role:"switch",type:y.value,tabIndex:-1===f?0:f,"aria-checked":c.value,"aria-labelledby":null==a?void 0:a.labelledby.value,"aria-describedby":null==a?void 0:a.describedby.value,onClick:w,onKeyup:S,onKeypress:P};return F(T,[null!=r&&null!=c.value?F(Ge,Ne({features:He.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:c.value,form:d,disabled:p.disabled,name:r,value:o})):null,Ie({ourProps:h,theirProps:u(u({},n),Ve(p,["modelValue","defaultChecked"])),slot:v,attrs:n,slots:l,name:"Switch"})])}}}),sl=Qn,dl=Vt,cl=E({props:{onFocus:{type:Function,required:!0}},setup(e){let t=b(!0);return()=>t.value?F(Ge,{as:"button",type:"button",features:He.Focusable,onFocus(n){n.preventDefault();let l,r=50;l=requestAnimationFrame(function n(){var o;if(!(r--<=0))return null!=(o=e.onFocus)&&o.call(e)?(t.value=!1,void cancelAnimationFrame(l)):void(l=requestAnimationFrame(n));l&&cancelAnimationFrame(l)})}}):null}});var fl,pl=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(pl||{}),vl=((fl=vl||{})[fl.Less=-1]="Less",fl[fl.Equal=0]="Equal",fl[fl.Greater=1]="Greater",fl);let hl=Symbol("TabsContext");function bl(e){let t=g(hl,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,bl),t}return t}let ml=Symbol("TabsSSRContext"),yl=E({name:"TabGroup",emits:{change:e=>!0},props:{as:{type:[Object,String],default:"template"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:t,attrs:n,emit:l}){var r;let o=b(null!=(r=e.selectedIndex)?r:e.defaultIndex),i=b([]),a=b([]),s=d(()=>null!==e.selectedIndex),c=d(()=>s.value?e.selectedIndex:o.value);function f(e){var t;let n=we(v.tabs.value,ee),l=we(v.panels.value,ee),r=n.filter(e=>{var t;return!(null!=(t=ee(e))&&t.hasAttribute("disabled"))});if(e<0||e>n.length-1){let t=te(null===o.value?0:Math.sign(e-o.value),{[-1]:()=>1,0:()=>te(Math.sign(e),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0}),i=te(t,{0:()=>n.indexOf(r[0]),1:()=>n.indexOf(r[r.length-1])});-1!==i&&(o.value=i),v.tabs.value=n,v.panels.value=l}else{let i=n.slice(0,e),u=[...n.slice(e),...i].find(e=>r.includes(e));if(!u)return;let a=null!=(t=n.indexOf(u))?t:v.selectedIndex.value;-1===a&&(a=v.selectedIndex.value),o.value=a,v.tabs.value=n,v.panels.value=l}}let v={selectedIndex:d(()=>{var t,n;return null!=(n=null!=(t=o.value)?t:e.defaultIndex)?n:null}),orientation:d(()=>e.vertical?"vertical":"horizontal"),activation:d(()=>e.manual?"manual":"auto"),tabs:i,panels:a,setSelectedIndex(e){c.value!==e&&l("change",e),s.value||f(e)},registerTab(e){var t;if(i.value.includes(e))return;let n=i.value[o.value];if(i.value.push(e),i.value=we(i.value,ee),!s.value){let e=null!=(t=i.value.indexOf(n))?t:o.value;-1!==e&&(o.value=e)}},unregisterTab(e){let t=i.value.indexOf(e);-1!==t&&i.value.splice(t,1)},registerPanel(e){a.value.includes(e)||(a.value.push(e),a.value=we(a.value,ee))},unregisterPanel(e){let t=a.value.indexOf(e);-1!==t&&a.value.splice(t,1)}};w(hl,v);let h=b({tabs:[],panels:[]}),m=b(!1);x(()=>{m.value=!0}),w(ml,d(()=>m.value?null:h.value));let y=d(()=>e.selectedIndex);return x(()=>{p([y],()=>{var t;return f(null!=(t=e.selectedIndex)?t:e.defaultIndex)},{immediate:!0})}),P(()=>{if(!s.value||null==c.value||v.tabs.value.length<=0)return;let e=we(v.tabs.value,ee);e.some((e,t)=>ee(v.tabs.value[t])!==ee(e))&&v.setSelectedIndex(e.findIndex(e=>ee(e)===ee(v.tabs.value[c.value])))}),()=>{let l={selectedIndex:o.value};return F(T,[i.value.length<=0&&F(cl,{onFocus:()=>{for(let e of i.value){let t=ee(e);if(0===(null==t?void 0:t.tabIndex))return t.focus(),!0}return!1}}),Ie({theirProps:u(u({},n),Ve(e,["selectedIndex","defaultIndex","manual","vertical","onChange"])),ourProps:{},slot:l,slots:t,attrs:n,name:"TabGroup"})])}}}),gl=E({name:"TabList",props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:t,slots:n}){let l=bl("TabList");return()=>{let r={selectedIndex:l.selectedIndex.value};return Ie({ourProps:{role:"tablist","aria-orientation":l.orientation.value},theirProps:e,slot:r,attrs:t,slots:n,name:"TabList"})}}}),wl=E({name:"Tab",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-tabs-tab-${Z()}`,i=bl("Tab"),u=b(null);l({el:u,$el:u}),x(()=>i.registerTab(u)),m(()=>i.unregisterTab(u));let a=g(ml),c=d(()=>{if(a.value){let e=a.value.tabs.indexOf(o);return-1===e?a.value.tabs.push(o)-1:e}return-1}),f=d(()=>{let e=i.tabs.value.indexOf(u);return-1===e?c.value:e}),p=d(()=>f.value===i.selectedIndex.value);function v(e){var t;let n=e();if(n===ce.Success&&"auto"===i.activation.value){let e=null==(t=oe(u))?void 0:t.activeElement,n=i.tabs.value.findIndex(t=>ee(t)===e);-1!==n&&i.setSelectedIndex(n)}return n}function h(e){let t=i.tabs.value.map(e=>ee(e)).filter(Boolean);if(e.key===Ye.Space||e.key===Ye.Enter)return e.preventDefault(),e.stopPropagation(),void i.setSelectedIndex(f.value);switch(e.key){case Ye.Home:case Ye.PageUp:return e.preventDefault(),e.stopPropagation(),v(()=>Se(t,de.First));case Ye.End:case Ye.PageDown:return e.preventDefault(),e.stopPropagation(),v(()=>Se(t,de.Last))}return v(()=>te(i.orientation.value,{vertical:()=>e.key===Ye.ArrowUp?Se(t,de.Previous|de.WrapAround):e.key===Ye.ArrowDown?Se(t,de.Next|de.WrapAround):ce.Error,horizontal:()=>e.key===Ye.ArrowLeft?Se(t,de.Previous|de.WrapAround):e.key===Ye.ArrowRight?Se(t,de.Next|de.WrapAround):ce.Error}))===ce.Success?e.preventDefault():void 0}let y=b(!1);function w(){var t;y.value||(y.value=!0,!e.disabled&&(null==(t=ee(u))||t.focus({preventScroll:!0}),i.setSelectedIndex(f.value),_(()=>{y.value=!1})))}function S(e){e.preventDefault()}let P=Me(d(()=>({as:e.as,type:t.type})),u);return()=>{var l,r;let a={selected:p.value,disabled:null!=(l=e.disabled)&&l},d=s(e,[]);return Ie({ourProps:{ref:u,onKeydown:h,onMousedown:S,onClick:w,id:o,role:"tab",type:P.value,"aria-controls":null==(r=ee(i.panels.value[f.value]))?void 0:r.id,"aria-selected":p.value,tabIndex:p.value?0:-1,disabled:!!e.disabled||void 0},theirProps:d,slot:a,attrs:t,slots:n,name:"Tab"})}}}),Sl=E({name:"TabPanels",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:n}){let l=bl("TabPanels");return()=>{let r={selectedIndex:l.selectedIndex.value};return Ie({theirProps:e,ourProps:{},slot:r,attrs:n,slots:t,name:"TabPanels"})}}}),Pl=E({name:"TabPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null},tabIndex:{type:Number,default:0}},setup(e,{attrs:t,slots:n,expose:l}){var r;let o=null!=(r=e.id)?r:`headlessui-tabs-panel-${Z()}`,i=bl("TabPanel"),a=b(null);l({el:a,$el:a}),x(()=>i.registerPanel(a)),m(()=>i.unregisterPanel(a));let c=g(ml),f=d(()=>{if(c.value){let e=c.value.panels.indexOf(o);return-1===e?c.value.panels.push(o)-1:e}return-1}),p=d(()=>{let e=i.panels.value.indexOf(a);return-1===e?f.value:e}),v=d(()=>p.value===i.selectedIndex.value);return()=>{var l;let r={selected:v.value},d=e,{tabIndex:c}=d,f=s(d,["tabIndex"]),h={ref:a,id:o,role:"tabpanel","aria-labelledby":null==(l=ee(i.tabs.value[p.value]))?void 0:l.id,tabIndex:v.value?c:-1};return v.value||!e.unmount||e.static?Ie({ourProps:h,theirProps:f,slot:r,attrs:t,slots:n,features:De.Static|De.RenderStrategy,visible:v.value,name:"TabPanel"}):F(Ge,u({as:"span","aria-hidden":!0},h))}}});function xl(e,...t){e&&t.length>0&&e.classList.add(...t)}function Ol(e,...t){e&&t.length>0&&e.classList.remove(...t)}var Fl=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(Fl||{});function Tl(e,t,n,l,r,o){let i=Y(),u=void 0!==o?function(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}(o):()=>{};return Ol(e,...r),xl(e,...t,...n),i.nextFrame(()=>{Ol(e,...n),xl(e,...l),i.add(function(e,t){let n=Y();if(!e)return n.dispose;let{transitionDuration:l,transitionDelay:r}=getComputedStyle(e),[o,i]=[l,r].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t});return 0!==o?n.setTimeout(()=>t("finished"),o+i):t("finished"),n.add(()=>t("cancelled")),n.dispose}(e,n=>(Ol(e,...l,...t),xl(e,...r),u(n))))}),i.add(()=>Ol(e,...t,...n,...l,...r)),i.add(()=>u("cancelled")),i.dispose}function El(e=""){return e.split(/\s+/).filter(e=>e.length>1)}let Ml=Symbol("TransitionContext");var Bl,Cl=((Bl=Cl||{}).Visible="visible",Bl.Hidden="hidden",Bl);let Ll=Symbol("NestingContext");function jl(e){return"children"in e?jl(e.children):e.value.filter(({state:e})=>"visible"===e).length>0}function Dl(e){let t=b([]),n=b(!1);function l(l,r=ke.Hidden){let o=t.value.findIndex(({id:e})=>e===l);-1!==o&&(te(r,{[ke.Unmount](){t.value.splice(o,1)},[ke.Hidden](){t.value[o].state="hidden"}}),!jl(t)&&n.value&&(null==e||e()))}return x(()=>n.value=!0),m(()=>n.value=!1),{children:t,register:function(e){let n=t.value.find(({id:t})=>t===e);return n?"visible"!==n.state&&(n.state="visible"):t.value.push({id:e,state:"visible"}),()=>l(e,ke.Unmount)},unregister:l}}let kl=De.RenderStrategy,Il=E({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:n,slots:l,expose:r}){let o=b(0);function i(){o.value|=qe.Opening,t("beforeEnter")}function c(){o.value&=~qe.Opening,t("afterEnter")}function f(){o.value|=qe.Closing,t("beforeLeave")}function v(){o.value&=~qe.Closing,t("afterLeave")}if(null===g(Ml,null)&&null!==Ue())return()=>F($l,a(u({},e),{onBeforeEnter:i,onAfterEnter:c,onBeforeLeave:f,onAfterLeave:v}),l);let h=b(null),y=d(()=>e.unmount?ke.Unmount:ke.Hidden);r({el:h,$el:h});let{show:S,appear:O}=function(){let e=g(Ml,null);if(null===e)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}(),{register:T,unregister:E}=function(){let e=g(Ll,null);if(null===e)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}(),M=b(S.value?"visible":"hidden"),B={value:!0},C=Z(),L={value:!1},D=Dl(()=>{!L.value&&"hidden"!==M.value&&(M.value="hidden",E(C),v())});x(()=>{let e=T(C);m(e)}),P(()=>{if(y.value===ke.Hidden&&C){if(S.value&&"visible"!==M.value)return void(M.value="visible");te(M.value,{hidden:()=>E(C),visible:()=>T(C)})}});let k=El(e.enter),I=El(e.enterFrom),A=El(e.enterTo),$=El(e.entered),R=El(e.leave),N=El(e.leaveFrom),V=El(e.leaveTo);return x(()=>{P(()=>{if("visible"===M.value){let e=ee(h);if(e instanceof Comment&&""===e.data)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})}),x(()=>{p([S],(e,t,n)=>{(function(e){let t=B.value&&!O.value,n=ee(h);!n||!(n instanceof HTMLElement)||t||(L.value=!0,S.value&&i(),S.value||f(),e(S.value?Tl(n,k,I,A,$,e=>{L.value=!1,e===Fl.Finished&&c()}):Tl(n,R,N,V,$,e=>{L.value=!1,e===Fl.Finished&&(jl(D)||(M.value="hidden",E(C),v()))})))})(n),B.value=!1},{immediate:!0})}),w(Ll,D),ze(d(()=>te(M.value,{visible:qe.Open,hidden:qe.Closed})|o.value)),()=>{let t=e,{appear:r,show:o,enter:i,enterFrom:a,enterTo:d,entered:c,leave:f,leaveFrom:p,leaveTo:v}=t,b=s(t,["appear","show","enter","enterFrom","enterTo","entered","leave","leaveFrom","leaveTo"]),m={ref:h};return Ie({theirProps:u(u({},b),O.value&&S.value&&re.isServer?{class:j([n.class,b.class,...k,...I])}:{}),ourProps:m,slot:{},slots:l,attrs:n,features:kl,visible:"visible"===M.value,name:"TransitionChild"})}}}),Al=Il,$l=E({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:n,slots:l}){let r=Ue(),o=d(()=>null===e.show&&null!==r?(r.value&qe.Open)===qe.Open:e.show);P(()=>{if(![!0,!1].includes(o.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let i=b(o.value?"visible":"hidden"),s=Dl(()=>{i.value="hidden"}),c=b(!0),f={show:o,appear:d(()=>e.appear||!c.value)};return x(()=>{P(()=>{c.value=!1,o.value?i.value="visible":jl(s)||(i.value="hidden")})}),w(Ll,s),w(Ml,f),()=>{let r=Ve(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),o={unmount:e.unmount};return Ie({ourProps:a(u({},o),{as:"template"}),theirProps:{},slot:{},slots:a(u({},l),{default:()=>[F(Al,u(u(u({onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave")},n),o),r),l.default)]}),attrs:{},features:kl,visible:"visible"===i.value,name:"Transition"})}}});const Rl=Object.freeze(Object.defineProperty({__proto__:null,Combobox:vt,ComboboxButton:bt,ComboboxInput:mt,ComboboxLabel:ht,ComboboxOption:gt,ComboboxOptions:yt,Dialog:Zt,DialogBackdrop:tn,DialogDescription:rn,DialogOverlay:en,DialogPanel:nn,DialogTitle:ln,Disclosure:dn,DisclosureButton:cn,DisclosurePanel:fn,FocusTrap:Ft,Listbox:Pn,ListboxButton:On,ListboxLabel:xn,ListboxOption:Tn,ListboxOptions:Fn,Menu:Ln,MenuButton:jn,MenuItem:kn,MenuItems:Dn,Popover:Gn,PopoverButton:Kn,PopoverGroup:zn,PopoverOverlay:qn,PopoverPanel:Un,Portal:Kt,PortalGroup:Wt,RadioGroup:el,RadioGroupDescription:ol,RadioGroupLabel:rl,RadioGroupOption:ll,Switch:al,SwitchDescription:dl,SwitchGroup:ul,SwitchLabel:sl,Tab:wl,TabGroup:yl,TabList:gl,TabPanel:Pl,TabPanels:Sl,TransitionChild:Il,TransitionRoot:$l,provideUseId:function(e){w(J,e)}},Symbol.toStringTag,{value:"Module"}));export{Fn as A,Tn as F,Rl as H,Pn as I,On as j};
