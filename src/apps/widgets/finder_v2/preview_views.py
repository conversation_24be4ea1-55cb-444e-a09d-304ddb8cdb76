"""
Template Preview Views for Finder-v2 Widget
Provides Django admin integration for live template preview functionality.
"""

import json
import os
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.template import Template, Context
from django.template.exceptions import TemplateSyntaxError, TemplateDoesNotExist
from django.conf import settings
from django.utils.html import escape
from django.utils.safestring import mark_safe

# Import the template engine from the Vue.js app
# We'll create a Python equivalent for server-side rendering
from src.apps.widgets.finder_v2.template_engine import TemplateEngine


class TemplatePreviewAPI:
    """
    API class for template preview functionality in Django admin.
    Handles template rendering with sample data and theme integration.
    """
    
    @staticmethod
    def get_sample_data():
        """Load sample search results data from the JSON file."""
        sample_file = os.path.join(
            os.path.dirname(__file__), 
            'sample_data', 
            'search_results.json'
        )
        
        try:
            with open(sample_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Return the first item from the data array for preview
                if data.get('data') and len(data['data']) > 0:
                    return data['data'][0]
                else:
                    return TemplatePreviewAPI.get_fallback_data()
        except (FileNotFoundError, json.JSONDecodeError, KeyError):
            return TemplatePreviewAPI.get_fallback_data()
    
    @staticmethod
    def get_fallback_data():
        """Provide fallback data if sample file is not available."""
        return {
            "slug": "sample-vehicle",
            "name": "3.0i",
            "trim": "3.0i Sport",
            "make": {
                "slug": "mitsubishi",
                "name": "Mitsubishi",
                "name_en": "Mitsubishi"
            },
            "model": {
                "slug": "outlander",
                "name": "Outlander", 
                "name_en": "Outlander"
            },
            "generation": {
                "slug": "sample-gen",
                "name": "III (GF) Facelift",
                "platform": "GS",
                "start": 2015,
                "end": 2018
            },
            "start_year": 2015,
            "end_year": 2018,
            "engine": {
                "fuel": "Petrol",
                "capacity": "3.0",
                "type": "V6",
                "power": {
                    "kW": 169,
                    "PS": 230,
                    "hp": 227
                }
            },
            "technical": {
                "stud_holes": 5,
                "pcd": 114.3,
                "centre_bore": "67.1",
                "bolt_pattern": "5x114.3"
            },
            "wheels": [
                {
                    "is_stock": True,
                    "showing_fp_only": True,
                    "front": {
                        "rim": "7Jx18 ET38",
                        "rim_diameter": 18,
                        "rim_width": 7,
                        "rim_offset": 38,
                        "tire_full": "225/55R18 98V",
                        "tire": "225/55R18",
                        "tire_pressure": {
                            "bar": 2.4,
                            "psi": 35
                        }
                    },
                    "rear": {
                        "rim": "",
                        "tire": ""
                    }
                },
                {
                    "is_stock": False,
                    "showing_fp_only": True,
                    "front": {
                        "rim": "7.5Jx18 ET38",
                        "rim_diameter": 18,
                        "rim_width": 7.5,
                        "rim_offset": 38,
                        "tire_full": "245/50R18 98V",
                        "tire": "245/50R18",
                        "tire_pressure": {
                            "bar": 2.6,
                            "psi": 38
                        }
                    },
                    "rear": {
                        "rim": "",
                        "tire": ""
                    }
                }
            ]
        }

    @staticmethod
    def hex_to_rgb(hex_color):
        """Convert hex color to RGB tuple"""
        if not hex_color or not hex_color.startswith('#'):
            return "0, 0, 0"  # Default to black
        
        try:
            hex_color = hex_color.lstrip('#')
            if len(hex_color) == 3:
                hex_color = ''.join([c*2 for c in hex_color])
            return f"{int(hex_color[0:2], 16)}, {int(hex_color[2:4], 16)}, {int(hex_color[4:6], 16)}"
        except ValueError:
            return "0, 0, 0"

    @staticmethod
    def apply_theme_css(theme_config):
        """
        Generate CSS variables from theme configuration.
        Integrates with the existing finder-v2 theme system including opacity variants.
        """
        if not theme_config:
            # Default theme colors
            theme_config = {
                'primary_color': '#3b82f6',
                'secondary_color': '#6b7280', 
                'accent_color': '#10b981',
                'background_color': '#ffffff',
                'main_color': '#111827',
                'font_family': 'Inter, sans-serif',
                'font_size': '14px',
                'border_radius': '6px'
            }
        
        # Convert colors to RGB for opacity variants
        primary_rgb = TemplatePreviewAPI.hex_to_rgb(theme_config.get('primary_color', '#3b82f6'))
        secondary_rgb = TemplatePreviewAPI.hex_to_rgb(theme_config.get('secondary_color', '#6b7280'))
        accent_rgb = TemplatePreviewAPI.hex_to_rgb(theme_config.get('accent_color', '#10b981'))
        background_rgb = TemplatePreviewAPI.hex_to_rgb(theme_config.get('background_color', '#ffffff'))
        main_rgb = TemplatePreviewAPI.hex_to_rgb(theme_config.get('main_color', '#111827'))
        
        css_vars = f"""
        <style>
        .template-preview {{
            --primary-color: {theme_config.get('primary_color', '#3b82f6')};
            --secondary-color: {theme_config.get('secondary_color', '#6b7280')};
            --accent-color: {theme_config.get('accent_color', '#10b981')};
            --background-color: {theme_config.get('background_color', '#ffffff')};
            --main-color: {theme_config.get('main_color', '#111827')};
            --primary-rgb: {primary_rgb};
            --secondary-rgb: {secondary_rgb};
            --accent-rgb: {accent_rgb};
            --background-rgb: {background_rgb};
            --main-rgb: {main_rgb};
            font-family: {theme_config.get('font_family', 'Inter, sans-serif')};
            font-size: {theme_config.get('font_size', '14px')};
            --border-radius: {theme_config.get('border_radius', '6px')};
        }}
        
        /* Basic theme color classes */
        .template-preview .text-primary-color {{ color: var(--primary-color) !important; }}
        .template-preview .bg-primary-color {{ background-color: var(--primary-color) !important; }}
        .template-preview .border-primary-color {{ border-color: var(--primary-color) !important; }}
        .template-preview .text-secondary-color {{ color: var(--secondary-color) !important; }}
        .template-preview .bg-secondary-color {{ background-color: var(--secondary-color) !important; }}
        .template-preview .border-secondary-color {{ border-color: var(--secondary-color) !important; }}
        .template-preview .text-accent-color {{ color: var(--accent-color) !important; }}
        .template-preview .bg-accent-color {{ background-color: var(--accent-color) !important; }}
        .template-preview .text-main-color {{ color: var(--main-color) !important; }}
        .template-preview .bg-background-color {{ background-color: var(--background-color) !important; }}
        
        /* Opacity variants for primary color */
        .template-preview .bg-primary-color\\/5 {{ background-color: rgba(var(--primary-rgb), 0.05) !important; }}
        .template-preview .bg-primary-color\\/10 {{ background-color: rgba(var(--primary-rgb), 0.1) !important; }}
        .template-preview .bg-primary-color\\/20 {{ background-color: rgba(var(--primary-rgb), 0.2) !important; }}
        .template-preview .bg-primary-color\\/25 {{ background-color: rgba(var(--primary-rgb), 0.25) !important; }}
        .template-preview .bg-primary-color\\/30 {{ background-color: rgba(var(--primary-rgb), 0.3) !important; }}
        .template-preview .bg-primary-color\\/40 {{ background-color: rgba(var(--primary-rgb), 0.4) !important; }}
        .template-preview .bg-primary-color\\/50 {{ background-color: rgba(var(--primary-rgb), 0.5) !important; }}
        .template-preview .bg-primary-color\\/60 {{ background-color: rgba(var(--primary-rgb), 0.6) !important; }}
        .template-preview .bg-primary-color\\/70 {{ background-color: rgba(var(--primary-rgb), 0.7) !important; }}
        .template-preview .bg-primary-color\\/75 {{ background-color: rgba(var(--primary-rgb), 0.75) !important; }}
        .template-preview .bg-primary-color\\/80 {{ background-color: rgba(var(--primary-rgb), 0.8) !important; }}
        .template-preview .bg-primary-color\\/90 {{ background-color: rgba(var(--primary-rgb), 0.9) !important; }}
        .template-preview .bg-primary-color\\/95 {{ background-color: rgba(var(--primary-rgb), 0.95) !important; }}
        
        /* Opacity variants for secondary color */
        .template-preview .bg-secondary-color\\/5 {{ background-color: rgba(var(--secondary-rgb), 0.05) !important; }}
        .template-preview .bg-secondary-color\\/10 {{ background-color: rgba(var(--secondary-rgb), 0.1) !important; }}
        .template-preview .bg-secondary-color\\/20 {{ background-color: rgba(var(--secondary-rgb), 0.2) !important; }}
        .template-preview .bg-secondary-color\\/25 {{ background-color: rgba(var(--secondary-rgb), 0.25) !important; }}
        .template-preview .bg-secondary-color\\/30 {{ background-color: rgba(var(--secondary-rgb), 0.3) !important; }}
        .template-preview .bg-secondary-color\\/40 {{ background-color: rgba(var(--secondary-rgb), 0.4) !important; }}
        .template-preview .bg-secondary-color\\/50 {{ background-color: rgba(var(--secondary-rgb), 0.5) !important; }}
        
        /* Opacity variants for accent color */
        .template-preview .bg-accent-color\\/5 {{ background-color: rgba(var(--accent-rgb), 0.05) !important; }}
        .template-preview .bg-accent-color\\/10 {{ background-color: rgba(var(--accent-rgb), 0.1) !important; }}
        .template-preview .bg-accent-color\\/20 {{ background-color: rgba(var(--accent-rgb), 0.2) !important; }}
        .template-preview .bg-accent-color\\/25 {{ background-color: rgba(var(--accent-rgb), 0.25) !important; }}
        .template-preview .bg-accent-color\\/30 {{ background-color: rgba(var(--accent-rgb), 0.3) !important; }}
        .template-preview .bg-accent-color\\/40 {{ background-color: rgba(var(--accent-rgb), 0.4) !important; }}
        .template-preview .bg-accent-color\\/50 {{ background-color: rgba(var(--accent-rgb), 0.5) !important; }}
        
        /* Text opacity variants */
        .template-preview .text-primary-color\\/90 {{ color: rgba(var(--primary-rgb), 0.9) !important; }}
        .template-preview .text-primary-color\\/80 {{ color: rgba(var(--primary-rgb), 0.8) !important; }}
        .template-preview .text-primary-color\\/70 {{ color: rgba(var(--primary-rgb), 0.7) !important; }}
        .template-preview .text-secondary-color\\/90 {{ color: rgba(var(--secondary-rgb), 0.9) !important; }}
        .template-preview .text-secondary-color\\/80 {{ color: rgba(var(--secondary-rgb), 0.8) !important; }}
        .template-preview .text-secondary-color\\/70 {{ color: rgba(var(--secondary-rgb), 0.7) !important; }}
        .template-preview .text-main-color\\/90 {{ color: rgba(var(--main-rgb), 0.9) !important; }}
        .template-preview .text-main-color\\/80 {{ color: rgba(var(--main-rgb), 0.8) !important; }}
        .template-preview .text-main-color\\/70 {{ color: rgba(var(--main-rgb), 0.7) !important; }}
        
        /* Border opacity variants */
        .template-preview .border-primary-color\\/50 {{ border-color: rgba(var(--primary-rgb), 0.5) !important; }}
        .template-preview .border-primary-color\\/30 {{ border-color: rgba(var(--primary-rgb), 0.3) !important; }}
        .template-preview .border-primary-color\\/20 {{ border-color: rgba(var(--primary-rgb), 0.2) !important; }}
        .template-preview .border-secondary-color\\/50 {{ border-color: rgba(var(--secondary-rgb), 0.5) !important; }}
        .template-preview .border-secondary-color\\/30 {{ border-color: rgba(var(--secondary-rgb), 0.3) !important; }}
        .template-preview .border-secondary-color\\/20 {{ border-color: rgba(var(--secondary-rgb), 0.2) !important; }}
        </style>
        """
        return css_vars


@csrf_exempt
@require_http_methods(["POST"])
def template_preview(request):
    """
    AJAX endpoint for real-time template preview.
    Accepts template HTML and theme configuration, returns rendered preview.
    """
    try:
        data = json.loads(request.body)
        template_html = data.get('template', '')
        theme_config = data.get('theme', {})
        
        if not template_html or template_html.strip() == '':
            return JsonResponse({
                'success': False,
                'error': 'Template cannot be empty',
                'html': '<div class="text-gray-500 p-4">No template provided</div>'
            })
        
        # Get sample data
        sample_data = TemplatePreviewAPI.get_sample_data()
        
        # Use the template engine to render the template
        template_engine = TemplateEngine()
        
        try:
            rendered_html = template_engine.render_template(template_html, sample_data)
            
            # Apply theme CSS
            theme_css = TemplatePreviewAPI.apply_theme_css(theme_config)
            
            # Wrap in preview container with theme styles
            final_html = f"""
            {theme_css}
            <div class="template-preview p-1 bg-white border_ rounded-lg">
                {rendered_html}
            </div>
            """
            
            return JsonResponse({
                'success': True,
                'html': final_html,
                'data_used': sample_data  # For debugging
            })
            
        except Exception as template_error:
            return JsonResponse({
                'success': False,
                'error': f'Template rendering error: {str(template_error)}',
                'html': f'<div class="text-red-600 p-4 bg-red-50 border border-red-200 rounded">Template Error: {escape(str(template_error))}</div>'
            })
            
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
            'html': '<div class="text-red-600 p-4">Invalid request data</div>'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Server error: {str(e)}',
            'html': f'<div class="text-red-600 p-4">Server Error: {escape(str(e))}</div>'
        })


@require_http_methods(["GET"])
def template_preview_demo(request):
    """
    Demo endpoint to show template preview functionality.
    Useful for testing and development.
    """
    sample_data = TemplatePreviewAPI.get_sample_data()
    
    # Simple default template for demo
    default_template = """
    <div class="space-y-4">
        <h3 class="text-lg font-semibold mb-2 text-main-color">
            {{ make.name }} {{ model.name }} ({{ start_year }}-{{ end_year }})
        </h3>
        {% for wheel in wheels %}
            <div class="p-3 rounded-md border {% if wheel.is_stock %}border-primary-color bg-primary-color/10{% else %}border-secondary-color{% endif %}">
                <div class="font-medium uppercase tracking-wide text-sm mb-1 {% if wheel.is_stock %}text-primary-color{% else %}text-secondary-color{% endif %}">
                    {% if wheel.is_stock %}OE option{% else %}After-market option{% endif %}
                </div>
                <div class="grid grid-cols-2 gap-2 text-sm">
                    <div>
                        <span class="text-secondary-color">Front:</span>
                        <span class="text-main-color">{{ wheel.front.tire }} – {{ wheel.front.rim }}</span>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    """
    
    template_engine = TemplateEngine()
    rendered_html = template_engine.render_template(default_template, sample_data)
    
    theme_css = TemplatePreviewAPI.apply_theme_css(None)  # Use default theme
    
    html_response = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Template Preview Demo</title>
        <script src="https://cdn.tailwindcss.com"></script>
        {theme_css}
    </head>
    <body class="bg-gray-100 p-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-2xl font-bold mb-6">Template Preview Demo</h1>
            <div class="bg-white p-6 rounded-lg shadow">
                {rendered_html}
            </div>
            <div class="mt-6 p-4 bg-gray-50 rounded">
                <h2 class="font-semibold mb-2">Sample Data Used:</h2>
                <pre class="text-xs text-gray-600 overflow-auto">{json.dumps(sample_data, indent=2)}</pre>
            </div>
        </div>
    </body>
    </html>
    """
    
    return HttpResponse(html_response)