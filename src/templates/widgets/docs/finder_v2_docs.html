{% extends 'portal/base.html' %}

{% load i18n %}
{% load static %}

{% block extra_css %}
  <link rel="stylesheet" href="{% static 'portal/css/hljs/default.min.css' %}">
  <link rel="stylesheet" href="{% static 'portal/css/hljs/hybrid.css' %}">
  <link rel="stylesheet" href="{% static 'portal/css/hljs/ws-doc.css' %}">
  <style>
    /* TailwindCSS v4.1 inspired documentation styles */
    .doc-section {
      margin-bottom: 3rem;
      padding: 2rem;
      background: #ffffff;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .doc-section h2 {
      font-size: 1.875rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: #1a202c;
      border-bottom: 2px solid #e2e8f0;
      padding-bottom: 0.5rem;
    }
    
    .doc-section h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-top: 2rem;
      margin-bottom: 1rem;
      color: #2d3748;
    }
    
    .doc-section h4 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-top: 1.5rem;
      margin-bottom: 0.75rem;
      color: #4a5568;
    }
    
    .code-inline {
      background: #f7fafc;
      padding: 0.125rem 0.375rem;
      border-radius: 0.25rem;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 0.875rem;
      color: #2b6cb0;
      border: 1px solid #e2e8f0;
    }
    
    .doc-table {
      width: 100%;
      border-collapse: collapse;
      margin: 1rem 0;
    }
    
    .doc-table th {
      background: #f7fafc;
      padding: 0.75rem;
      text-align: left;
      font-weight: 600;
      border: 1px solid #e2e8f0;
      color: #2d3748;
    }
    
    .doc-table td {
      padding: 0.75rem;
      border: 1px solid #e2e8f0;
      vertical-align: top;
    }
    
    .doc-table tbody tr:hover {
      background: #f7fafc;
    }
    
    .badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    .badge-primary {
      background: #ebf8ff;
      color: #2b6cb0;
    }
    
    .badge-success {
      background: #f0fff4;
      color: #22543d;
    }
    
    .badge-warning {
      background: #fffdf7;
      color: #744210;
    }
    
    .alert-info {
      background: #ebf8ff;
      border-left: 4px solid #2b6cb0;
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0.25rem;
    }
    
    .alert-warning {
      background: #fffdf7;
      border-left: 4px solid #f6ad55;
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0.25rem;
    }
    
    pre.hljs {
      background: #1a202c;
      border-radius: 0.5rem;
      padding: 1.5rem;
      overflow-x: auto;
      margin: 1rem 0;
    }
    
    .toc {
      background: #f7fafc;
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
      padding: 1.5rem;
      margin-bottom: 2rem;
    }
    
    .toc h3 {
      margin-top: 0;
      margin-bottom: 1rem;
      font-size: 1.25rem;
      color: #2d3748;
    }
    
    .toc ul {
      list-style: none;
      padding-left: 0;
    }
    
    .toc li {
      margin: 0.5rem 0;
    }
    
    .toc a {
      color: #2b6cb0;
      text-decoration: none;
      display: flex;
      align-items: center;
    }
    
    .toc a:hover {
      color: #2c5282;
      text-decoration: underline;
    }
    
    .toc li li {
      margin-left: 1.5rem;
      font-size: 0.9rem;
    }
  </style>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'portal/js/highlight.min.js' %}"></script>
  <script>
    hljs.initHighlightingOnLoad();
    
    // Smooth scroll to anchors
    document.addEventListener('DOMContentLoaded', function() {
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        });
      });
    });
  </script>
{% endblock %}

{% block title %}Finder-v2 Widget Documentation{% endblock %}

{% block content %}
<div class="container">
  <div class="row">
    <div class="col-md-12">
      <h1 class="page-title mb-4">
        <span>Finder-v2 Widget Documentation</span>
      </h1>
      
      <!-- Table of Contents -->
      <div class="toc">
        <h3>📚 Table of Contents</h3>
        <ul>
          <li><a href="#overview">🔍 Overview</a></li>
          <li><a href="#event-system">🎯 Event System</a>
            <ul>
              <li><a href="#event-types">Event Types</a></li>
              <li><a href="#event-payloads">Event Payloads</a></li>
              <li><a href="#parent-integration">Parent Page Integration</a></li>
            </ul>
          </li>
          <li><a href="#template-system">📝 Template System</a>
            <ul>
              <li><a href="#template-syntax">Template Syntax</a></li>
              <li><a href="#template-variables">Available Variables</a></li>
              <li><a href="#template-examples">Template Examples</a></li>
            </ul>
          </li>
          <li><a href="#color-system">🎨 Color System</a>
            <ul>
              <li><a href="#color-classes">CSS Classes</a></li>
              <li><a href="#color-usage">Usage Examples</a></li>
            </ul>
          </li>
          <li><a href="#api-reference">📖 API Reference</a></li>
          <li><a href="#examples">💡 Complete Examples</a></li>
        </ul>
      </div>

      <!-- Overview Section -->
      <div class="doc-section" id="overview">
        <h2>🔍 Overview</h2>
        <p>
          The Finder-v2 widget is a modern, Vue 3-based vehicle wheel/tire finder widget that provides a seamless integration experience for client websites. 
          It features a powerful event system, customizable templates, and a flexible color system.
        </p>
        
        <div class="alert-info">
          <strong>Key Features:</strong>
          <ul style="margin-top: 0.5rem;">
            <li>✅ Vue 3 Composition API architecture</li>
            <li>✅ TailwindCSS v4 styling system</li>
            <li>✅ Full backward compatibility with legacy WheelSizeWidgets API</li>
            <li>✅ Cross-origin communication via postMessage</li>
            <li>✅ Customizable templates with Django-like syntax</li>
            <li>✅ Dynamic color theming system</li>
          </ul>
        </div>
        
        <h4>Architecture Components</h4>
        <ul>
          <li><code class="code-inline">src/apps/widgets/finder_v2/app/src/composables/useWidgetEvents.js</code> - Event system composable</li>
          <li><code class="code-inline">src/apps/widgets/finder_v2/app/src/stores/finder.js</code> - Store integration</li>
          <li><code class="code-inline">src/apps/widgets/finder_v2/app/src/main.js</code> - App bootstrapping</li>
        </ul>
      </div>

      <!-- Event System Section -->
      <div class="doc-section" id="event-system">
        <h2>🎯 Event System</h2>
        <p>
          The Finder-v2 widget implements a comprehensive event system that maintains full compatibility with the legacy 
          <code class="code-inline">WheelSizeWidgets</code> JavaScript API while leveraging modern Vue 3 patterns.
        </p>

        <h3 id="event-types">Event Types</h3>
        <table class="doc-table">
          <thead>
            <tr>
              <th>Event Name</th>
              <th>Description</th>
              <th>When Fired</th>
              <th>Payload</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">ready:document</code></td>
              <td>DOM is ready</td>
              <td>When widget DOM is fully loaded</td>
              <td>Context object</td>
            </tr>
            <tr>
              <td><code class="code-inline">ready:window</code></td>
              <td>Window and data loaded</td>
              <td>After window load AND initial data load complete</td>
              <td>Context object</td>
            </tr>
            <tr>
              <td><code class="code-inline">change:year</code></td>
              <td>Year selection changed</td>
              <td>When user selects a year</td>
              <td><code>{ value: { slug, title } }</code></td>
            </tr>
            <tr>
              <td><code class="code-inline">change:make</code></td>
              <td>Make selection changed</td>
              <td>When user selects a make</td>
              <td><code>{ value: { slug, title } }</code></td>
            </tr>
            <tr>
              <td><code class="code-inline">change:model</code></td>
              <td>Model selection changed</td>
              <td>When user selects a model</td>
              <td><code>{ value: { slug, title } }</code></td>
            </tr>
            <tr>
              <td><code class="code-inline">change:generation</code></td>
              <td>Generation selection changed</td>
              <td>When user selects a generation</td>
              <td><code>{ value: { slug, title } }</code></td>
            </tr>
            <tr>
              <td><code class="code-inline">change:modification</code></td>
              <td>Modification selection changed</td>
              <td>When user selects a modification</td>
              <td><code>{ value: { slug, title } }</code></td>
            </tr>
            <tr>
              <td><code class="code-inline">search:start</code></td>
              <td>Search initiated</td>
              <td>Before <code>searchByVehicle()</code> call</td>
              <td>Context with selections</td>
            </tr>
            <tr>
              <td><code class="code-inline">search:complete</code></td>
              <td>Search completed</td>
              <td>After results loaded</td>
              <td>Includes counts and timings</td>
            </tr>
            <tr>
              <td><code class="code-inline">search:error</code></td>
              <td>Search failed</td>
              <td>On search error</td>
              <td><code>{ error_message: string }</code></td>
            </tr>
            <tr>
              <td><code class="code-inline">results:display</code></td>
              <td>Results rendered</td>
              <td>After DOM renders results</td>
              <td>See detailed structure below</td>
            </tr>
          </tbody>
        </table>

        <h3 id="event-payloads">Event Payload Structure</h3>
        <p>All events are sent with a standardized envelope structure via <code class="code-inline">window.parent.postMessage</code>:</p>
        
        <pre class="hljs"><code class="json">{
  "src": "&lt;child iframe url&gt;",
  "type": "&lt;event type&gt;",
  "data": {
    "context": {
      "config": {},
      "widgetUuid": "&lt;uuid&gt;",
      "widgetType": "finder-v2",
      "flowType": "primary|alternative|year_select",
      "selections": {
        "year": "&lt;slug&gt;",
        "make": "&lt;slug&gt;",
        "model": "&lt;slug&gt;",
        "generation": "&lt;slug&gt;",
        "modification": "&lt;slug&gt;"
      }
    }
  }
}</code></pre>

        <h4>Special Event: results:display</h4>
        <p>The <code class="code-inline">results:display</code> event is fired after search results are rendered in the widget's DOM:</p>
        
        <pre class="hljs"><code class="json">{
  "type": "results:display",
  "data": {
    "results_count": 3,
    "context": {
      "config": { /* widget configuration */ },
      "widgetUuid": "...",
      "widgetType": "finder-v2",
      "flowType": "primary|alternative|year_select",
      "selections": {
        "year": "&lt;slug&gt;",
        "make": "&lt;slug&gt;",
        "model": "&lt;slug&gt;",
        "generation": "&lt;slug&gt;",
        "modification": "&lt;slug&gt;"
      }
    }
  }
}</code></pre>

        <div class="alert-warning">
          <strong>Note:</strong> The <code class="code-inline">results:display</code> event does NOT include full results data to keep messages lightweight. 
          Results are rendered inside the widget. Parent pages can use <code class="code-inline">results_count</code> and context for analytics.
        </div>

        <h3 id="parent-integration">Parent Page Integration</h3>
        <p>Here's how to integrate the event system in your parent page:</p>
        
        <pre class="hljs"><code class="html">&lt;script src="//services.wheel-size.com/static/widget/code/local/ws-widget.js"&gt;&lt;/script&gt;
&lt;script&gt;
  // Create widget instance
  var widget = WheelSizeWidgets.create('#ws-widget', { 
    uuid: 'your-uuid', 
    type: 'finder-v2' 
  });
  
  // Listen to events
  widget.on('ready:window', function(e) {
    console.log('Widget ready', e);
  });
  
  widget.on('change:make', function(e) {
    console.log('Make selected:', e.data.value);
    // e.data.value = { slug: 'toyota', title: 'Toyota' }
  });
  
  widget.on('search:start', function(e) {
    console.log('Search started with selections:', e.data.context.selections);
  });
  
  widget.on('search:complete', function(e) {
    console.log('Search completed');
  });
  
  widget.on('search:error', function(e) {
    console.error('Search failed:', e.data.error_message);
  });
  
  widget.on('results:display', function(e) {
    console.log('Results displayed. Count:', e.data.results_count);
    console.log('Vehicle:', e.data.context.selections);
  });
&lt;/script&gt;</code></pre>
      </div>

      <!-- Template System Section -->
      <div class="doc-section" id="template-system">
        <h2>📝 Template System</h2>
        <p>
          The Finder-v2 widget uses a Django-like template syntax that allows for powerful customization of the results display. 
          Templates can access all data returned from the Finder API and use various control structures.
        </p>

        <h3 id="template-syntax">Template Syntax</h3>
        <table class="doc-table">
          <thead>
            <tr>
              <th>Syntax Type</th>
              <th>Pattern</th>
              <th>Example</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><span class="badge badge-primary">Variables</span></td>
              <td><code class="code-inline">{% verbatim %}{{ variable }}{% endverbatim %}</code></td>
              <td><code class="code-inline">{% verbatim %}{{ make.name }}{% endverbatim %}</code></td>
              <td>Output variable value</td>
            </tr>
            <tr>
              <td><span class="badge badge-primary">Nested Access</span></td>
              <td><code class="code-inline">{% verbatim %}{{ parent.child }}{% endverbatim %}</code></td>
              <td><code class="code-inline">{% verbatim %}{{ engine.power.hp }}{% endverbatim %}</code></td>
              <td>Access nested properties</td>
            </tr>
            <tr>
              <td><span class="badge badge-success">Ternary</span></td>
              <td><code class="code-inline">{% verbatim %}{{ condition ? true : false }}{% endverbatim %}</code></td>
              <td><code class="code-inline">{% verbatim %}{{ wheel.is_stock ? "OE" : "AM" }}{% endverbatim %}</code></td>
              <td>Inline conditional output</td>
            </tr>
            <tr>
              <td><span class="badge badge-warning">If Statement</span></td>
              <td><code class="code-inline">{% verbatim %}{% if condition %}...{% endif %}{% endverbatim %}</code></td>
              <td><code class="code-inline">{% verbatim %}{% if wheel.is_stock %}OE{% endif %}{% endverbatim %}</code></td>
              <td>Conditional block</td>
            </tr>
            <tr>
              <td><span class="badge badge-warning">If-Else</span></td>
              <td><code class="code-inline">{% verbatim %}{% if condition %}...{% else %}...{% endif %}{% endverbatim %}</code></td>
              <td><code class="code-inline">{% verbatim %}{% if wheel.rear.tire %}Dual{% else %}Single{% endif %}{% endverbatim %}</code></td>
              <td>Conditional with fallback</td>
            </tr>
            <tr>
              <td><span class="badge badge-warning">Negation</span></td>
              <td><code class="code-inline">{% verbatim %}{% if not condition %}...{% endif %}{% endverbatim %}</code></td>
              <td><code class="code-inline">{% verbatim %}{% if not wheel.showing_fp_only %}...{% endif %}{% endverbatim %}</code></td>
              <td>Negated condition</td>
            </tr>
            <tr>
              <td><span class="badge badge-success">For Loop</span></td>
              <td><code class="code-inline">{% verbatim %}{% for item in array %}...{% endfor %}{% endverbatim %}</code></td>
              <td><code class="code-inline">{% verbatim %}{% for wheel in wheels %}...{% endfor %}{% endverbatim %}</code></td>
              <td>Iterate over array</td>
            </tr>
          </tbody>
        </table>

        <h3 id="template-variables">Available Template Variables</h3>
        <p>The following variables are available in templates based on the Finder API v2 response:</p>

        <h4>Vehicle Information</h4>
        <table class="doc-table">
          <thead>
            <tr>
              <th>Variable Path</th>
              <th>Type</th>
              <th>Description</th>
              <th>Example Value</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">make.name</code></td>
              <td>string</td>
              <td>Make/brand name</td>
              <td>"Toyota"</td>
            </tr>
            <tr>
              <td><code class="code-inline">make.slug</code></td>
              <td>string</td>
              <td>Make identifier</td>
              <td>"toyota"</td>
            </tr>
            <tr>
              <td><code class="code-inline">model.name</code></td>
              <td>string</td>
              <td>Model name</td>
              <td>"Camry"</td>
            </tr>
            <tr>
              <td><code class="code-inline">model.slug</code></td>
              <td>string</td>
              <td>Model identifier</td>
              <td>"camry"</td>
            </tr>
            <tr>
              <td><code class="code-inline">generation.name</code></td>
              <td>string</td>
              <td>Generation name</td>
              <td>"XV70"</td>
            </tr>
            <tr>
              <td><code class="code-inline">generation.start</code></td>
              <td>number</td>
              <td>Generation start year</td>
              <td>2017</td>
            </tr>
            <tr>
              <td><code class="code-inline">generation.end</code></td>
              <td>number</td>
              <td>Generation end year</td>
              <td>2024</td>
            </tr>
            <tr>
              <td><code class="code-inline">generation.platform</code></td>
              <td>string</td>
              <td>Platform code</td>
              <td>"GA-K"</td>
            </tr>
            <tr>
              <td><code class="code-inline">generation.bodies</code></td>
              <td>array</td>
              <td>Body types</td>
              <td>["Sedan", "Wagon"]</td>
            </tr>
            <tr>
              <td><code class="code-inline">start_year</code></td>
              <td>number</td>
              <td>Production start year</td>
              <td>2018</td>
            </tr>
            <tr>
              <td><code class="code-inline">end_year</code></td>
              <td>number</td>
              <td>Production end year</td>
              <td>2024</td>
            </tr>
            <tr>
              <td><code class="code-inline">name</code></td>
              <td>string</td>
              <td>Modification/trim name</td>
              <td>"LE 2.5L"</td>
            </tr>
            <tr>
              <td><code class="code-inline">trim</code></td>
              <td>string</td>
              <td>Trim level</td>
              <td>"LE"</td>
            </tr>
            <tr>
              <td><code class="code-inline">body</code></td>
              <td>string</td>
              <td>Body type</td>
              <td>"Sedan"</td>
            </tr>
          </tbody>
        </table>

        <h4>Engine Information</h4>
        <table class="doc-table">
          <thead>
            <tr>
              <th>Variable Path</th>
              <th>Type</th>
              <th>Description</th>
              <th>Example Value</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">engine.fuel</code></td>
              <td>string</td>
              <td>Fuel type</td>
              <td>"Gasoline"</td>
            </tr>
            <tr>
              <td><code class="code-inline">engine.capacity</code></td>
              <td>string</td>
              <td>Engine displacement</td>
              <td>"2.5"</td>
            </tr>
            <tr>
              <td><code class="code-inline">engine.type</code></td>
              <td>string</td>
              <td>Engine type</td>
              <td>"I4"</td>
            </tr>
            <tr>
              <td><code class="code-inline">engine.code</code></td>
              <td>string</td>
              <td>Engine code</td>
              <td>"A25A-FKS"</td>
            </tr>
            <tr>
              <td><code class="code-inline">engine.power.hp</code></td>
              <td>number</td>
              <td>Power in HP</td>
              <td>203</td>
            </tr>
            <tr>
              <td><code class="code-inline">engine.power.PS</code></td>
              <td>number</td>
              <td>Power in PS</td>
              <td>206</td>
            </tr>
            <tr>
              <td><code class="code-inline">engine.power.kW</code></td>
              <td>number</td>
              <td>Power in kW</td>
              <td>151</td>
            </tr>
          </tbody>
        </table>

        <h4>Wheel/Tire Information (in wheels array)</h4>
        <table class="doc-table">
          <thead>
            <tr>
              <th>Variable Path</th>
              <th>Type</th>
              <th>Description</th>
              <th>Example Value</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">wheel.is_stock</code></td>
              <td>boolean</td>
              <td>OE fitment flag</td>
              <td>true</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.is_recommended_for_winter</code></td>
              <td>boolean</td>
              <td>Winter recommendation</td>
              <td>false</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.is_runflat_tires</code></td>
              <td>boolean</td>
              <td>Run-flat tires</td>
              <td>false</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.is_extra_load_tires</code></td>
              <td>boolean</td>
              <td>Extra load rating</td>
              <td>false</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.is_pressed_steel_rims</code></td>
              <td>boolean</td>
              <td>Steel wheels</td>
              <td>false</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.showing_fp_only</code></td>
              <td>boolean</td>
              <td>Front pair only</td>
              <td>false</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.front.tire</code></td>
              <td>string</td>
              <td>Front tire size</td>
              <td>"215/55R17"</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.front.tire_full</code></td>
              <td>string</td>
              <td>Full tire spec</td>
              <td>"215/55R17 94V"</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.front.rim</code></td>
              <td>string</td>
              <td>Front rim size</td>
              <td>"7Jx17 ET45"</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.front.rim_diameter</code></td>
              <td>string</td>
              <td>Rim diameter</td>
              <td>"17"</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.front.rim_width</code></td>
              <td>string</td>
              <td>Rim width</td>
              <td>"7"</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.front.rim_offset</code></td>
              <td>string</td>
              <td>Rim offset</td>
              <td>"45"</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.front.tire_pressure.bar</code></td>
              <td>number</td>
              <td>Pressure in bar</td>
              <td>2.3</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.front.tire_pressure.psi</code></td>
              <td>number</td>
              <td>Pressure in PSI</td>
              <td>33</td>
            </tr>
            <tr>
              <td><code class="code-inline">wheel.rear.*</code></td>
              <td>various</td>
              <td>Rear specs (same structure as front)</td>
              <td>-</td>
            </tr>
          </tbody>
        </table>

        <h4>Technical Specifications</h4>
        <table class="doc-table">
          <thead>
            <tr>
              <th>Variable Path</th>
              <th>Type</th>
              <th>Description</th>
              <th>Example Value</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">technical.bolt_pattern</code></td>
              <td>string</td>
              <td>Bolt pattern</td>
              <td>"5x114.3"</td>
            </tr>
            <tr>
              <td><code class="code-inline">technical.stud_holes</code></td>
              <td>number</td>
              <td>Number of studs</td>
              <td>5</td>
            </tr>
            <tr>
              <td><code class="code-inline">technical.pcd</code></td>
              <td>string</td>
              <td>Pitch circle diameter</td>
              <td>"114.3"</td>
            </tr>
            <tr>
              <td><code class="code-inline">technical.centre_bore</code></td>
              <td>string</td>
              <td>Center bore diameter</td>
              <td>"60.1"</td>
            </tr>
            <tr>
              <td><code class="code-inline">technical.wheel_fasteners</code></td>
              <td>string</td>
              <td>Fastener type</td>
              <td>"Lug nuts"</td>
            </tr>
            <tr>
              <td><code class="code-inline">regions</code></td>
              <td>array</td>
              <td>Market regions</td>
              <td>["USDM", "CDM"]</td>
            </tr>
          </tbody>
        </table>

        <h3 id="template-examples">Template Examples</h3>
        
        <h4>Basic Vehicle Information</h4>
        {% verbatim %}<pre class="hljs"><code class="html">&lt;h3&gt;{{ make.name }} {{ model.name }} ({{ start_year }}-{{ end_year }})&lt;/h3&gt;
{% if generation.name %}
  &lt;p&gt;Generation: {{ generation.name }}&lt;/p&gt;
{% endif %}</code></pre>{% endverbatim %}

        <h4>Engine Details with Conditionals</h4>
        {% verbatim %}<pre class="hljs"><code class="html">{% if engine.type %}
  &lt;div class="engine-info"&gt;
    &lt;span&gt;{{ engine.type }} {{ engine.capacity }}L&lt;/span&gt;
    {% if engine.power.hp %}
      &lt;span&gt;• {{ engine.power.hp }} hp&lt;/span&gt;
    {% endif %}
    {% if engine.fuel %}
      &lt;span&gt;• {{ engine.fuel }}&lt;/span&gt;
    {% endif %}
  &lt;/div&gt;
{% endif %}</code></pre>{% endverbatim %}

        <h4>Wheel Fitments with Loops and Ternary</h4>
        {% verbatim %}<pre class="hljs"><code class="html">&lt;div class="fitments"&gt;
  {% for wheel in wheels %}
    &lt;div class="fitment-card"&gt;
      &lt;span class="badge"&gt;{{ wheel.is_stock ? "OE" : "Aftermarket" }}&lt;/span&gt;
      
      &lt;div class="specs"&gt;
        &lt;div&gt;Front: {{ wheel.front.tire }} on {{ wheel.front.rim }}&lt;/div&gt;
        
        {% if not wheel.showing_fp_only %}
          {% if wheel.rear.tire %}
            &lt;div&gt;Rear: {{ wheel.rear.tire }} on {{ wheel.rear.rim }}&lt;/div&gt;
          {% endif %}
        {% endif %}
        
        {% if wheel.front.tire_pressure.bar %}
          &lt;div&gt;Pressure: {{ wheel.front.tire_pressure.bar }} bar / {{ wheel.front.tire_pressure.psi }} psi&lt;/div&gt;
        {% endif %}
      &lt;/div&gt;
      
      {% if wheel.is_recommended_for_winter %}
        &lt;span class="winter-badge"&gt;❄️ Winter Recommended&lt;/span&gt;
      {% endif %}
    &lt;/div&gt;
  {% endfor %}
&lt;/div&gt;</code></pre>{% endverbatim %}

        <h4>Complete Template Example</h4>
        {% verbatim %}<pre class="hljs"><code class="html">&lt;div class="ws-results"&gt;
  &lt;header class="vehicle-header"&gt;
    &lt;h2&gt;{{ make.name }} {{ model.name }}&lt;/h2&gt;
    &lt;div class="vehicle-details"&gt;
      &lt;span&gt;{{ start_year }}-{{ end_year }}&lt;/span&gt;
      {% if generation.name %}
        &lt;span&gt;• {{ generation.name }}&lt;/span&gt;
      {% endif %}
      {% if trim %}
        &lt;span&gt;• {{ trim }}&lt;/span&gt;
      {% endif %}
    &lt;/div&gt;
    
    {% if engine.type %}
      &lt;div class="engine-specs"&gt;
        {{ engine.capacity }}L {{ engine.type }} 
        {% if engine.power.hp %}
          ({{ engine.power.hp }} hp)
        {% endif %}
      &lt;/div&gt;
    {% endif %}
  &lt;/header&gt;
  
  &lt;div class="technical-info"&gt;
    {% if technical.bolt_pattern %}
      &lt;div&gt;Bolt Pattern: {{ technical.bolt_pattern }}&lt;/div&gt;
    {% endif %}
    {% if technical.centre_bore %}
      &lt;div&gt;Center Bore: {{ technical.centre_bore }} mm&lt;/div&gt;
    {% endif %}
  &lt;/div&gt;
  
  &lt;div class="wheel-options"&gt;
    &lt;h3&gt;Wheel &amp; Tire Options&lt;/h3&gt;
    
    {% for wheel in wheels %}
      &lt;div class="option-card {{ wheel.is_stock ? 'oe-option' : 'aftermarket-option' }}"&gt;
        &lt;div class="option-header"&gt;
          &lt;span class="option-type"&gt;
            {{ wheel.is_stock ? "Original Equipment" : "Aftermarket Option" }}
          &lt;/span&gt;
          &lt;span class="wheel-size"&gt;{{ wheel.front.rim_diameter }}" wheels&lt;/span&gt;
        &lt;/div&gt;
        
        &lt;div class="fitment-details"&gt;
          &lt;div class="front-fitment"&gt;
            &lt;strong&gt;Front:&lt;/strong&gt;
            &lt;div&gt;Tire: {{ wheel.front.tire_full }}&lt;/div&gt;
            &lt;div&gt;Rim: {{ wheel.front.rim }}&lt;/div&gt;
            {% if wheel.front.tire_pressure.bar %}
              &lt;div&gt;Pressure: {{ wheel.front.tire_pressure.bar }} bar&lt;/div&gt;
            {% endif %}
          &lt;/div&gt;
          
          {% if not wheel.showing_fp_only %}
            {% if wheel.rear.tire %}
              &lt;div class="rear-fitment"&gt;
                &lt;strong&gt;Rear:&lt;/strong&gt;
                &lt;div&gt;Tire: {{ wheel.rear.tire_full }}&lt;/div&gt;
                &lt;div&gt;Rim: {{ wheel.rear.rim }}&lt;/div&gt;
                {% if wheel.rear.tire_pressure.bar %}
                  &lt;div&gt;Pressure: {{ wheel.rear.tire_pressure.bar }} bar&lt;/div&gt;
                {% endif %}
              &lt;/div&gt;
            {% endif %}
          {% endif %}
        &lt;/div&gt;
        
        &lt;div class="fitment-flags"&gt;
          {% if wheel.is_recommended_for_winter %}
            &lt;span class="flag winter"&gt;❄️ Winter&lt;/span&gt;
          {% endif %}
          {% if wheel.is_runflat_tires %}
            &lt;span class="flag runflat"&gt;🛡️ Run-flat&lt;/span&gt;
          {% endif %}
          {% if wheel.is_extra_load_tires %}
            &lt;span class="flag xl"&gt;💪 XL&lt;/span&gt;
          {% endif %}
          {% if wheel.is_pressed_steel_rims %}
            &lt;span class="flag steel"&gt;🔧 Steel&lt;/span&gt;
          {% endif %}
        &lt;/div&gt;
      &lt;/div&gt;
    {% endfor %}
  &lt;/div&gt;
&lt;/div&gt;</code></pre>{% endverbatim %}
      </div>

      <!-- Color System Section -->
      <div class="doc-section" id="color-system">
        <h2>🎨 Color System</h2>
        <p>
          The Finder-v2 widget implements a flexible color theming system that allows customization to match your brand. 
          The system uses CSS custom properties that can be overridden while maintaining consistent naming conventions.
        </p>

        <h3 id="color-classes">Primary Brand Color Classes</h3>
        <p>
          The primary brand color is the main accent color used throughout the widget for interactive elements, 
          highlights, and brand-specific styling. These classes adapt to your configured primary color.
        </p>

        <table class="doc-table">
          <thead>
            <tr>
              <th>CSS Class</th>
              <th>Purpose</th>
              <th>Usage Example</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">text-primary-color</code></td>
              <td>Primary color text</td>
              <td>Links, important labels, OE badges</td>
            </tr>
            <tr>
              <td><code class="code-inline">bg-primary-color</code></td>
              <td>Primary color background</td>
              <td>Buttons, selected states, badges</td>
            </tr>
            <tr>
              <td><code class="code-inline">border-primary-color</code></td>
              <td>Primary color border</td>
              <td>Active inputs, highlighted cards</td>
            </tr>
            <tr>
              <td><code class="code-inline">hover:bg-primary-color</code></td>
              <td>Primary hover background</td>
              <td>Button hover states</td>
            </tr>
            <tr>
              <td><code class="code-inline">hover:text-primary-color</code></td>
              <td>Primary hover text</td>
              <td>Link hover states</td>
            </tr>
            <tr>
              <td><code class="code-inline">hover:border-primary-color</code></td>
              <td>Primary hover border</td>
              <td>Card hover states</td>
            </tr>
          </tbody>
        </table>

        <h4>Opacity Variants</h4>
        <p>Use opacity modifiers for subtle backgrounds and overlays:</p>
        
        <table class="doc-table">
          <thead>
            <tr>
              <th>CSS Class</th>
              <th>Opacity</th>
              <th>Use Case</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">bg-primary-color/5</code></td>
              <td>5%</td>
              <td>Very subtle background tint</td>
            </tr>
            <tr>
              <td><code class="code-inline">bg-primary-color/10</code></td>
              <td>10%</td>
              <td>Light background for OE options</td>
            </tr>
            <tr>
              <td><code class="code-inline">bg-primary-color/20</code></td>
              <td>20%</td>
              <td>Badge backgrounds, hover states</td>
            </tr>
            <tr>
              <td><code class="code-inline">bg-primary-color/50</code></td>
              <td>50%</td>
              <td>Overlays, disabled states</td>
            </tr>
          </tbody>
        </table>

        <h4>Additional Color Classes</h4>
        <table class="doc-table">
          <thead>
            <tr>
              <th>CSS Class</th>
              <th>Purpose</th>
              <th>Typical Use</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">text-main-color</code></td>
              <td>Main text color</td>
              <td>Body text, headings</td>
            </tr>
            <tr>
              <td><code class="code-inline">text-secondary-color</code></td>
              <td>Secondary text</td>
              <td>Labels, descriptions, muted text</td>
            </tr>
            <tr>
              <td><code class="code-inline">bg-background-color</code></td>
              <td>Background color</td>
              <td>Card backgrounds, containers</td>
            </tr>
            <tr>
              <td><code class="code-inline">border-secondary-color</code></td>
              <td>Secondary borders</td>
              <td>Dividers, neutral borders</td>
            </tr>
            <tr>
              <td><code class="code-inline">text-accent-color</code></td>
              <td>Accent color text</td>
              <td>Alternative highlights</td>
            </tr>
            <tr>
              <td><code class="code-inline">bg-accent-color</code></td>
              <td>Accent backgrounds</td>
              <td>Aftermarket badges</td>
            </tr>
          </tbody>
        </table>

        <h3 id="color-usage">Usage Examples</h3>

        <h4>OE vs Aftermarket Distinction</h4>
        <pre class="hljs"><code class="html">&lt;!-- OE Option with primary color --&gt;
&lt;div class="border border-primary-color bg-primary-color/10 p-4 rounded"&gt;
  &lt;span class="text-primary-color font-semibold"&gt;Original Equipment&lt;/span&gt;
  &lt;!-- content --&gt;
&lt;/div&gt;

&lt;!-- Aftermarket Option with secondary color --&gt;
&lt;div class="border border-secondary-color hover:bg-secondary-color/10 p-4 rounded"&gt;
  &lt;span class="text-secondary-color font-semibold"&gt;Aftermarket&lt;/span&gt;
  &lt;!-- content --&gt;
&lt;/div&gt;</code></pre>

        <h4>Interactive Elements</h4>
        <pre class="hljs"><code class="html">&lt;!-- Primary button --&gt;
&lt;button class="bg-primary-color text-white hover:bg-primary-color/90 px-4 py-2 rounded"&gt;
  Search
&lt;/button&gt;

&lt;!-- Secondary button --&gt;
&lt;button class="border border-primary-color text-primary-color hover:bg-primary-color/10 px-4 py-2 rounded"&gt;
  Clear
&lt;/button&gt;

&lt;!-- Link with primary color --&gt;
&lt;a href="#" class="text-primary-color hover:text-primary-color/80 underline"&gt;
  View Details
&lt;/a&gt;</code></pre>

        <h4>Badges and Labels</h4>
        <pre class="hljs"><code class="html">&lt;!-- OE Badge --&gt;
&lt;span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-primary-color/20 text-primary-color"&gt;
  OE
&lt;/span&gt;

&lt;!-- Aftermarket Badge --&gt;
&lt;span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-secondary-color/20 text-secondary-color"&gt;
  AM
&lt;/span&gt;

&lt;!-- Feature Badge --&gt;
&lt;span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-accent-color/20 text-accent-color"&gt;
  Winter
&lt;/span&gt;</code></pre>

        <h4>Cards with Hover Effects</h4>
        <pre class="hljs"><code class="html">&lt;div class="bg-background-color border border-secondary-color hover:border-primary-color hover:shadow-lg transition-all duration-300 p-4 rounded-lg"&gt;
  &lt;h3 class="text-main-color font-semibold"&gt;17" Wheel Option&lt;/h3&gt;
  &lt;p class="text-secondary-color"&gt;215/55R17 - 7Jx17 ET45&lt;/p&gt;
  &lt;div class="mt-2"&gt;
    &lt;span class="text-primary-color font-medium"&gt;View Details →&lt;/span&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>

        <h4>Complete Color System Example</h4>
        {% verbatim %}<pre class="hljs"><code class="html">&lt;div class="widget-container"&gt;
  &lt;!-- Header with primary accent --&gt;
  &lt;header class="border-b-2 border-primary-color pb-4 mb-6"&gt;
    &lt;h1 class="text-2xl font-bold text-main-color"&gt;Wheel Finder Results&lt;/h1&gt;
    &lt;p class="text-secondary-color"&gt;Found 3 options for your vehicle&lt;/p&gt;
  &lt;/header&gt;
  
  &lt;!-- Results grid --&gt;
  &lt;div class="grid gap-4"&gt;
    &lt;!-- OE Option Card --&gt;
    &lt;div class="bg-primary-color/5 border-2 border-primary-color rounded-lg p-6"&gt;
      &lt;div class="flex justify-between items-start mb-4"&gt;
        &lt;h3 class="text-lg font-semibold text-main-color"&gt;Factory Standard&lt;/h3&gt;
        &lt;span class="bg-primary-color text-white px-3 py-1 rounded-full text-sm font-medium"&gt;
          OE
        &lt;/span&gt;
      &lt;/div&gt;
      
      &lt;div class="space-y-2"&gt;
        &lt;div class="flex justify-between"&gt;
          &lt;span class="text-secondary-color"&gt;Tire:&lt;/span&gt;
          &lt;span class="text-main-color font-medium"&gt;215/55R17&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class="flex justify-between"&gt;
          &lt;span class="text-secondary-color"&gt;Rim:&lt;/span&gt;
          &lt;span class="text-main-color font-medium"&gt;7Jx17 ET45&lt;/span&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;button class="mt-4 w-full bg-primary-color hover:bg-primary-color/90 text-white py-2 rounded-lg transition-colors"&gt;
        Select This Option
      &lt;/button&gt;
    &lt;/div&gt;
    
    &lt;!-- Aftermarket Option Card --&gt;
    &lt;div class="bg-background-color border border-secondary-color hover:border-primary-color rounded-lg p-6 transition-colors"&gt;
      &lt;div class="flex justify-between items-start mb-4"&gt;
        &lt;h3 class="text-lg font-semibold text-main-color"&gt;Performance Upgrade&lt;/h3&gt;
        &lt;span class="bg-accent-color/20 text-accent-color px-3 py-1 rounded-full text-sm font-medium"&gt;
          AM
        &lt;/span&gt;
      &lt;/div&gt;
      
      &lt;div class="space-y-2"&gt;
        &lt;div class="flex justify-between"&gt;
          &lt;span class="text-secondary-color"&gt;Tire:&lt;/span&gt;
          &lt;span class="text-main-color font-medium"&gt;225/50R18&lt;/span&gt;
        &lt;/div&gt;
        &lt;div class="flex justify-between"&gt;
          &lt;span class="text-secondary-color"&gt;Rim:&lt;/span&gt;
          &lt;span class="text-main-color font-medium"&gt;8Jx18 ET40&lt;/span&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;button class="mt-4 w-full border border-primary-color text-primary-color hover:bg-primary-color/10 py-2 rounded-lg transition-colors"&gt;
        View Details
      &lt;/button&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>{% endverbatim %}

        <div class="alert-info">
          <strong>💡 Pro Tip:</strong> The color system automatically adapts to your widget's configured theme. 
          The primary color will match your brand settings, while secondary and accent colors provide consistent contrast and hierarchy.
        </div>
      </div>

      <!-- API Reference Section -->
      <div class="doc-section" id="api-reference">
        <h2>📖 API Reference</h2>
        
        <h3>Widget Creation</h3>
        <pre class="hljs"><code class="javascript">// Basic widget creation
var widget = WheelSizeWidgets.create('#container-id', {
  uuid: 'your-widget-uuid',
  type: 'finder-v2',
  // Optional configuration
  config: {
    theme: 'light',
    language: 'en',
    // ... other config options
  }
});

// Alternative: Use existing iframe
var widget = WheelSizeWidgets.create('#existing-iframe-id');</code></pre>

        <h3>Event Handling Methods</h3>
        <pre class="hljs"><code class="javascript">// Subscribe to an event
widget.on('event-name', function(eventData) {
  console.log('Event received:', eventData);
});

// Unsubscribe from an event
widget.off('event-name', callbackFunction);

// One-time event listener
widget.once('ready:window', function(eventData) {
  console.log('Widget is ready (fires once)');
});</code></pre>

        <h3>Widget Methods</h3>
        <table class="doc-table">
          <thead>
            <tr>
              <th>Method</th>
              <th>Parameters</th>
              <th>Description</th>
              <th>Returns</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">create</code></td>
              <td>selector, config</td>
              <td>Create widget instance</td>
              <td>Widget instance</td>
            </tr>
            <tr>
              <td><code class="code-inline">on</code></td>
              <td>event, callback</td>
              <td>Add event listener</td>
              <td>void</td>
            </tr>
            <tr>
              <td><code class="code-inline">off</code></td>
              <td>event, callback</td>
              <td>Remove event listener</td>
              <td>void</td>
            </tr>
            <tr>
              <td><code class="code-inline">once</code></td>
              <td>event, callback</td>
              <td>One-time event listener</td>
              <td>void</td>
            </tr>
            <tr>
              <td><code class="code-inline">destroy</code></td>
              <td>-</td>
              <td>Destroy widget instance</td>
              <td>void</td>
            </tr>
          </tbody>
        </table>

        <h3>Configuration Options</h3>
        <table class="doc-table">
          <thead>
            <tr>
              <th>Option</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code class="code-inline">uuid</code></td>
              <td>string</td>
              <td>required</td>
              <td>Widget UUID from admin panel</td>
            </tr>
            <tr>
              <td><code class="code-inline">type</code></td>
              <td>string</td>
              <td>"finder-v2"</td>
              <td>Widget type identifier</td>
            </tr>
            <tr>
              <td><code class="code-inline">language</code></td>
              <td>string</td>
              <td>"en"</td>
              <td>Widget language (en, de, fr, etc.)</td>
            </tr>
            <tr>
              <td><code class="code-inline">theme</code></td>
              <td>string</td>
              <td>"light"</td>
              <td>Color theme (light, dark, custom)</td>
            </tr>
            <tr>
              <td><code class="code-inline">flowType</code></td>
              <td>string</td>
              <td>"primary"</td>
              <td>Search flow (primary, alternative, year_select)</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Complete Examples Section -->
      <div class="doc-section" id="examples">
        <h2>💡 Complete Integration Examples</h2>
        
        <h3>Basic Integration</h3>
        <pre class="hljs"><code class="html">&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;title&gt;Finder-v2 Widget Integration&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
  &lt;!-- Widget container --&gt;
  &lt;div id="wheel-finder"&gt;&lt;/div&gt;
  
  &lt;!-- Load widget API --&gt;
  &lt;script src="//services.wheel-size.com/static/widget/code/local/ws-widget.js"&gt;&lt;/script&gt;
  
  &lt;script&gt;
    // Create widget when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
      var widget = WheelSizeWidgets.create('#wheel-finder', {
        uuid: 'your-widget-uuid',
        type: 'finder-v2'
      });
      
      // Listen for widget ready
      widget.on('ready:window', function() {
        console.log('Widget is ready for interaction');
      });
      
      // Track user selections
      widget.on('change:make', function(e) {
        console.log('User selected make:', e.data.value.title);
      });
      
      widget.on('change:model', function(e) {
        console.log('User selected model:', e.data.value.title);
      });
      
      // Handle search results
      widget.on('results:display', function(e) {
        console.log('Results displayed:', e.data.results_count);
        // Send to analytics, update page, etc.
      });
    });
  &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>

        <h3>Advanced Integration with Analytics</h3>
        <pre class="hljs"><code class="html">&lt;script&gt;
document.addEventListener('DOMContentLoaded', function() {
  var widget = WheelSizeWidgets.create('#wheel-finder', {
    uuid: 'your-widget-uuid',
    type: 'finder-v2',
    language: 'en',
    theme: 'light'
  });
  
  // Analytics tracking
  function trackEvent(action, label, value) {
    if (typeof gtag !== 'undefined') {
      gtag('event', action, {
        'event_category': 'WheelFinder',
        'event_label': label,
        'value': value
      });
    }
  }
  
  // Track widget initialization
  widget.on('ready:window', function() {
    trackEvent('widget_loaded', 'finder-v2', 1);
  });
  
  // Track user journey
  var selectionPath = [];
  
  ['year', 'make', 'model', 'generation', 'modification'].forEach(function(field) {
    widget.on('change:' + field, function(e) {
      selectionPath.push({
        field: field,
        value: e.data.value.slug,
        title: e.data.value.title
      });
      
      trackEvent('selection_made', field, selectionPath.length);
    });
  });
  
  // Track search events
  widget.on('search:start', function(e) {
    trackEvent('search_initiated', 'vehicle_search', 1);
    console.log('Search path:', selectionPath);
  });
  
  widget.on('search:complete', function(e) {
    trackEvent('search_completed', 'success', 1);
  });
  
  widget.on('search:error', function(e) {
    trackEvent('search_failed', e.data.error_message, 0);
    console.error('Search error:', e.data.error_message);
  });
  
  // Track results interaction
  widget.on('results:display', function(e) {
    var vehicle = e.data.context.selections;
    var vehicleString = [
      vehicle.year,
      vehicle.make,
      vehicle.model,
      vehicle.generation,
      vehicle.modification
    ].filter(Boolean).join(' / ');
    
    trackEvent('results_shown', vehicleString, e.data.results_count);
    
    // Update page title or breadcrumb
    document.title = 'Wheels for ' + vehicleString;
    
    // Show additional content based on results
    if (e.data.results_count > 0) {
      document.getElementById('related-products').style.display = 'block';
    }
  });
});
&lt;/script&gt;</code></pre>

        <h3>Error Handling and Fallbacks</h3>
        <pre class="hljs"><code class="javascript">document.addEventListener('DOMContentLoaded', function() {
  var widgetContainer = document.getElementById('wheel-finder');
  var errorContainer = document.getElementById('widget-error');
  var loadingIndicator = document.getElementById('widget-loading');
  
  try {
    var widget = WheelSizeWidgets.create('#wheel-finder', {
      uuid: 'your-widget-uuid',
      type: 'finder-v2'
    });
    
    // Set loading timeout
    var loadTimeout = setTimeout(function() {
      loadingIndicator.style.display = 'none';
      errorContainer.innerHTML = 'Widget is taking longer than expected to load...';
      errorContainer.style.display = 'block';
    }, 10000); // 10 second timeout
    
    // Clear timeout on successful load
    widget.on('ready:window', function() {
      clearTimeout(loadTimeout);
      loadingIndicator.style.display = 'none';
      console.log('Widget loaded successfully');
    });
    
    // Handle search errors
    widget.on('search:error', function(e) {
      console.error('Search error:', e.data.error_message);
      
      // Display user-friendly error message
      var userMessage = 'Unable to load results. Please try again.';
      
      if (e.data.error_message.includes('network')) {
        userMessage = 'Connection error. Please check your internet connection.';
      } else if (e.data.error_message.includes('timeout')) {
        userMessage = 'Request timed out. Please try again.';
      }
      
      // Show error notification
      showNotification(userMessage, 'error');
    });
    
    // Handle no results scenario
    widget.on('results:display', function(e) {
      if (e.data.results_count === 0) {
        showNotification('No wheel options found for this vehicle.', 'info');
        // Optionally show alternative suggestions
        showAlternativeSuggestions(e.data.context.selections);
      }
    });
    
  } catch (error) {
    console.error('Failed to initialize widget:', error);
    loadingIndicator.style.display = 'none';
    errorContainer.innerHTML = 'Unable to load the wheel finder. Please refresh the page.';
    errorContainer.style.display = 'block';
  }
});

function showNotification(message, type) {
  // Implementation of notification display
  var notification = document.createElement('div');
  notification.className = 'notification notification-' + type;
  notification.textContent = message;
  document.body.appendChild(notification);
  
  setTimeout(function() {
    notification.remove();
  }, 5000);
}

function showAlternativeSuggestions(selections) {
  // Implementation to show alternative vehicle suggestions
  console.log('Show alternatives for:', selections);
}</code></pre>
      </div>

      <!-- Footer -->
      <div class="doc-section" style="background: #f7fafc; text-align: center;">
        <p style="margin: 0; color: #718096;">
          Finder-v2 Widget Documentation • Version 2.0 • 
          <a href="http://development.local:8000/widget/finder-v2/?config" style="color: #2b6cb0;">Preview Widget</a>
        </p>
      </div>
      
    </div>
  </div>
</div>
{% endblock %}