{% load i18n %}
{% load form_ext %}

<script>
// Template Gallery Data - Simple templates with escaped Django syntax
const TEMPLATE_GALLERY = {
  'default': {
    'name': 'Default Layout',
    'description': 'Clean card-based layout with OE/Aftermarket distinction', 
    'preview': '🏷️ Cards with badges',
    'template': `<div class="space-y-4">
  <h3 class="text-lg font-semibold mb-2 text-main-color">
    \{\{ make.name \}\} \{\{ model.name \}\} (\{\{ start_year \}\}-\{\{ end_year \}\})
  </h3>
  \{\% for wheel in wheels \%\}
    <div class="p-3 rounded-md border \{\% if wheel.is_stock \%\}border-primary-color bg-primary-color/10\{\% else \%\}border-secondary-color\{\% endif \%\}">
      <div class="font-medium uppercase tracking-wide text-sm mb-1 \{\% if wheel.is_stock \%\}text-primary-color\{\% else \%\}text-secondary-color\{\% endif \%\}">
        \{\% if wheel.is_stock \%\}OE option\{\% else \%\}After-market option\{\% endif \%\}
      </div>
      <div class="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span class="text-secondary-color">Front:</span>
          <span class="text-main-color">\{\{ wheel.front.tire \}\} – \{\{ wheel.front.rim \}\}</span>
        </div>
        \{\% if not wheel.showing_fp_only \%\}
          \{\% if wheel.rear.tire \%\}
            <div>
              <span class="text-secondary-color">Rear:</span>
              <span class="text-main-color">\{\{ wheel.rear.tire \}\} – \{\{ wheel.rear.rim \}\}</span>
            </div>
          \{\% endif \%\}
        \{\% endif \%\}
      </div>
    </div>
  \{\% endfor \%\}
</div>`
  },
  'compact': {
    'name': 'Compact List',
    'description': 'Space-efficient list view with essential information',
    'preview': '📋 Minimal rows',
    'template': `<div class="bg-background-color rounded-lg border border-secondary-color overflow-hidden">
  <div class="bg-secondary-color/10 px-4 py-3 border-b border-secondary-color">
    <h3 class="text-lg font-medium text-main-color">
      \{\{ make.name \}\} \{\{ model.name \}\} (\{\{ start_year \}\}-\{\{ end_year \}\})
    </h3>
  </div>
  <div class="divide-y divide-secondary-color">
    \{\% for wheel in wheels \%\}
      <div class="px-4 py-3 hover:bg-secondary-color/10 transition-colors">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full \{\% if wheel.is_stock \%\}bg-primary-color/20 text-primary-color\{\% else \%\}bg-secondary-color/20 text-secondary-color\{\% endif \%\}">
              \{\% if wheel.is_stock \%\}OE\{\% else \%\}AM\{\% endif \%\}
            </span>
            <span class="text-sm font-medium text-main-color">
              \{\{ wheel.front.tire \}\}
            </span>
          </div>
          <span class="text-sm text-secondary-color">
            \{\{ wheel.front.rim \}\}
          </span>
        </div>
        \{\% if not wheel.showing_fp_only \%\}
          \{\% if wheel.rear.tire \%\}
            <div class="mt-1 text-xs text-secondary-color ml-12">
              Rear: \{\{ wheel.rear.tire \}\} on \{\{ wheel.rear.rim \}\}
            </div>
          \{\% endif \%\}
        \{\% endif \%\}
      </div>
    \{\% endfor \%\}
  </div>
</div>`
  },
  'modern': {
    'name': 'Modern Cards',
    'description': 'Beautiful card grid with shadows and hover effects',
    'preview': '🎨 Premium grid',
    'template': `<div class="mb-6">
  <h2 class="text-2xl font-bold text-main-color mb-2">
    \{\{ make.name \}\} \{\{ model.name \}\}
  </h2>

  <p class="text-secondary-color mb-6">\{\{ generation.name \}\}  \{\{ start_year \}\}-\{\{ end_year \}\} •  \{\{ engine.capacity \}\} \{\{ engine.type \}\} \{\{ engine.fuel \}\} \{\{ engine.power.hp \}\}hp</p>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  \{\% for wheel in wheels \%\}
    <div class="bg-background-color rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-secondary-color">
      <div class="p-3">
        <div class="flex items-center justify-between mb-4">
          <div class="text-xl font-bold text-main-color p-1">
            \{\{ wheel.front.rim_diameter \}\}
          </div>
          <span class="px-3 py-1 text-sm font-medium rounded-full \{\% if wheel.is_stock \%\}bg-primary-color/20 text-primary-color\{\% else \%\}bg-accent-color/20 text-accent-color\{\% endif \%\}">
            \{\% if wheel.is_stock \%\}Original\{\% else \%\}Upgrade\{\% endif \%\}
          </span>
        </div>
        
        <div class="space-y-3">
          <div class="bg-secondary-color/10 rounded-lg p-1">
            <div class="text-xs text-secondary-color uppercase tracking-wide mb-1">Tire</div>
            <div class="font-semibold text-main-color">\{\{ wheel.front.tire \}\}</div>
          </div>
          <div class="bg-secondary-color/10 rounded-lg p-1">
            <div class="text-xs text-secondary-color uppercase tracking-wide mb-1">Rim</div>
            <div class="font-semibold text-main-color">\{\{ wheel.front.rim \}\}</div>
          </div>
          \{\% if not wheel.showing_fp_only \%\}
            \{\% if wheel.rear.tire \%\}
              <div class="bg-secondary-color/10 rounded-lg p-1">
                <div class="text-xs text-secondary-color uppercase tracking-wide mb-1">Rear</div>
                <div class="font-semibold text-main-color">\{\{ wheel.rear.tire \}\}</div>
              </div>
            \{\% endif \%\}
          \{\% endif \%\}
        </div>
      </div>
    </div>
  \{\% endfor \%\}
</div>`
  },
  'table': {
    'name': 'Professional Table',
    'description': 'Clean table layout with alternating rows',
    'preview': '📊 Data table',
    'template': `<div class="bg-background-color shadow-sm rounded-lg overflow-hidden">
  <div class="px-6 py-4 bg-secondary-color/10 border-b border-secondary-color">
    <h3 class="text-lg font-medium text-main-color">
      \{\{ make.name \}\} \{\{ model.name \}\} (\{\{ start_year \}\}-\{\{ end_year \}\})
    </h3>
  </div>
  
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-secondary-color">
      <thead class="bg-secondary-color/10">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-secondary-color uppercase tracking-wider">Type</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-secondary-color uppercase tracking-wider">Tire Size</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-secondary-color uppercase tracking-wider">Rim Size</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-secondary-color uppercase tracking-wider">Pressure</th>
        </tr>
      </thead>
      <tbody class="bg-background-color divide-y divide-secondary-color">
        \{\% for wheel in wheels \%\}
          <tr class="\{\% if wheel.is_stock \%\}bg-primary-color/10\{\% else \%\}hover:bg-secondary-color/10\{\% endif \%\}">
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full \{\% if wheel.is_stock \%\}bg-primary-color/20 text-primary-color\{\% else \%\}bg-secondary-color/20 text-secondary-color\{\% endif \%\}">
                \{\% if wheel.is_stock \%\}Original Equipment\{\% else \%\}Aftermarket\{\% endif \%\}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-main-color">
              \{\{ wheel.front.tire \}\}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-color">
              \{\{ wheel.front.rim \}\}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-color">
              \{\{ wheel.front.tire_pressure.bar \}\} bar / \{\{ wheel.front.tire_pressure.psi \}\} psi
            </td>
          </tr>
          \{\% if not wheel.showing_fp_only \%\}
            \{\% if wheel.rear.tire \%\}
                              <tr class="bg-secondary-color/10">
                <td class="px-6 py-2 text-xs text-secondary-color">└ Rear</td>
                <td class="px-6 py-2 text-xs text-secondary-color">\{\{ wheel.rear.tire \}\}</td>
                <td class="px-6 py-2 text-xs text-secondary-color">\{\{ wheel.rear.rim \}\}</td>
                <td class="px-6 py-2 text-xs text-secondary-color">-</td>
              </tr>
            \{\% endif \%\}
          \{\% endif \%\}
        \{\% endfor \%\}
      </tbody>
    </table>
  </div>
</div>`
  },
  'minimal': {
    'name': 'Minimal Clean',
    'description': 'Ultra-clean minimal design with focus on content',
    'preview': '🎯 Simple & clean',
    'template': `<div class="max-w-2xl">
  <header class="mb-8">
    <h1 class="text-3xl font-light text-main-color mb-2">
      \{\{ make.name \}\} \{\{ model.name \}\}
    </h1>
    <p class="text-secondary-color">\{\{ start_year \}\}–\{\{ end_year \}\}</p>
  </header>
  
  <div class="space-y-6">
    \{\% for wheel in wheels \%\}
      <div class="border-l-4 \{\% if wheel.is_stock \%\}border-primary-color bg-primary-color/10\{\% else \%\}border-secondary-color bg-secondary-color/10\{\% endif \%\} pl-6 py-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="font-medium text-main-color">
            \{\{ wheel.front.rim_diameter \}\} Wheels
          </h3>
          <span class="text-sm \{\% if wheel.is_stock \%\}text-primary-color\{\% else \%\}text-secondary-color\{\% endif \%\}">
            \{\% if wheel.is_stock \%\}Factory Standard\{\% else \%\}Alternative Option\{\% endif \%\}
          </span>
        </div>
        
        <dl class="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
          <dt class="text-secondary-color">Tire:</dt>
          <dd class="font-mono text-main-color">\{\{ wheel.front.tire \}\}</dd>
          <dt class="text-secondary-color">Rim:</dt>
          <dd class="font-mono text-main-color">\{\{ wheel.front.rim \}\}</dd>
          \{\% if not wheel.showing_fp_only \%\}
            \{\% if wheel.rear.tire \%\}
              <dt class="text-secondary-color">Rear Tire:</dt>
              <dd class="font-mono text-main-color">\{\{ wheel.rear.tire \}\}</dd>
            \{\% endif \%\}
          \{\% endif \%\}
        </dl>
      </div>
    \{\% endfor \%\}
  </div>
</div>`
  },
  'badges': {
    'name': 'Badge Style',
    'description': 'Colorful badge-based layout with visual hierarchy',
    'preview': '🏆 Badge design',
    'template': `<div class="bg-gradient-to-br from-secondary-color/10 to-secondary-color/20 rounded-2xl p-6">
  <div class="text-center mb-6">
    <h2 class="text-2xl font-bold text-main-color mb-1">
      \{\{ make.name \}\} \{\{ model.name \}\}
    </h2>
    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-secondary-color/20 text-secondary-color">
      \{\{ start_year \}\} - \{\{ end_year \}\}
    </span>
  </div>
  
  <div class="grid gap-4">
    \{\% for wheel in wheels \%\}
      <div class="bg-background-color rounded-xl p-4 shadow-sm border border-secondary-color">
        <div class="flex flex-wrap items-center gap-2 mb-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \{\% if wheel.is_stock \%\}bg-primary-color/20 text-primary-color\{\% else \%\}bg-accent-color/20 text-accent-color\{\% endif \%\}">
            \{\% if wheel.is_stock \%\}✓ OE Standard\{\% else \%\}⚡ Aftermarket\{\% endif \%\}
          </span>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary-color/20 text-secondary-color">
            \{\{ wheel.front.rim_diameter \}\} Diameter
          </span>
          \{\% if wheel.is_runflat_tires \%\}
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-accent-color/20 text-accent-color">
              🛡️ Run-flat
            </span>
          \{\% endif \%\}
        </div>
        
        <div class="flex justify-between items-center">
          <div>
            <div class="font-semibold text-main-color">\{\{ wheel.front.tire \}\}</div>
            <div class="text-sm text-secondary-color">\{\{ wheel.front.rim \}\}</div>
          </div>
          \{\% if not wheel.showing_fp_only \%\}
            \{\% if wheel.rear.tire \%\}
              <div class="text-right">
                <div class="text-xs text-secondary-color">Rear</div>
                <div class="text-sm text-secondary-color">\{\{ wheel.rear.tire \}\}</div>
              </div>
            \{\% endif \%\}
          \{\% endif \%\}
        </div>
      </div>
    \{\% endfor \%\}
  </div>
</div>`
  }
};

// Initialize template gallery on page load
document.addEventListener('DOMContentLoaded', function() {
  initializeTemplateGallery();
  setupTextareaMonitoring();
  prettifyTranslationTextarea();
  setupTranslationValidation(); // Initialize JSON validation
  initializeTemplatePreview(); // Initialize template preview functionality
  
  // Initialize template tabs (set editor as active by default)
  switchTemplateTab('editor');
});

// Template tab switching functionality
function switchTemplateTab(tabName) {
  // Get tab buttons
  const editorTab = document.getElementById('template-editor-tab');
  const previewTab = document.getElementById('template-preview-tab');
  
  // Get tab panels
  const editorPanel = document.getElementById('template-editor-panel');
  const previewPanel = document.getElementById('template-preview-panel');
  
  if (tabName === 'editor') {
    // Show editor, hide preview
    if (editorTab) {
      editorTab.classList.remove('text-gray-600', 'hover:text-gray-900');
      editorTab.classList.add('text-gray-900', 'bg-white', 'shadow-sm');
      editorTab.setAttribute('aria-current', 'page');
    }
    if (previewTab) {
      previewTab.classList.remove('text-gray-900', 'bg-white', 'shadow-sm');
      previewTab.classList.add('text-gray-600', 'hover:text-gray-900');
      previewTab.removeAttribute('aria-current');
    }
    if (editorPanel) {
      editorPanel.classList.remove('hidden');
    }
    if (previewPanel) {
      previewPanel.classList.add('hidden');
    }
  } else if (tabName === 'preview') {
    // Show preview, hide editor
    if (previewTab) {
      previewTab.classList.remove('text-gray-600', 'hover:text-gray-900');
      previewTab.classList.add('text-gray-900', 'bg-white', 'shadow-sm');
      previewTab.setAttribute('aria-current', 'page');
    }
    if (editorTab) {
      editorTab.classList.remove('text-gray-900', 'bg-white', 'shadow-sm');
      editorTab.classList.add('text-gray-600', 'hover:text-gray-900');
      editorTab.removeAttribute('aria-current');
    }
    if (previewPanel) {
      previewPanel.classList.remove('hidden');
    }
    if (editorPanel) {
      editorPanel.classList.add('hidden');
    }
    
    // Trigger preview update when switching to preview tab
    if (tabName === 'preview') {
      debouncePreviewUpdate();
    }
  }
}

function setupTextareaMonitoring() {
  const textarea = document.getElementById('id_interface-output_template');
  if (!textarea) {
    console.warn('🎯 MONITOR: Could not find textarea to monitor');
    return;
  }
  
  console.log('🎯 MONITOR: Setting up textarea monitoring');
  console.log('🎯 MONITOR: Initial textarea value length:', textarea.value.length);
  
  // Monitor all events that could change the textarea
  const events = ['input', 'change', 'keyup', 'paste', 'blur', 'focus'];
  events.forEach(eventType => {
    textarea.addEventListener(eventType, function(event) {
      console.log(`🎯 MONITOR: ${eventType} event - textarea value length:`, textarea.value.length);
      console.log(`🎯 MONITOR: ${eventType} event - first 50 chars:`, textarea.value.substring(0, 50));
    });
  });
  
  // Monitor for external changes (like from other JavaScript)
  let lastValue = textarea.value;
  setInterval(() => {
    if (textarea.value !== lastValue) {
      console.log('🎯 MONITOR: Textarea value changed externally!');
      console.log('🎯 MONITOR: Old length:', lastValue.length, 'New length:', textarea.value.length);
      console.log('🎯 MONITOR: New value preview:', textarea.value.substring(0, 100));
      lastValue = textarea.value;
    }
  }, 1000);
}

function initializeTemplateGallery() {
  const galleryContainer = document.getElementById('template-gallery');
  if (!galleryContainer) return;
  
  // Clear existing content
  galleryContainer.innerHTML = '';
  
  // Create template cards
  Object.entries(TEMPLATE_GALLERY).forEach(([key, template]) => {
    const card = createTemplateCard(key, template);
    galleryContainer.appendChild(card);
  });
}

function createTemplateCard(key, template) {
  const card = document.createElement('div');
  card.className = 'relative bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-indigo-300 hover:shadow-md transition-all duration-200';
  card.setAttribute('data-template', key);
  
  card.innerHTML = `
    <div class="text-lg mb-1">${template.preview}</div>
    <h4 class="font-semibold text-gray-900 mb-1">${template.name}</h4>
    <p class="text-xs text-gray-500 mb-3">${template.description}</p>
    <button class="w-full bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 px-3 py-2 rounded-md text-sm font-medium transition-colors">
      Use Template
    </button>
  `;
  
  // Add click handler
  card.addEventListener('click', function() {
    selectTemplate(key);
  });
  
  return card;
}

function selectTemplate(templateKey) {
  const template = TEMPLATE_GALLERY[templateKey];
  if (!template) {
    console.error('Template not found:', templateKey);
    return;
  }
  
  console.log('🎯 TEMPLATE GALLERY: Selecting template:', templateKey);
  console.log('🎯 TEMPLATE GALLERY: Template data:', template);
  
  // Convert escaped Django syntax back to normal
  const normalizedTemplate = template.template
    .replace(/\\{\\{/g, '{{')
    .replace(/\\}\\}/g, '}}')
    .replace(/\\{\\%/g, '{%')
    .replace(/\\%\\}/g, '%}');
  
  console.log('🎯 TEMPLATE GALLERY: Normalized template length:', normalizedTemplate.length);
  console.log('🎯 TEMPLATE GALLERY: First 200 chars:', normalizedTemplate.substring(0, 200));
  
  // Get the textarea
  const textarea = document.getElementById('id_interface-output_template');
  
  if (!textarea) {
    console.error('🎯 TEMPLATE GALLERY: Textarea not found with id: id_interface-output_template');
    alert('Error: Could not find the output template field. Please refresh the page and try again.');
    return;
  }
  
  console.log('🎯 TEMPLATE GALLERY: Found textarea, current value length:', textarea.value.length);
  console.log('🎯 TEMPLATE GALLERY: Current value preview:', textarea.value.substring(0, 100));
  
  // Store old value for comparison
  const oldValue = textarea.value;
  
  // Update textarea value
  textarea.value = normalizedTemplate;
  
  console.log('🎯 TEMPLATE GALLERY: Updated textarea, new value length:', textarea.value.length);
  console.log('🎯 TEMPLATE GALLERY: Value actually changed:', oldValue !== textarea.value);
  
  // Mark the field as dirty/modified
  textarea.setAttribute('data-modified', 'true');
  
  // Trigger multiple events to ensure form frameworks detect the change
  const events = ['input', 'change', 'keyup', 'blur'];
  events.forEach(eventType => {
    console.log('🎯 TEMPLATE GALLERY: Triggering event:', eventType);
    const event = new Event(eventType, { bubbles: true, cancelable: true });
    textarea.dispatchEvent(event);
  });
  
  // Focus and then blur to ensure the field is marked as touched
  textarea.focus();
  
  // Add a visual indicator that the field has been modified
  textarea.style.borderColor = '#10B981'; // Green border
  textarea.style.borderWidth = '2px';
  
  setTimeout(() => {
    textarea.blur();
    // Reset border after a moment
    setTimeout(() => {
      textarea.style.borderColor = '';
      textarea.style.borderWidth = '';
    }, 2000);
  }, 100);
  
  // Update visual selection
  document.querySelectorAll('[data-template]').forEach(card => {
    card.classList.remove('ring-2', 'ring-indigo-500');
  });
  
  const selectedCard = document.querySelector(`[data-template="${templateKey}"]`);
  if (selectedCard) {
    selectedCard.classList.add('ring-2', 'ring-indigo-500');
  }
  
  // Show success feedback
  showTemplateFeedback(template.name);
  
  console.log('🎯 TEMPLATE GALLERY: Template selection completed');
}

function showTemplateFeedback(templateName) {
  // Create temporary feedback element
  const feedback = document.createElement('div');
  feedback.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-md shadow-lg z-50';
  feedback.innerHTML = `✓ Applied "${templateName}" template`;
  
  document.body.appendChild(feedback);
  
  // Remove after 3 seconds
  setTimeout(() => {
    if (feedback.parentNode) {
      feedback.parentNode.removeChild(feedback);
    }
  }, 3000);
}

function showPlaceholderHelp() {
  document.getElementById('placeholder-help').classList.remove('hidden');
}

function hidePlaceholderHelp() {
  document.getElementById('placeholder-help').classList.add('hidden');
}

function showColorHelp() {
  document.getElementById('color-help').classList.remove('hidden');
}

function hideColorHelp() {
  document.getElementById('color-help').classList.add('hidden');
}

function prettifyTranslationTextarea() {
  const textarea = document.getElementById('id_interface-translation');
  if (!textarea || !textarea.value) return;
  
  try {
    // Parse the potentially escaped JSON
    let jsonStr = textarea.value;
    
    // Remove outer quotes if present
    if (jsonStr.startsWith('"') && jsonStr.endsWith('"')) {
      jsonStr = jsonStr.slice(1, -1);
    }
    
    // Replace escaped newlines and quotes
    jsonStr = jsonStr
      .replace(/\\n/g, '\n')
      .replace(/\\"/g, '"')
      .replace(/\\\\/g, '\\');
    
    // Parse and re-stringify with indentation
    const parsed = JSON.parse(jsonStr);
    textarea.value = JSON.stringify(parsed, null, 2);
    
    // Trigger events to notify form of change
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    textarea.dispatchEvent(new Event('change', { bubbles: true }));
  } catch (e) {
    console.warn('Could not prettify translation JSON:', e);
  }
}

function restoreDefaultTranslations() {
  const defaultTranslations = {
    "year_label": "Year",
    "make_label": "Make", 
    "model_label": "Model",
    "generation_label": "Generation",
    "modification_label": "Modification",
    "select_year": "Select Year",
    "select_make": "Select Make",
    "select_model": "Select Model", 
    "select_generation": "Select Generation",
    "select_modification": "Select Modification",
    "loading": "Loading...",
    "loading_results": "Loading results...",
    "no_results": "No results found. Please try different search criteria.",
    "search_button": "Unlock More Insights at Wheel-Size.com"
  };
  
  const textarea = document.getElementById('id_interface-translation');
  if (textarea) {
    textarea.value = JSON.stringify(defaultTranslations, null, 2);
    // Trigger change event
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    textarea.dispatchEvent(new Event('change', { bubbles: true }));
    validateTranslationJSON(); // Validate after restoring defaults
  }
}

function validateTranslationJSON() {
  const textarea = document.getElementById('id_interface-translation');
  const errorElement = document.getElementById('translation-error');
  const successElement = document.getElementById('translation-success');
  
  if (!textarea) return true;
  
  const value = textarea.value.trim();
  
  // Clear previous states
  textarea.classList.remove('border-red-500', 'border-green-500', 'focus:border-red-500', 'focus:border-green-500');
  if (errorElement) errorElement.style.display = 'none';
  if (successElement) successElement.style.display = 'none';
  
  if (!value) {
    // Empty is valid (will use defaults)
    textarea.classList.add('border-green-500', 'focus:border-green-500');
    if (successElement) {
      successElement.textContent = 'Empty - will use default translations';
      successElement.style.display = 'block';
    }
    return true;
  }
  
  try {
    const parsed = JSON.parse(value);
    
    // Additional validation: ensure it's an object, not array or primitive
    if (typeof parsed !== 'object' || Array.isArray(parsed) || parsed === null) {
      throw new Error('Translation must be a JSON object (not array, string, or null)');
    }
    
    // Success
    textarea.classList.add('border-green-500', 'focus:border-green-500');
    if (successElement) {
      const keyCount = Object.keys(parsed).length;
      successElement.textContent = `Valid JSON with ${keyCount} translation key${keyCount !== 1 ? 's' : ''}`;
      successElement.style.display = 'block';
    }
    return true;
    
  } catch (e) {
    // Error
    textarea.classList.add('border-red-500', 'focus:border-red-500');
    if (errorElement) {
      errorElement.textContent = `Invalid JSON: ${e.message}`;
      errorElement.style.display = 'block';
    }
    return false;
  }
}

function setupTranslationValidation() {
  const textarea = document.getElementById('id_interface-translation');
  if (textarea) {
    // Add event listeners for real-time validation
    textarea.addEventListener('input', validateTranslationJSON);
    textarea.addEventListener('blur', validateTranslationJSON);
    
    // Initial validation
    validateTranslationJSON();
    
    // Add form submission validation
    const form = textarea.closest('form');
    if (form) {
      form.addEventListener('submit', function(e) {
        if (!validateTranslationJSON()) {
          e.preventDefault();
          alert('Please fix the JSON syntax errors in the translation field before saving.');
          textarea.focus();
          return false;
        }
      });
    }
  }
}

// ========================================
// Template Preview Functionality
// ========================================

let currentPreviewTab = 'desktop';
let previewTimeout = null;
let isPreviewLoading = false;

// Template preview will be initialized in the main DOMContentLoaded listener above

function initializeTemplatePreview() {
  console.log('🎨 PREVIEW: Initializing template preview functionality');
  
  const textarea = document.getElementById('id_interface-output_template');
  if (!textarea) {
    console.warn('🎨 PREVIEW: Template textarea not found');
    return;
  }
  
  // Set up live preview updates as user types
  setupLivePreview(textarea);
  
  // Load initial preview if textarea has content
  if (textarea.value.trim()) {
    debouncePreviewUpdate();
  }
}

function setupLivePreview(textarea) {
  // Monitor textarea changes with debouncing
  const events = ['input', 'change', 'keyup', 'paste'];
  events.forEach(eventType => {
    textarea.addEventListener(eventType, function() {
      debouncePreviewUpdate();
    });
  });
  
  // Also monitor theme form changes if they exist
  setupThemeMonitoring();
}

function setupThemeMonitoring() {
  // Monitor theme form changes to update preview
  const themeFields = [
    'id_theme-primary_color',
    'id_theme-secondary_color', 
    'id_theme-accent_color',
    'id_theme-background_color',
    'id_theme-main_color',
    'id_theme-font_family',
    'id_theme-font_size',
    'id_theme-border_radius'
  ];
  
  themeFields.forEach(fieldId => {
    const field = document.getElementById(fieldId);
    if (field) {
      field.addEventListener('change', function() {
        debouncePreviewUpdate();
      });
      // For color inputs, also listen to input event for real-time updates
      if (field.type === 'color') {
        field.addEventListener('input', function() {
          debouncePreviewUpdate();
        });
      }
    }
  });
}

function debouncePreviewUpdate() {
  // Clear existing timeout
  if (previewTimeout) {
    clearTimeout(previewTimeout);
  }
  
  // Set new timeout for preview update
  previewTimeout = setTimeout(() => {
    updateTemplatePreview();
  }, 500); // 500ms delay to avoid too many API calls
}

function updateTemplatePreview() {
  if (isPreviewLoading) {
    return; // Prevent multiple simultaneous requests
  }
  
  const textarea = document.getElementById('id_interface-output_template');
  if (!textarea) {
    return;
  }
  
  const templateContent = textarea.value.trim();
  
  // If template is empty, show placeholder
  if (!templateContent) {
    showPreviewPlaceholder();
    return;
  }
  
  // Collect theme configuration
  const themeConfig = collectThemeConfig();
  
  // Show loading state
  showPreviewLoading();
  
  isPreviewLoading = true;
  
  // Make AJAX request to preview endpoint
  fetch('/widget/finder-v2/template-preview/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': getCSRFToken()
    },
    body: JSON.stringify({
      template: templateContent,
      theme: themeConfig
    })
  })
  .then(response => response.json())
  .then(data => {
    isPreviewLoading = false;
    
    if (data.success) {
      showPreviewContent(data.html);
      hidePreviewError();
    } else {
      showPreviewError(data.error);
    }
  })
  .catch(error => {
    isPreviewLoading = false;
    console.error('🎨 PREVIEW: Error updating preview:', error);
    showPreviewError('Failed to update preview. Please check your internet connection.');
  });
}

function collectThemeConfig() {
  // Collect theme configuration from form fields
  const themeConfig = {};
  
  const themeFields = {
    'primary_color': 'id_theme-primary_color',
    'secondary_color': 'id_theme-secondary_color',
    'accent_color': 'id_theme-accent_color', 
    'background_color': 'id_theme-background_color',
    'main_color': 'id_theme-main_color',
    'font_family': 'id_theme-font_family',
    'font_size': 'id_theme-font_size',
    'border_radius': 'id_theme-border_radius'
  };
  
  Object.entries(themeFields).forEach(([key, fieldId]) => {
    const field = document.getElementById(fieldId);
    if (field && field.value) {
      themeConfig[key] = field.value;
    }
  });
  
  return themeConfig;
}

function switchPreviewTab(tabName) {
  // Update active tab
  currentPreviewTab = tabName;
  
  // Update tab buttons
  document.querySelectorAll('.preview-tab-button').forEach(button => {
    button.classList.remove('border-indigo-500', 'text-indigo-600');
    button.classList.add('border-transparent', 'text-gray-500');
  });
  
  const activeButton = document.getElementById(tabName + '-preview-tab');
  if (activeButton) {
    activeButton.classList.remove('border-transparent', 'text-gray-500');
    activeButton.classList.add('border-indigo-500', 'text-indigo-600');
  }
  
  // Update preview viewports
  document.querySelectorAll('.preview-viewport').forEach(viewport => {
    viewport.classList.add('hidden');
  });
  
  const activeViewport = document.getElementById(tabName + '-preview');
  if (activeViewport) {
    activeViewport.classList.remove('hidden');
  }
  
  // Update device label
  const deviceLabel = document.getElementById('preview-device-label');
  if (deviceLabel) {
    const labels = {
      'desktop': 'Desktop Preview',
      'tablet': 'Tablet Preview (768px)',
      'mobile': 'Mobile Preview (375px)'
    };
    deviceLabel.textContent = labels[tabName] || 'Preview';
  }
}

function refreshTemplatePreview() {
  // Force refresh the preview
  updateTemplatePreview();
}

function showPreviewContent(html) {
  // Update all preview viewports with the rendered HTML
  const viewports = ['desktop', 'tablet', 'mobile'];
  
  viewports.forEach(viewport => {
    const contentEl = document.getElementById(viewport + '-preview-content');
    if (contentEl) {
      contentEl.innerHTML = html;
    }
  });
  
  showPreviewStatus('Preview updated successfully');
}

function showPreviewPlaceholder() {
  const placeholderHTML = `
    <div class="text-gray-500 text-center py-8">
      <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
      </svg>
      Template preview will appear here<br>
      <span class="text-sm">Start typing in the template field above</span>
    </div>
  `;
  
  showPreviewContent(placeholderHTML);
  hidePreviewError();
}

function showPreviewLoading() {
  const loadingHTML = `
    <div class="text-gray-500 text-center py-8">
      <svg class="animate-spin w-8 h-8 mx-auto mb-4 text-gray-400" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      Updating preview...
    </div>
  `;
  
  showPreviewContent(loadingHTML);
}

function showPreviewError(error) {
  const errorEl = document.getElementById('preview-error');
  const errorTextEl = document.getElementById('preview-error-text');
  
  if (errorEl && errorTextEl) {    
    errorTextEl.textContent = error;
    errorEl.classList.remove('hidden');
  }
}

function hidePreviewError() {
  const errorEl = document.getElementById('preview-error');
  if (errorEl) {
    errorEl.classList.add('hidden');
  }
}

function showPreviewStatus(message) {
  const statusEl = document.getElementById('preview-status');
  const statusTextEl = document.getElementById('preview-status-text');
  
  if (statusEl && statusTextEl) {
    statusTextEl.textContent = message;
    statusEl.classList.remove('hidden');
    
    // Hide status after 3 seconds
    setTimeout(() => {
      statusEl.classList.add('hidden');
    }, 3000);
  }
}

function getCSRFToken() {
  // Get CSRF token from Django
  const cookies = document.cookie.split(';');
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'csrftoken') {
      return value;
    }
  }
  
  // Fallback: try to get from meta tag
  const csrfMeta = document.querySelector('meta[name=csrf-token]');
  if (csrfMeta) {
    return csrfMeta.getAttribute('content');
  }
  
  // Fallback: try to get from hidden input
  const csrfInput = document.querySelector('input[name=csrfmiddlewaretoken]');
  if (csrfInput) {
    return csrfInput.value;
  }
  
  return '';
}
</script>


    {# OE Modification Filtering hidden from UI - form field will use default value #}  

    <!-- Hidden fields for tab configuration - finder-v2 only has one tab (by_vehicle) -->
    <!-- These fields are kept for form validation but hidden from UI since tab selection is not needed -->
    <div style="display: none;">
      {{ form.interface.tabs }}
      {{ form.interface.primary_tab }}
    </div>


    <!--API Flow Type-->
    <div class="border-b border-gray-200 my-5 max-w-4xl">
      <div class="form-group {% if form.interface.errors.flow_type %} has-error{% endif %}">
        <label class="control-label"></label>
        <h3 class="text-base font-semibold text-gray-900">
          User Search Path
          
          
        </h3>
        {% if form.interface.flow_type.help_text %}
        <p class="my-2 max-w-4xl text-sm text-gray-500 help-block">
          <!--{{ form.interface.flow_type.help_text }}-->
          Select the order of dropdowns users will see when searching for a vehicle.
        </p>
        {% endif %}
        <div>
          <label for="location" class="block text-sm/6 font-medium text-gray-900">{{ form.interface.flow_type.label }}</label>
          <div class="mt-2 grid grid-cols-1">
                {{ form.interface.flow_type|htmlclass:'col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-1.5 pr-8 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6' }}
            <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        
        

        {% if form.interface.errors.flow_type %}
          <span class="text-danger">{{ form.interface.errors.flow_type.as_text }}</span>
        {% endif %}
      </div>
    </div>

    <!-- Combined Template Editor and Preview Section with Tabs -->
    <div class="border-b border-gray-200 my-5 max-w-4xl">
      <h3 class="text-base font-semibold text-gray-900 mb-3">
        {% trans 'Template Configuration' %}
      </h3>
      <p class="my-2 max-w-4xl text-sm text-gray-500">
        {% trans 'Customize your widget template and preview changes in real-time.' %}
      </p>
      
      <!-- Tab Navigation -->
      <div class="mt-4">
        <nav class="flex space-x-1 bg-gray-100 rounded-lg p-1" aria-label="Tabs">
          <button
            type="button"
            id="template-editor-tab"
            onclick="switchTemplateTab('editor')"
            class="template-tab-btn flex-1 rounded-md py-2 px-3 text-sm font-medium text-gray-900 bg-white shadow-sm transition-all duration-200"
            aria-current="page">
            <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
            </svg>
            {% trans 'Template Editor' %}
          </button>
          <button
            type="button"
            id="template-preview-tab"
            onclick="switchTemplateTab('preview')"
            class="template-tab-btn flex-1 rounded-md py-2 px-3 text-sm font-medium text-gray-600 hover:text-gray-900 transition-all duration-200">
            <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            {% trans 'Live Preview' %}
          </button>
        </nav>
      </div>
      
      <!-- Tab Panels -->
      <div class="mt-4">
        <!-- Template Editor Panel -->
        <div id="template-editor-panel" class="template-tab-panel">
          <div class="form-group {% if form.interface.errors.output_template %} has-error{% endif %}">
            <div class="flex items-center justify-between mb-3">
              <p class="text-sm text-gray-600">
                {% trans 'Customize how search results are displayed. Template is pre-populated with the default design.' %}
              </p>
              <div class="flex space-x-2">
                <a href="#" class="text-sm text-indigo-600 hover:text-indigo-500" onclick="showPlaceholderHelp(); return false;">
                  {% trans 'Placeholders' %}
                </a>
                <span class="text-gray-400">|</span>
                <a href="#" class="text-sm text-indigo-600 hover:text-indigo-500" onclick="showColorHelp(); return false;">
                  {% trans 'Colors' %}
                </a>
              </div>
            </div>
        
        <!-- Template Gallery Accordion -->
        <div class="mb-4">
          <details class="group">
            <summary class="flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-left text-gray-900 bg-gray-50 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1">
              <span class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                </svg>
                {% trans 'Template Gallery' %}
                <span class="ml-2 text-xs text-gray-500">(6 templates)</span>
              </span>
              <svg class="w-4 h-4 text-gray-500 transition-transform group-open:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </summary>
            <div class="mt-3 border border-gray-200 rounded-lg p-4 bg-white">
              <p class="text-xs text-gray-500 mb-3">{% trans 'Click on any template below to apply it to your configuration' %}</p>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3" id="template-gallery">
                <!-- Templates will be loaded here via JavaScript -->
              </div>
            </div>
          </details>
        </div>

        <div class="mt-6">
          <label for="{{ form.interface.output_template.id_for_label }}" class="block text-sm font-medium text-gray-900 mb-3">
            {{ form.interface.output_template.label }}
          </label>
          {{ form.interface.output_template }}
          {% if form.interface.output_template.help_text %}
            <p class="text-xs text-gray-500 mt-1">{{ form.interface.output_template.help_text }}</p>
          {% endif %}
          {% if form.interface.errors.output_template %}
            <span class="text-red-600 text-sm">{{ form.interface.errors.output_template.as_text }}</span>
          {% endif %}
        </div>
        
        <!-- Placeholder Help Modal (hidden by default) -->
        <div id="placeholder-help" class="hidden mt-4 p-4 bg-gray-50 rounded-md max-h-96 overflow-y-auto">
          <h4 class="text-sm font-medium text-gray-900 mb-3">{% trans 'Available Template Placeholders' %}</h4>
          
          <div class="space-y-4 text-xs">
            <!-- Vehicle Information -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Vehicle Information' %}</h5>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600">
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} make.name {% templatetag closevariable %}</code> - Make name</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} make.slug {% templatetag closevariable %}</code> - Make identifier</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} model.name {% templatetag closevariable %}</code> - Model name</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} model.slug {% templatetag closevariable %}</code> - Model identifier</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} start_year {% templatetag closevariable %}</code> - Start year</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} end_year {% templatetag closevariable %}</code> - End year</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} generation.name {% templatetag closevariable %}</code> - Generation name</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} generation.platform {% templatetag closevariable %}</code> - Platform code</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} trim {% templatetag closevariable %}</code> - Trim level</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} name {% templatetag closevariable %}</code> - Modification name</div>
                <div><code class="bg-blue-100 px-1 rounded">{% templatetag openvariable %} slug {% templatetag closevariable %}</code> - Unique identifier</div>
              </div>
            </div>

            <!-- Engine & Technical -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Engine & Technical' %}</h5>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600">
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} engine.fuel {% templatetag closevariable %}</code> - Fuel type</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} engine.capacity {% templatetag closevariable %}</code> - Engine displacement</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} engine.type {% templatetag closevariable %}</code> - Engine configuration</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} engine.power.kW {% templatetag closevariable %}</code> - Power (kW)</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} engine.power.PS {% templatetag closevariable %}</code> - Power (PS)</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} engine.power.hp {% templatetag closevariable %}</code> - Power (HP)</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} engine.code {% templatetag closevariable %}</code> - Engine code</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} tire_type {% templatetag closevariable %}</code> - Vehicle category</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} technical.stud_holes {% templatetag closevariable %}</code> - Number of bolts</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} technical.pcd {% templatetag closevariable %}</code> - Pitch circle diameter</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} technical.centre_bore {% templatetag closevariable %}</code> - Center bore</div>
                <div><code class="bg-green-100 px-1 rounded">{% templatetag openvariable %} technical.bolt_pattern {% templatetag closevariable %}</code> - Bolt pattern</div>
              </div>
            </div>

            <!-- Wheels Loop -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Wheels Loop' %}</h5>
              <div class="mb-2">
                <code class="bg-purple-100 px-1 rounded">{% templatetag openblock %} for wheel in wheels {% templatetag closeblock %} ... {% templatetag openblock %} endfor {% templatetag closeblock %}</code> - Loop through wheel options
              </div>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600 ml-4">
                <div><code class="bg-orange-100 px-1 rounded">{% templatetag openvariable %} wheel.is_stock {% templatetag closevariable %}</code> - True for OE wheels</div>
                <div><code class="bg-orange-100 px-1 rounded">{% templatetag openvariable %} wheel.showing_fp_only {% templatetag closevariable %}</code> - Front/rear identical</div>
                <div><code class="bg-orange-100 px-1 rounded">{% templatetag openvariable %} wheel.is_extra_load_tires {% templatetag closevariable %}</code> - Extra load rating</div>
                <div><code class="bg-orange-100 px-1 rounded">{% templatetag openvariable %} wheel.is_runflat_tires {% templatetag closevariable %}</code> - Run-flat tires</div>
                <div><code class="bg-orange-100 px-1 rounded">{% templatetag openvariable %} wheel.is_recommended_for_winter {% templatetag closevariable %}</code> - Winter suitable</div>
              </div>
            </div>

            <!-- Front Wheel Data -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Front Wheel Data' %}</h5>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600">
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.rim {% templatetag closevariable %}</code> - Complete rim spec</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.rim_diameter {% templatetag closevariable %}</code> - Rim diameter</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.rim_width {% templatetag closevariable %}</code> - Rim width</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.rim_offset {% templatetag closevariable %}</code> - Rim offset</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.tire {% templatetag closevariable %}</code> - Tire size</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.tire_full {% templatetag closevariable %}</code> - Full tire spec</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.tire_width {% templatetag closevariable %}</code> - Tire width</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.tire_aspect_ratio {% templatetag closevariable %}</code> - Aspect ratio</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.load_index {% templatetag closevariable %}</code> - Load index</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.speed_index {% templatetag closevariable %}</code> - Speed rating</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.tire_pressure.bar {% templatetag closevariable %}</code> - Pressure (bar)</div>
                <div><code class="bg-yellow-100 px-1 rounded">{% templatetag openvariable %} wheel.front.tire_pressure.psi {% templatetag closevariable %}</code> - Pressure (PSI)</div>
              </div>
            </div>

            <!-- Rear Wheel Data -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Rear Wheel Data' %}</h5>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600">
                <div><code class="bg-pink-100 px-1 rounded">{% templatetag openvariable %} wheel.rear.rim {% templatetag closevariable %}</code> - Complete rim spec</div>
                <div><code class="bg-pink-100 px-1 rounded">{% templatetag openvariable %} wheel.rear.tire {% templatetag closevariable %}</code> - Tire size</div>
                <div><code class="bg-pink-100 px-1 rounded">{% templatetag openvariable %} wheel.rear.rim_diameter {% templatetag closevariable %}</code> - Rim diameter</div>
                <div><code class="bg-pink-100 px-1 rounded">{% templatetag openvariable %} wheel.rear.tire_width {% templatetag closevariable %}</code> - Tire width</div>
              </div>
            </div>

            <!-- Syntax Examples -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Syntax Examples' %}</h5>
              <div class="space-y-1 text-gray-600">
                <div><code class="bg-indigo-100 px-1 rounded">{% templatetag openvariable %} wheel.is_stock ? "OE" : "Aftermarket" {% templatetag closevariable %}</code> - Ternary operator</div>
                <div><code class="bg-indigo-100 px-1 rounded">{% templatetag openblock %} if wheel.is_stock {% templatetag closeblock %}...{% templatetag openblock %} endif {% templatetag closeblock %}</code> - Simple conditional</div>
                <div><code class="bg-indigo-100 px-1 rounded">{% templatetag openblock %} if not wheel.showing_fp_only {% templatetag closeblock %}...{% templatetag openblock %} endif {% templatetag closeblock %}</code> - Negated condition</div>
                <div><code class="bg-indigo-100 px-1 rounded">{% templatetag openblock %} if condition {% templatetag closeblock %}...{% templatetag openblock %} else {% templatetag closeblock %}...{% templatetag openblock %} endif {% templatetag closeblock %}</code> - If-else</div>
              </div>
            </div>
          </div>
          
          <div class="mt-4 pt-3 border-t border-gray-200">
            <button type="button" onclick="hidePlaceholderHelp()" class="text-sm text-indigo-600 hover:text-indigo-500 font-medium">
              {% trans 'Hide help' %}
            </button>
          </div>
        </div>
        
        <!-- Color Help Modal (hidden by default) -->
        <div id="color-help" class="hidden mt-4 p-4 bg-gray-50 rounded-md max-h-96 overflow-y-auto">
          <h4 class="text-sm font-medium text-gray-900 mb-3">{% trans 'Available Theme Colors' %}</h4>
          
          <div class="space-y-4 text-xs">
            <!-- Primary Colors -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Primary Brand Color' %}</h5>
              <p class="text-gray-600 text-xs mb-2">Primary brand color (buttons, links, highlights)</p>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600">
                <div><code class="bg-blue-100 px-1 rounded">text-primary-color</code> - Primary text color</div>
                <div><code class="bg-blue-100 px-1 rounded">bg-primary-color</code> - Primary background</div>
                <div><code class="bg-blue-100 px-1 rounded">border-primary-color</code> - Primary border</div>
                <div><code class="bg-blue-100 px-1 rounded">hover:bg-primary-color</code> - Primary hover background</div>
              </div>
              <div class="mt-2">
                <p class="text-gray-500 text-xs mb-1">Opacity variants:</p>
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-1 text-gray-600 text-xs">
                  <div><code class="bg-blue-50 px-1 rounded">bg-primary-color/5</code> - 5% opacity</div>
                  <div><code class="bg-blue-50 px-1 rounded">bg-primary-color/10</code> - 10% opacity</div>
                  <div><code class="bg-blue-50 px-1 rounded">bg-primary-color/20</code> - 20% opacity</div>
                  <div><code class="bg-blue-50 px-1 rounded">bg-primary-color/50</code> - 50% opacity</div>
                </div>
              </div>
            </div>

            <!-- Secondary Colors -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Secondary Color' %}</h5>
              <p class="text-gray-600 text-xs mb-2">Secondary color (borders, dividers, subtle elements)</p>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600">
                <div><code class="bg-gray-100 px-1 rounded">text-secondary-color</code> - Secondary text color</div>
                <div><code class="bg-gray-100 px-1 rounded">bg-secondary-color</code> - Secondary background</div>
                <div><code class="bg-gray-100 px-1 rounded">border-secondary-color</code> - Secondary border</div>
                <div><code class="bg-gray-100 px-1 rounded">hover:bg-secondary-color</code> - Secondary hover background</div>
              </div>
              <div class="mt-2">
                <p class="text-gray-500 text-xs mb-1">Opacity variants:</p>
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-1 text-gray-600 text-xs">
                  <div><code class="bg-gray-50 px-1 rounded">bg-secondary-color/5</code> - 5% opacity</div>
                  <div><code class="bg-gray-50 px-1 rounded">bg-secondary-color/10</code> - 10% opacity</div>
                  <div><code class="bg-gray-50 px-1 rounded">bg-secondary-color/20</code> - 20% opacity</div>
                  <div><code class="bg-gray-50 px-1 rounded">bg-secondary-color/50</code> - 50% opacity</div>
                </div>
              </div>
            </div>

            <!-- Accent Colors -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Accent Color' %}</h5>
              <p class="text-gray-600 text-xs mb-2">Accent color (success states, calls-to-action)</p>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600">
                <div><code class="bg-green-100 px-1 rounded">text-accent-color</code> - Accent text color</div>
                <div><code class="bg-green-100 px-1 rounded">bg-accent-color</code> - Accent background</div>
                <div><code class="bg-green-100 px-1 rounded">border-accent-color</code> - Accent border</div>
                <div><code class="bg-green-100 px-1 rounded">hover:bg-accent-color</code> - Accent hover background</div>
              </div>
              <div class="mt-2">
                <p class="text-gray-500 text-xs mb-1">Opacity variants:</p>
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-1 text-gray-600 text-xs">
                  <div><code class="bg-green-50 px-1 rounded">bg-accent-color/5</code> - 5% opacity</div>
                  <div><code class="bg-green-50 px-1 rounded">bg-accent-color/10</code> - 10% opacity</div>
                  <div><code class="bg-green-50 px-1 rounded">bg-accent-color/20</code> - 20% opacity</div>
                  <div><code class="bg-green-50 px-1 rounded">bg-accent-color/50</code> - 50% opacity</div>
                </div>
              </div>
            </div>

            <!-- Background Colors -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Background Color' %}</h5>
              <p class="text-gray-600 text-xs mb-2">Background color for the widget</p>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600">
                <div><code class="bg-purple-100 px-1 rounded">text-background-color</code> - Background as text color</div>
                <div><code class="bg-purple-100 px-1 rounded">bg-background-color</code> - Background color</div>
                <div><code class="bg-purple-100 px-1 rounded">border-background-color</code> - Background as border color</div>
                <div><code class="bg-purple-100 px-1 rounded">hover:bg-background-color</code> - Background hover</div>
              </div>
            </div>

            <!-- Text Colors -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Text Color' %}</h5>
              <p class="text-gray-600 text-xs mb-2">Primary text color</p>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-1 text-gray-600">
                <div><code class="bg-yellow-100 px-1 rounded">text-main-color</code> - Main text color</div>
                <div><code class="bg-yellow-100 px-1 rounded">bg-main-color</code> - Main color as background</div>
                <div><code class="bg-yellow-100 px-1 rounded">border-main-color</code> - Main color as border</div>
                <div><code class="bg-yellow-100 px-1 rounded">hover:text-main-color</code> - Main text hover</div>
              </div>
            </div>

            <!-- Usage Examples -->
            <div>
              <h5 class="font-semibold text-gray-800 mb-2">{% trans 'Usage Examples' %}</h5>
              <div class="space-y-1 text-gray-600 text-xs">
                <div><code class="bg-indigo-100 px-1 rounded">class="bg-primary-color/10 text-primary-color"</code> - Light primary background with primary text</div>
                <div><code class="bg-indigo-100 px-1 rounded">class="border-secondary-color bg-secondary-color/20"</code> - Secondary border with light background</div>
                <div><code class="bg-indigo-100 px-1 rounded">class="bg-accent-color text-background-color"</code> - Accent background with contrasting text</div>
                <div><code class="bg-indigo-100 px-1 rounded">class="hover:bg-primary-color/10 transition-colors"</code> - Hover effect with smooth transition</div>
              </div>
            </div>
          </div>
          
          <div class="mt-4 pt-3 border-t border-gray-200">
            <button type="button" onclick="hideColorHelp()" class="text-sm text-indigo-600 hover:text-indigo-500 font-medium">
              {% trans 'Hide colors' %}
            </button>
          </div>
        </div>
      </div>
    </div>
        
        <!-- Template Preview Panel (Hidden by default) -->
        <div id="template-preview-panel" class="template-tab-panel hidden">
          <div class="form-group">
            <p class="text-sm text-gray-600 mb-4">
              {% trans 'Preview how your custom template will look with sample data. Updates automatically as you type.' %}
            </p>
        
        <!-- Preview Tab Controls -->
        <div class="mb-4">
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                id="desktop-preview-tab"
                type="button"
                class="preview-tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm border-indigo-500 text-indigo-600"
                onclick="switchPreviewTab('desktop')"
                aria-current="page">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                {% trans 'Desktop' %}
              </button>
              <button
                id="tablet-preview-tab"
                type="button"
                class="preview-tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                onclick="switchPreviewTab('tablet')">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"></path>
                </svg>
                {% trans 'Tablet' %}
              </button>
              <button
                id="mobile-preview-tab"
                type="button"
                class="preview-tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                onclick="switchPreviewTab('mobile')">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                </svg>
                {% trans 'Mobile' %}
              </button>
            </nav>
          </div>
        </div>
        
        <!-- Preview Container -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-red-400 rounded-full"></div>
              <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
              <div class="w-3 h-3 bg-green-400 rounded-full"></div>
              <span class="ml-3 text-sm text-gray-500" id="preview-device-label">Desktop Preview</span>
            </div>
            <button
              type="button"
              id="refresh-preview-btn"
              onclick="refreshTemplatePreview()"
              class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              {% trans 'Refresh' %}
            </button>
          </div>
          
          <!-- Desktop Preview -->
          <div id="desktop-preview" class="preview-viewport bg-white border border-gray-300 rounded-lg overflow-auto" style="height: 400px; max-width: 100%;">
            <div id="desktop-preview-content" class="p-4">
              <div class="text-gray-500 text-center py-8">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                {% trans 'Template preview will appear here' %}<br>
                <span class="text-sm">{% trans 'Start typing in the template field above' %}</span>
              </div>
            </div>
          </div>
          
          <!-- Tablet Preview -->
          <div id="tablet-preview" class="preview-viewport bg-white border border-gray-300 rounded-lg overflow-auto hidden" style="height: 400px; max-width: 768px; margin: 0 auto;">
            <div id="tablet-preview-content" class="p-4">
              <div class="text-gray-500 text-center py-8">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                {% trans 'Tablet preview will appear here' %}
              </div>
            </div>
          </div>
          
          <!-- Mobile Preview -->
          <div id="mobile-preview" class="preview-viewport bg-white border border-gray-300 rounded-lg overflow-auto hidden" style="height: 400px; max-width: 375px; margin: 0 auto;">
            <div id="mobile-preview-content" class="p-4">
              <div class="text-gray-500 text-center py-8">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                {% trans 'Mobile preview will appear here' %}
              </div>
            </div>
          </div>
          
          <!-- Preview Status -->
          <div id="preview-status" class="mt-3 text-xs text-gray-500 hidden">
            <span id="preview-status-text"></span>
          </div>
          
          <!-- Preview Error Display -->
          <div id="preview-error" class="mt-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded p-3 hidden">
            <div class="flex">
              <svg class="w-4 h-4 mr-2 mt-0.5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <p class="font-medium">{% trans 'Template Error' %}</p>
                <p id="preview-error-text" class="mt-1"></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
      </div>
    </div>

    <!-- Translations Section -->
    <div class="border-b border-gray-200 my-5 max-w-4xl">
      <div class="form-group {% if form.interface.errors.translation %} has-error{% endif %}">
        <h3 class="text-base font-semibold text-gray-900 mb-3">
          {% trans 'Custom Translations' %}
        </h3>
        <p class="my-2 max-w-4xl text-sm text-gray-500">
          {% trans 'Customize widget text in JSON format. Defaults will be used for missing keys.' %}
        </p>
        <div class="mt-6">
          <label for="{{ form.interface.translation.id_for_label }}" class="block text-sm font-medium text-gray-900 mb-3">
            {{ form.interface.translation.label }}
          </label>
          {{ form.interface.translation }}
          
          <!-- Client-side validation messages -->
          <div id="translation-error" style="display: none;" class="text-red-600 text-sm mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
            <!-- Error message will be inserted here by JavaScript -->
          </div>
          <div id="translation-success" style="display: none;" class="text-green-600 text-sm mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
            <!-- Success message will be inserted here by JavaScript -->
          </div>
          
          {% if form.interface.translation.help_text %}
            <p class="text-xs text-gray-500 mt-1">{{ form.interface.translation.help_text }}</p>
          {% endif %}
          {% if form.interface.errors.translation %}
            <span class="text-red-600 text-sm">{{ form.interface.errors.translation.as_text }}</span>
          {% endif %}
        </div>
        <button type="button" onclick="restoreDefaultTranslations()" class="mt-3 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm">
          Restore Defaults
        </button>
      </div>
    </div>

    {% if object.subscription.is_paid %}
      <div>
        <p class="note">
          You have a paid widget version.
        </p>

        <div class="checkbox">
          <label>
            {{ form.interface.button_to_ws }} {{ form.interface.button_to_ws.label }}
          </label>
        </div>
      </div>
    {% endif %}


