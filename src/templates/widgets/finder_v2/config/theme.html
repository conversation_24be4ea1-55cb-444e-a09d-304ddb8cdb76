{% load i18n %}
{% load form_ext %}

<!-- Theme & Styling Section -->
<div class="mb-8">
  <h3 class="text-base font-semibold text-gray-900 mb-3">
    {% trans 'Theme & Styling' %}
  </h3>
  
  <!-- Theme & Styling Accordion -->
  <div class="mb-6">
    <details class="group">
      <summary class="flex cursor-pointer items-center justify-between w-full px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 focus-visible:bg-gray-100 dark:bg-gray-800/50 dark:text-white dark:border-gray-700 dark:hover:bg-gray-700/50">
        <span class="flex items-center">
          <svg class="size-4 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
          </svg>
          {% trans 'Theme & Visual Customization' %}
          <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(11 options)</span>
        </span>
        <svg class="size-4 text-gray-500 transition-transform duration-200 group-open:rotate-180 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </summary>
      <div class="mt-3 border border-gray-200 rounded-lg p-6 bg-white shadow-sm dark:bg-gray-900 dark:border-gray-700">
        <p class="text-sm text-gray-600 mb-6 dark:text-gray-300">
          {% trans 'Customize the visual appearance of your widget to match your website design.' %}
        </p>
        
        <div class="space-y-8">
          {% if form.theme %}
            <!-- Theme Selection Section -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Theme Name -->
              <div class="space-y-2">
                {% if form.theme.theme_name %}
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.theme_name.label }}
                  </label>
                  <div class="relative">
                    {{ form.theme.theme_name|htmlclass:"block w-full rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-2 focus:outline-indigo-500 focus:ring-0 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 invalid:border-red-500 invalid:text-red-600 focus:invalid:border-red-500 focus:invalid:outline-red-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-indigo-400 dark:disabled:border-gray-700 dark:disabled:bg-gray-800/50" }}
                    {% if form.theme.theme_name.help_text %}
                      <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ form.theme.theme_name.help_text }}</div>
                    {% endif %}
                    {% if form.theme.theme_name.errors %}
                      <div class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.theme.theme_name.errors }}</div>
                    {% endif %}
                  </div>
                {% else %}
                  <label for="theme-theme_name" class="block text-sm font-medium text-gray-900 dark:text-gray-100">Theme Name</label>
                  <div class="relative">
                    <input type="text" name="theme-theme_name" id="theme-theme_name" class="block w-full rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-2 focus:outline-indigo-500 focus:ring-0 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400" placeholder="Custom Theme" value="Custom Theme" />
                    <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">Name for this theme configuration</div>
                  </div>
                {% endif %}
              </div>

              <!-- Predefined Theme Selection -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ form.theme.predefined_theme.label }}
                </label>
                <div class="relative">
                  {{ form.theme.predefined_theme|htmlclass:"block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-2 focus:outline-indigo-500 focus:ring-0 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:disabled:border-gray-700 dark:disabled:bg-gray-800/50" }}
                  {% if form.theme.predefined_theme.help_text %}
                    <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ form.theme.predefined_theme.help_text }}</div>
                  {% endif %}
                  {% if form.theme.predefined_theme.errors %}
                    <div class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.theme.predefined_theme.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Color Customization -->
            <div class="space-y-4">
              <h4 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 dark:text-gray-100 dark:border-gray-700">{% trans 'Color Settings' %}</h4>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Primary Color -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.primary_color.label }}
                  </label>
                  <div class="flex items-center space-x-3">
                    {{ form.theme.primary_color|htmlclass:"size-12 rounded-md border border-gray-300 cursor-pointer focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600" }}
                    <div class="flex-1">
                      {% if form.theme.primary_color.help_text %}
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ form.theme.primary_color.help_text }}</div>
                      {% endif %}
                      {% if form.theme.primary_color.errors %}
                        <div class="text-sm text-red-600 dark:text-red-400">{{ form.theme.primary_color.errors }}</div>
                      {% endif %}
                    </div>
                  </div>
                </div>

                <!-- Secondary Color -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.secondary_color.label }}
                  </label>
                  <div class="flex items-center space-x-3">
                    {{ form.theme.secondary_color|htmlclass:"size-12 rounded-md border border-gray-300 cursor-pointer focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600" }}
                    <div class="flex-1">
                      {% if form.theme.secondary_color.help_text %}
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ form.theme.secondary_color.help_text }}</div>
                      {% endif %}
                      {% if form.theme.secondary_color.errors %}
                        <div class="text-sm text-red-600 dark:text-red-400">{{ form.theme.secondary_color.errors }}</div>
                      {% endif %}
                    </div>
                  </div>
                </div>

                <!-- Accent Color -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.accent_color.label }}
                  </label>
                  <div class="flex items-center space-x-3">
                    {{ form.theme.accent_color|htmlclass:"size-12 rounded-md border border-gray-300 cursor-pointer focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600" }}
                    <div class="flex-1">
                      {% if form.theme.accent_color.help_text %}
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ form.theme.accent_color.help_text }}</div>
                      {% endif %}
                      {% if form.theme.accent_color.errors %}
                        <div class="text-sm text-red-600 dark:text-red-400">{{ form.theme.accent_color.errors }}</div>
                      {% endif %}
                    </div>
                  </div>
                </div>

                <!-- Background Color -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.background_color.label }}
                  </label>
                  <div class="flex items-center space-x-3">
                    {{ form.theme.background_color|htmlclass:"size-12 rounded-md border border-gray-300 cursor-pointer focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600" }}
                    <div class="flex-1">
                      {% if form.theme.background_color.help_text %}
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ form.theme.background_color.help_text }}</div>
                      {% endif %}
                      {% if form.theme.background_color.errors %}
                        <div class="text-sm text-red-600 dark:text-red-400">{{ form.theme.background_color.errors }}</div>
                      {% endif %}
                    </div>
                  </div>
                </div>

                <!-- Text Color -->
                <div class="space-y-2 md:col-span-1">
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.text_color.label }}
                  </label>
                  <div class="flex items-center space-x-3">
                    {{ form.theme.text_color|htmlclass:"size-12 rounded-md border border-gray-300 cursor-pointer focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600" }}
                    <div class="flex-1">
                      {% if form.theme.text_color.help_text %}
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ form.theme.text_color.help_text }}</div>
                      {% endif %}
                      {% if form.theme.text_color.errors %}
                        <div class="text-sm text-red-600 dark:text-red-400">{{ form.theme.text_color.errors }}</div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Typography Settings -->
            <div class="space-y-4">
              <h4 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 dark:text-gray-100 dark:border-gray-700">{% trans 'Typography Settings' %}</h4>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Font Family -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.font_family.label }}
                  </label>
                  <div class="relative">
                    {{ form.theme.font_family|htmlclass:"block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-2 focus:outline-indigo-500 focus:ring-0 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:disabled:border-gray-700 dark:disabled:bg-gray-800/50" }}
                    {% if form.theme.font_family.help_text %}
                      <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ form.theme.font_family.help_text }}</div>
                    {% endif %}
                    {% if form.theme.font_family.errors %}
                      <div class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.theme.font_family.errors }}</div>
                    {% endif %}
                  </div>
                </div>

                <!-- Base Font Size -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.base_font_size.label }}
                  </label>
                  <div class="relative">
                    {{ form.theme.base_font_size|htmlclass:"block w-full rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-2 focus:outline-indigo-500 focus:ring-0 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 invalid:border-red-500 invalid:text-red-600 focus:invalid:border-red-500 focus:invalid:outline-red-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-indigo-400 dark:disabled:border-gray-700 dark:disabled:bg-gray-800/50" }}
                    {% if form.theme.base_font_size.help_text %}
                      <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ form.theme.base_font_size.help_text }}</div>
                    {% endif %}
                    {% if form.theme.base_font_size.errors %}
                      <div class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.theme.base_font_size.errors }}</div>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>

            <!-- Visual Effects -->
            <div class="space-y-4">
              <h4 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 dark:text-gray-100 dark:border-gray-700">{% trans 'Visual Effects' %}</h4>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Border Radius -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.border_radius.label }}
                  </label>
                  <div class="relative">
                    {{ form.theme.border_radius|htmlclass:"block w-full rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-2 focus:outline-indigo-500 focus:ring-0 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 invalid:border-red-500 invalid:text-red-600 focus:invalid:border-red-500 focus:invalid:outline-red-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-indigo-400 dark:disabled:border-gray-700 dark:disabled:bg-gray-800/50" }}
                    {% if form.theme.border_radius.help_text %}
                      <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ form.theme.border_radius.help_text }}</div>
                    {% endif %}
                    {% if form.theme.border_radius.errors %}
                      <div class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.theme.border_radius.errors }}</div>
                    {% endif %}
                  </div>
                </div>

                <!-- Shadow Intensity -->
                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ form.theme.shadow_intensity.label }}
                  </label>
                  <div class="relative">
                    {{ form.theme.shadow_intensity|htmlclass:"block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-2 focus:outline-indigo-500 focus:ring-0 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:disabled:border-gray-700 dark:disabled:bg-gray-800/50" }}
                    {% if form.theme.shadow_intensity.help_text %}
                      <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ form.theme.shadow_intensity.help_text }}</div>
                    {% endif %}
                    {% if form.theme.shadow_intensity.errors %}
                      <div class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.theme.shadow_intensity.errors }}</div>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>

            <!-- Real-time Preview Enhancement -->
            <div class="rounded-lg bg-blue-50 p-4 border border-blue-200 dark:bg-blue-950/20 dark:border-blue-800/30">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="size-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                    {% trans 'Live Preview' %}
                  </h3>
                  <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                    <p class="mb-3">{% trans 'Your theme changes are reflected in real-time in the preview section below. Adjust colors, fonts, and visual effects to see how they look on your widget.' %}</p>
                    <p class="font-medium mb-2">{% trans 'How it works:' %}</p>
                    <ul class="list-disc list-inside space-y-1">
                      <li>{% trans 'Select a predefined theme to automatically fill in all color and styling options' %}</li>
                      <li>{% trans 'Manually adjust individual colors, fonts, and effects for custom styling' %}</li>
                      <li>{% trans 'Click "Update Configuration" to save your theme changes' %}</li>
                      <li>{% trans 'The preview will reload automatically to show your changes' %}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

          {% else %}
            <div class="rounded-lg bg-yellow-50 p-4 border border-yellow-200 dark:bg-yellow-950/20 dark:border-yellow-800/30">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="size-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    {% trans 'Theme Form Not Available' %}
                  </h3>
                  <div class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>{% trans 'The theme customization form is not available for this widget configuration.' %}</p>
                  </div>
                </div>
              </div>
            </div>
          {% endif %}
        </div>
      </div>
    </details>
  </div>
</div>

<!-- Theme Real-time Preview JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  console.log('Theme form initialization');
  
  // Initialize theme form functionality
  initThemeForm();
  
  function initThemeForm() {
    // Handle color picker changes
    document.querySelectorAll('input[type="color"]').forEach(function(colorInput) {
      colorInput.addEventListener('change', function() {
        console.log('Color changed:', this.name, this.value);
        updatePreview();
      });
    });
    
    // Handle predefined theme selection
    const themeSelector = document.querySelector('select[name="theme-predefined_theme"]');
    if (themeSelector) {
      themeSelector.addEventListener('change', function() {
        console.log('Predefined theme changed:', this.value);
        applyPredefinedTheme(this.value);
      });
    }
    
    // Handle typography changes
    document.querySelectorAll('select[name^="theme-font"], input[name^="theme-base_font_size"]').forEach(function(input) {
      input.addEventListener('change', function() {
        console.log('Typography changed:', this.name, this.value);
        updatePreview();
      });
    });
    
    // Handle visual effects changes
    document.querySelectorAll('input[name^="theme-border_radius"], select[name^="theme-shadow_intensity"]').forEach(function(input) {
      input.addEventListener('change', function() {
        console.log('Visual effect changed:', this.name, this.value);
        updatePreview();
      });
    });
  }
  
  function updatePreview() {
    // Update the preview iframe by reloading it with a cache buster
    const iframe = document.querySelector('.iframe-preview iframe');
    if (iframe) {
      // Add timestamp to force reload and bypass cache
      const originalSrc = iframe.src.split('?')[0];
      iframe.src = originalSrc + '?t=' + Date.now();
    }
  }
  
  function applyPredefinedTheme(themeName) {
    console.log('Applying predefined theme:', themeName);
    
    if (!themeName || themeName === '') {
      return;
    }
    
    // Define predefined themes (exactly matching Python predefined_themes.py)
    const predefinedThemes = {
      'modern-blue': {
        name: 'Modern Blue',
        colors: {
          primary: '#2563EB',
          secondary: '#64748B',
          accent: '#0EA5E9',
          background: '#FFFFFF',
          text: '#1E293B'
        },
        typography: {
          font_family: 'Inter',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'medium'
        }
      },
      'corporate-gray': {
        name: 'Corporate Gray',
        colors: {
          primary: '#374151',
          secondary: '#9CA3AF',
          accent: '#F59E0B',
          background: '#F9FAFB',
          text: '#111827'
        },
        typography: {
          font_family: 'system-ui',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.375rem',
          shadow_intensity: 'light'
        }
      },
      'vibrant-green': {
        name: 'Vibrant Green',
        colors: {
          primary: '#059669',
          secondary: '#6B7280',
          accent: '#10B981',
          background: '#FFFFFF',
          text: '#1F2937'
        },
        typography: {
          font_family: 'Roboto',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'medium'
        }
      },
      'elegant-purple': {
        name: 'Elegant Purple',
        colors: {
          primary: '#7C3AED',
          secondary: '#A78BFA',
          accent: '#8B5CF6',
          background: '#FAFAFA',
          text: '#1F2937'
        },
        typography: {
          font_family: 'Inter',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.75rem',
          shadow_intensity: 'heavy'
        }
      },
      'warm-orange': {
        name: 'Warm Orange',
        colors: {
          primary: '#EA580C',
          secondary: '#78716C',
          accent: '#F97316',
          background: '#FFFBEB',
          text: '#1C1917'
        },
        typography: {
          font_family: 'Open Sans',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'medium'
        }
      },
      'ocean-teal': {
        name: 'Ocean Teal',
        colors: {
          primary: '#0D9488',
          secondary: '#6B7280',
          accent: '#14B8A6',
          background: '#F0FDFA',
          text: '#134E4A'
        },
        typography: {
          font_family: 'Lato',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'light'
        }
      },
      'sunset-red': {
        name: 'Sunset Red',
        colors: {
          primary: '#DC2626',
          secondary: '#6B7280',
          accent: '#EF4444',
          background: '#FEF2F2',
          text: '#1F2937'
        },
        typography: {
          font_family: 'Montserrat',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.375rem',
          shadow_intensity: 'medium'
        }
      },
      'midnight-dark': {
        name: 'Midnight Dark',
        colors: {
          primary: '#3B82F6',
          secondary: '#6B7280',
          accent: '#60A5FA',
          background: '#1F2937',
          text: '#F9FAFB'
        },
        typography: {
          font_family: 'Poppins',
          base_font_size: '16px'
        },
        effects: {
          border_radius: '0.5rem',
          shadow_intensity: 'heavy'
        }
      }
    };
    
    const theme = predefinedThemes[themeName];
    if (!theme) {
      console.warn('Unknown predefined theme:', themeName);
      return;
    }
    
    // Apply theme colors
    if (theme.colors) {
      document.querySelector('input[name="theme-primary_color"]').value = theme.colors.primary;
      document.querySelector('input[name="theme-secondary_color"]').value = theme.colors.secondary;
      document.querySelector('input[name="theme-accent_color"]').value = theme.colors.accent;
      document.querySelector('input[name="theme-background_color"]').value = theme.colors.background;
      document.querySelector('input[name="theme-text_color"]').value = theme.colors.text;
    }
    
    // Apply typography
    if (theme.typography) {
      const fontFamilySelect = document.querySelector('select[name="theme-font_family"]');
      if (fontFamilySelect) {
        fontFamilySelect.value = theme.typography.font_family;
      }
      const fontSizeInput = document.querySelector('input[name="theme-base_font_size"]');
      if (fontSizeInput) {
        fontSizeInput.value = theme.typography.base_font_size;
      }
    }
    
    // Apply effects
    if (theme.effects) {
      const borderRadiusInput = document.querySelector('input[name="theme-border_radius"]');
      if (borderRadiusInput) {
        borderRadiusInput.value = theme.effects.border_radius;
      }
      const shadowSelect = document.querySelector('select[name="theme-shadow_intensity"]');
      if (shadowSelect) {
        shadowSelect.value = theme.effects.shadow_intensity;
      }
    }
    
    // Apply theme name
    const themeNameInput = document.querySelector('input[name="theme-theme_name"]');
    if (themeNameInput) {
      themeNameInput.value = theme.name;
    }
    
    // Show visual feedback that theme is being applied
    const themeSelector = document.querySelector('select[name="theme-predefined_theme"]');
    if (themeSelector) {
      themeSelector.style.borderColor = '#28a745';
      setTimeout(() => {
        themeSelector.style.borderColor = '';
      }, 2000);
    }
    
    // Trigger preview update
    updatePreview();
  }
});
</script>

