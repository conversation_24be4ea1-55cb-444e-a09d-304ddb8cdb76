{% load i18n %}
{% load form_ext %}

<!-- Search History Settings Section -->
<div class="mb-8">
  <h3 class="text-base font-semibold text-gray-900 mb-3">
    {% trans 'Search History Settings' %}
  </h3>
  
  <!-- Search History Settings Accordion -->
  <div class="mb-6">
    <details class="group">
      <summary class="flex cursor-pointer items-center justify-between w-full px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 focus-visible:bg-gray-100 dark:bg-gray-800/50 dark:text-white dark:border-gray-700 dark:hover:bg-gray-700/50">
        <span class="flex items-center">
          <svg class="size-4 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          {% trans 'Search History Configuration' %}
          <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(4 options)</span>
        </span>
        <svg class="size-4 text-gray-500 transition-transform duration-200 group-open:rotate-180 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </summary>
      <div class="mt-3 border border-gray-200 rounded-lg p-6 bg-white shadow-sm dark:bg-gray-900 dark:border-gray-700">
        <p class="text-sm text-gray-600 mb-6 dark:text-gray-300">
          {% trans 'Configure how search history behaves for users of this widget. Search history allows users to save and quickly re-execute their recent vehicle searches.' %}
        </p>
        
        <div class="space-y-6">
          <!-- First Row: Two Columns -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Enable Search History -->
            <div class="space-y-2">
              <label class="flex items-center text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ form.search_history.enabled.label }}
                {% if form.search_history.enabled.help_text %}
                  <button type="button" class="ml-1.5 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300" title="{{ form.search_history.enabled.help_text }}">
                    <svg class="size-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                  </button>
                {% endif %}
              </label>
              <div class="relative">
                {{ form.search_history.enabled|htmlclass:"size-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 focus:ring-offset-0 focus:ring-2 disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:checked:bg-indigo-600 dark:focus:ring-indigo-500" }}
                {% if form.search_history.enabled.errors %}
                  <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                    {{ form.search_history.enabled.errors.0 }}
                  </div>
                {% endif %}
              </div>
            </div>

            <!-- Show Timestamps -->
            <div class="space-y-2">
              <label class="flex items-center text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ form.search_history.show_timestamps.label }}
                {% if form.search_history.show_timestamps.help_text %}
                  <button type="button" class="ml-1.5 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300" title="{{ form.search_history.show_timestamps.help_text }}">
                    <svg class="size-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                  </button>
                {% endif %}
              </label>
              <div class="relative">
                {{ form.search_history.show_timestamps|htmlclass:"size-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 focus:ring-offset-0 focus:ring-2 disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:checked:bg-indigo-600 dark:focus:ring-indigo-500" }}
                {% if form.search_history.show_timestamps.errors %}
                  <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                    {{ form.search_history.show_timestamps.errors.0 }}
                  </div>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- Second Row: Two Columns -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Maximum Stored Searches -->
            <div class="space-y-2">
              <label class="flex items-center text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ form.search_history.max_items.label }}
                {% if form.search_history.max_items.help_text %}
                  <button type="button" class="ml-1.5 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300" title="{{ form.search_history.max_items.help_text }}">
                    <svg class="size-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                  </button>
                {% endif %}
              </label>
              <div class="relative">
                {{ form.search_history.max_items|htmlclass:"block w-full rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-2 focus:outline-indigo-500 focus:ring-0 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 invalid:border-red-500 invalid:text-red-600 focus:invalid:border-red-500 focus:invalid:outline-red-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-indigo-400 dark:disabled:border-gray-700 dark:disabled:bg-gray-800/50" }}
                {% if form.search_history.max_items.errors %}
                  <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                    {{ form.search_history.max_items.errors.0 }}
                  </div>
                {% endif %}
              </div>
            </div>

            <!-- Default Display Items -->
            <div class="space-y-2">
              <label class="flex items-center text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ form.search_history.display_items.label }}
                {% if form.search_history.display_items.help_text %}
                  <button type="button" class="ml-1.5 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300" title="{{ form.search_history.display_items.help_text }}">
                    <svg class="size-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                  </button>
                {% endif %}
              </label>
              <div class="relative">
                {{ form.search_history.display_items|htmlclass:"block w-full rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-2 focus:outline-indigo-500 focus:ring-0 disabled:border-gray-200 disabled:bg-gray-50 disabled:text-gray-500 invalid:border-red-500 invalid:text-red-600 focus:invalid:border-red-500 focus:invalid:outline-red-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-indigo-400 dark:disabled:border-gray-700 dark:disabled:bg-gray-800/50" }}
                {% if form.search_history.display_items.errors %}
                  <div class="mt-1 text-sm text-red-600 dark:text-red-400">
                    {{ form.search_history.display_items.errors.0 }}
                  </div>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- Usage Instructions -->
          <div class="mt-8 rounded-lg bg-blue-50 p-4 border border-blue-200 dark:bg-blue-950/20 dark:border-blue-800/30">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="size-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {% trans 'How Search History Works' %}
                </h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                  <ul class="list-disc list-inside space-y-1">
                    <li>{% trans 'Search history is stored locally in the user\'s browser using localStorage' %}</li>
                    <li>{% trans 'Users can click on any previous search to instantly re-execute it' %}</li>
                    <li>{% trans 'Search data is automatically cleaned and deduplicated' %}</li>
                    <li>{% trans 'No personal data is sent to servers - everything stays in the user\'s browser' %}</li>
                    <li>{% trans 'History gracefully degrades if localStorage is not available' %}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </details>
  </div>
</div>

