{% load i18n %}
{% load form_ext %}


<div class="space-y-6">
  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex items-start space-x-2">
      <i class="fa fa-info-circle text-blue-600 mt-0.5"></i>
      <p class="text-sm text-blue-800">
        {% blocktrans %}
          If you change widget language, save configuration to update preview.
        {% endblocktrans %}
      </p>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    {% if not form.config.is_trial %}
      <div class="form-group{% if form.config.errors.name %} has-error{% endif %}">
        <label class="form-label">{% trans 'Widget Name' %}</label>
        <input type="text" class="form-control" name="config-name" value="{{ form.config.name.value }}" required>
        {% if form.config.errors.name %}
          <span class="form-error">{{ form.config.errors.name.as_text }}</span>
        {% endif %}
      </div>
    {% endif %}

    <div class="form-group{% if form.config.errors.lang %} has-error{% endif %}">
      <label class="form-label">{% trans 'Widget Language' %}</label>
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
        <div>
          {{ form.config.lang|htmlclass:'form-control' }}
          {% if form.config.errors.lang %}
            <span class="form-error">{{ form.config.errors.lang.as_text }}</span>
          {% endif %}
        </div>
        <div>
          <a class="btn-warning w-full text-center" href="{% url 'widget-translation:translate' object.type %}" target="_blank">
            <i class="fa fa-plus mr-1"></i>{% trans 'Add your own translation' %}
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Widget Size Configuration -->
<div class="space-y-6">
  <div>

    <h3 class="text-base font-semibold text-gray-900">{% trans 'Customize Size' %}</h3>
    <p class="mt-2 max-w-4xl text-sm text-gray-500">

    </p>
    <!--
    <div class="space-y-4">
      {% if not form.config.is_trial %}
        <div class="bg-gray-50 border border-ws-secondary-200 rounded-lg p-4">
          <div class="flex items-start space-x-2">
            <i class="fa fa-code text-gray-600 mt-0.5"></i>
            <p class="text-sm text-gray-700">
              {% blocktrans %}
                These options will affect only the embedding snippet code.
              {% endblocktrans %}
            </p>
          </div>
        </div>
      {% endif %}
    -->
      <!--
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-start space-x-2">
          <i class="fa fa-exclamation-triangle text-yellow-600 mt-0.5"></i>
          <div class="text-sm text-yellow-800 space-y-2">
            <p>
              {% blocktrans %}
                If you change height from autoresize to fixed or vice versa, preview will be updated only after saving.
              {% endblocktrans %}
            </p>
            <p>
              {% trans 'Widget width must not be less than' %} <strong>{{ object.widget_type.min_width }}px</strong>
              {% trans 'for correct representation.' %}
            </p>
          </div>
        </div>
      </div>
-->
    </div>
  </div>

  <!-- Size Input Fields -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="form-group{% if form.interface.errors.width %} has-error{% endif %}">
      <label class="form-label">{% trans 'Widget Width' %}</label>
      <input type="text" class="form-control" name="interface-width" ng-model="config.width" placeholder="e.g., 600px or 100%">
      <p class="form-help">{% trans 'Empty width means 100%' %}</p>
      {% if form.interface.errors.width %}
        <span class="form-error">{{ form.interface.errors.width.as_text }}</span>
      {% endif %}
    </div>

    <div class="form-group{% if form.interface.errors.height %} has-error{% endif %}">
      <label class="form-label">{% trans 'Widget Height' %}</label>
      <input type="text" class="form-control" name="interface-height" ng-model="config.height" placeholder="e.g., 400px or auto">
      <p class="form-help">{% trans 'Empty height means autoresize' %}</p>
      {% if form.interface.errors.height %}
        <span class="form-error">{{ form.interface.errors.height.as_text }}</span>
      {% endif %}
    </div>
  </div>
</div>
