{% extends 'portal/base.html' %}

{% load i18n %}
{% load static %}
{% load dict_utils %}

{% block title %}{% trans 'Widgets Dashboard' %} - Wheel-size.com{% endblock %}

{% block content %}
  <!-- Widget Dashboard -->
  <div class="space-y-8">
    {% if request.user.is_authenticated %}
      <div class="space-y-12">
        
        {% comment %} ============ PRIMARY WIDGET SECTION (Finder v2) ============ {% endcomment %}
        {% for widget in WidgetType.types.values %}
          {% if widget.type == 'finder-v2' %}
            <div class="bg-blue-50 rounded-xl border-2 border-blue-200 shadow-xl overflow-hidden">
              <!-- Primary Widget Header -->
              <div class="bg-blue-100 px-8 py-6">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <!-- <div class="w-14 h-14 bg-blue-200 rounded-xl flex items-center justify-center">
                      <img src="{% static widget.icon %}" alt="{{ widget.label }}" class="w-8 h-8"/>
                    </div> -->
                    <div>
                      <h1 class="text-2xl font-bold text-blue-900">Wheel Fitment Widget v2</h1>
                      <p class="text-blue-700 mt-1">{% trans 'Latest generation search interface' %}</p>
                    </div>
                  </div>
                  <div class="hidden sm:flex items-center space-x-2">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-600 text-white border border-green-700 shadow-sm">
                      <i class="fa fa-star mr-1"></i>
                      {% trans 'Recommended' %}
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-700 text-white border border-blue-800 shadow-sm">
                      {% trans 'Latest' %}
                    </span>
                  </div>
                </div>
              </div>

              <div class="p-8">
                <!-- Primary Widget Actions -->
                <div class="space-y-6">
                  <!-- Create New Widget Button -->
                  <a href="{% url 'widget:configure' widget.type %}" class="group relative bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 w-full flex items-center justify-center space-x-3">
                    <i class="fa fa-plus text-lg"></i>
                    <span class="text-lg">{% trans 'Create New Widget' %}</span>
                    <i class="fa fa-arrow-right opacity-0 group-hover:opacity-100 transition-all duration-200"></i>
                  </a>

                  <!-- Existing Widgets List -->
                  <div class="space-y-3">
                    {% for config in request.user.widgets|get:widget.type %}
                      <div class="p-6 bg-white border-2 border-blue-100 rounded-xl hover:border-blue-300 hover:shadow-lg transition-all duration-200 group">
                        <div class="flex items-start space-x-4">
                          <i class="fa fa-cog text-blue-600 mt-1 text-lg"></i>
                          <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-3">
                              <h4 class="text-lg font-semibold text-blue-900">{{ config.name }}</h4>
                              <div class="flex items-center space-x-2">
                                {% if config.subscription.is_paid %}
                                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                    <i class="fa fa-usd mr-1"></i>
                                    {% trans 'Paid' %}
                                  </span>
                                {% endif %}
                                <div class="flex items-center space-x-1">
                                  <a href="{% url 'widget:configure' config.slug %}" class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fa fa-pencil mr-1"></i>
                                    {% trans 'Edit' %}
                                  </a>
                                  {% if widget.type == 'finder-v2' %}
                                    <button onclick="deleteWidget('{{ config.slug }}', '{{ config.name|escapejs }}')" class="inline-flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                      <i class="fa fa-trash mr-1"></i>
                                      {% trans 'Delete' %}
                                    </button>
                                  {% endif %}
                                </div>
                              </div>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-blue-700">
                              <p><span class="font-medium text-blue-900">{% trans 'Language' %}:</span> {{ config.language.lang }}</p>
                              <p><span class="font-medium text-blue-900">{% trans 'Last modified' %}:</span> {{ config.updated_at|date:"M d, Y" }}</p>
                              {% if config.params.permissions.domains %}
                                <p class="sm:col-span-2"><span class="font-medium text-blue-900">{% trans 'Domains' %}:</span> {{ config.params.permissions.domains|join:", " }}</p>
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      </div>
                    {% empty %}
                      <div class="text-center py-12 bg-white rounded-xl border-2 border-dashed border-blue-200">
                        <i class="fa fa-inbox text-4xl text-blue-300 mb-4"></i>
                        <h3 class="text-lg font-medium text-blue-900 mb-2">{% trans 'No widgets yet' %}</h3>
                        <p class="text-blue-700">{% trans 'Create your first' %} {{ widget.label|lower }} {% trans 'widget to get started' %}</p>
                      </div>
                    {% endfor %}
                  </div>

                  {% if widget.type != 'finder-v2' %}
                  <!-- Translation Section -->
                  <div class="mt-8 pt-8 border-t border-blue-200">
                    <div class="flex items-center space-x-3 mb-6">
                      <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                        <i class="fa fa-language text-yellow-600 text-lg"></i>
                      </div>
                      <div>
                        <h3 class="text-xl font-semibold text-blue-900">{% trans 'Translations' %}</h3>
                        <p class="text-blue-700">{% trans 'Customize widget text for different languages' %}</p>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <!-- Create New Translation Button -->
                      <a href="{% url 'widget-translation:translate' widget.type %}" class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-3 px-6 rounded-xl transition-colors duration-200 w-full flex items-center justify-center space-x-3">
                        <i class="fa fa-plus"></i>
                        <span>{% trans 'Create New Translation' %}</span>
                      </a>

                      <!-- Existing Translations List -->
                      <div class="space-y-2">
                        {% for wt in request.user.widget_translations|get:widget.type %}
                          <a href="{% url 'widget-translation:translate' wt.type wt.slug %}" class="block p-4 bg-white border border-yellow-200 rounded-lg hover:border-yellow-300 hover:shadow-md transition-all duration-200">
                            <div class="flex items-start space-x-3">
                              <i class="fa fa-pencil text-yellow-600 mt-1"></i>
                              <div class="flex-1 min-w-0">
                                <h4 class="font-semibold text-blue-900 mb-1">{{ wt.language }}</h4>
                                <p class="text-sm text-blue-700">
                                  <span class="font-medium">{% trans 'Translation code' %}:</span> {{ wt.slug }}
                                </p>
                              </div>
                            </div>
                          </a>
                        {% empty %}
                          <div class="text-center py-8 bg-white rounded-lg border border-dashed border-yellow-200">
                            <i class="fa fa-language text-2xl text-yellow-300 mb-2"></i>
                            <p class="text-blue-700">{% trans 'No translations for' %} {{ widget.label|lower }} {% trans 'widgets' %}</p>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>
          {% endif %}
        {% endfor %}

        {% comment %} ============ SECONDARY WIDGET SECTION (Calculator) ============ {% endcomment %}
        {% for widget in WidgetType.types.values %}
          {% if widget.type == 'calc' %}
            <div class="bg-gray-50 rounded-xl border border-gray-200 shadow-lg overflow-hidden">
              <!-- Secondary Widget Header -->
              <div class="bg-gray-600 px-6 py-4">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                    <img src="{% static widget.icon %}" alt="{{ widget.label }}" class="w-7 h-7"/>
                  </div>
                  <div>
                    <h2 class="text-xl font-bold text-white">{{ widget.label }} Widgets</h2>
                    <p class="text-gray-200 text-sm">{% trans 'Wheel size calculator and visualization' %}</p>
                  </div>
                </div>
              </div>

              <div class="p-6">
                <!-- Secondary Widget Actions -->
                <div class="space-y-4">
                  <!-- Create New Widget Button -->
                  <a href="{% url 'widget:configure' widget.type %}" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 w-full flex items-center justify-center space-x-2">
                    <i class="fa fa-plus"></i>
                    <span>{% trans 'Create New Widget' %}</span>
                  </a>

                  <!-- Existing Widgets List -->
                  <div class="space-y-2">
                    {% for config in request.user.widgets|get:widget.type %}
                      <a href="{% url 'widget:configure' config.slug %}" class="block p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all duration-200">
                        <div class="flex items-start space-x-3">
                          <i class="fa fa-pencil text-blue-600 mt-1"></i>
                          <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-2">
                              <h4 class="font-semibold text-gray-900">{{ config.name }}</h4>
                              {% if config.subscription.is_paid %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                  <i class="fa fa-usd mr-1"></i>
                                  {% trans 'Paid' %}
                                </span>
                              {% endif %}
                            </div>
                            <div class="space-y-1 text-sm text-gray-600">
                              <p><span class="font-medium">{% trans 'Language' %}:</span> {{ config.language.lang }}</p>
                              <p><span class="font-medium">{% trans 'Last modified' %}:</span> {{ config.updated_at|date:"M d, Y" }}</p>
                              {% if config.params.permissions.domains %}
                                <p><span class="font-medium">{% trans 'Domains' %}:</span> {{ config.params.permissions.domains|join:", " }}</p>
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      </a>
                    {% empty %}
                      <div class="text-center py-6 text-gray-500 bg-gray-100 rounded-lg">
                        <i class="fa fa-inbox text-2xl mb-2"></i>
                        <p>{% trans 'No' %} {{ widget.label|lower }} {% trans 'widgets yet' %}</p>
                      </div>
                    {% endfor %}
                  </div>

                  {% if widget.type != 'finder-v2' %}
                  <!-- Translation Section -->
                  <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-3 mb-4">
                      <div class="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center">
                        <i class="fa fa-language text-warning-600"></i>
                      </div>
                      <h3 class="text-lg font-semibold text-gray-900">{% trans 'Translations' %}</h3>
                    </div>

                    <div class="space-y-4">
                      <!-- Create New Translation Button -->
                      <a href="{% url 'widget-translation:translate' widget.type %}" class="btn-warning w-full flex items-center justify-center space-x-2 py-3">
                        <i class="fa fa-plus"></i>
                        <span>{% trans 'Create New Translation' %}</span>
                      </a>

                      <!-- Existing Translations List -->
                      <div class="space-y-2">
                        {% for wt in request.user.widget_translations|get:widget.type %}
                          <a href="{% url 'widget-translation:translate' wt.type wt.slug %}" class="block p-4 border border-ws-secondary-200 rounded-lg hover:border-warning-300 hover:shadow-md transition-all duration-200">
                            <div class="flex items-start space-x-3">
                              <i class="fa fa-pencil text-warning-600 mt-1"></i>
                              <div class="flex-1 min-w-0">
                                <h4 class="font-semibold text-gray-900 mb-1">{{ wt.language }}</h4>
                                <p class="text-sm text-gray-600">
                                  <span class="font-medium">{% trans 'Translation code' %}:</span> {{ wt.slug }}
                                </p>
                              </div>
                            </div>
                          </a>
                        {% empty %}
                          <div class="text-center py-6 text-gray-500">
                            <i class="fa fa-language text-2xl mb-2"></i>
                            <p>{% trans 'No translations for' %} {{ widget.label|lower }} {% trans 'widgets' %}</p>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>
          {% endif %}
        {% endfor %}

        {% comment %} ============ LEGACY WIDGET SECTION (Finder v1) ============ {% endcomment %}
        {% for widget in WidgetType.types.values %}
          {% if widget.type == 'finder' %}
            <div class="bg-gray-100 rounded-lg border border-gray-300 shadow-sm overflow-hidden opacity-80">
              <!-- Legacy Widget Header -->
              <div class="bg-gray-500 px-4 py-3">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-md flex items-center justify-center">
                      <img src="{% static widget.icon %}" alt="{{ widget.label }}" class="w-5 h-5 opacity-70"/>
                    </div>
                    <div>
                      <h3 class="text-lg font-medium text-white">{{ widget.label }} Widgets</h3>
                      <p class="text-gray-300 text-xs">{% trans 'Legacy version - for existing configurations only' %}</p>
                    </div>
                  </div>
                  <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-600 text-gray-200">
                    {% trans 'Legacy' %}
                  </span>
                </div>
              </div>

              <div class="p-4">
                <!-- Legacy Notice -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                  <div class="flex items-start space-x-3">
                    <i class="fa fa-exclamation-triangle text-yellow-500 mt-1"></i>
                    <div class="text-sm">
                      <h4 class="font-medium text-yellow-800 mb-1">{% trans 'Legacy Widget Notice' %}</h4>
                      <p class="text-yellow-700">
                        {% blocktrans %}This is the legacy version of our search widget. New widget creation is disabled. 
                        Please use <strong>Search Form v2</strong> for new implementations as it offers enhanced features and better performance.{% endblocktrans %}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Legacy Widget Actions -->
                <div class="space-y-3">
                  <!-- Disabled Create New Button -->
                  <div class="bg-gray-200 text-gray-500 font-medium py-3 px-4 rounded-lg text-center cursor-not-allowed">
                    <i class="fa fa-ban mr-2"></i>
                    <span>{% trans 'New Widget Creation Disabled' %}</span>
                  </div>

                  <!-- Existing Widgets List -->
                  <div class="space-y-2">
                    {% for config in request.user.widgets|get:widget.type %}
                      <a href="{% url 'widget:configure' config.slug %}" class="block p-3 bg-white border border-gray-200 rounded-md hover:border-gray-300 hover:shadow-sm transition-all duration-200">
                        <div class="flex items-start space-x-3">
                          <i class="fa fa-pencil text-gray-500 mt-1 text-sm"></i>
                          <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-1">
                              <h4 class="font-medium text-gray-900 text-sm">{{ config.name }}</h4>
                              {% if config.subscription.is_paid %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                  <i class="fa fa-usd mr-1"></i>
                                  {% trans 'Paid' %}
                                </span>
                              {% endif %}
                            </div>
                            <div class="space-y-1 text-xs text-gray-600">
                              <p><span class="font-medium">{% trans 'Language' %}:</span> {{ config.language.lang }}</p>
                              <p><span class="font-medium">{% trans 'Last modified' %}:</span> {{ config.updated_at|date:"M d, Y" }}</p>
                              {% if config.params.permissions.domains %}
                                <p><span class="font-medium">{% trans 'Domains' %}:</span> {{ config.params.permissions.domains|join:", " }}</p>
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      </a>
                    {% empty %}
                      <div class="text-center py-4 text-gray-500 bg-gray-200 rounded-md">
                        <i class="fa fa-inbox text-lg mb-1"></i>
                        <p class="text-sm">{% trans 'No legacy widgets found' %}</p>
                      </div>
                    {% endfor %}
                  </div>

                  {% if widget.type != 'finder-v2' %}
                  <!-- Translation Section -->
                  <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center space-x-2 mb-3">
                      <div class="w-8 h-8 bg-gray-200 rounded-md flex items-center justify-center">
                        <i class="fa fa-language text-gray-500 text-sm"></i>
                      </div>
                      <h4 class="text-base font-medium text-gray-700">{% trans 'Translations' %}</h4>
                    </div>

                    <div class="space-y-3">
                      <!-- Create New Translation Button -->
                      <a href="{% url 'widget-translation:translate' widget.type %}" class="bg-gray-400 hover:bg-gray-500 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 w-full flex items-center justify-center space-x-2 text-sm">
                        <i class="fa fa-plus"></i>
                        <span>{% trans 'Create New Translation' %}</span>
                      </a>

                      <!-- Existing Translations List -->
                      <div class="space-y-2">
                        {% for wt in request.user.widget_translations|get:widget.type %}
                          <a href="{% url 'widget-translation:translate' wt.type wt.slug %}" class="block p-3 border border-gray-200 rounded-md hover:border-gray-300 hover:shadow-sm transition-all duration-200">
                            <div class="flex items-start space-x-2">
                              <i class="fa fa-pencil text-gray-500 mt-1 text-sm"></i>
                              <div class="flex-1 min-w-0">
                                <h5 class="font-medium text-gray-900 mb-1 text-sm">{{ wt.language }}</h5>
                                <p class="text-xs text-gray-600">
                                  <span class="font-medium">{% trans 'Translation code' %}:</span> {{ wt.slug }}
                                </p>
                              </div>
                            </div>
                          </a>
                        {% empty %}
                          <div class="text-center py-3 text-gray-500">
                            <i class="fa fa-language text-lg mb-1"></i>
                            <p class="text-xs">{% trans 'No translations for legacy widgets' %}</p>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>
          {% endif %}
        {% endfor %}

      </div>
    {% else %}
      <div class="text-center py-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">{% trans 'Login Required' %}</h2>
        <p class="text-gray-600 mb-6">{% trans 'Please login to access your widget dashboard' %}</p>
        <a href="{% url 'auth_login' %}" class="btn btn-primary">{% trans 'Login' %}</a>
      </div>
    {% endif %}
  </div>

  <!-- Delete Widget JavaScript -->
  <script>
    function deleteWidget(widgetSlug, widgetName) {
      if (confirm('{% trans "Are you sure you want to delete the widget" %} "' + widgetName + '"? {% trans "This action cannot be undone." %}')) {
        // Create a form to submit DELETE request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/widget/' + widgetSlug + '/delete/';
        
        // Add CSRF token directly from Django template
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = '{{ csrf_token }}';
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
      }
    }
  </script>
{% endblock content %} 