---
alwaysApply: true
---
# Finder-v2 Widget Development Guide

## Architecture Overview

Finder-v2 is a modern Vue 3 + TailwindCSS widget that replaces the legacy LESS-based finder widget. As of 2025-08-16, the widget has been significantly improved with a modular store architecture, enhanced CSRF protection, and comprehensive error handling.

## Recent Major Improvements (2025-08-16)

### ✅ Store Refactoring Complete
- **824-line monolithic store** successfully split into **6 focused modules** (~130 lines each)
- **100% backward compatibility** maintained
- **Refactored store is now the DEFAULT** (no feature flag needed)
- Average module size reduced from 824 to ~130 lines for better maintainability

### ✅ Enhanced CSRF Protection
- **Session-based tokens** with rotation implemented
- **Stronger security** compared to User-Agent-only approach
- **Cross-domain support** maintained for widget embedding
- **Automatic token refresh** on session changes

### ✅ Error Boundaries Implemented
- **Comprehensive error handling** with user-friendly recovery
- **Smart retry logic** with exponential backoff
- **Parent page protection** - errors contained within widget

## Key Development Files

### Backend (Django)
- **API proxy**: [src/apps/widgets/api_proxy/views.py](mdc:src/apps/widgets/api_proxy/views.py) - Contains `FinderV2WidgetProxyView` with parameter normalization
- **CSRF protection**: [src/apps/widgets/api_proxy/csrf.py](mdc:src/apps/widgets/api_proxy/csrf.py) - Enhanced session-based CSRF tokens
- **Widget type**: [src/apps/widgets/finder_v2/widget_type.py](mdc:src/apps/widgets/finder_v2/widget_type.py) - Widget registration and configuration
- **Forms**: [src/apps/widgets/finder_v2/forms.py](mdc:src/apps/widgets/finder_v2/forms.py) - Configuration forms
- **Default config**: [src/apps/widgets/finder_v2/default_config/config.py](mdc:src/apps/widgets/finder_v2/default_config/config.py) - Default widget settings

### Frontend (Vue 3) - Modular Store Architecture
- **Vue app root**: [src/apps/widgets/finder_v2/app/](mdc:src/apps/widgets/finder_v2/app) - Vue 3 application source
- **Store selector**: [src/apps/widgets/finder_v2/app/src/stores/index.js](mdc:src/apps/widgets/finder_v2/app/src/stores/index.js) - Store version management (refactored is default)
- **Main orchestrator**: [src/apps/widgets/finder_v2/app/src/stores/finder-refactored-final.js](mdc:src/apps/widgets/finder_v2/app/src/stores/finder-refactored-final.js) - Coordinates all modules
- **Store modules** (6 focused modules):
  - `modules/state.js` - Core state management (80 lines)
  - `modules/api-client.js` - API communication with deduplication (120 lines)
  - `modules/filter-builder.js` - Filter logic for regions/brands (60 lines)
  - `modules/vehicle-loader.js` - Vehicle data loading (180 lines)
  - `modules/search-executor.js` - Search execution with bot protection (220 lines)
  - `modules/history-manager.js` - Search history management (120 lines)
- **Legacy store**: [src/apps/widgets/finder_v2/app/src/stores/finder.js](mdc:src/apps/widgets/finder_v2/app/src/stores/finder.js) - Original monolithic store (deprecated, 824 lines)
- **Static build**: [src/apps/widgets/finder_v2/static/finder_v2/](mdc:src/apps/widgets/finder_v2/static/finder_v2) - Built Vue app assets

## Deployment Commands

### Vue.js Build and Deploy
```bash
# Full deployment (build + copy + restart)
   ./deploy-finder-v2.sh dev  - Development build (with console.logs)
   ./deploy-finder-v2.sh prod - Production build (without console.logs)
   ./deploy-finder-v2.sh      - Production build (default)

# Manual build process
docker exec ws_services bash -c "cd src/apps/widgets/finder_v2/app && npm run build"
cp -R src/apps/widgets/finder_v2/app/dist/* src/apps/widgets/finder_v2/static/finder_v2/
```

## Critical Bug Fix: Region Filtering

### Issue
- Frontend sent `region[]=usdm&region[]=cdm` 
- Backend API proxy didn't normalize `region[]` → `region`
- External API ignored bracketed parameters, returned all regions

### Solution
- **Backend**: Parameter normalization in [src/apps/widgets/api_proxy/views.py](mdc:src/apps/widgets/api_proxy/views.py)
- **Frontend**: Custom serializer in [src/apps/widgets/finder_v2/app/src/stores/finder.js](mdc:src/apps/widgets/finder_v2/app/src/stores/finder.js)
- **Tests**: Regression tests in [tests/widget/finder_v2/test_region_filtering.py](mdc:tests/widget/finder_v2/test_region_filtering.py)

## API Integration

### Parameter Normalization Pattern
```python
# Strip [] suffixes from parameter names
normalized_params = {}
for key, value in request.query_params.items():
    clean_key = key.rstrip('[]')
    normalized_params[clean_key] = value
```

### Frontend API Calls
```javascript
// Custom URLSearchParams serializer for arrays
const params = new URLSearchParams()
regionFilters.forEach(region => params.append('region', region))
// Results in: region=usdm&region=cdm (not region[]=usdm)
```

## Testing

### Run Finder-v2 Tests
```bash
# Region filtering tests (critical)
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py

# All finder-v2 tests
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/
```

## Development Workflow

1. **Backend changes**: Modify Django files, restart if needed
2. **Frontend changes**: Edit Vue files in `app/src/`, run build, copy static files
3. **Test changes**: Run relevant tests before deployment
4. **Deploy**: Use `./deploy-finder-v2.sh dev` for development (with console.logs) or `./deploy-finder-v2.sh` for production

## Store Version Management

The refactored modular store is now the **default**. To use different store versions for testing:

### Via URL Parameter
- Default (no parameter): Uses refactored store
- `?useRefactoredStore=0`: Legacy monolithic store
- `?useRefactoredStore=1`: Phase 1 refactored (state module only)
- `?useRefactoredStore=2`: Phase 2 refactored (state + API + vehicle loader)
- `?useRefactoredStore=3`: Final refactored (all 6 modules) - same as default

### Via JavaScript Console
```javascript
// Switch store versions (requires page reload)
switchStore(0)  // Legacy store
switchStore(1)  // Phase 1 refactored
switchStore(2)  // Phase 2 refactored
switchStore(3)  // Final refactored (default)

// Check current store
getCurrentStoreType()  // Returns: 'refactored-final' (default)
```

## Enhanced CSRF Protection

### Implementation Details
- **Session-based tokens**: Uses Django session + User-Agent + timestamp
- **Token rotation**: Automatic rotation every hour for enhanced security
- **Cross-domain support**: Works seamlessly on client websites
- **Backward compatibility**: Falls back to User-Agent-only for older clients

### Security Improvements
```python
# Enhanced token generation (simplified)
def get_enhanced_token(request):
    session_key = request.session.session_key or ''
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    timestamp = int(time.time() // 3600)  # Hour-based rotation
    combined = f"{session_key}:{user_agent}:{timestamp}"
    return hashlib.sha256(combined.encode()).hexdigest()[:32]
```

## Error Handling Features

### Error Boundaries
- **Component-level protection**: Each major component wrapped in error boundaries
- **Graceful degradation**: Widget shows helpful error messages instead of crashing
- **Smart retry logic**: Automatic retry with exponential backoff for transient errors
- **Parent page isolation**: Errors in widget don't affect the host website

### Error Recovery
```javascript
// Automatic retry for API errors
const { retry, attemptCount } = useErrorRecovery()

// User-triggered retry with "Try Again" button
<button @click="retry">Try Again (Attempt {{ attemptCount }})</button>
```

## Key Differences from Legacy Finder

- **No LESS compilation**: Uses TailwindCSS instead
- **Vue 3 + Pinia**: Modern reactive state management
- **Modular store architecture**: 6 focused modules instead of 824-line monolithic file
- **API parameter normalization**: Handles array parameters correctly
- **Enhanced CSRF protection**: Session-based tokens with rotation
- **Error boundaries**: Comprehensive error handling and recovery
- **Isolated testing**: Clean test environment without LESS errors
- **Bundle optimization**: 36% smaller (293KB vs 467KB)

