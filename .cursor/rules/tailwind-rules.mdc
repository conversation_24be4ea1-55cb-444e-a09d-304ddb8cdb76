---
description: Use when CSS styles are used
globs: 
alwaysApply: false
---
- Implement proper Tailwind CSS purging for production builds.
- Use Tailwind's @layer directive for custom styles.
- Implement utility-first CSS approach.
- Follow Tailwind naming conventions.
- Implement Tailwind CSS classes for styling.
- Utilize @apply directive in CSS files for reusable styles.
- Implement responsive design using Tailwind's responsive classes.
- Use Tailwind's configuration file for customization.
- Implement Tailwind CSS purging for production builds.

## Design Principles
- Generous spacing: Use plenty of whitespace, avoid cramped layouts
- Cards: Subtle elevation, consistent padding, avoid heavy borders
- Modern aesthetics: Subtle shadows, rounded corners, clean typography
- Interactive states: Smooth hover effects, button feedback, loading states
- Visual hierarchy: Clear information structure with proper heading levels
- Accessibility: Good color contrast, readable fonts, proper focus states
- Consistent system: Reusable components, unified spacing scale
- Use consistent spacing units(8px, 16px, 24px, 32px) throughout your design system
- Test colors in both light and dark modes
- Implement consistent iconography from a single icon family