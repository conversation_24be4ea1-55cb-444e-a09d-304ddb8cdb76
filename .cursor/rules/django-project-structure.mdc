---
alwaysApply: true
---
# Django Project Structure Guide

## Settings Configuration

### Environment-Specific Settings
- **Development**: [src/settings/dev_docker.py](mdc:src/settings/dev_docker.py) - Docker development environment
- **Production**: [src/settings/aws_prod.py](mdc:src/settings/aws_prod.py) - AWS production settings
- **Testing**: [src/settings/test.py](mdc:src/settings/test.py) - Optimized test settings with LESS compilation disabled
- **Base**: [src/settings/base.py](mdc:src/settings/base.py) - Shared Django settings

## Widget Architecture

### Common Widget Infrastructure
- **Base models**: [src/apps/widgets/common/models/config.py](mdc:src/apps/widgets/common/models/config.py) - `WidgetConfig` model
- **Subscriptions**: [src/apps/widgets/common/models/subscription.py](mdc:src/apps/widgets/common/models/subscription.py) - `WidgetSubscription` model
- **LESS compiler**: [src/apps/widgets/common/less/compiler.py](mdc:src/apps/widgets/common/less/compiler.py) - CSS compilation with test mode support

### Widget Types
- **Finder-v2**: [src/apps/widgets/finder_v2/](mdc:src/apps/widgets/finder_v2) - Modern Vue 3 + TailwindCSS widget
- **Legacy Finder**: [src/apps/widgets/finder/](mdc:src/apps/widgets/finder) - Legacy LESS-based widget
- **Calculator**: [src/apps/widgets/calc/](mdc:src/apps/widgets/calc) - Tire calculator widget

### API Proxy & Security
- **Main proxy**: [src/apps/widgets/api_proxy/views.py](mdc:src/apps/widgets/api_proxy/views.py) - Widget API proxy with parameter normalization
- **Enhanced CSRF**: [src/apps/widgets/api_proxy/csrf.py](mdc:src/apps/widgets/api_proxy/csrf.py) - Session-based CSRF protection with token rotation (2025-08-16)
- **URLs**: [src/apps/widgets/api_proxy/urls.py](mdc:src/apps/widgets/api_proxy/urls.py) - API routing

#### Enhanced CSRF Protection (Implemented 2025-08-16)
- **Session-based tokens**: Combines Django session key + User-Agent + timestamp for stronger security
- **Automatic rotation**: Tokens rotate every hour for enhanced protection
- **Cross-domain support**: Works seamlessly when widgets are embedded on client websites
- **Backward compatibility**: Falls back to User-Agent-only tokens for older widget versions
- **No client-side changes needed**: Enhancement is transparent to existing widget implementations

### Finder-v2 Vue.js Architecture (Refactored 2025-08-16)
- **Main app**: [src/apps/widgets/finder_v2/app/](mdc:src/apps/widgets/finder_v2/app) - Vue 3 application root
- **Store (Modular)**: Now split into 6 focused modules:
  - [src/apps/widgets/finder_v2/app/src/stores/index.js](mdc:src/apps/widgets/finder_v2/app/src/stores/index.js) - Store selector (refactored is default)
  - [src/apps/widgets/finder_v2/app/src/stores/finder-refactored-final.js](mdc:src/apps/widgets/finder_v2/app/src/stores/finder-refactored-final.js) - Main orchestrator
  - [src/apps/widgets/finder_v2/app/src/stores/modules/state.js](mdc:src/apps/widgets/finder_v2/app/src/stores/modules/state.js) - Core state management
  - [src/apps/widgets/finder_v2/app/src/stores/modules/api-client.js](mdc:src/apps/widgets/finder_v2/app/src/stores/modules/api-client.js) - API communication
  - [src/apps/widgets/finder_v2/app/src/stores/modules/filter-builder.js](mdc:src/apps/widgets/finder_v2/app/src/stores/modules/filter-builder.js) - Filter logic
  - [src/apps/widgets/finder_v2/app/src/stores/modules/vehicle-loader.js](mdc:src/apps/widgets/finder_v2/app/src/stores/modules/vehicle-loader.js) - Data loading
  - [src/apps/widgets/finder_v2/app/src/stores/modules/search-executor.js](mdc:src/apps/widgets/finder_v2/app/src/stores/modules/search-executor.js) - Search logic
  - [src/apps/widgets/finder_v2/app/src/stores/modules/history-manager.js](mdc:src/apps/widgets/finder_v2/app/src/stores/modules/history-manager.js) - History management
- **Components**: [src/apps/widgets/finder_v2/app/src/components/](mdc:src/apps/widgets/finder_v2/app/src/components) - Vue components with error boundaries
- **Composables**: [src/apps/widgets/finder_v2/app/src/composables/](mdc:src/apps/widgets/finder_v2/app/src/composables) - Reusable logic

## Development Environment

### Docker Configuration
- **Docker Compose**: [docker-compose.yml](mdc:docker-compose.yml) - Development environment setup
- **Dockerfile**: [docker/Dockerfile](mdc:docker/Dockerfile) - Django container configuration

### Deployment Scripts
- **Finder-v2 deploy**: [deploy-finder-v2.sh](mdc:deploy-finder-v2.sh) - Vue.js build and deployment
- **Test runner**: [scripts/run_tests.sh](mdc:scripts/run_tests.sh) - Clean test execution

## Key Django Apps

### Portal
- **Main portal**: [src/apps/portal/](mdc:src/apps/portal) - User interface and authentication

### Widgets
- **Widget main**: [src/apps/widgets/main/](mdc:src/apps/widgets/main) - Widget management views
- **Common widgets**: [src/apps/widgets/common/](mdc:src/apps/widgets/common) - Shared widget functionality

### Sync
- **Data sync**: [src/apps/sync/](mdc:src/apps/sync) - Data synchronization utilities

## Templates and Static Files

### Templates
- **Widget templates**: [src/templates/widgets/](mdc:src/templates/widgets) - Widget HTML templates
- **Portal templates**: [src/templates/portal/](mdc:src/templates/portal) - Portal HTML templates

### Static Files
- **Widget static**: Widget-specific static files in respective app directories
- **Shared theme**: [src/shared/tailwind-theme.js](mdc:src/shared/tailwind-theme.js) - TailwindCSS theme configuration

## Testing Structure

### Test Organization
- **Widget tests**: [tests/widget/](mdc:tests/widget) - Widget-specific tests
- **General tests**: [tests/](mdc:tests) - General application tests
- **Test configuration**: [pytest.ini](mdc:pytest.ini) - Pytest settings

## Documentation

### Development Docs
- **API documentation**: [docs/api/](mdc:docs/api) - API specifications
- **Development guides**: [docs/development/](mdc:docs/development) - Development workflows
- **Security docs**: [docs/security/](mdc:docs/security) - Security guidelines
- **Upgrade docs**: [docs/upgrade/](mdc:docs/upgrade) - Django upgrade documentation

## URL Configuration

### Main URLs
- **Root URLs**: [src/urls/services.py](mdc:src/urls/services.py) - Main URL configuration
- **Widget URLs**: [src/apps/widgets/urls.py](mdc:src/apps/widgets/urls.py) - Widget routing

## Database Models

### Widget Models
- **Widget Config**: Defined in [src/apps/widgets/common/models/config.py](mdc:src/apps/widgets/common/models/config.py)
- **Widget Subscription**: Defined in [src/apps/widgets/common/models/subscription.py](mdc:src/apps/widgets/common/models/subscription.py)

### Import Patterns
```python
# Correct imports for widget models
from src.apps.widgets.common.models.config import WidgetConfig
from src.apps.widgets.common.models.subscription import WidgetSubscription
```

## Development Commands

### Local Development
```bash
# Start development environment
docker compose up

# Access Django shell
docker compose exec web python manage.py shell

# Run migrations
docker compose exec web python manage.py migrate
```

### Admin Interface
- **URL**: `http://development.local:8000/admin/`
- **Models**: Registered in respective app `admin.py` files

