<!DOCTYPE html>
<html>
<head>
    <title>Enhanced CSRF Widget Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        iframe {
            width: 100%;
            min-height: 600px;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
        .console {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .console-line {
            margin: 2px 0;
        }
        .console-line.error {
            color: #f48771;
        }
        .console-line.success {
            color: #89d185;
        }
        .console-line.info {
            color: #75beff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Enhanced CSRF Widget Test</h1>
        
        <div class="status info">
            <strong>Test Configuration:</strong>
            <ul>
                <li>Widget UUID: 8966286f2ec64c0090e70cec714dbd69</li>
                <li>Enhanced CSRF: Enabled</li>
                <li>Environment: Development</li>
                <li>Expected: Widget should load and make API calls successfully</li>
            </ul>
        </div>
        
        <div id="test-status" class="status warning">
            <strong>Status:</strong> Loading widget...
        </div>
        
        <h2>Widget Iframe:</h2>
        <iframe 
            id="widget-frame"
            src="http://development.local:8000/widget/8966286f2ec64c0090e70cec714dbd69?config"
            frameborder="0">
        </iframe>
        
        <h2>Console Output:</h2>
        <div id="console" class="console">
            <div class="console-line info">Starting enhanced CSRF test...</div>
        </div>
    </div>
    
    <script>
        const consoleDiv = document.getElementById('console');
        const statusDiv = document.getElementById('test-status');
        const iframe = document.getElementById('widget-frame');
        
        function addConsoleMessage(message, type = 'info') {
            const line = document.createElement('div');
            line.className = `console-line ${type}`;
            line.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleDiv.appendChild(line);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        // Monitor iframe load
        iframe.onload = function() {
            addConsoleMessage('Widget iframe loaded successfully', 'success');
            
            // Check if widget is responsive
            setTimeout(() => {
                try {
                    // Try to access iframe content (will fail for cross-origin)
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    addConsoleMessage('Widget document accessible (same-origin)', 'info');
                } catch (e) {
                    addConsoleMessage('Widget loaded (cross-origin security active)', 'info');
                }
                
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '<strong>Status:</strong> Widget loaded successfully! ✅';
            }, 1000);
        };
        
        iframe.onerror = function(e) {
            addConsoleMessage('Widget failed to load: ' + e.message, 'error');
            statusDiv.className = 'status error';
            statusDiv.innerHTML = '<strong>Status:</strong> Widget failed to load ❌';
        };
        
        // Listen for messages from widget
        window.addEventListener('message', function(e) {
            if (e.origin === 'http://development.local:8000') {
                addConsoleMessage('Message from widget: ' + JSON.stringify(e.data), 'info');
            }
        });
        
        // Monitor network activity (for debugging)
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && url.includes('widget')) {
                addConsoleMessage('Network request: ' + url, 'info');
            }
            return originalFetch.apply(this, args);
        };
        
        // Test API endpoint directly
        setTimeout(() => {
            addConsoleMessage('Testing API endpoint directly...', 'info');
            
            fetch('http://development.local:8000/widget/api/refresh-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    widget_uuid: '8966286f2ec64c0090e70cec714dbd69'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.token) {
                    addConsoleMessage('Token refresh endpoint working: ' + data.token.substring(0, 10) + '...', 'success');
                } else {
                    addConsoleMessage('Token refresh failed: ' + JSON.stringify(data), 'error');
                }
            })
            .catch(err => {
                addConsoleMessage('Token refresh error: ' + err.message, 'error');
            });
        }, 2000);
    </script>
</body>
</html>