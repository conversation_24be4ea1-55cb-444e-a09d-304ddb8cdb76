# Wheel Size Services - Project Documentation Index

## 📋 Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Development Environment](#development-environment)
4. [Widget System](#widget-system)
5. [API Documentation](#api-documentation)
6. [Security](#security)
7. [Testing](#testing)
8. [Deployment](#deployment)
9. [Frontend Development](#frontend-development)
10. [Documentation Structure](#documentation-structure)
11. [Quick Reference](#quick-reference)

---

## 🎯 Project Overview

**Wheel Size Services** is a Django-based SaaS platform providing embeddable wheel/tire finder widgets for client websites. The system uses a multi-tenant architecture where each client gets customizable widgets with unique configurations.

### Key Features
- 🔧 **Multi-tenant widget system** with per-client customization
- 🌐 **Cross-domain widget embedding** with secure API proxy
- 🛡️ **Enhanced CSRF protection** for widget API security
- 🎨 **Customizable themes** and visual configurations
- 🌍 **Multi-language support** with translation system
- 📊 **Google Analytics 4 integration** for widget analytics
- 💳 **Subscription-based access** with tiered features

### Technology Stack
- **Backend**: Django 5.0, Python 3.12.0
- **Frontend**: Vue 3, TailwindCSS v4, Vite
- **Database**: PostgreSQL 15
- **Container**: Docker, Amazon Linux base
- **Package Management**: Poetry (Python), npm (JavaScript)

---

## 🏗️ Architecture

### System Architecture
```
┌─────────────────────────────────────────────────┐
│           Client Websites (Multiple)             │
├─────────────────────────────────────────────────┤
│                Widget JavaScript                 │
│         (Embedded on client domains)             │
├─────────────────────────────────────────────────┤
│              API Proxy Layer                     │
│        (CSRF Protection + CORS Headers)          │
├─────────────────────────────────────────────────┤
│            Django Application                    │
│    ┌──────────────┬──────────────┬────────┐    │
│    │  Widget Apps │   Portal App  │  Sync  │    │
│    │  (finder_v2) │    (Admin)    │  App   │    │
│    └──────────────┴──────────────┴────────┘    │
├─────────────────────────────────────────────────┤
│            PostgreSQL Database                   │
└─────────────────────────────────────────────────┘
```

### Directory Structure
```
wheel-size-services/
├── src/                      # Main application code
│   ├── apps/                 # Django applications
│   │   ├── widgets/          # Widget system
│   │   │   ├── finder_v2/    # Modern Vue 3 widget
│   │   │   ├── finder/       # Legacy widget
│   │   │   ├── calc/         # Calculator widget
│   │   │   ├── api_proxy/    # API proxy with CSRF
│   │   │   └── common/       # Shared widget code
│   │   ├── portal/           # Admin portal
│   │   └── sync/             # Data synchronization
│   ├── settings/             # Environment configs
│   ├── templates/            # Django templates
│   └── static/               # Static files
├── tests/                    # Test suite
├── docs/                     # Documentation
├── deployment/               # Deployment configs
├── scripts/                  # Utility scripts
└── docker/                   # Docker configuration
```

---

## 🐳 Development Environment

### Docker Setup
- **Container**: `ws_services` (Django application)
- **Database**: `postgres15` (PostgreSQL 15)
- **Web Server**: `nginx` (static files and reverse proxy)

### Quick Start Commands
```bash
# Start development environment
docker-compose up -d

# Access container shell
docker exec -it ws_services bash

# View application logs
docker logs ws_services --tail 50 -f

# Restart after changes
docker-compose restart web
```

### Environment Configuration
- **Development**: `src.settings.dev_docker`
- **Testing**: `src.settings.test`
- **Production**: `src.settings.aws_prod`

---

## 🎯 Widget System

### Available Widgets

#### Finder-v2 (Modern)
- **Technology**: Vue 3, TailwindCSS v4, Vite
- **Features**: Vehicle search, tire fitment, region filtering
- **Bundle Size**: ~298KB (optimized)
- **Location**: `src/apps/widgets/finder_v2/`

#### Finder (Legacy)
- **Technology**: jQuery, LESS
- **Status**: Maintenance mode
- **Location**: `src/apps/widgets/finder/`

#### Calculator
- **Purpose**: Tire size calculations
- **Location**: `src/apps/widgets/calc/`

### Widget Configuration
- **Admin Interface**: `/admin/` for widget configuration
- **Default Configs**: `{widget}/default_config/config.py`
- **Themes**: `{widget}/default_config/themes.py`
- **Translations**: `{widget}/translations/*.json`

### Widget Request Flow
1. Client website loads widget JavaScript
2. Widget generates CSRF token from User-Agent
3. API calls to `/widget/{type}/api/` with token
4. Server validates token and proxies to backend
5. Response returned with CORS headers

---

## 📡 API Documentation

### Widget API Endpoints
- **Base URL**: `/widget/{widget_type}/api/`
- **Authentication**: CSRF token required
- **Methods**: GET, POST
- **CORS**: Enabled for all origins

### Key API Documentation
- [Wheel Fitment API v2](docs/api/wheel-fitment-api-v2.md)
- [Widget API Testing Guide](docs/security/widget-api-testing-guide.md)

---

## 🔒 Security

### CSRF Protection
- **Custom Implementation**: User-Agent based token generation
- **Validation**: Every API request validated
- **Documentation**: [CSRF Token Algorithm](docs/security/csrf-token-algorithm.md)

### Security Features
- API rate limiting
- Cross-origin security headers
- Input validation and sanitization
- Secure session management

### Security Documentation
- [Widget Security Guide](docs/security/widget-security-guide.md)
- [Finder-v2 Security Audit](docs/security/finder-v2-security-audit.md)
- [Enhanced CSRF Protection](docs/development/protection/enhanced-csrf-completion-report.md)

---

## 🧪 Testing

### Test Structure
```
tests/
├── widget/               # Widget-specific tests
│   ├── finder_v2/        # Finder-v2 tests
│   └── security/         # Security tests
├── README.md             # Test documentation
├── pytest-usage-guide.md # Pytest guide
└── pytest-quick-start.md # Quick start guide
```

### Running Tests
```bash
# Run all tests
docker exec ws_services bash /code/scripts/run_tests.sh tests/

# Run specific test file
docker exec ws_services bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_integration.py

# Run with coverage
docker exec ws_services bash /code/scripts/test_runner.sh tests/ --cov
```

### Test Coverage Areas
- Widget functionality
- CSRF protection
- API proxy behavior
- Theme system
- Form persistence
- Region filtering

---

## 🚀 Deployment

### Deployment Scripts
- **Finder-v2**: `./deploy-finder-v2.sh [dev|prod]`
- **Production**: See [Production Deployment Guide](docs/upgrade/production-deployment-guide.md)

### Build Modes
```bash
# Production build (removes console.logs)
./deploy-finder-v2.sh prod

# Development build (keeps console.logs)
./deploy-finder-v2.sh dev
```

### Deployment Documentation
- [Production Deployment Checklist](docs/deployment/production-deployment-checklist.md)
- [Docker Container Troubleshooting](docs/update/docker-container-troubleshooting.md)

---

## 💻 Frontend Development

### Finder-v2 Widget Development
```bash
# Enter widget directory
cd src/apps/widgets/finder_v2/app

# Install dependencies
npm install

# Development with hot reload
npm run dev

# Production build
npm run build

# Run tests
npm run test
```

### TailwindCSS v4
- **Theme Config**: `src/shared/tailwind-theme.js`
- **Documentation**: [TailwindCSS v4 Implementation Guide](docs/development/tailwindcss-v4-implementation-guide.md)
- **Design Principles**: [TailwindCSS Design Guide](docs/prompts/tailwind-design.md)

### Vue 3 Components
- **Main App**: `src/apps/widgets/finder_v2/app/src/App.vue`
- **Components**: `src/apps/widgets/finder_v2/app/src/components/`
- **Stores**: `src/apps/widgets/finder_v2/app/src/stores/`
- **Utils**: `src/apps/widgets/finder_v2/app/src/utils/`

---

## 📚 Documentation Structure

### Key Documentation Areas

#### Development
- [Finder-v2 Knowledge Transfer](docs/development/finder-v2-knowledge-transfer.md)
- [Console.log Management](docs/development/console-log-management.md)
- [Template System](docs/development/template-system/)
- [Translation System](docs/development/translation/finder-v2-translation-system.md)

#### Features
- [Widget Event System](docs/features/finder-v2-widget-event-system.md)
- [Paid Subscription Behavior](docs/features/finder-v2-paid-subscription-behavior.md)
- [Widget Pricing Plans](docs/features/widget-pricing-plans.md)

#### Analytics
- [GA4 Widget Analytics Setup](docs/analytics/ga4-widget-analytics-setup-guide.md)
- [GA4 Quick Reference](docs/analytics/ga4-quick-reference.md)

#### Security
- [Widget Security Guide](docs/security/widget-security-guide.md)
- [Finder-v2 Security Audit](docs/security/finder-v2-security-audit.md)
- [Enhanced CSRF Protection](docs/development/protection/enhanced-csrf-completion-report.md)

#### Testing
- [Testing Guide](tests/README.md)
- [Pytest Usage Guide](docs/development/testing-procedures.md)

#### Docker
- [Docker Project Configuration](docs/settings/docker-project-configuration.md)
- [Docker Container Troubleshooting](docs/update/docker-container-troubleshooting.md)


---

## ⚡ Quick Reference

### Essential Commands
```bash
# Container management
docker ps --filter name=ws_services
docker-compose restart web
docker exec -it ws_services bash

# Django management
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py migrate
docker exec ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py shell

# Deployment
./deploy-finder-v2.sh dev    # Development build
./deploy-finder-v2.sh prod   # Production build

# Testing
docker exec ws_services bash /code/scripts/run_tests.sh tests/
```

### Important Files
- **Main Config**: [CLAUDE.md](CLAUDE.md) - AI assistant guidance
- **Settings**: `src/settings/dev_docker.py` - Development settings
- **Widget Type**: `src/apps/widgets/finder_v2/widget_type.py`
- **API Proxy**: `src/apps/widgets/api_proxy/views.py`
- **Vue App**: `src/apps/widgets/finder_v2/app/src/App.vue`

### Key URLs
- **Admin Interface**: http://development.local:8000/admin/
- **Widget Test**: http://development.local:8000/widget/finder-v2/test/
- **API Endpoint**: http://development.local:8000/widget/finder-v2/api/

---

## 📝 Additional Resources

### Project Documentation
- [Project README](README.md)
- [Claude Instructions](CLAUDE.md)
- [Docker Configuration](docs/settings/docker-project-configuration.md)

### External Documentation
- [Django Documentation](https://docs.djangoproject.com/)
- [Vue 3 Documentation](https://vuejs.org/)
- [TailwindCSS v4 Documentation](https://tailwindcss.com/)
- [Docker Documentation](https://docs.docker.com/)

---

*Last Updated: August 16, 2025*
*Generated with Claude Code /sc:index command*